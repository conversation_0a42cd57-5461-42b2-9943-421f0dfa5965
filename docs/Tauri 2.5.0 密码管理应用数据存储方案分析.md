# Tauri 2.5.0 密码管理应用数据存储方案分析

## 1. 引言

在使用 Tauri 2.5.0 开发密码管理应用时，选择合适的本地数据存储方案至关重要。本文将从安全性、维护性和同步便利性三个维度，分析几种主流的数据存储方案，并给出最佳实践建议。

## 2. Tauri 2.5.0 支持的主要数据存储方案

### 2.1 Store 插件（键值对存储）

**概述**：
Tauri 官方提供的持久化键值对存储插件，适合存储简单的配置和数据。

**实现方式**：
```rust
// Rust 后端
use tauri_plugin_store::StoreBuilder;

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_store::Builder::default().build())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

```typescript
// 前端使用
import { Store } from '@tauri-apps/plugin-store';

const store = new Store('app.dat');
await store.set('passwords', encryptedData);
const data = await store.get('passwords');
```

**特点**：
- 简单易用，API 友好
- 数据以 JSON 格式存储在本地文件中
- 适合存储中小型数据集
- 不提供内置加密功能，需自行实现

### 2.2 文件系统（自定义 JSON/文件）

**概述**：
使用 Tauri 的文件系统 API 直接读写自定义格式的文件。

**实现方式**：
```rust
// Rust 后端
use tauri_plugin_fs::FsBuilder;

fn main() {
    tauri::Builder::default()
        .plugin(FsBuilder::default().build())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

```typescript
// 前端使用
import { writeTextFile, readTextFile } from '@tauri-apps/plugin-fs';

// 写入加密数据
await writeTextFile('vault.dat', JSON.stringify(encryptedData));

// 读取加密数据
const content = await readTextFile('vault.dat');
const data = JSON.parse(content);
```

**特点**：
- 完全自定义文件格式和存储结构
- 灵活性高，可实现复杂的数据组织
- 需自行处理文件读写、格式转换和错误处理
- 不提供内置加密功能，需自行实现

### 2.3 SQL 插件（SQLite）

**概述**：
Tauri 官方提供的 SQL 数据库插件，支持 SQLite、MySQL 和 PostgreSQL，但在本地应用中主要使用 SQLite。

**实现方式**：
```rust
// Rust 后端
use tauri_plugin_sql::{Builder, Migration, MigrationKind};

fn main() {
    tauri::Builder::default()
        .plugin(
            Builder::default()
                .add_migrations("sqlite:vault.db", vec![Migration {
                    version: 1,
                    description: "create initial tables",
                    sql: "CREATE TABLE IF NOT EXISTS passwords (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        username TEXT,
                        encrypted_password TEXT NOT NULL,
                        website TEXT,
                        notes TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )",
                    kind: MigrationKind::Up,
                }])
                .build(),
        )
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

```typescript
// 前端使用
import Database from '@tauri-apps/plugin-sql';

const db = await Database.load('sqlite:vault.db');
await db.execute(
  'INSERT INTO passwords VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
  [id, name, username, encryptedPassword, website, notes, createdAt, updatedAt]
);
const passwords = await db.select('SELECT * FROM passwords');
```

**特点**：
- 结构化数据存储，支持复杂查询和关系
- 内置事务支持，确保数据一致性
- 成熟的数据库引擎，性能和稳定性有保障
- 支持数据库级别的加密（通过 SQLCipher）
- 适合存储大量结构化数据

### 2.4 Stronghold 插件（加密安全存储）

**概述**：
基于 IOTA Stronghold 的加密安全存储插件，专为存储敏感数据和密钥设计。

**实现方式**：
```rust
// Rust 后端
use tauri_plugin_stronghold::StrongholdBuilder;

fn main() {
    tauri::Builder::default()
        .plugin(StrongholdBuilder::default().build())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

```typescript
// 前端使用
import { Stronghold } from '@tauri-apps/plugin-stronghold';

const stronghold = new Stronghold({ password: 'master-password', snapshotPath: 'vault.stronghold' });
await stronghold.store('passwords', encryptedData);
const data = await stronghold.read('passwords');
```

**特点**：
- 专为安全存储设计，内置高级加密
- 提供内存隔离和防篡改保护
- 适合存储加密密钥和高度敏感数据
- 相对较新，生态和文档可能不如其他方案成熟

### 2.5 系统密钥链集成

**概述**：
通过 Rust 库集成操作系统的密钥链/钥匙串服务。

**实现方式**：
```rust
// Rust 后端
use keyring::Keyring;

#[tauri::command]
fn store_secret(service: String, username: String, password: String) -> Result<(), String> {
    let keyring = Keyring::new(&service, &username).map_err(|e| e.to_string())?;
    keyring.set_password(&password).map_err(|e| e.to_string())
}

#[tauri::command]
fn get_secret(service: String, username: String) -> Result<String, String> {
    let keyring = Keyring::new(&service, &username).map_err(|e| e.to_string())?;
    keyring.get_password().map_err(|e| e.to_string())
}
```

```typescript
// 前端使用
import { invoke } from '@tauri-apps/api';

await invoke('store_secret', { 
  service: 'my-password-manager', 
  username: 'master-key', 
  password: encryptionKey 
});

const key = await invoke('get_secret', { 
  service: 'my-password-manager', 
  username: 'master-key' 
});
```

**特点**：
- 利用操作系统内置的安全存储机制
- 适合存储主密码派生的加密密钥
- 跨平台实现有差异，需要适配不同操作系统
- 不适合存储大量数据，主要用于存储少量敏感信息

## 3. 方案对比分析

### 3.1 安全性对比

| 存储方案 | 加密支持 | 防篡改 | 密钥管理 | 安全隔离 | 总体评分 |
|---------|---------|-------|---------|---------|---------|
| Store 插件 | 需自行实现 | 弱 | 需自行实现 | 无 | ★★☆☆☆ |
| 文件系统 | 需自行实现 | 弱 | 需自行实现 | 无 | ★★☆☆☆ |
| SQLite | 支持 SQLCipher | 中 | 需自行实现 | 部分 | ★★★★☆ |
| Stronghold | 内置高级加密 | 强 | 内置 | 强 | ★★★★★ |
| 系统密钥链 | 由操作系统提供 | 强 | 由操作系统管理 | 强 | ★★★★★ |

**分析**：
- **Store 插件**和**文件系统**方案在安全性上较弱，需要自行实现加密和密钥管理
- **SQLite** 通过 SQLCipher 提供数据库级别的加密，安全性较好
- **Stronghold** 和**系统密钥链**在安全性上表现最佳，专为敏感数据设计

### 3.2 维护性对比

| 存储方案 | 结构化程度 | 版本管理 | 错误恢复 | 调试便利性 | 总体评分 |
|---------|-----------|---------|---------|-----------|---------|
| Store 插件 | 低 | 弱 | 弱 | 高 | ★★★☆☆ |
| 文件系统 | 自定义 | 需自行实现 | 需自行实现 | 中 | ★★☆☆☆ |
| SQLite | 高 | 支持迁移 | 支持事务和回滚 | 高 | ★★★★★ |
| Stronghold | 中 | 中 | 中 | 低 | ★★★☆☆ |
| 系统密钥链 | 低 | 无 | 由操作系统提供 | 低 | ★★☆☆☆ |

**分析**：
- **SQLite** 在维护性上表现最佳，提供完整的结构化数据管理、迁移和事务支持
- **Store 插件**维护相对简单，但缺乏高级数据管理功能
- **文件系统**方案需要自行处理所有维护相关的逻辑，工作量大
- **Stronghold** 和**系统密钥链**主要面向安全性，在维护性上相对较弱

### 3.3 同步便利性对比

| 存储方案 | 增量同步 | 冲突解决 | 数据压缩 | API 友好度 | 总体评分 |
|---------|---------|---------|---------|-----------|---------|
| Store 插件 | 需自行实现 | 需自行实现 | 无 | 高 | ★★★☆☆ |
| 文件系统 | 需自行实现 | 需自行实现 | 需自行实现 | 中 | ★★☆☆☆ |
| SQLite | 支持 | 支持事务 | 内置 | 高 | ★★★★★ |
| Stronghold | 弱 | 弱 | 未知 | 中 | ★★☆☆☆ |
| 系统密钥链 | 不适用 | 不适用 | 不适用 | 低 | ★☆☆☆☆ |

**分析**：
- **SQLite** 在同步便利性上表现最佳，支持增量同步、事务和冲突解决
- **Store 插件**的 JSON 格式便于传输，但需自行实现同步逻辑
- **文件系统**方案完全自定义，同步逻辑复杂度高
- **Stronghold** 主要面向安全性，同步功能有限
- **系统密钥链**不适合作为主要数据存储，仅用于存储少量敏感信息

## 4. 密码管理应用的最佳存储方案

### 4.1 推荐方案：混合存储架构

基于上述分析，对于 Tauri 2.5.0 密码管理应用，我们推荐采用**混合存储架构**：

1. **主数据存储**：使用 **SQLite + SQLCipher** 存储所有密码项和相关元数据
2. **加密密钥存储**：使用**系统密钥链**存储主密码派生的加密密钥
3. **配置存储**：使用 **Store 插件**存储应用配置和非敏感设置

这种混合架构结合了各方案的优势，既保证了安全性，又兼顾了维护性和同步便利性。

### 4.2 方案优势

1. **安全性**：
   - 所有密码数据通过 SQLCipher 加密存储
   - 加密密钥存储在操作系统的安全密钥链中
   - 实现了数据和密钥的分离存储，提高安全性

2. **维护性**：
   - SQLite 提供完整的数据库功能，支持复杂查询和关系
   - 支持数据库迁移，便于版本升级
   - 事务支持确保数据一致性

3. **同步便利性**：
   - SQLite 数据库可以方便地进行增量同步
   - 支持冲突检测和解决
   - 可以通过 API 轻松实现与远程服务器的同步

## 5. 实现指南

### 5.1 数据库设计

```sql
-- 密码库表
CREATE TABLE IF NOT EXISTS vaults (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    last_synced_at TEXT
);

-- 密码项表
CREATE TABLE IF NOT EXISTS items (
    id TEXT PRIMARY KEY,
    vault_id TEXT NOT NULL,
    type TEXT NOT NULL, -- 'login', 'card', 'note', etc.
    name TEXT NOT NULL,
    favorite INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (vault_id) REFERENCES vaults(id) ON DELETE CASCADE
);

-- 登录信息表
CREATE TABLE IF NOT EXISTS logins (
    item_id TEXT PRIMARY KEY,
    username TEXT,
    encrypted_password TEXT NOT NULL,
    website TEXT,
    notes TEXT,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);

-- 同步状态表
CREATE TABLE IF NOT EXISTS sync_status (
    id TEXT PRIMARY KEY,
    last_sync_time TEXT,
    last_sync_status TEXT,
    sync_token TEXT
);
```

### 5.2 加密实现

```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};
use argon2::{Argon2, PasswordHasher};
use argon2::password_hash::SaltString;
use rand::{rngs::OsRng, RngCore};
use keyring::Keyring;
use base64::{Engine as _, engine::general_purpose};

// 从主密码派生加密密钥
fn derive_key_from_password(password: &str, salt: &[u8]) -> Result<Vec<u8>, String> {
    let salt = SaltString::from_b64(general_purpose::STANDARD.encode(salt).as_str())
        .map_err(|e| e.to_string())?;
    
    let argon2 = Argon2::default();
    let password_hash = argon2
        .hash_password(password.as_bytes(), &salt)
        .map_err(|e| e.to_string())?;
    
    Ok(password_hash.hash.unwrap().as_bytes().to_vec())
}

// 存储加密密钥到系统密钥链
fn store_encryption_key(key: &[u8]) -> Result<(), String> {
    let keyring = Keyring::new("my-password-manager", "master-key")
        .map_err(|e| e.to_string())?;
    
    keyring.set_password(&general_purpose::STANDARD.encode(key))
        .map_err(|e| e.to_string())
}

// 从系统密钥链获取加密密钥
fn get_encryption_key() -> Result<Vec<u8>, String> {
    let keyring = Keyring::new("my-password-manager", "master-key")
        .map_err(|e| e.to_string())?;
    
    let encoded_key = keyring.get_password().map_err(|e| e.to_string())?;
    general_purpose::STANDARD.decode(encoded_key).map_err(|e| e.to_string())
}

// 加密数据
fn encrypt_data(data: &str, key: &[u8]) -> Result<String, String> {
    let key = Key::<Aes256Gcm>::from_slice(key);
    let cipher = Aes256Gcm::new(key);
    
    let mut nonce_bytes = [0u8; 12];
    OsRng.fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);
    
    let ciphertext = cipher
        .encrypt(nonce, data.as_bytes().as_ref())
        .map_err(|e| e.to_string())?;
    
    // 将 nonce 和密文拼接后进行 Base64 编码
    let mut result = Vec::with_capacity(nonce_bytes.len() + ciphertext.len());
    result.extend_from_slice(&nonce_bytes);
    result.extend_from_slice(&ciphertext);
    
    Ok(general_purpose::STANDARD.encode(result))
}

// 解密数据
fn decrypt_data(encrypted_data: &str, key: &[u8]) -> Result<String, String> {
    let key = Key::<Aes256Gcm>::from_slice(key);
    let cipher = Aes256Gcm::new(key);
    
    let data = general_purpose::STANDARD.decode(encrypted_data)
        .map_err(|e| e.to_string())?;
    
    if data.len() < 12 {
        return Err("Invalid encrypted data".to_string());
    }
    
    let nonce = Nonce::from_slice(&data[..12]);
    let ciphertext = &data[12..];
    
    let plaintext = cipher
        .decrypt(nonce, ciphertext.as_ref())
        .map_err(|e| e.to_string())?;
    
    String::from_utf8(plaintext).map_err(|e| e.to_string())
}
```

### 5.3 SQLite 集成

```rust
use tauri::{AppHandle, Manager, State};
use tauri_plugin_sql::{Migration, MigrationKind, SqlitePool};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;

struct AppState {
    pool: SqlitePool,
}

#[derive(Serialize, Deserialize)]
struct LoginItem {
    id: String,
    vault_id: String,
    name: String,
    username: Option<String>,
    encrypted_password: String,
    website: Option<String>,
    notes: Option<String>,
    favorite: bool,
    created_at: String,
    updated_at: String,
}

// 初始化数据库
fn init_database(app: &AppHandle) -> Result<SqlitePool, String> {
    let app_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    std::fs::create_dir_all(&app_dir).map_err(|e| e.to_string())?;
    
    let db_path = app_dir.join("vault.db");
    let db_url = format!("sqlite:{}", db_path.to_string_lossy());
    
    let pool = SqlitePool::connect(&db_url).map_err(|e| e.to_string())?;
    
    // 执行迁移
    let migrations = vec![
        Migration {
            version: 1,
            description: "Initial schema",
            sql: "CREATE TABLE IF NOT EXISTS vaults (...); CREATE TABLE IF NOT EXISTS items (...); CREATE TABLE IF NOT EXISTS logins (...);",
            kind: MigrationKind::Up,
        },
    ];
    
    for migration in migrations {
        pool.execute(migration.sql).map_err(|e| e.to_string())?;
    }
    
    Ok(pool)
}

// 添加登录项
#[tauri::command]
async fn add_login_item(
    state: State<'_, AppState>,
    vault_id: String,
    name: String,
    username: Option<String>,
    password: String,
    website: Option<String>,
    notes: Option<String>,
) -> Result<String, String> {
    // 获取加密密钥
    let key = get_encryption_key()?;
    
    // 加密密码
    let encrypted_password = encrypt_data(&password, &key)?;
    
    // 生成 ID
    let id = Uuid::new_v4().to_string();
    let now = Utc::now().to_rfc3339();
    
    // 插入 items 表
    state.pool.execute(
        "INSERT INTO items (id, vault_id, type, name, favorite, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
        &[&id, &vault_id, &"login", &name, &0, &now, &now],
    ).await.map_err(|e| e.to_string())?;
    
    // 插入 logins 表
    state.pool.execute(
        "INSERT INTO logins (item_id, username, encrypted_password, website, notes) VALUES (?, ?, ?, ?, ?)",
        &[&id, &username.unwrap_or_default(), &encrypted_password, &website.unwrap_or_default(), &notes.unwrap_or_default()],
    ).await.map_err(|e| e.to_string())?;
    
    Ok(id)
}

// 获取登录项
#[tauri::command]
async fn get_login_items(
    state: State<'_, AppState>,
    vault_id: String,
) -> Result<Vec<LoginItem>, String> {
    let rows = state.pool.select(
        "SELECT i.id, i.vault_id, i.name, l.username, l.encrypted_password, l.website, l.notes, i.favorite, i.created_at, i.updated_at 
         FROM items i 
         JOIN logins l ON i.id = l.item_id 
         WHERE i.vault_id = ? AND i.type = 'login'
         ORDER BY i.name",
        &[&vault_id],
    ).await.map_err(|e| e.to_string())?;
    
    // 获取加密密钥
    let key = get_encryption_key()?;
    
    let mut items = Vec::new();
    for row in rows {
        let encrypted_password: String = row.get("encrypted_password")?;
        
        // 这里不解密密码，只在需要时解密
        items.push(LoginItem {
            id: row.get("id")?,
            vault_id: row.get("vault_id")?,
            name: row.get("name")?,
            username: row.get("username")?,
            encrypted_password,
            website: row.get("website")?,
            notes: row.get("notes")?,
            favorite: row.get("favorite")? == 1,
            created_at: row.get("created_at")?,
            updated_at: row.get("updated_at")?,
        });
    }
    
    Ok(items)
}

// 解密密码
#[tauri::command]
async fn decrypt_password(encrypted_password: String) -> Result<String, String> {
    let key = get_encryption_key()?;
    decrypt_data(&encrypted_password, &key)
}
```

### 5.4 同步实现

```rust
use reqwest::Client;
use serde_json::json;

// 同步状态
#[derive(Serialize, Deserialize)]
struct SyncStatus {
    last_sync_time: String,
    last_sync_status: String,
    sync_token: Option<String>,
}

// 获取同步状态
#[tauri::command]
async fn get_sync_status(state: State<'_, AppState>) -> Result<SyncStatus, String> {
    let rows = state.pool.select(
        "SELECT last_sync_time, last_sync_status, sync_token FROM sync_status WHERE id = 'main'",
        &[],
    ).await.map_err(|e| e.to_string())?;
    
    if rows.is_empty() {
        return Ok(SyncStatus {
            last_sync_time: "".to_string(),
            last_sync_status: "never".to_string(),
            sync_token: None,
        });
    }
    
    let row = &rows[0];
    Ok(SyncStatus {
        last_sync_time: row.get("last_sync_time")?,
        last_sync_status: row.get("last_sync_status")?,
        sync_token: row.get("sync_token")?,
    })
}

// 执行同步
#[tauri::command]
async fn sync_vault(
    state: State<'_, AppState>,
    api_url: String,
    api_key: String,
) -> Result<SyncStatus, String> {
    // 获取同步状态
    let sync_status = get_sync_status(state.clone()).await?;
    
    // 获取上次同步后的更改
    let changes = state.pool.select(
        "SELECT id, vault_id, type, updated_at FROM items WHERE updated_at > ?",
        &[&sync_status.last_sync_time],
    ).await.map_err(|e| e.to_string())?;
    
    // 准备同步数据
    let mut items_to_sync = Vec::new();
    for row in changes {
        let id: String = row.get("id")?;
        let item_type: String = row.get("type")?;
        
        if item_type == "login" {
            let login_rows = state.pool.select(
                "SELECT username, encrypted_password, website, notes FROM logins WHERE item_id = ?",
                &[&id],
            ).await.map_err(|e| e.to_string())?;
            
            if !login_rows.is_empty() {
                let login_row = &login_rows[0];
                items_to_sync.push(json!({
                    "id": id,
                    "vault_id": row.get::<String>("vault_id")?,
                    "type": "login",
                    "username": login_row.get::<Option<String>>("username")?,
                    "encrypted_password": login_row.get::<String>("encrypted_password")?,
                    "website": login_row.get::<Option<String>>("website")?,
                    "notes": login_row.get::<Option<String>>("notes")?,
                    "updated_at": row.get::<String>("updated_at")?,
                }));
            }
        }
        // 处理其他类型...
    }
    
    // 发送同步请求
    let client = Client::new();
    let response = client.post(&api_url)
        .header("Authorization", format!("Bearer {}", api_key))
        .json(&json!({
            "sync_token": sync_status.sync_token,
            "changes": items_to_sync,
        }))
        .send()
        .await
        .map_err(|e| e.to_string())?;
    
    let sync_response: serde_json::Value = response.json()
        .await
        .map_err(|e| e.to_string())?;
    
    // 处理服务器返回的更改
    if let Some(server_changes) = sync_response.get("changes").and_then(|v| v.as_array()) {
        // 处理服务器更改...
    }
    
    // 更新同步状态
    let now = Utc::now().to_rfc3339();
    let new_sync_token = sync_response.get("sync_token")
        .and_then(|v| v.as_str())
        .unwrap_or("");
    
    state.pool.execute(
        "INSERT OR REPLACE INTO sync_status (id, last_sync_time, last_sync_status, sync_token) VALUES ('main', ?, 'success', ?)",
        &[&now, &new_sync_token],
    ).await.map_err(|e| e.to_string())?;
    
    Ok(SyncStatus {
        last_sync_time: now,
        last_sync_status: "success".to_string(),
        sync_token: Some(new_sync_token.to_string()),
    })
}
```

## 6. 总结与建议

### 6.1 最佳实践总结

1. **采用混合存储架构**：
   - 使用 SQLite + SQLCipher 存储主要数据
   - 使用系统密钥链存储加密密钥
   - 使用 Store 插件存储配置

2. **安全最佳实践**：
   - 使用强密码学算法（Argon2id、AES-256-GCM）
   - 实现密钥分离存储
   - 避免在内存中长时间保留明文密码
   - 实现自动锁定功能

3. **维护最佳实践**：
   - 使用数据库迁移管理架构变更
   - 实现定期备份功能
   - 提供导入/导出功能
   - 记录详细的操作日志

4. **同步最佳实践**：
   - 实现增量同步
   - 使用乐观锁或版本控制处理冲突
   - 支持离线操作
   - 加密传输所有数据

### 6.2 实施建议

1. **开发阶段**：
   - 先实现基本的本地存储功能
   - 确保加密正确实现
   - 全面测试数据完整性和安全性

2. **测试阶段**：
   - 进行安全审计
   - 测试极端情况（大量数据、频繁同步等）
   - 验证跨平台兼容性

3. **部署阶段**：
   - 提供详细的隐私政策
   - 实现安全的自动更新机制
   - 考虑提供数据恢复选项

通过采用这些建议，您可以在 Tauri 2.5.0 中构建一个既安全又易于维护和同步的密码管理应用。
