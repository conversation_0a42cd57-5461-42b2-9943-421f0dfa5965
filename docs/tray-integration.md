# 系统托盘集成功能

本文档介绍了 Secure Password 应用中的系统托盘集成功能的实现和使用方法。

## 功能概述

系统托盘集成功能允许应用程序在用户关闭主窗口时继续在后台运行，并通过系统托盘图标提供快速访问。

### 主要特性

1. **窗口关闭拦截**：当用户点击应用窗口的关闭按钮时，应用不直接退出，而是最小化到系统托盘
2. **托盘菜单**：在系统托盘图标上添加右键菜单，包含"显示主窗口"和"退出"选项
3. **快速访问**：双击托盘图标可以快速显示主窗口
4. **安全退出**：只有通过托盘菜单中的"退出"选项，才能真正关闭应用程序

## 技术实现

### 后端实现 (Rust/Tauri)

#### 核心模块

1. **`src-tauri/src/tray_integration.rs`** - 托盘集成主模块
   - `AppTrayIntegration` - 托盘集成管理器
   - `AppTrayEventHandler` - 托盘事件处理器
   - `setup_window_close_behavior` - 窗口关闭行为设置

2. **现有托盘模块** - `src-tauri/src/tray/`
   - 跨平台托盘实现
   - 事件处理系统
   - 图标管理
   - 菜单系统

#### 关键组件

```rust
// 托盘集成管理器
pub struct AppTrayIntegration {
    tray_manager: Arc<Mutex<TrayManager>>,
    app_handle: AppHandle,
    main_window_name: String,
}

// 托盘事件处理器
struct AppTrayEventHandler {
    app_handle: AppHandle,
    main_window_name: String,
}
```

#### Tauri 命令

- `show_main_window_command` - 显示主窗口
- `hide_to_tray_command` - 隐藏主窗口到托盘
- `quit_application_command` - 退出应用程序
- `is_tray_available_command` - 检查托盘是否可用

### 前端实现 (React/TypeScript)

#### 组件

1. **`src/components/TrayControls.tsx`** - 托盘控制组件
   - 提供托盘功能的用户界面
   - 调用 Tauri 命令与后端交互

2. **`src/pages/TrayTestPage.tsx`** - 托盘功能测试页面
   - 演示和测试托盘功能
   - 提供使用说明

## 使用方法

### 开发环境设置

1. 确保已安装 Rust 和 Node.js
2. 安装项目依赖：
   ```bash
   npm install
   cd src-tauri && cargo build
   ```

### 运行应用

```bash
npm run tauri dev
```

### 测试托盘功能

1. 启动应用后，导航到托盘测试页面
2. 确认托盘状态显示为"可用"
3. 按照页面上的测试步骤进行功能验证

## 平台支持

### Windows
- 使用 Windows API 实现系统托盘
- 支持 ICO 和 PNG 格式图标
- 支持气球提示和系统主题

### macOS
- 使用 NSStatusBar 实现菜单栏
- 支持模板图标和深色模式
- 支持 ICNS 格式图标

### Linux
- 支持多种桌面环境 (KDE, XFCE, Unity)
- 使用 AppIndicator 或 StatusNotifierItem
- 支持 PNG 和 SVG 格式图标

## 配置选项

### 托盘配置

```rust
let config = TrayConfigBuilder::new()
    .title("Secure Password")
    .tooltip("安全密码管理器 - 右键查看选项")
    .icon_path("icons/32x32.png")
    .show_menu_on_right_click(true)
    .enable_double_click(true)
    .hide_to_tray_on_close(true)
    .build()?;
```

### 菜单配置

```rust
let menu = TrayMenuBuilder::new()
    .add_normal_item("show_window", "显示主窗口")?
    .add_separator()?
    .add_normal_item("quit", "退出")?
    .build()
```

## 故障排除

### 常见问题

1. **托盘图标不显示**
   - 检查系统是否支持系统托盘
   - 确认图标文件路径正确
   - 查看控制台错误信息

2. **窗口关闭行为异常**
   - 确认窗口关闭事件监听器已正确设置
   - 检查托盘集成是否已初始化

3. **菜单点击无响应**
   - 检查事件处理器是否已注册
   - 查看后端日志输出

### 调试方法

1. 启用详细日志输出：
   ```bash
   RUST_LOG=debug npm run tauri dev
   ```

2. 检查托盘状态：
   ```typescript
   const available = await invoke<boolean>('is_tray_available_command');
   console.log('托盘可用性:', available);
   ```

## 扩展功能

### 自定义托盘菜单

可以通过修改 `create_tray_menu` 函数来添加更多菜单项：

```rust
fn create_tray_menu() -> TrayResult<TrayMenu> {
    TrayMenuBuilder::new()
        .add_normal_item("show_window", "显示主窗口")?
        .add_normal_item("settings", "设置")?
        .add_separator()?
        .add_normal_item("about", "关于")?
        .add_separator()?
        .add_normal_item("quit", "退出")?
        .build()
}
```

### 动态图标切换

可以根据应用状态动态切换托盘图标：

```rust
// 设置不同状态的图标
tray_manager.set_icon_by_type(TrayIconType::Active).await?;
tray_manager.set_icon_by_type(TrayIconType::Inactive).await?;
```

## 许可证

本功能遵循项目的整体许可证协议。托盘模块的实现确保与 GPL 3.0 许可证兼容，同时支持闭源使用。
