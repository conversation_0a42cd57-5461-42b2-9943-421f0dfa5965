# 基于 Tauri/React/Ant Design 的全端密码管理器项目结构

```
secure-vault/                      # 项目根目录
│
├── .github/                       # GitHub 相关配置
│   ├── workflows/                 # CI/CD 工作流
│   │   ├── desktop-build.yml      # 桌面端构建流程
│   │   ├── mobile-build.yml       # 移动端构建流程
│   │   └── release.yml            # 发布流程
│   └── ISSUE_TEMPLATE/            # Issue 模板
│
├── src/                           # 前端源代码（React）
│   ├── assets/                    # 静态资源
│   │   ├── icons/                 # 图标
│   │   ├── images/                # 图片
│   │   └── fonts/                 # 字体
│   │
│   ├── components/                # 共享组件
│   │   ├── common/                # 通用组件
│   │   │   ├── Button.tsx         # 按钮组件
│   │   │   ├── Input.tsx          # 输入框组件
│   │   │   ├── Modal.tsx          # 模态框组件
│   │   │   └── ...
│   │   │
│   │   ├── auth/                  # 认证相关组件
│   │   │   ├── LoginForm.tsx      # 登录表单
│   │   │   ├── RegisterForm.tsx   # 注册表单
│   │   │   ├── TwoFactorForm.tsx  # 双因素认证表单
│   │   │   └── ...
│   │   │
│   │   ├── vault/                 # 密码库相关组件
│   │   │   ├── ItemList.tsx       # 密码项列表
│   │   │   ├── ItemDetails.tsx    # 密码项详情
│   │   │   ├── ItemForm.tsx       # 密码项表单
│   │   │   └── ...
│   │   │
│   │   ├── settings/              # 设置相关组件
│   │   │   ├── AccountSettings.tsx # 账户设置
│   │   │   ├── SecuritySettings.tsx # 安全设置
│   │   │   ├── SyncSettings.tsx   # 同步设置
│   │   │   └── ...
│   │   │
│   │   ├── desktop/               # 桌面端特有组件
│   │   │   ├── TrayMenu.tsx       # 系统托盘菜单
│   │   │   ├── ShortcutManager.tsx # 快捷键管理器
│   │   │   └── ...
│   │   │
│   │   └── mobile/                # 移动端特有组件
│   │       ├── BottomTabs.tsx     # 底部标签导航
│   │       ├── SwipeActions.tsx   # 滑动操作
│   │       ├── BiometricPrompt.tsx # 生物识别提示
│   │       └── ...
│   │
│   ├── contexts/                  # React 上下文
│   │   ├── AuthContext.tsx        # 认证上下文
│   │   ├── VaultContext.tsx       # 密码库上下文
│   │   ├── SettingsContext.tsx    # 设置上下文
│   │   └── ...
│   │
│   ├── hooks/                     # 自定义 Hooks
│   │   ├── useAuth.ts             # 认证 Hook
│   │   ├── useVault.ts            # 密码库 Hook
│   │   ├── useSync.ts             # 同步 Hook
│   │   ├── usePlatform.ts         # 平台检测 Hook
│   │   └── ...
│   │
│   ├── layouts/                   # 布局组件
│   │   ├── MainLayout.tsx         # 主布局
│   │   ├── AuthLayout.tsx         # 认证布局
│   │   ├── DesktopLayout.tsx      # 桌面端布局
│   │   ├── MobileLayout.tsx       # 移动端布局
│   │   └── ...
│   │
│   ├── pages/                     # 页面组件
│   │   ├── auth/                  # 认证页面
│   │   │   ├── Login.tsx          # 登录页面
│   │   │   ├── Register.tsx       # 注册页面
│   │   │   └── ...
│   │   │
│   │   ├── vault/                 # 密码库页面
│   │   │   ├── VaultHome.tsx      # 密码库主页
│   │   │   ├── ItemView.tsx       # 密码项查看页面
│   │   │   ├── ItemEdit.tsx       # 密码项编辑页面
│   │   │   └── ...
│   │   │
│   │   ├── tools/                 # 工具页面
│   │   │   ├── PasswordGenerator.tsx # 密码生成器
│   │   │   ├── HealthReport.tsx   # 健康报告
│   │   │   └── ...
│   │   │
│   │   ├── settings/              # 设置页面
│   │   │   ├── SettingsHome.tsx   # 设置主页
│   │   │   ├── Account.tsx        # 账户设置页面
│   │   │   ├── Security.tsx       # 安全设置页面
│   │   │   └── ...
│   │   │
│   │   └── ...
│   │
│   ├── services/                  # 服务
│   │   ├── api/                   # API 服务
│   │   │   ├── apiClient.ts       # API 客户端
│   │   │   ├── authApi.ts         # 认证 API
│   │   │   ├── vaultApi.ts        # 密码库 API
│   │   │   └── ...
│   │   │
│   │   ├── crypto/                # 加密服务
│   │   │   ├── keyDerivation.ts   # 密钥派生
│   │   │   ├── symmetricEncryption.ts # 对称加密
│   │   │   ├── passwordGenerator.ts # 密码生成器
│   │   │   └── ...
│   │   │
│   │   ├── storage/               # 存储服务
│   │   │   ├── secureStorage.ts   # 安全存储
│   │   │   ├── localCache.ts      # 本地缓存
│   │   │   └── ...
│   │   │
│   │   ├── sync/                  # 同步服务
│   │   │   ├── syncService.ts     # 同步服务
│   │   │   ├── conflictResolver.ts # 冲突解决器
│   │   │   └── ...
│   │   │
│   │   ├── platform/              # 平台服务
│   │   │   ├── platformService.ts # 平台服务
│   │   │   ├── desktopService.ts  # 桌面端服务
│   │   │   ├── mobileService.ts   # 移动端服务
│   │   │   └── ...
│   │   │
│   │   └── ...
│   │
│   ├── store/                     # 状态管理
│   │   ├── slices/                # Redux 切片
│   │   │   ├── authSlice.ts       # 认证状态切片
│   │   │   ├── vaultSlice.ts      # 密码库状态切片
│   │   │   ├── settingsSlice.ts   # 设置状态切片
│   │   │   └── ...
│   │   │
│   │   ├── store.ts               # Redux 存储
│   │   └── hooks.ts               # Redux Hooks
│   │
│   ├── styles/                    # 样式
│   │   ├── global.css             # 全局样式
│   │   ├── themes/                # 主题
│   │   │   ├── light.ts           # 亮色主题
│   │   │   ├── dark.ts            # 暗色主题
│   │   │   └── ...
│   │   │
│   │   ├── desktop.css            # 桌面端特有样式
│   │   ├── mobile.css             # 移动端特有样式
│   │   └── ...
│   │
│   ├── types/                     # TypeScript 类型定义
│   │   ├── models.ts              # 数据模型类型
│   │   ├── api.ts                 # API 类型
│   │   ├── crypto.ts              # 加密类型
│   │   └── ...
│   │
│   ├── utils/                     # 工具函数
│   │   ├── format.ts              # 格式化工具
│   │   ├── validation.ts          # 验证工具
│   │   ├── logger.ts              # 日志工具
│   │   └── ...
│   │
│   ├── locales/                   # 国际化
│   │   ├── en/                    # 英文翻译
│   │   ├── zh/                    # 中文翻译
│   │   └── ...
│   │
│   ├── App.tsx                    # 应用根组件
│   ├── main.tsx                   # 应用入口
│   └── vite-env.d.ts              # Vite 环境定义
│
├── src-tauri/                     # Tauri 后端源代码（Rust）
│   ├── src/                       # Rust 源代码
│   │   ├── main.rs                # 主入口
│   │   │
│   │   ├── auth/                  # 认证模块
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── password.rs        # 密码处理
│   │   │   ├── biometric.rs       # 生物识别
│   │   │   └── ...
│   │   │
│   │   ├── crypto/                # 加密模块
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── key_derivation.rs  # 密钥派生
│   │   │   ├── symmetric.rs       # 对称加密
│   │   │   ├── random.rs          # 随机数生成
│   │   │   └── ...
│   │   │
│   │   ├── storage/               # 存储模块
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── secure_storage.rs  # 安全存储
│   │   │   ├── keychain.rs        # 密钥链
│   │   │   └── ...
│   │   │
│   │   ├── sync/                  # 同步模块
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── api_client.rs      # API 客户端
│   │   │   ├── sync_manager.rs    # 同步管理器
│   │   │   └── ...
│   │   │
│   │   ├── platform/              # 平台特定模块
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── desktop/           # 桌面端特有功能
│   │   │   │   ├── mod.rs         # 模块定义
│   │   │   │   ├── tray.rs        # 系统托盘
│   │   │   │   ├── shortcuts.rs   # 快捷键
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── mobile/            # 移动端特有功能
│   │   │   │   ├── mod.rs         # 模块定义
│   │   │   │   ├── autofill.rs    # 自动填充
│   │   │   │   ├── biometric.rs   # 生物识别
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── common.rs          # 通用平台功能
│   │   │   └── ...
│   │   │
│   │   ├── commands/              # Tauri 命令
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── auth_commands.rs   # 认证命令
│   │   │   ├── crypto_commands.rs # 加密命令
│   │   │   ├── vault_commands.rs  # 密码库命令
│   │   │   └── ...
│   │   │
│   │   ├── models/                # 数据模型
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── user.rs            # 用户模型
│   │   │   ├── vault_item.rs      # 密码库项目模型
│   │   │   └── ...
│   │   │
│   │   ├── utils/                 # 工具函数
│   │   │   ├── mod.rs             # 模块定义
│   │   │   ├── error.rs           # 错误处理
│   │   │   ├── logging.rs         # 日志
│   │   │   └── ...
│   │   │
│   │   └── config.rs              # 配置管理
│   │
│   ├── Cargo.toml                 # Rust 依赖配置
│   ├── build.rs                   # 构建脚本
│   ├── icons/                     # 应用图标
│   └── tauri.conf.json            # Tauri 配置
│
├── src-tauri/capabilities/        # Tauri 能力配置
│   ├── main.json                  # 主能力配置
│   └── ...
│
├── mobile/                        # 移动端特有代码
│   ├── android/                   # Android 特有代码
│   │   ├── app/                   # Android 应用
│   │   │   ├── src/               # Java/Kotlin 源代码
│   │   │   │   ├── main/          # 主代码
│   │   │   │   │   ├── java/      # Java/Kotlin 代码
│   │   │   │   │   │   └── com/example/securevault/ # 包名
│   │   │   │   │   │       ├── MainActivity.kt # 主活动
│   │   │   │   │   │       ├── AutofillService.kt # 自动填充服务
│   │   │   │   │   │       └── ...
│   │   │   │   │   │
│   │   │   │   │   ├── res/       # 资源
│   │   │   │   │   └── AndroidManifest.xml # 清单文件
│   │   │   │   │
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── build.gradle       # Gradle 构建脚本
│   │   │   └── ...
│   │   │
│   │   ├── gradle/                # Gradle 配置
│   │   ├── build.gradle           # 项目构建脚本
│   │   └── ...
│   │
│   ├── ios/                       # iOS 特有代码
│   │   ├── App/                   # iOS 应用
│   │   │   ├── Sources/           # Swift 源代码
│   │   │   │   ├── AppDelegate.swift # 应用代理
│   │   │   │   ├── CredentialProviderExtension/ # 凭据提供扩展
│   │   │   │   │   ├── CredentialProviderViewController.swift
│   │   │   │   │   └── ...
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── Resources/         # 资源
│   │   │   │   ├── Info.plist     # 信息属性列表
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   ├── Podfile                # CocoaPods 配置
│   │   └── ...
│   │
│   └── shared/                    # 移动端共享代码
│       ├── autofill/              # 自动填充相关
│       ├── biometric/             # 生物识别相关
│       └── ...
│
├── desktop/                       # 桌面端特有代码
│   ├── main/                      # 主进程代码
│   │   ├── tray.js                # 系统托盘
│   │   ├── shortcuts.js           # 全局快捷键
│   │   └── ...
│   │
│   ├── preload/                   # 预加载脚本
│   │   ├── index.js               # 主预加载脚本
│   │   └── ...
│   │
│   └── ...
│
├── shared/                        # 共享代码和资源
│   ├── constants/                 # 常量
│   ├── schemas/                   # 数据模式
│   └── ...
│
├── tests/                         # 测试
│   ├── unit/                      # 单元测试
│   │   ├── crypto.test.ts         # 加密测试
│   │   ├── auth.test.ts           # 认证测试
│   │   └── ...
│   │
│   ├── integration/               # 集成测试
│   │   ├── vault.test.ts          # 密码库测试
│   │   ├── sync.test.ts           # 同步测试
│   │   └── ...
│   │
│   ├── e2e/                       # 端到端测试
│   │   ├── auth.spec.ts           # 认证流程测试
│   │   ├── vault.spec.ts          # 密码库流程测试
│   │   └── ...
│   │
│   └── ...
│
├── docs/                          # 文档
│   ├── api/                       # API 文档
│   ├── architecture/              # 架构文档
│   ├── user-guide/                # 用户指南
│   └── ...
│
├── scripts/                       # 脚本
│   ├── build.js                   # 构建脚本
│   ├── release.js                 # 发布脚本
│   └── ...
│
├── .gitignore                     # Git 忽略文件
├── package.json                   # NPM 包配置
├── tsconfig.json                  # TypeScript 配置
├── vite.config.ts                 # Vite 配置
├── README.md                      # 项目说明
└── LICENSE                        # 许可证
```

## 关键文件说明

### 前端 (React)

1. **`src/App.tsx`**: 应用根组件，处理路由和全局状态。
2. **`src/main.tsx`**: 应用入口，初始化 React 和 Tauri。
3. **`src/hooks/usePlatform.ts`**: 平台检测 Hook，用于区分桌面端和移动端。
4. **`src/layouts/`**: 包含桌面端和移动端的不同布局组件。
5. **`src/components/desktop/`** 和 **`src/components/mobile/`**: 平台特有组件。
6. **`src/services/platform/`**: 平台特有服务的抽象和实现。

### 后端 (Rust/Tauri)

1. **`src-tauri/src/main.rs`**: Tauri 应用入口点。
2. **`src-tauri/src/commands/`**: 前端可调用的 Tauri 命令。
3. **`src-tauri/src/platform/`**: 平台特有功能的实现。
4. **`src-tauri/Cargo.toml`**: Rust 依赖配置。
5. **`src-tauri/tauri.conf.json`**: Tauri 配置，包括多平台设置。
6. **`src-tauri/capabilities/`**: Tauri 能力配置，定义应用权限。

### 移动端特有

1. **`mobile/android/`**: Android 特有代码，包括自动填充服务。
2. **`mobile/ios/`**: iOS 特有代码，包括凭据提供扩展。
3. **`mobile/shared/`**: 移动端共享代码，如自动填充和生物识别。

### 桌面端特有

1. **`desktop/main/`**: 桌面端特有功能，如系统托盘和全局快捷键。
2. **`desktop/preload/`**: 桌面端预加载脚本。

## 平台适配策略

### 代码组织

1. **共享代码**: 大部分业务逻辑和 UI 组件放在共享目录中。
2. **条件渲染**: 使用平台检测 Hook 进行条件渲染。
3. **平台抽象**: 通过接口抽象平台特有功能，实现依赖注入。
4. **响应式设计**: 使用响应式布局适配不同屏幕尺寸。

### 构建配置

1. **Tauri 配置**: 在 `tauri.conf.json` 中配置多平台支持。
2. **条件编译**: 使用 Rust 的条件编译特性处理平台差异。
3. **构建脚本**: 使用不同的构建脚本处理桌面端和移动端构建。

### 资源管理

1. **图标**: 为不同平台提供不同分辨率的图标。
2. **资源优化**: 针对移动端优化资源大小和加载性能。
3. **本地化**: 支持多语言和区域设置。

## 注意事项

1. **平台检测**: 使用 `usePlatform` Hook 检测当前平台，避免直接使用平台特有 API。
2. **条件导入**: 使用动态导入避免在不支持的平台上加载特定代码。
3. **权限管理**: 针对不同平台实现不同的权限请求策略。
4. **UI 适配**: 确保 UI 组件在所有目标平台上都有良好的体验。
5. **测试策略**: 为每个平台实现特定的测试用例。
```
