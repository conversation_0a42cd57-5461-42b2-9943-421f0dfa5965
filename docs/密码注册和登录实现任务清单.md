# 邮箱/密码注册和登录实现任务清单

## 1. 基础架构设计

### 1.1 定义认证流程
- [ ] 设计注册流程图
- [ ] 设计登录流程图
- [ ] 设计密码重置流程图
- [ ] 定义会话管理策略
- [ ] 设计认证状态管理

### 1.2 设计数据模型
- [ ] 设计用户模型
```typescript
// 前端用户模型
interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: number;
  lastLoginAt: number;
  emailVerified: boolean;
  kdfType: number; // 密钥派生函数类型
  kdfIterations: number; // 密钥派生函数迭代次数
}

// 注册请求模型
interface RegisterRequest {
  email: string;
  name?: string;
  masterPasswordHash: string; // 客户端哈希后的主密码
  masterPasswordHint?: string;
  kdfType: number;
  kdfIterations: number;
}

// 登录请求模型
interface LoginRequest {
  email: string;
  masterPasswordHash: string;
  twoFactorToken?: string;
  twoFactorProvider?: TwoFactorProviderType;
  captchaToken?: string;
}
```
- [ ] 设计认证令牌模型
- [ ] 设计错误响应模型
- [ ] 设计验证模型

### 1.3 定义安全策略
- [ ] 制定密码策略（长度、复杂度等）
- [ ] 设计防暴力破解机制
- [ ] 设计防重放攻击机制
- [ ] 定义会话超时策略
- [ ] 设计敏感操作验证策略

## 2. 前端实现

### 2.1 注册表单组件
- [ ] 创建注册表单组件
```tsx
// src/components/auth/RegisterForm.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Alert, Progress } from 'antd';
import { validatePassword, calculatePasswordStrength } from '../../utils/passwordUtils';
import { useAuth } from '../../hooks/useAuth';

interface RegisterFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onCancel }) => {
  const [form] = Form.useForm();
  const { register, loading, error } = useAuth();
  const [password, setPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordStrength(calculatePasswordStrength(newPassword));
  };
  
  const handleSubmit = async (values: any) => {
    try {
      await register(values.email, values.password, values.passwordHint);
      if (onSuccess) onSuccess();
    } catch (err) {
      // 错误已在 useAuth 中处理
    }
  };
  
  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      {error && <Alert type="error" message={error} style={{ marginBottom: 16 }} />}
      
      <Form.Item
        name="email"
        label="电子邮箱"
        rules={[
          { required: true, message: '请输入电子邮箱' },
          { type: 'email', message: '请输入有效的电子邮箱' }
        ]}
      >
        <Input placeholder="<EMAIL>" />
      </Form.Item>
      
      <Form.Item
        name="password"
        label="主密码"
        rules={[
          { required: true, message: '请输入主密码' },
          { validator: (_, value) => validatePassword(value) }
        ]}
      >
        <Input.Password placeholder="输入主密码" onChange={handlePasswordChange} />
      </Form.Item>
      
      {password && (
        <Form.Item>
          <Progress
            percent={passwordStrength}
            status={passwordStrength < 60 ? 'exception' : 'success'}
            strokeColor={{
              '0%': '#ff4d4f',
              '50%': '#faad14',
              '100%': '#52c41a',
            }}
          />
          <div style={{ fontSize: 12, color: '#8c8c8c' }}>
            {passwordStrength < 40 && '弱密码'}
            {passwordStrength >= 40 && passwordStrength < 70 && '中等强度'}
            {passwordStrength >= 70 && '强密码'}
          </div>
        </Form.Item>
      )}
      
      <Form.Item
        name="confirmPassword"
        label="确认主密码"
        dependencies={['password']}
        rules={[
          { required: true, message: '请确认主密码' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致'));
            },
          }),
        ]}
      >
        <Input.Password placeholder="再次输入主密码" />
      </Form.Item>
      
      <Form.Item name="passwordHint" label="密码提示（可选）">
        <Input placeholder="输入密码提示（请勿包含密码本身）" />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          创建账户
        </Button>
      </Form.Item>
      
      {onCancel && (
        <Button onClick={onCancel} block style={{ marginTop: 8 }}>
          取消
        </Button>
      )}
    </Form>
  );
};
```
- [ ] 实现密码强度检测
- [ ] 实现表单验证
- [ ] 添加密码提示字段
- [ ] 实现注册状态反馈

### 2.2 登录表单组件
- [ ] 创建登录表单组件
```tsx
// src/components/auth/LoginForm.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Alert, Checkbox } from 'antd';
import { useAuth } from '../../hooks/useAuth';

interface LoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
  onRegister?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ 
  onSuccess, 
  onForgotPassword, 
  onRegister 
}) => {
  const [form] = Form.useForm();
  const { login, loading, error } = useAuth();
  const [rememberEmail, setRememberEmail] = useState(true);
  
  const handleSubmit = async (values: any) => {
    try {
      await login(values.email, values.password, rememberEmail);
      if (onSuccess) onSuccess();
    } catch (err) {
      // 错误已在 useAuth 中处理
    }
  };
  
  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      {error && <Alert type="error" message={error} style={{ marginBottom: 16 }} />}
      
      <Form.Item
        name="email"
        label="电子邮箱"
        rules={[
          { required: true, message: '请输入电子邮箱' },
          { type: 'email', message: '请输入有效的电子邮箱' }
        ]}
      >
        <Input placeholder="<EMAIL>" />
      </Form.Item>
      
      <Form.Item
        name="password"
        label="主密码"
        rules={[
          { required: true, message: '请输入主密码' }
        ]}
      >
        <Input.Password placeholder="输入主密码" />
      </Form.Item>
      
      <Form.Item>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Checkbox 
            checked={rememberEmail} 
            onChange={e => setRememberEmail(e.target.checked)}
          >
            记住邮箱
          </Checkbox>
          
          {onForgotPassword && (
            <Button type="link" onClick={onForgotPassword} style={{ padding: 0 }}>
              忘记密码？
            </Button>
          )}
        </div>
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          登录
        </Button>
      </Form.Item>
      
      {onRegister && (
        <div style={{ textAlign: 'center' }}>
          <span>没有账户？</span>
          <Button type="link" onClick={onRegister} style={{ padding: '0 4px' }}>
            创建账户
          </Button>
        </div>
      )}
    </Form>
  );
};
```
- [ ] 实现记住邮箱功能
- [ ] 添加忘记密码链接
- [ ] 实现登录状态反馈
- [ ] 添加注册入口

### 2.3 密码重置组件
- [ ] 创建忘记密码表单
- [ ] 实现密码重置请求
- [ ] 创建密码重置确认表单
- [ ] 实现密码重置状态反馈

### 2.4 认证状态管理
- [ ] 创建认证上下文
```tsx
// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { User } from '../models/user';
import { hashPassword } from '../utils/passwordUtils';
import { setAuthToken, getAuthToken, removeAuthToken } from '../utils/storageUtils';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  register: (email: string, password: string, passwordHint?: string) => Promise<void>;
  login: (email: string, password: string, rememberEmail?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  confirmResetPassword: (token: string, password: string) => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    // 检查是否已经登录
    const checkAuth = async () => {
      try {
        const token = getAuthToken();
        if (token) {
          const userData = await invoke<User>('get_current_user');
          setUser(userData);
        }
      } catch (err) {
        console.error('Auth check failed:', err);
        removeAuthToken();
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, []);
  
  const register = async (email: string, password: string, passwordHint?: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // 在客户端哈希密码
      const passwordHash = await hashPassword(password, email);
      
      // 调用 Tauri 命令进行注册
      await invoke('register_user', { 
        email, 
        passwordHash, 
        passwordHint,
        kdfType: 0, // Argon2id
        kdfIterations: 100000
      });
      
      // 注册成功后自动登录
      await login(email, password);
    } catch (err: any) {
      setError(err.message || '注册失败，请稍后重试');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  const login = async (email: string, password: string, rememberEmail = true) => {
    setLoading(true);
    setError(null);
    
    try {
      // 在客户端哈希密码
      const passwordHash = await hashPassword(password, email);
      
      // 调用 Tauri 命令进行登录
      const response = await invoke<{ user: User; token: string }>('login_user', { 
        email, 
        passwordHash 
      });
      
      // 保存认证令牌
      setAuthToken(response.token);
      
      // 保存用户信息
      setUser(response.user);
      
      // 如果选择记住邮箱，则保存邮箱
      if (rememberEmail) {
        localStorage.setItem('rememberedEmail', email);
      } else {
        localStorage.removeItem('rememberedEmail');
      }
    } catch (err: any) {
      setError(err.message || '登录失败，请检查邮箱和密码');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  const logout = async () => {
    setLoading(true);
    
    try {
      await invoke('logout_user');
      removeAuthToken();
      setUser(null);
    } catch (err: any) {
      console.error('Logout failed:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const resetPassword = async (email: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await invoke('request_password_reset', { email });
    } catch (err: any) {
      setError(err.message || '密码重置请求失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  const confirmResetPassword = async (token: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // 调用 Tauri 命令确认密码重置
      await invoke('confirm_password_reset', { token, password });
    } catch (err: any) {
      setError(err.message || '密码重置失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  const clearError = () => {
    setError(null);
  };
  
  const value = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    register,
    login,
    logout,
    resetPassword,
    confirmResetPassword,
    clearError
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```
- [ ] 实现认证状态持久化
- [ ] 创建认证 Hook
- [ ] 实现自动登录检查
- [ ] 添加认证状态变更监听

### 2.5 密码处理工具
- [ ] 实现密码哈希函数
```typescript
// src/utils/passwordUtils.ts
import { invoke } from '@tauri-apps/api/tauri';

/**
 * 计算密码强度（0-100）
 */
export function calculatePasswordStrength(password: string): number {
  if (!password) return 0;
  
  let score = 0;
  
  // 长度得分（最高 30 分）
  const lengthScore = Math.min(30, password.length * 2);
  score += lengthScore;
  
  // 复杂度得分
  if (/[a-z]/.test(password)) score += 10; // 小写字母
  if (/[A-Z]/.test(password)) score += 10; // 大写字母
  if (/\d/.test(password)) score += 10;    // 数字
  if (/[^A-Za-z0-9]/.test(password)) score += 15; // 特殊字符
  
  // 多样性得分
  const uniqueChars = new Set(password.split('')).size;
  const uniqueRatio = uniqueChars / password.length;
  score += Math.round(uniqueRatio * 15);
  
  // 连续字符或数字会降低分数
  let hasSequence = false;
  for (let i = 0; i < password.length - 2; i++) {
    const c1 = password.charCodeAt(i);
    const c2 = password.charCodeAt(i + 1);
    const c3 = password.charCodeAt(i + 2);
    
    if ((c1 + 1 === c2 && c2 + 1 === c3) || (c1 - 1 === c2 && c2 - 1 === c3)) {
      hasSequence = true;
      break;
    }
  }
  
  if (hasSequence) score -= 10;
  
  // 确保分数在 0-100 范围内
  return Math.max(0, Math.min(100, score));
}

/**
 * 验证密码是否符合要求
 */
export async function validatePassword(password: string): Promise<void> {
  if (!password) {
    throw new Error('请输入密码');
  }
  
  if (password.length < 8) {
    throw new Error('密码长度至少为 8 个字符');
  }
  
  if (!/[a-z]/.test(password)) {
    throw new Error('密码必须包含小写字母');
  }
  
  if (!/[A-Z]/.test(password)) {
    throw new Error('密码必须包含大写字母');
  }
  
  if (!/\d/.test(password)) {
    throw new Error('密码必须包含数字');
  }
  
  if (!/[^A-Za-z0-9]/.test(password)) {
    throw new Error('密码必须包含特殊字符');
  }
}

/**
 * 使用 Argon2id 哈希密码
 * 注意：实际哈希操作在 Rust 后端执行，以提高安全性
 */
export async function hashPassword(password: string, email: string): Promise<string> {
  return invoke<string>('hash_password', { password, email });
}
```
- [ ] 实现密码强度检测
- [ ] 创建密码验证函数
- [ ] 实现安全的密码比较

### 2.6 存储工具
- [ ] 实现令牌存储函数
```typescript
// src/utils/storageUtils.ts
import { invoke } from '@tauri-apps/api/tauri';

// 令牌存储键名
const AUTH_TOKEN_KEY = 'auth_token';

/**
 * 安全地存储认证令牌
 * 在桌面端使用系统密钥链，在 Web 端使用 localStorage
 */
export async function setAuthToken(token: string): Promise<void> {
  try {
    // 尝试使用系统密钥链存储
    await invoke('store_auth_token', { token });
  } catch (err) {
    // 如果系统密钥链不可用，回退到 localStorage
    console.warn('System keychain unavailable, falling back to localStorage');
    localStorage.setItem(AUTH_TOKEN_KEY, token);
  }
}

/**
 * 获取存储的认证令牌
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    // 尝试从系统密钥链获取
    return await invoke<string>('get_auth_token');
  } catch (err) {
    // 如果系统密钥链不可用，回退到 localStorage
    console.warn('System keychain unavailable, falling back to localStorage');
    return localStorage.getItem(AUTH_TOKEN_KEY);
  }
}

/**
 * 移除存储的认证令牌
 */
export async function removeAuthToken(): Promise<void> {
  try {
    // 尝试从系统密钥链移除
    await invoke('remove_auth_token');
  } catch (err) {
    // 如果系统密钥链不可用，回退到 localStorage
    console.warn('System keychain unavailable, falling back to localStorage');
    localStorage.removeItem(AUTH_TOKEN_KEY);
  }
}

/**
 * 安全地存储敏感数据
 */
export async function setSecureItem(key: string, value: string): Promise<void> {
  try {
    await invoke('store_secure_item', { key, value });
  } catch (err) {
    console.warn(`Failed to store secure item: ${key}`, err);
    throw err;
  }
}

/**
 * 获取安全存储的敏感数据
 */
export async function getSecureItem(key: string): Promise<string | null> {
  try {
    return await invoke<string>('get_secure_item', { key });
  } catch (err) {
    console.warn(`Failed to get secure item: ${key}`, err);
    return null;
  }
}

/**
 * 移除安全存储的敏感数据
 */
export async function removeSecureItem(key: string): Promise<void> {
  try {
    await invoke('remove_secure_item', { key });
  } catch (err) {
    console.warn(`Failed to remove secure item: ${key}`, err);
  }
}
```
- [ ] 实现安全存储工具
- [ ] 创建本地存储工具
- [ ] 实现存储回退机制

### 2.7 页面组件
- [ ] 创建注册页面
- [ ] 创建登录页面
- [ ] 创建密码重置页面
- [ ] 实现页面路由

## 3. 后端实现

### 3.1 用户模型
- [ ] 实现用户结构体
```rust
// src/models/user.rs
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub email: String,
    pub name: Option<String>,
    pub password_hash: String,
    pub password_hint: Option<String>,
    pub email_verified: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_at: Option<DateTime<Utc>>,
    pub kdf_type: u32,
    pub kdf_iterations: u32,
}

impl User {
    pub fn new(
        email: String,
        password_hash: String,
        password_hint: Option<String>,
        kdf_type: u32,
        kdf_iterations: u32,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            email,
            name: None,
            password_hash,
            password_hint,
            email_verified: false,
            created_at: now,
            updated_at: now,
            last_login_at: None,
            kdf_type,
            kdf_iterations,
        }
    }
    
    pub fn to_response(&self) -> UserResponse {
        UserResponse {
            id: self.id.clone(),
            email: self.email.clone(),
            name: self.name.clone(),
            email_verified: self.email_verified,
            created_at: self.created_at.timestamp(),
            last_login_at: self.last_login_at.map(|dt| dt.timestamp()),
            kdf_type: self.kdf_type,
            kdf_iterations: self.kdf_iterations,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: String,
    pub email: String,
    pub name: Option<String>,
    pub email_verified: bool,
    pub created_at: i64,
    pub last_login_at: Option<i64>,
    pub kdf_type: u32,
    pub kdf_iterations: u32,
}
```
- [ ] 实现用户请求模型
- [ ] 实现用户响应模型
- [ ] 添加用户验证方法

### 3.2 认证命令
- [ ] 实现注册命令
```rust
// src/commands/auth_commands.rs
use tauri::{command, AppHandle, State};
use chrono::Utc;
use uuid::Uuid;
use argon2::{self, Config, ThreadMode, Variant, Version};
use rand::Rng;

use crate::models::user::{User, UserResponse};
use crate::services::user_service::UserService;
use crate::services::token_service::TokenService;
use crate::AppState;

#[command]
pub async fn register_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
    email: String,
    password_hash: String,
    password_hint: Option<String>,
    kdf_type: u32,
    kdf_iterations: u32,
) -> Result<(), String> {
    // 检查邮箱是否已注册
    let user_service = UserService::new(&app_handle);
    if user_service.find_by_email(&email).await.is_ok() {
        return Err("该邮箱已注册".to_string());
    }
    
    // 创建新用户
    let user = User::new(
        email,
        password_hash,
        password_hint,
        kdf_type,
        kdf_iterations,
    );
    
    // 保存用户
    user_service.save(&user).await.map_err(|e| e.to_string())?;
    
    // 发送验证邮件（可选）
    // ...
    
    Ok(())
}

#[command]
pub async fn login_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
    email: String,
    password_hash: String,
) -> Result<serde_json::Value, String> {
    // 查找用户
    let user_service = UserService::new(&app_handle);
    let mut user = user_service.find_by_email(&email).await
        .map_err(|_| "邮箱或密码不正确".to_string())?;
    
    // 验证密码
    if user.password_hash != password_hash {
        return Err("邮箱或密码不正确".to_string());
    }
    
    // 更新最后登录时间
    user.last_login_at = Some(Utc::now());
    user_service.update(&user).await.map_err(|e| e.to_string())?;
    
    // 生成认证令牌
    let token_service = TokenService::new(&app_handle);
    let token = token_service.generate_token(&user.id)
        .map_err(|e| e.to_string())?;
    
    // 返回用户信息和令牌
    let response = serde_json::json!({
        "user": user.to_response(),
        "token": token
    });
    
    Ok(response)
}

#[command]
pub async fn logout_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // 清除当前令牌
    let token_service = TokenService::new(&app_handle);
    token_service.clear_current_token().map_err(|e| e.to_string())?;
    
    Ok(())
}

#[command]
pub async fn get_current_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<UserResponse, String> {
    // 获取当前令牌
    let token_service = TokenService::new(&app_handle);
    let token = token_service.get_current_token()
        .map_err(|e| e.to_string())?;
    
    // 验证令牌并获取用户 ID
    let user_id = token_service.verify_token(&token)
        .map_err(|e| e.to_string())?;
    
    // 查找用户
    let user_service = UserService::new(&app_handle);
    let user = user_service.find_by_id(&user_id).await
        .map_err(|e| e.to_string())?;
    
    Ok(user.to_response())
}

#[command]
pub async fn hash_password(
    password: String,
    email: String,
) -> Result<String, String> {
    // 使用邮箱作为盐的一部分，确保不同用户的相同密码产生不同的哈希
    let mut salt = [0u8; 16];
    rand::thread_rng().fill(&mut salt);
    
    let mut combined_salt = Vec::with_capacity(salt.len() + email.len());
    combined_salt.extend_from_slice(&salt);
    combined_salt.extend_from_slice(email.as_bytes());
    
    // 配置 Argon2id
    let config = Config {
        variant: Variant::Argon2id,
        version: Version::Version13,
        mem_cost: 65536, // 64 MB
        time_cost: 3,    // 3 次迭代
        lanes: 4,        // 4 个并行通道
        thread_mode: ThreadMode::Parallel,
        secret: &[],     // 无额外密钥
        ad: &[],         // 无额外数据
        hash_length: 32, // 256 位哈希
    };
    
    // 哈希密码
    let hash = argon2::hash_encoded(password.as_bytes(), &combined_salt, &config)
        .map_err(|e| e.to_string())?;
    
    Ok(hash)
}

#[command]
pub async fn request_password_reset(
    app_handle: AppHandle,
    email: String,
) -> Result<(), String> {
    // 查找用户
    let user_service = UserService::new(&app_handle);
    let user = user_service.find_by_email(&email).await
        .map_err(|_| "如果该邮箱已注册，我们将发送密码重置链接".to_string())?;
    
    // 生成重置令牌
    let token = Uuid::new_v4().to_string();
    
    // 保存重置令牌（实际应用中应设置过期时间）
    // ...
    
    // 发送重置邮件
    // ...
    
    Ok(())
}

#[command]
pub async fn confirm_password_reset(
    app_handle: AppHandle,
    token: String,
    password: String,
) -> Result<(), String> {
    // 验证重置令牌
    // ...
    
    // 更新用户密码
    // ...
    
    Ok(())
}
```
- [ ] 实现登录命令
- [ ] 实现注销命令
- [ ] 实现密码重置命令
- [ ] 添加当前用户获取命令

### 3.3 用户服务
- [ ] 实现用户服务
```rust
// src/services/user_service.rs
use tauri::AppHandle;
use serde_json::{json, Value};
use std::fs;
use std::path::PathBuf;

use crate::models::user::User;

pub struct UserService {
    app_handle: AppHandle,
}

impl UserService {
    pub fn new(app_handle: &AppHandle) -> Self {
        Self {
            app_handle: app_handle.clone(),
        }
    }
    
    fn get_users_path(&self) -> Result<PathBuf, String> {
        let app_dir = self.app_handle.path().app_data_dir()
            .map_err(|e| format!("Failed to get app data dir: {}", e))?;
        
        let users_dir = app_dir.join("users");
        
        if !users_dir.exists() {
            fs::create_dir_all(&users_dir)
                .map_err(|e| format!("Failed to create users directory: {}", e))?;
        }
        
        Ok(users_dir)
    }
    
    fn get_user_path(&self, user_id: &str) -> Result<PathBuf, String> {
        let users_path = self.get_users_path()?;
        Ok(users_path.join(format!("{}.json", user_id)))
    }
    
    pub async fn save(&self, user: &User) -> Result<(), String> {
        let user_path = self.get_user_path(&user.id)?;
        
        let user_json = serde_json::to_string_pretty(user)
            .map_err(|e| format!("Failed to serialize user: {}", e))?;
        
        fs::write(&user_path, user_json)
            .map_err(|e| format!("Failed to write user file: {}", e))?;
        
        // 更新邮箱索引
        self.update_email_index(&user.email, &user.id).await?;
        
        Ok(())
    }
    
    pub async fn update(&self, user: &User) -> Result<(), String> {
        self.save(user).await
    }
    
    pub async fn find_by_id(&self, user_id: &str) -> Result<User, String> {
        let user_path = self.get_user_path(user_id)?;
        
        if !user_path.exists() {
            return Err(format!("User not found: {}", user_id));
        }
        
        let user_json = fs::read_to_string(&user_path)
            .map_err(|e| format!("Failed to read user file: {}", e))?;
        
        let user: User = serde_json::from_str(&user_json)
            .map_err(|e| format!("Failed to parse user JSON: {}", e))?;
        
        Ok(user)
    }
    
    pub async fn find_by_email(&self, email: &str) -> Result<User, String> {
        let user_id = self.get_user_id_by_email(email).await?;
        self.find_by_id(&user_id).await
    }
    
    async fn get_user_id_by_email(&self, email: &str) -> Result<String, String> {
        let email_index = self.get_email_index().await?;
        
        let user_id = email_index.get(email)
            .and_then(|v| v.as_str())
            .ok_or_else(|| format!("User not found with email: {}", email))?;
        
        Ok(user_id.to_string())
    }
    
    async fn get_email_index(&self) -> Result<Value, String> {
        let users_path = self.get_users_path()?;
        let index_path = users_path.join("email_index.json");
        
        if !index_path.exists() {
            return Ok(json!({}));
        }
        
        let index_json = fs::read_to_string(&index_path)
            .map_err(|e| format!("Failed to read email index: {}", e))?;
        
        let index: Value = serde_json::from_str(&index_json)
            .map_err(|e| format!("Failed to parse email index: {}", e))?;
        
        Ok(index)
    }
    
    async fn update_email_index(&self, email: &str, user_id: &str) -> Result<(), String> {
        let users_path = self.get_users_path()?;
        let index_path = users_path.join("email_index.json");
        
        let mut index = self.get_email_index().await?;
        
        if let Some(obj) = index.as_object_mut() {
            obj.insert(email.to_string(), json!(user_id));
        }
        
        let index_json = serde_json::to_string_pretty(&index)
            .map_err(|e| format!("Failed to serialize email index: {}", e))?;
        
        fs::write(&index_path, index_json)
            .map_err(|e| format!("Failed to write email index: {}", e))?;
        
        Ok(())
    }
}
```
- [ ] 实现用户查找方法
- [ ] 实现用户保存方法
- [ ] 实现用户更新方法
- [ ] 添加邮箱索引

### 3.4 令牌服务
- [ ] 实现令牌生成
```rust
// src/services/token_service.rs
use tauri::AppHandle;
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use chrono::{Utc, Duration};
use uuid::Uuid;

use crate::services::keychain_service::KeychainService;

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,         // 用户 ID
    exp: usize,          // 过期时间
    iat: usize,          // 签发时间
    jti: String,         // JWT ID
}

pub struct TokenService {
    app_handle: AppHandle,
    keychain_service: KeychainService,
}

impl TokenService {
    pub fn new(app_handle: &AppHandle) -> Self {
        Self {
            app_handle: app_handle.clone(),
            keychain_service: KeychainService::new(app_handle),
        }
    }
    
    pub fn generate_token(&self, user_id: &str) -> Result<String, String> {
        // 获取或生成密钥
        let secret = self.get_or_create_jwt_secret()?;
        
        // 创建 claims
        let now = Utc::now();
        let expires_at = now + Duration::days(7); // 令牌有效期 7 天
        
        let claims = Claims {
            sub: user_id.to_string(),
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
            jti: Uuid::new_v4().to_string(),
        };
        
        // 生成令牌
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        ).map_err(|e| format!("Failed to generate token: {}", e))?;
        
        // 保存当前令牌
        self.save_current_token(&token)?;
        
        Ok(token)
    }
    
    pub fn verify_token(&self, token: &str) -> Result<String, String> {
        // 获取密钥
        let secret = self.get_jwt_secret()?;
        
        // 验证令牌
        let validation = Validation::default();
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        ).map_err(|e| format!("Invalid token: {}", e))?;
        
        Ok(token_data.claims.sub)
    }
    
    pub fn save_current_token(&self, token: &str) -> Result<(), String> {
        self.keychain_service.set("current_token", token)
    }
    
    pub fn get_current_token(&self) -> Result<String, String> {
        self.keychain_service.get("current_token")
    }
    
    pub fn clear_current_token(&self) -> Result<(), String> {
        self.keychain_service.delete("current_token")
    }
    
    fn get_or_create_jwt_secret(&self) -> Result<String, String> {
        match self.get_jwt_secret() {
            Ok(secret) => Ok(secret),
            Err(_) => {
                // 生成新的密钥
                let secret = Uuid::new_v4().to_string();
                self.keychain_service.set("jwt_secret", &secret)?;
                Ok(secret)
            }
        }
    }
    
    fn get_jwt_secret(&self) -> Result<String, String> {
        self.keychain_service.get("jwt_secret")
    }
}
```
- [ ] 实现令牌验证
- [ ] 实现令牌存储
- [ ] 添加令牌管理方法

### 3.5 密钥链服务
- [ ] 实现密钥链服务
```rust
// src/services/keychain_service.rs
use tauri::AppHandle;
use keyring::Keyring;

pub struct KeychainService {
    app_handle: AppHandle,
}

impl KeychainService {
    pub fn new(app_handle: &AppHandle) -> Self {
        Self {
            app_handle: app_handle.clone(),
        }
    }
    
    pub fn set(&self, key: &str, value: &str) -> Result<(), String> {
        let service = self.get_service_name();
        let keyring = Keyring::new(&service, key)
            .map_err(|e| format!("Failed to create keyring: {}", e))?;
        
        keyring.set_password(value)
            .map_err(|e| format!("Failed to set keychain value: {}", e))
    }
    
    pub fn get(&self, key: &str) -> Result<String, String> {
        let service = self.get_service_name();
        let keyring = Keyring::new(&service, key)
            .map_err(|e| format!("Failed to create keyring: {}", e))?;
        
        keyring.get_password()
            .map_err(|e| format!("Failed to get keychain value: {}", e))
    }
    
    pub fn delete(&self, key: &str) -> Result<(), String> {
        let service = self.get_service_name();
        let keyring = Keyring::new(&service, key)
            .map_err(|e| format!("Failed to create keyring: {}", e))?;
        
        // 尝试删除密码，如果不存在则忽略错误
        match keyring.delete_password() {
            Ok(_) => Ok(()),
            Err(keyring::Error::NoEntry) => Ok(()),
            Err(e) => Err(format!("Failed to delete keychain value: {}", e)),
        }
    }
    
    fn get_service_name(&self) -> String {
        // 使用应用名称作为服务名称
        self.app_handle.config().tauri.bundle.identifier.clone()
    }
}
```
- [ ] 实现密钥链存储方法
- [ ] 实现密钥链获取方法
- [ ] 实现密钥链删除方法
- [ ] 添加平台特定适配

### 3.6 主函数配置
- [ ] 配置 Tauri 应用
```rust
// src/main.rs
#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]

mod commands;
mod models;
mod services;

use std::sync::Mutex;
use tauri::State;
use commands::auth_commands::{
    register_user, login_user, logout_user, get_current_user,
    hash_password, request_password_reset, confirm_password_reset
};

// 应用状态
pub struct AppState {
    // 可以添加全局状态
}

fn main() {
    tauri::Builder::default()
        .manage(AppState {})
        .invoke_handler(tauri::generate_handler![
            register_user,
            login_user,
            logout_user,
            get_current_user,
            hash_password,
            request_password_reset,
            confirm_password_reset
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```
- [ ] 注册认证命令
- [ ] 配置应用状态
- [ ] 添加错误处理

## 4. 安全增强

### 4.1 防暴力破解
- [ ] 实现登录尝试限制
- [ ] 添加验证码支持
- [ ] 实现账户锁定机制
- [ ] 添加异常登录检测

### 4.2 会话安全
- [ ] 实现会话超时
- [ ] 添加会话刷新机制
- [ ] 实现设备追踪
- [ ] 添加会话撤销功能

### 4.3 数据保护
- [ ] 实现敏感数据加密
- [ ] 添加安全头部配置
- [ ] 实现 CSRF 保护
- [ ] 添加 XSS 防护

## 5. 移动端适配

### 5.1 Android 适配
- [ ] 配置 Android 权限
- [ ] 实现 Android 密钥存储
- [ ] 添加生物识别支持
- [ ] 优化 Android UI

### 5.2 iOS 适配
- [ ] 配置 iOS 权限
- [ ] 实现 iOS Keychain 集成
- [ ] 添加 Touch ID/Face ID 支持
- [ ] 优化 iOS UI

## 6. 测试与调试

### 6.1 单元测试
- [ ] 测试认证流程
- [ ] 测试密码哈希函数
- [ ] 测试令牌服务
- [ ] 测试表单验证

### 6.2 集成测试
- [ ] 测试注册流程
- [ ] 测试登录流程
- [ ] 测试密码重置流程
- [ ] 测试会话管理

### 6.3 安全测试
- [ ] 进行密码强度测试
- [ ] 测试暴力破解防护
- [ ] 进行会话安全测试
- [ ] 测试数据保护机制

## 7. 文档与示例

### 7.1 用户指南
- [ ] 创建注册指南
- [ ] 编写登录指南
- [ ] 添加密码重置指南
- [ ] 编写安全最佳实践

### 7.2 开发者文档
- [ ] 编写认证 API 文档
- [ ] 创建组件使用指南
- [ ] 添加安全配置文档
- [ ] 编写测试指南
