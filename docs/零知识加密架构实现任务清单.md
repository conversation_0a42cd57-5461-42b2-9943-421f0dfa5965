# 零知识加密架构实现任务清单

## 1. 基础架构设计

### 1.1 定义零知识加密原则
- [ ] 研究并记录零知识加密的核心原则
- [ ] 确定密码管理器的零知识安全模型
- [ ] 定义数据加密和密钥管理策略
- [ ] 制定密码学算法选择标准（AES-256-GCM、Argon2id 等）
- [ ] 设计密钥派生和密钥分离机制

### 1.2 设计数据流程
- [ ] 设计用户注册/登录流程
- [ ] 设计主密码派生流程
- [ ] 设计数据加密/解密流程
- [ ] 设计同步流程
- [ ] 设计密码恢复流程
- [ ] 创建数据流程图和架构图

### 1.3 定义安全边界
- [ ] 确定前端和后端的安全边界
- [ ] 定义本地存储的安全策略
- [ ] 设计网络传输的安全措施
- [ ] 确定密钥存储的安全策略
- [ ] 制定内存安全处理策略

## 2. 密钥管理实现

### 2.1 主密码处理
- [ ] 实现主密码强度验证
```typescript
// 前端实现
function validateMasterPassword(password: string): ValidationResult {
  const minLength = 12;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasDigit = /\d/.test(password);
  const hasSpecial = /[^A-Za-z0-9]/.test(password);
  
  const strength = calculatePasswordStrength(password);
  
  return {
    isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasDigit && hasSpecial,
    strength,
    feedback: generateFeedback(password, minLength, hasUpperCase, hasLowerCase, hasDigit, hasSpecial)
  };
}
```
- [ ] 实现主密码哈希提示（密码提示）
- [ ] 实现主密码更改流程
- [ ] 添加主密码强度反馈机制

### 2.2 密钥派生
- [ ] 实现基于 Argon2id 的密钥派生函数
```rust
use argon2::{self, Config, ThreadMode, Variant, Version};
use rand::Rng;

#[tauri::command]
fn derive_master_key(password: String, email: String) -> Result<Vec<u8>, String> {
    let mut salt = [0u8; 16];
    rand::thread_rng().fill(&mut salt);
    
    // 使用邮箱作为盐的一部分，确保不同用户的相同密码产生不同的密钥
    let mut combined_salt = Vec::with_capacity(salt.len() + email.len());
    combined_salt.extend_from_slice(&salt);
    combined_salt.extend_from_slice(email.as_bytes());
    
    let config = Config {
        variant: Variant::Argon2id,
        version: Version::Version13,
        mem_cost: 65536, // 64 MB
        time_cost: 3,    // 3 次迭代
        lanes: 4,        // 4 个并行通道
        thread_mode: ThreadMode::Parallel,
        secret: &[],     // 无额外密钥
        ad: &[],         // 无额外数据
        hash_length: 32, // 256 位密钥
    };
    
    argon2::hash_raw(password.as_bytes(), &combined_salt, &config)
        .map_err(|e| e.to_string())
}
```
- [ ] 实现账户密钥派生
- [ ] 实现加密密钥派生
- [ ] 实现签名密钥派生
- [ ] 实现密钥缓存和内存安全处理

### 2.3 密钥存储
- [ ] 实现加密密钥的安全存储
```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};
use rand::RngCore;

#[tauri::command]
fn encrypt_symmetric_key(master_key: Vec<u8>, symmetric_key: Vec<u8>) -> Result<Vec<u8>, String> {
    if master_key.len() != 32 {
        return Err("Master key must be 32 bytes".to_string());
    }
    
    let key = Key::<Aes256Gcm>::from_slice(&master_key);
    let cipher = Aes256Gcm::new(key);
    
    let mut nonce_bytes = [0u8; 12];
    rand::thread_rng().fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);
    
    let encrypted_key = cipher
        .encrypt(nonce, symmetric_key.as_slice())
        .map_err(|e| e.to_string())?;
    
    // 将 nonce 和加密后的密钥合并
    let mut result = Vec::with_capacity(nonce_bytes.len() + encrypted_key.len());
    result.extend_from_slice(&nonce_bytes);
    result.extend_from_slice(&encrypted_key);
    
    Ok(result)
}
```
- [ ] 实现密钥分离存储策略
- [ ] 实现密钥轮换机制
- [ ] 实现紧急访问密钥备份

### 2.4 密钥验证
- [ ] 实现密钥派生验证
- [ ] 实现密钥完整性检查
- [ ] 实现防篡改机制
- [ ] 添加密钥使用审计日志

## 3. 数据加密实现

### 3.1 对称加密
- [ ] 实现 AES-256-GCM 加密/解密
```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};
use rand::RngCore;

#[tauri::command]
fn encrypt_data(symmetric_key: Vec<u8>, data: String) -> Result<Vec<u8>, String> {
    if symmetric_key.len() != 32 {
        return Err("Symmetric key must be 32 bytes".to_string());
    }
    
    let key = Key::<Aes256Gcm>::from_slice(&symmetric_key);
    let cipher = Aes256Gcm::new(key);
    
    let mut nonce_bytes = [0u8; 12];
    rand::thread_rng().fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);
    
    let encrypted_data = cipher
        .encrypt(nonce, data.as_bytes())
        .map_err(|e| e.to_string())?;
    
    // 将 nonce 和加密后的数据合并
    let mut result = Vec::with_capacity(nonce_bytes.len() + encrypted_data.len());
    result.extend_from_slice(&nonce_bytes);
    result.extend_from_slice(&encrypted_data);
    
    Ok(result)
}

#[tauri::command]
fn decrypt_data(symmetric_key: Vec<u8>, encrypted_data: Vec<u8>) -> Result<String, String> {
    if symmetric_key.len() != 32 {
        return Err("Symmetric key must be 32 bytes".to_string());
    }
    
    if encrypted_data.len() < 12 {
        return Err("Invalid encrypted data".to_string());
    }
    
    let key = Key::<Aes256Gcm>::from_slice(&symmetric_key);
    let cipher = Aes256Gcm::new(key);
    
    let nonce = Nonce::from_slice(&encrypted_data[..12]);
    let ciphertext = &encrypted_data[12..];
    
    let decrypted_data = cipher
        .decrypt(nonce, ciphertext)
        .map_err(|e| e.to_string())?;
    
    String::from_utf8(decrypted_data)
        .map_err(|e| e.to_string())
}
```
- [ ] 实现加密元数据处理
- [ ] 实现数据分块加密（用于大文件）
- [ ] 添加加密版本标记，支持未来算法升级

### 3.2 非对称加密
- [ ] 实现 RSA 或 X25519 密钥对生成
```rust
use ring::{rand, signature};

#[tauri::command]
fn generate_keypair() -> Result<KeyPair, String> {
    let rng = rand::SystemRandom::new();
    
    let pkcs8_bytes = signature::Ed25519KeyPair::generate_pkcs8(&rng)
        .map_err(|_| "Failed to generate key pair".to_string())?;
    
    let key_pair = signature::Ed25519KeyPair::from_pkcs8(pkcs8_bytes.as_ref())
        .map_err(|_| "Failed to parse key pair".to_string())?;
    
    let public_key = key_pair.public_key().as_ref().to_vec();
    
    Ok(KeyPair {
        public_key,
        private_key: pkcs8_bytes.as_ref().to_vec(),
    })
}
```
- [ ] 实现公钥加密/私钥解密
- [ ] 实现数字签名和验证
- [ ] 实现密钥交换协议

### 3.3 数据结构加密
- [ ] 设计加密数据结构
```typescript
// 前端数据结构
interface EncryptedItem {
  id: string;                 // 明文唯一标识符
  type: string;               // 明文项目类型（登录、卡片、笔记等）
  name: string;               // 加密项目名称
  favorite: boolean;          // 明文收藏状态
  encryptedData: string;      // 加密的项目数据
  encryptedDataHmac: string;  // 数据完整性验证码
  createdAt: number;          // 明文创建时间戳
  updatedAt: number;          // 明文更新时间戳
  deletedAt: number | null;   // 明文删除时间戳（用于软删除）
}

interface EncryptedLoginData {
  username: string;           // 加密用户名
  password: string;           // 加密密码
  uris: string[];             // 加密 URI 列表
  totp: string | null;        // 加密 TOTP 密钥
  notes: string | null;       // 加密笔记
  customFields: {             // 加密自定义字段
    name: string;
    value: string;
    type: string;
  }[];
}
```
- [ ] 实现结构化数据加密/解密
- [ ] 实现加密数据索引
- [ ] 实现加密数据搜索

### 3.4 加密数据验证
- [ ] 实现 HMAC 数据完整性验证
```rust
use hmac::{Hmac, Mac, NewMac};
use sha2::Sha256;

type HmacSha256 = Hmac<Sha256>;

#[tauri::command]
fn generate_hmac(mac_key: Vec<u8>, data: Vec<u8>) -> Result<Vec<u8>, String> {
    if mac_key.len() != 32 {
        return Err("MAC key must be 32 bytes".to_string());
    }
    
    let mut mac = HmacSha256::new_from_slice(&mac_key)
        .map_err(|e| e.to_string())?;
    
    mac.update(&data);
    
    Ok(mac.finalize().into_bytes().to_vec())
}

#[tauri::command]
fn verify_hmac(mac_key: Vec<u8>, data: Vec<u8>, hmac: Vec<u8>) -> Result<bool, String> {
    if mac_key.len() != 32 {
        return Err("MAC key must be 32 bytes".to_string());
    }
    
    let mut mac = HmacSha256::new_from_slice(&mac_key)
        .map_err(|e| e.to_string())?;
    
    mac.update(&data);
    
    let result = mac.verify(&hmac.into());
    Ok(result.is_ok())
}
```
- [ ] 实现加密数据版本验证
- [ ] 实现防重放攻击机制
- [ ] 添加数据签名验证

## 4. 前端实现

### 4.1 React 组件
- [ ] 创建主密码输入组件
```tsx
// src/components/auth/MasterPasswordInput.tsx
import React, { useState } from 'react';
import { Input, Progress, Form } from 'antd';
import { validateMasterPassword } from '../../services/cryptoService';

interface MasterPasswordInputProps {
  onPasswordChange: (password: string, isValid: boolean) => void;
}

export const MasterPasswordInput: React.FC<MasterPasswordInputProps> = ({ onPasswordChange }) => {
  const [password, setPassword] = useState('');
  const [validation, setValidation] = useState({ isValid: false, strength: 0, feedback: '' });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    
    const result = validateMasterPassword(newPassword);
    setValidation(result);
    onPasswordChange(newPassword, result.isValid);
  };
  
  return (
    <Form.Item
      label="主密码"
      validateStatus={password ? (validation.isValid ? 'success' : 'error') : ''}
      help={password ? validation.feedback : ''}
    >
      <Input.Password
        value={password}
        onChange={handleChange}
        placeholder="输入主密码"
      />
      {password && (
        <Progress
          percent={validation.strength}
          status={validation.isValid ? 'success' : 'exception'}
          showInfo={false}
          size="small"
          style={{ marginTop: 5 }}
        />
      )}
    </Form.Item>
  );
};
```
- [ ] 创建密码库解锁组件
- [ ] 创建加密数据展示组件
- [ ] 创建加密数据编辑组件

### 4.2 加密服务
- [ ] 实现前端加密服务
```typescript
// src/services/cryptoService.ts
import { invoke } from '@tauri-apps/api/tauri';
import { encode, decode } from '@msgpack/msgpack';

export class CryptoService {
  private masterKey: Uint8Array | null = null;
  private symmetricKey: Uint8Array | null = null;
  
  async deriveMasterKey(password: string, email: string): Promise<Uint8Array> {
    const masterKey = await invoke<number[]>('derive_master_key', { password, email });
    this.masterKey = new Uint8Array(masterKey);
    return this.masterKey;
  }
  
  async generateSymmetricKey(): Promise<Uint8Array> {
    // 生成随机的对称密钥
    const key = new Uint8Array(32);
    crypto.getRandomValues(key);
    this.symmetricKey = key;
    return key;
  }
  
  async encryptSymmetricKey(): Promise<string> {
    if (!this.masterKey || !this.symmetricKey) {
      throw new Error('Master key or symmetric key not available');
    }
    
    const encryptedKey = await invoke<number[]>('encrypt_symmetric_key', {
      masterKey: Array.from(this.masterKey),
      symmetricKey: Array.from(this.symmetricKey)
    });
    
    return Buffer.from(encryptedKey).toString('base64');
  }
  
  async decryptSymmetricKey(encryptedKeyBase64: string): Promise<void> {
    if (!this.masterKey) {
      throw new Error('Master key not available');
    }
    
    const encryptedKey = new Uint8Array(Buffer.from(encryptedKeyBase64, 'base64'));
    
    const symmetricKey = await invoke<number[]>('decrypt_symmetric_key', {
      masterKey: Array.from(this.masterKey),
      encryptedKey: Array.from(encryptedKey)
    });
    
    this.symmetricKey = new Uint8Array(symmetricKey);
  }
  
  async encryptData<T>(data: T): Promise<string> {
    if (!this.symmetricKey) {
      throw new Error('Symmetric key not available');
    }
    
    // 使用 MessagePack 序列化数据
    const serialized = encode(data);
    
    const encrypted = await invoke<number[]>('encrypt_data', {
      symmetricKey: Array.from(this.symmetricKey),
      data: Array.from(serialized)
    });
    
    return Buffer.from(encrypted).toString('base64');
  }
  
  async decryptData<T>(encryptedDataBase64: string): Promise<T> {
    if (!this.symmetricKey) {
      throw new Error('Symmetric key not available');
    }
    
    const encryptedData = new Uint8Array(Buffer.from(encryptedDataBase64, 'base64'));
    
    const decrypted = await invoke<number[]>('decrypt_data', {
      symmetricKey: Array.from(this.symmetricKey),
      encryptedData: Array.from(encryptedData)
    });
    
    // 使用 MessagePack 反序列化数据
    return decode(new Uint8Array(decrypted)) as T;
  }
  
  clearKeys(): void {
    // 安全清除内存中的密钥
    if (this.masterKey) {
      this.masterKey.fill(0);
      this.masterKey = null;
    }
    
    if (this.symmetricKey) {
      this.symmetricKey.fill(0);
      this.symmetricKey = null;
    }
  }
}

export const cryptoService = new CryptoService();
```
- [ ] 实现密钥缓存服务
- [ ] 实现内存安全处理
- [ ] 添加自动锁定机制

### 4.3 状态管理
- [ ] 实现加密状态管理
```typescript
// src/stores/cryptoStore.ts
import { create } from 'zustand';
import { cryptoService } from '../services/cryptoService';

interface CryptoState {
  isLocked: boolean;
  isInitialized: boolean;
  hasEncryptionKey: boolean;
  lockVault: () => void;
  unlockVault: (masterPassword: string, email: string) => Promise<boolean>;
  initializeVault: (masterPassword: string, email: string) => Promise<void>;
}

export const useCryptoStore = create<CryptoState>((set, get) => ({
  isLocked: true,
  isInitialized: false,
  hasEncryptionKey: false,
  
  lockVault: () => {
    cryptoService.clearKeys();
    set({ isLocked: true });
  },
  
  unlockVault: async (masterPassword, email) => {
    try {
      await cryptoService.deriveMasterKey(masterPassword, email);
      
      // 从存储中获取加密的对称密钥
      const encryptedKeyBase64 = await getEncryptedSymmetricKey();
      
      if (encryptedKeyBase64) {
        await cryptoService.decryptSymmetricKey(encryptedKeyBase64);
        set({ isLocked: false, hasEncryptionKey: true });
        return true;
      }
      
      set({ isLocked: false, hasEncryptionKey: false });
      return false;
    } catch (error) {
      console.error('Failed to unlock vault:', error);
      return false;
    }
  },
  
  initializeVault: async (masterPassword, email) => {
    await cryptoService.deriveMasterKey(masterPassword, email);
    
    // 生成新的对称密钥
    await cryptoService.generateSymmetricKey();
    
    // 加密并存储对称密钥
    const encryptedKeyBase64 = await cryptoService.encryptSymmetricKey();
    await saveEncryptedSymmetricKey(encryptedKeyBase64);
    
    set({ isLocked: false, isInitialized: true, hasEncryptionKey: true });
  }
}));

// 辅助函数，从存储中获取加密的对称密钥
async function getEncryptedSymmetricKey(): Promise<string | null> {
  // 实现从本地存储获取加密密钥的逻辑
  return localStorage.getItem('encryptedSymmetricKey');
}

// 辅助函数，将加密的对称密钥保存到存储中
async function saveEncryptedSymmetricKey(encryptedKeyBase64: string): Promise<void> {
  // 实现将加密密钥保存到本地存储的逻辑
  localStorage.setItem('encryptedSymmetricKey', encryptedKeyBase64);
}
```
- [ ] 实现加密数据状态管理
- [ ] 实现用户会话管理
- [ ] 添加状态持久化

## 5. 后端实现

### 5.1 Rust 命令
- [ ] 实现密钥派生命令
```rust
// src/commands/crypto_commands.rs
use argon2::{self, Config, ThreadMode, Variant, Version};
use rand::Rng;
use tauri::command;

#[command]
pub fn derive_master_key(password: String, email: String) -> Result<Vec<u8>, String> {
    let mut salt = [0u8; 16];
    rand::thread_rng().fill(&mut salt);
    
    // 使用邮箱作为盐的一部分，确保不同用户的相同密码产生不同的密钥
    let mut combined_salt = Vec::with_capacity(salt.len() + email.len());
    combined_salt.extend_from_slice(&salt);
    combined_salt.extend_from_slice(email.as_bytes());
    
    let config = Config {
        variant: Variant::Argon2id,
        version: Version::Version13,
        mem_cost: 65536, // 64 MB
        time_cost: 3,    // 3 次迭代
        lanes: 4,        // 4 个并行通道
        thread_mode: ThreadMode::Parallel,
        secret: &[],     // 无额外密钥
        ad: &[],         // 无额外数据
        hash_length: 32, // 256 位密钥
    };
    
    argon2::hash_raw(password.as_bytes(), &combined_salt, &config)
        .map_err(|e| e.to_string())
}
```
- [ ] 实现加密/解密命令
- [ ] 实现密钥管理命令
- [ ] 添加安全验证命令

### 5.2 安全存储
- [ ] 实现安全存储服务
```rust
// src/services/secure_storage.rs
use tauri::{AppHandle, Manager};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::fs;
use std::io::{Read, Write};

#[derive(Serialize, Deserialize)]
pub struct EncryptedData {
    pub data: Vec<u8>,
    pub hmac: Vec<u8>,
}

pub struct SecureStorageService {
    app_handle: AppHandle,
}

impl SecureStorageService {
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }
    
    pub fn get_storage_path(&self) -> Result<PathBuf, String> {
        let app_dir = self.app_handle.path().app_data_dir()
            .map_err(|e| e.to_string())?;
        
        let storage_dir = app_dir.join("secure_storage");
        
        if !storage_dir.exists() {
            fs::create_dir_all(&storage_dir)
                .map_err(|e| format!("Failed to create storage directory: {}", e))?;
        }
        
        Ok(storage_dir)
    }
    
    pub fn save(&self, key: &str, data: &EncryptedData) -> Result<(), String> {
        let storage_path = self.get_storage_path()?;
        let file_path = storage_path.join(format!("{}.dat", key));
        
        let serialized = bincode::serialize(data)
            .map_err(|e| format!("Failed to serialize data: {}", e))?;
        
        let mut file = fs::File::create(file_path)
            .map_err(|e| format!("Failed to create file: {}", e))?;
        
        file.write_all(&serialized)
            .map_err(|e| format!("Failed to write data: {}", e))?;
        
        Ok(())
    }
    
    pub fn load(&self, key: &str) -> Result<EncryptedData, String> {
        let storage_path = self.get_storage_path()?;
        let file_path = storage_path.join(format!("{}.dat", key));
        
        if !file_path.exists() {
            return Err(format!("Data for key '{}' not found", key));
        }
        
        let mut file = fs::File::open(file_path)
            .map_err(|e| format!("Failed to open file: {}", e))?;
        
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer)
            .map_err(|e| format!("Failed to read file: {}", e))?;
        
        let data: EncryptedData = bincode::deserialize(&buffer)
            .map_err(|e| format!("Failed to deserialize data: {}", e))?;
        
        Ok(data)
    }
    
    pub fn delete(&self, key: &str) -> Result<(), String> {
        let storage_path = self.get_storage_path()?;
        let file_path = storage_path.join(format!("{}.dat", key));
        
        if file_path.exists() {
            fs::remove_file(file_path)
                .map_err(|e| format!("Failed to delete file: {}", e))?;
        }
        
        Ok(())
    }
}
```
- [ ] 实现密钥安全存储
- [ ] 实现加密数据存储
- [ ] 添加存储完整性验证

### 5.3 系统集成
- [ ] 实现系统密钥链集成
```rust
// src/platform/keychain.rs
use keyring::Keyring;
use tauri::command;

#[command]
pub fn store_in_keychain(service: String, username: String, password: String) -> Result<(), String> {
    let keyring = Keyring::new(&service, &username)
        .map_err(|e| e.to_string())?;
    
    keyring.set_password(&password)
        .map_err(|e| e.to_string())
}

#[command]
pub fn get_from_keychain(service: String, username: String) -> Result<String, String> {
    let keyring = Keyring::new(&service, &username)
        .map_err(|e| e.to_string())?;
    
    keyring.get_password()
        .map_err(|e| e.to_string())
}

#[command]
pub fn delete_from_keychain(service: String, username: String) -> Result<(), String> {
    let keyring = Keyring::new(&service, &username)
        .map_err(|e| e.to_string())?;
    
    keyring.delete_password()
        .map_err(|e| e.to_string())
}
```
- [ ] 实现安全随机数生成
- [ ] 实现内存保护
- [ ] 添加平台特定安全功能

## 6. 同步与恢复

### 6.1 安全同步
- [ ] 设计加密同步协议
```typescript
// src/services/syncService.ts
import { invoke } from '@tauri-apps/api/tauri';
import { cryptoService } from './cryptoService';
import { vaultService } from './vaultService';

export class SyncService {
  private serverUrl: string;
  private authToken: string | null = null;
  
  constructor(serverUrl: string) {
    this.serverUrl = serverUrl;
  }
  
  async authenticate(email: string, authHash: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.serverUrl}/auth`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, authHash }),
      });
      
      if (!response.ok) {
        return false;
      }
      
      const data = await response.json();
      this.authToken = data.token;
      return true;
    } catch (error) {
      console.error('Authentication failed:', error);
      return false;
    }
  }
  
  async syncVault(): Promise<boolean> {
    if (!this.authToken) {
      throw new Error('Not authenticated');
    }
    
    try {
      // 获取本地保管库数据
      const localVault = await vaultService.getVault();
      
      // 获取本地同步状态
      const lastSync = await vaultService.getLastSyncTimestamp();
      
      // 准备同步请求
      const syncRequest = {
        lastSync,
        changes: localVault.items.filter(item => item.updatedAt > lastSync),
      };
      
      // 加密同步请求
      const encryptedSyncRequest = await cryptoService.encryptData(syncRequest);
      
      // 发送同步请求
      const response = await fetch(`${this.serverUrl}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({ encryptedData: encryptedSyncRequest }),
      });
      
      if (!response.ok) {
        return false;
      }
      
      const encryptedResponse = await response.json();
      
      // 解密同步响应
      const syncResponse = await cryptoService.decryptData(encryptedResponse.encryptedData);
      
      // 处理服务器更改
      await vaultService.applyServerChanges(syncResponse.changes);
      
      // 更新同步时间戳
      await vaultService.updateLastSyncTimestamp(syncResponse.timestamp);
      
      return true;
    } catch (error) {
      console.error('Sync failed:', error);
      return false;
    }
  }
}

export const syncService = new SyncService(import.meta.env.VITE_API_URL);
```
- [ ] 实现增量同步
- [ ] 实现冲突解决
- [ ] 添加同步状态管理

### 6.2 密码恢复
- [ ] 设计安全恢复机制
- [ ] 实现紧急访问
- [ ] 实现账户恢复
- [ ] 添加恢复验证

### 6.3 备份与导出
- [ ] 实现加密备份
```typescript
// src/services/backupService.ts
import { invoke } from '@tauri-apps/api/tauri';
import { save } from '@tauri-apps/api/dialog';
import { writeTextFile } from '@tauri-apps/api/fs';
import { cryptoService } from './cryptoService';
import { vaultService } from './vaultService';

export class BackupService {
  async createEncryptedBackup(): Promise<string | null> {
    try {
      // 获取保管库数据
      const vault = await vaultService.getVault();
      
      // 创建备份元数据
      const backupData = {
        version: '1.0',
        timestamp: Date.now(),
        vault,
      };
      
      // 使用当前对称密钥加密备份数据
      const encryptedBackup = await cryptoService.encryptData(backupData);
      
      // 添加备份头信息
      const backupFile = JSON.stringify({
        format: 'password-manager-backup',
        version: '1.0',
        encryptedData: encryptedBackup,
      });
      
      // 提示用户选择保存位置
      const savePath = await save({
        filters: [{
          name: 'Backup Files',
          extensions: ['bak']
        }],
        defaultPath: `password-manager-backup-${new Date().toISOString().split('T')[0]}.bak`,
      });
      
      if (!savePath) {
        return null;
      }
      
      // 保存备份文件
      await writeTextFile(savePath, backupFile);
      
      return savePath;
    } catch (error) {
      console.error('Backup creation failed:', error);
      return null;
    }
  }
  
  async restoreFromBackup(backupPath: string, masterPassword: string, email: string): Promise<boolean> {
    try {
      // 读取备份文件
      const backupContent = await invoke<string>('read_file', { path: backupPath });
      
      // 解析备份文件
      const backupFile = JSON.parse(backupContent);
      
      if (backupFile.format !== 'password-manager-backup') {
        throw new Error('Invalid backup file format');
      }
      
      // 派生主密钥
      await cryptoService.deriveMasterKey(masterPassword, email);
      
      // 解密备份数据
      const backupData = await cryptoService.decryptData(backupFile.encryptedData);
      
      // 恢复保管库数据
      await vaultService.restoreVault(backupData.vault);
      
      return true;
    } catch (error) {
      console.error('Backup restoration failed:', error);
      return false;
    }
  }
}

export const backupService = new BackupService();
```
- [ ] 实现安全导出
- [ ] 实现导入验证
- [ ] 添加版本兼容性处理

## 7. 安全审计与测试

### 7.1 安全审计
- [ ] 实现密码学审计日志
- [ ] 添加关键操作审计
- [ ] 实现安全事件监控
- [ ] 创建安全报告功能

### 7.2 单元测试
- [ ] 测试密钥派生函数
```rust
// src/tests/crypto_tests.rs
#[cfg(test)]
mod tests {
    use super::*;
    use crate::commands::crypto_commands::*;
    
    #[test]
    fn test_derive_master_key() {
        let password = "StrongPassword123!";
        let email = "<EMAIL>";
        
        let result = derive_master_key(password.to_string(), email.to_string());
        
        assert!(result.is_ok());
        let master_key = result.unwrap();
        assert_eq!(master_key.len(), 32); // 256 位密钥
        
        // 相同输入应产生相同输出
        let result2 = derive_master_key(password.to_string(), email.to_string());
        assert!(result2.is_ok());
        
        // 不同邮箱应产生不同密钥
        let result3 = derive_master_key(password.to_string(), "<EMAIL>".to_string());
        assert!(result3.is_ok());
        assert_ne!(result3.unwrap(), master_key);
        
        // 不同密码应产生不同密钥
        let result4 = derive_master_key("DifferentPassword123!".to_string(), email.to_string());
        assert!(result4.is_ok());
        assert_ne!(result4.unwrap(), master_key);
    }
    
    #[test]
    fn test_encrypt_decrypt_data() {
        let key = vec![0u8; 32]; // 全零密钥，仅用于测试
        let data = "Secret data to encrypt";
        
        let encrypted = encrypt_data(key.clone(), data.to_string()).unwrap();
        assert!(!encrypted.is_empty());
        
        let decrypted = decrypt_data(key.clone(), encrypted).unwrap();
        assert_eq!(decrypted, data);
    }
}
```
- [ ] 测试加密/解密函数
- [ ] 测试数据完整性验证
- [ ] 测试边界情况和错误处理

### 7.3 集成测试
- [ ] 测试完整加密流程
- [ ] 测试密钥管理流程
- [ ] 测试同步和恢复流程
- [ ] 测试跨平台兼容性

## 8. 文档与示例

### 8.1 架构文档
- [ ] 创建零知识架构概述
- [ ] 编写密钥管理文档
- [ ] 编写数据加密文档
- [ ] 添加安全最佳实践

### 8.2 API 文档
- [ ] 编写加密 API 文档
- [ ] 编写密钥管理 API 文档
- [ ] 编写同步 API 文档
- [ ] 添加示例代码

### 8.3 用户指南
- [ ] 创建主密码最佳实践指南
- [ ] 编写安全备份指南
- [ ] 编写恢复流程指南
- [ ] 添加常见问题解答
