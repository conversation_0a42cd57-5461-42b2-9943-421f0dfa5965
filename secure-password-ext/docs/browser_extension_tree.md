# Browser Extension Project File Structure

```
password-manager-extension/                # 项目根目录
│
├── src/                                   # 源代码目录
│   ├── background/                        # 后台脚本
│   │   ├── index.ts                       # 后台脚本入口
│   │   ├── listeners/                     # 事件监听器
│   │   │   ├── auth.listener.ts           # 认证相关监听器
│   │   │   ├── sync.listener.ts           # 同步相关监听器
│   │   │   ├── storage.listener.ts        # 存储相关监听器
│   │   │   └── ...
│   │   │
│   │   ├── services/                      # 后台服务
│   │   │   ├── auth.service.ts            # 认证服务
│   │   │   ├── crypto.service.ts          # 加密服务
│   │   │   ├── storage.service.ts         # 存储服务
│   │   │   ├── sync.service.ts            # 同步服务
│   │   │   └── ...
│   │   │
│   │   ├── controllers/                   # 控制器
│   │   │   ├── auth.controller.ts         # 认证控制器
│   │   │   ├── vault.controller.ts        # 密码库控制器
│   │   │   ├── settings.controller.ts     # 设置控制器
│   │   │   └── ...
│   │   │
│   │   └── utils/                         # 工具函数
│   │       ├── messaging.ts               # 消息传递工具
│   │       ├── logging.ts                 # 日志工具
│   │       └── ...
│   │
│   ├── content/                           # 内容脚本
│   │   ├── index.ts                       # 内容脚本入口
│   │   ├── form-detection/                # 表单检测
│   │   │   ├── detector.ts                # 表单检测器
│   │   │   ├── analyzer.ts                # 表单分析器
│   │   │   ├── field-matcher.ts           # 字段匹配器
│   │   │   └── ...
│   │   │
│   │   ├── autofill/                      # 自动填充
│   │   │   ├── autofiller.ts              # 自动填充器
│   │   │   ├── field-filler.ts            # 字段填充器
│   │   │   ├── dom-manipulator.ts         # DOM 操作器
│   │   │   └── ...
│   │   │
│   │   ├── overlay/                       # 页面覆盖层
│   │   │   ├── inline-menu/               # 内联菜单
│   │   │   │   ├── menu-injector.ts       # 菜单注入器
│   │   │   │   ├── menu-positioner.ts     # 菜单定位器
│   │   │   │   ├── menu-renderer.ts       # 菜单渲染器
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── notifications/             # 页面通知
│   │   │   │   ├── notification-injector.ts # 通知注入器
│   │   │   │   ├── notification-renderer.ts # 通知渲染器
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   ├── iframe/                        # iframe 处理
│   │   │   ├── detector.ts                # iframe 检测器
│   │   │   ├── communicator.ts            # iframe 通信器
│   │   │   └── ...
│   │   │
│   │   └── utils/                         # 工具函数
│   │       ├── dom-utils.ts               # DOM 工具
│   │       ├── messaging.ts               # 消息传递工具
│   │       ├── security.ts                # 安全工具
│   │       └── ...
│   │
│   ├── popup/                             # 弹出窗口
│   │   ├── index.tsx                      # 弹出窗口入口
│   │   ├── App.tsx                        # 主应用组件
│   │   ├── pages/                         # 页面组件
│   │   │   ├── login/                     # 登录页面
│   │   │   │   ├── Login.tsx              # 登录组件
│   │   │   │   ├── LoginForm.tsx          # 登录表单
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── vault/                     # 密码库页面
│   │   │   │   ├── Vault.tsx              # 密码库组件
│   │   │   │   ├── ItemList.tsx           # 项目列表
│   │   │   │   ├── ItemDetails.tsx        # 项目详情
│   │   │   │   ├── ItemForm.tsx           # 项目表单
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── generator/                 # 密码生成器页面
│   │   │   │   ├── Generator.tsx          # 生成器组件
│   │   │   │   ├── GeneratorForm.tsx      # 生成器表单
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── settings/                  # 设置页面
│   │   │   │   ├── Settings.tsx           # 设置组件
│   │   │   │   ├── SecuritySettings.tsx   # 安全设置
│   │   │   │   ├── SyncSettings.tsx       # 同步设置
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   ├── components/                    # 共享组件
│   │   │   ├── common/                    # 通用组件
│   │   │   │   ├── Button.tsx             # 按钮组件
│   │   │   │   ├── Input.tsx              # 输入框组件
│   │   │   │   ├── Modal.tsx              # 模态框组件
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── auth/                      # 认证组件
│   │   │   │   ├── AuthForm.tsx           # 认证表单
│   │   │   │   ├── TwoFactorForm.tsx      # 双因素认证表单
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── vault/                     # 密码库组件
│   │   │   │   ├── ItemCard.tsx           # 项目卡片
│   │   │   │   ├── PasswordField.tsx      # 密码字段
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   ├── hooks/                         # 自定义 Hooks
│   │   │   ├── useAuth.ts                 # 认证 Hook
│   │   │   ├── useVault.ts                # 密码库 Hook
│   │   │   ├── useSettings.ts             # 设置 Hook
│   │   │   └── ...
│   │   │
│   │   ├── services/                      # 前端服务
│   │   │   ├── api.service.ts             # API 服务
│   │   │   ├── messaging.service.ts       # 消息服务
│   │   │   └── ...
│   │   │
│   │   ├── store/                         # 状态管理
│   │   │   ├── index.ts                   # 存储入口
│   │   │   ├── slices/                    # 状态切片
│   │   │   │   ├── auth.slice.ts          # 认证状态
│   │   │   │   ├── vault.slice.ts         # 密码库状态
│   │   │   │   ├── settings.slice.ts      # 设置状态
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   └── styles/                        # 样式
│   │       ├── global.css                 # 全局样式
│   │       ├── themes/                    # 主题
│   │       │   ├── light.css              # 亮色主题
│   │       │   ├── dark.css               # 暗色主题
│   │       │   └── ...
│   │       │
│   │       └── ...
│   │
│   ├── options/                           # 选项页面
│   │   ├── index.tsx                      # 选项页面入口
│   │   ├── App.tsx                        # 主应用组件
│   │   ├── pages/                         # 页面组件
│   │   │   ├── general/                   # 通用设置
│   │   │   ├── security/                  # 安全设置
│   │   │   ├── advanced/                  # 高级设置
│   │   │   └── ...
│   │   │
│   │   └── ...                            # 与 popup 类似的结构
│   │
│   ├── inline-menu/                       # 内联菜单 iframe 内容
│   │   ├── index.tsx                      # 内联菜单入口
│   │   ├── App.tsx                        # 主应用组件
│   │   ├── components/                    # 组件
│   │   │   ├── ItemList.tsx               # 项目列表
│   │   │   ├── SearchBar.tsx              # 搜索栏
│   │   │   └── ...
│   │   │
│   │   └── ...                            # 与 popup 类似的结构
│   │
│   ├── shared/                            # 共享代码
│   │   ├── models/                        # 数据模型
│   │   │   ├── user.model.ts              # 用户模型
│   │   │   ├── vault-item.model.ts        # 密码库项目模型
│   │   │   ├── settings.model.ts          # 设置模型
│   │   │   └── ...
│   │   │
│   │   ├── services/                      # 共享服务
│   │   │   ├── crypto/                    # 加密服务
│   │   │   │   ├── key-derivation.ts      # 密钥派生
│   │   │   │   ├── symmetric-crypto.ts    # 对称加密
│   │   │   │   ├── random.ts              # 随机数生成
│   │   │   │   └── ...
│   │   │   │
│   │   │   ├── storage/                   # 存储服务
│   │   │   │   ├── secure-storage.ts      # 安全存储
│   │   │   │   ├── local-storage.ts       # 本地存储
│   │   │   │   └── ...
│   │   │   │
│   │   │   └── ...
│   │   │
│   │   ├── utils/                         # 共享工具
│   │   │   ├── string-utils.ts            # 字符串工具
│   │   │   ├── validation.ts              # 验证工具
│   │   │   ├── formatting.ts              # 格式化工具
│   │   │   └── ...
│   │   │
│   │   ├── constants/                     # 常量
│   │   │   ├── api.constants.ts           # API 常量
│   │   │   ├── storage.constants.ts       # 存储常量
│   │   │   ├── message.constants.ts       # 消息常量
│   │   │   └── ...
│   │   │
│   │   └── ...
│   │
│   ├── locales/                           # 国际化
│   │   ├── en/                            # 英文
│   │   │   ├── common.json                # 通用翻译
│   │   │   ├── auth.json                  # 认证翻译
│   │   │   ├── vault.json                 # 密码库翻译
│   │   │   └── ...
│   │   │
│   │   ├── zh/                            # 中文
│   │   │   ├── common.json                # 通用翻译
│   │   │   ├── auth.json                  # 认证翻译
│   │   │   ├── vault.json                 # 密码库翻译
│   │   │   └── ...
│   │   │
│   │   └── ...                            # 其他语言
│   │
│   ├── assets/                            # 静态资源
│   │   ├── icons/                         # 图标
│   │   │   ├── icon-16.png                # 16x16 图标
│   │   │   ├── icon-48.png                # 48x48 图标
│   │   │   ├── icon-128.png               # 128x128 图标
│   │   │   └── ...
│   │   │
│   │   ├── images/                        # 图片
│   │   │   ├── logo.png                   # 徽标
│   │   │   ├── background.png             # 背景
│   │   │   └── ...
│   │   │
│   │   └── ...
│   │
│   ├── manifest/                          # 浏览器清单文件
│   │   ├── base.json                      # 基础清单
│   │   ├── chrome.json                    # Chrome 特定配置
│   │   ├── firefox.json                   # Firefox 特定配置
│   │   ├── edge.json                      # Edge 特定配置
│   │   └── ...
│   │
│   └── ...
│
├── public/                                # 静态文件
│   ├── popup.html                         # 弹出窗口 HTML
│   ├── options.html                       # 选项页面 HTML
│   ├── inline-menu.html                   # 内联菜单 HTML
│   └── ...
│
├── tests/                                 # 测试
│   ├── unit/                              # 单元测试
│   │   ├── background/                    # 后台脚本测试
│   │   ├── content/                       # 内容脚本测试
│   │   ├── shared/                        # 共享代码测试
│   │   └── ...
│   │
│   ├── integration/                       # 集成测试
│   │   ├── auth.test.ts                   # 认证测试
│   │   ├── vault.test.ts                  # 密码库测试
│   │   ├── autofill.test.ts               # 自动填充测试
│   │   └── ...
│   │
│   ├── e2e/                               # 端到端测试
│   │   ├── auth.spec.ts                   # 认证流程测试
│   │   ├── vault.spec.ts                  # 密码库流程测试
│   │   ├── autofill.spec.ts               # 自动填充流程测试
│   │   └── ...
│   │
│   └── ...
│
├── scripts/                               # 脚本
│   ├── build.js                           # 构建脚本
│   ├── dev.js                             # 开发脚本
│   ├── package.js                         # 打包脚本
│   └── ...
│
├── config/                                # 配置
│   ├── webpack.common.js                  # Webpack 通用配置
│   ├── webpack.dev.js                     # Webpack 开发配置
│   ├── webpack.prod.js                    # Webpack 生产配置
│   ├── jest.config.js                     # Jest 配置
│   └── ...
│
├── docs/                                  # 文档
│   ├── architecture/                      # 架构文档
│   ├── api/                               # API 文档
│   ├── development/                       # 开发文档
│   ├── user-guide/                        # 用户指南
│   └── ...
│
├── .github/                               # GitHub 配置
│   ├── workflows/                         # GitHub Actions
│   │   ├── build.yml                      # 构建工作流
│   │   ├── test.yml                       # 测试工作流
│   │   ├── release.yml                    # 发布工作流
│   │   └── ...
│   │
│   └── ...
│
├── .vscode/                               # VS Code 配置
│   ├── settings.json                      # 编辑器设置
│   ├── extensions.json                    # 推荐扩展
│   └── ...
│
├── .gitignore                             # Git 忽略文件
├── package.json                           # NPM 包配置
├── tsconfig.json                          # TypeScript 配置
├── .eslintrc.js                           # ESLint 配置
├── .prettierrc.js                         # Prettier 配置
├── jest.config.js                         # Jest 配置
├── README.md                              # 项目说明
└── LICENSE                                # 许可证
```

## 关键文件说明

### 1. 核心功能文件

#### 1.1 后台脚本 (Background)

- **`src/background/index.ts`**: 后台脚本入口点，初始化后台服务和监听器。
- **`src/background/services/crypto.service.ts`**: 提供加密和解密功能，包括密钥派生和对称加密。
- **`src/background/services/auth.service.ts`**: 处理用户认证，包括登录、注册和会话管理。
- **`src/background/services/storage.service.ts`**: 管理安全存储，包括加密数据的存储和检索。
- **`src/background/services/sync.service.ts`**: 处理与服务器的数据同步。

#### 1.2 内容脚本 (Content)

- **`src/content/index.ts`**: 内容脚本入口点，初始化表单检测和自动填充功能。
- **`src/content/form-detection/detector.ts`**: 检测页面上的表单和输入字段。
- **`src/content/autofill/autofiller.ts`**: 实现自动填充功能。
- **`src/content/overlay/inline-menu/menu-injector.ts`**: 将内联菜单注入到页面中。
- **`src/content/iframe/communicator.ts`**: 处理与 iframe 的通信。

#### 1.3 弹出窗口 (Popup)

- **`src/popup/index.tsx`**: 弹出窗口入口点。
- **`src/popup/App.tsx`**: 弹出窗口的主应用组件。
- **`src/popup/pages/vault/Vault.tsx`**: 密码库页面，显示用户的密码项。
- **`src/popup/pages/login/Login.tsx`**: 登录页面。

#### 1.4 共享代码 (Shared)

- **`src/shared/services/crypto/key-derivation.ts`**: 实现密钥派生功能。
- **`src/shared/services/crypto/symmetric-crypto.ts`**: 实现对称加密功能。
- **`src/shared/models/vault-item.model.ts`**: 定义密码库项目的数据模型。

### 2. 通信和集成

- **`src/shared/utils/messaging.ts`**: 处理扩展内部通信。
- **`src/background/listeners/auth.listener.ts`**: 监听认证相关消息。
- **`src/popup/services/messaging.service.ts`**: 提供与后台脚本通信的服务。

### 3. 用户界面

- **`src/popup/components/common/Button.tsx`**: 通用按钮组件。
- **`src/popup/components/vault/ItemCard.tsx`**: 显示密码项的卡片组件。
- **`src/popup/components/auth/AuthForm.tsx`**: 认证表单组件。
- **`src/inline-menu/components/ItemList.tsx`**: 内联菜单中的项目列表组件。

### 4. 国际化和资源

- **`src/locales/en/common.json`**: 英文通用翻译。
- **`src/locales/zh/common.json`**: 中文通用翻译。
- **`src/assets/icons/icon-48.png`**: 48x48 图标。

### 5. 构建和配置

- **`config/webpack.common.js`**: Webpack 通用配置。
- **`src/manifest/base.json`**: 基础浏览器扩展清单。
- **`src/manifest/chrome.json`**: Chrome 特定的清单配置。

## 模块依赖关系

1. **核心模块**:
   - `shared/services/crypto` → 提供基础加密功能
   - `shared/models` → 定义数据结构
   - `shared/utils` → 提供通用工具函数

2. **后台模块**:
   - `background/services` → 依赖 `shared/services` 和 `shared/models`
   - `background/controllers` → 依赖 `background/services`
   - `background/listeners` → 依赖 `background/controllers`

3. **内容脚本模块**:
   - `content/form-detection` → 独立模块，提供表单检测
   - `content/autofill` → 依赖 `content/form-detection`
   - `content/overlay` → 依赖 `content/form-detection`

4. **UI 模块**:
   - `popup/components` → 提供 UI 组件
   - `popup/pages` → 依赖 `popup/components` 和 `popup/services`
   - `popup/services` → 依赖 `shared/services`

## 数据流

1. **认证流程**:
   用户输入 → `popup/pages/login` → `popup/services/messaging` → `background/listeners/auth` → `background/controllers/auth` → `background/services/auth` → `shared/services/crypto`

2. **自动填充流程**:
   表单焦点 → `content/form-detection` → `content/overlay` → 用户选择 → `content/autofill` → 填充表单

3. **数据同步流程**:
   `background/services/sync` → `background/services/storage` → `background/controllers/vault` → 通知 UI 更新

## 扩展点

1. **新增浏览器支持**:
   - 在 `src/manifest/` 中添加新的浏览器特定配置
   - 在 `scripts/build.js` 中添加新的构建目标

2. **新增功能模块**:
   - 在相应目录中添加新的服务、控制器或组件
   - 更新相关的监听器和消息处理

3. **UI 主题**:
   - 在 `src/popup/styles/themes/` 中添加新的主题文件
   - 更新主题切换逻辑

## 安全考虑

1. **敏感数据处理**:
   - 所有敏感数据在 `shared/services/crypto` 中加密
   - 明文密码仅在内存中短暂存在
   - 使用 `background/services/storage` 安全存储加密数据

2. **通信安全**:
   - 使用 `shared/utils/messaging` 确保扩展内部通信安全
   - 内容脚本与页面隔离，通过受控接口交互

3. **权限最小化**:
   - 在 `manifest` 文件中仅请求必要的权限
   - 使用内容脚本的 `activeTab` 权限而非广泛的 `tabs` 权限
