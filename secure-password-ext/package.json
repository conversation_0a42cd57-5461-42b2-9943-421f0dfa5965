{"name": "secure-password-ext", "displayName": "secure-password-ext", "version": "0.0.0", "author": "**", "description": "", "type": "module", "license": "MIT", "keywords": ["chrome-extension", "react", "vite", "create-chrome-ext"], "engines": {"node": ">=14.18.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "fmt": "prettier --write '**/*.{tsx,ts,json,css,scss,md}'", "zip": "npm run build && node src/zip.js"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@tailwindcss/vite": "^4.1.7", "@types/date-fns": "^2.6.3", "@types/uuid": "^10.0.0", "antd": "^5.25.2", "date-fns": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-use": "^17.6.0", "rxjs": "^7.8.2", "uuid": "^11.1.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.26", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.0.246", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.21", "gulp": "^5.0.0", "gulp-zip": "^6.0.0", "prettier": "^3.0.3", "tailwindcss": "^4.1.7", "typescript": "^5.2.2", "vite": "^5.4.10"}}