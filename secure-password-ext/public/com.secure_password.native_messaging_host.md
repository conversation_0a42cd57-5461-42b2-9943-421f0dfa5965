// IMPORTANT:
// 1. Replace "YOUR_EXTENSION_ID_HERE" with the actual ID of your built extension.
//    You can find this ID on the chrome://extensions page after loading the unpacked extension.
// 2. The "path" MUST point to the actual executable of your Tauri application.
//    The provided path is a placeholder for the debug build. Adjust it for release builds or if your setup differs.
// 3. This file needs to be placed in the correct directory for your browser on macOS:
//    - Chrome: ~/Library/Application Support/Google/Chrome/NativeMessagingHosts/
//    - Firefox: ~/Library/Application Support/Mozilla/NativeMessagingHosts/
//    - Edge: ~/Library/Application Support/Microsoft Edge/NativeMessagingHosts/
//    You will need to manually copy this file there after building the extension and the Tauri app.
