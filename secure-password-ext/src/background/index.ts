// secure-password-ext/src/background/index.ts
console.log("Secure Password Background Script Loaded - v5");

// Import the background services
import { BackgroundMessageService } from '../services/background-message.service';
import { CrossIframePositionerBackgroundService } from '../services/cross-iframe-positioner-background.service';
import { MessageRouterService } from '../services/message-router.service';
import { IframeTreeService } from '../services/iframe-tree.service';

// Define the Native Messaging host name
// This MUST match the name specified in the native manifest file
const nativeHostName = "com.secure_password.native_messaging_host";

// 声明全局变量，在 service worker 中使用
// 为 globalThis 添加类型声明，使其可以被其他模块访问
declare global {
  var port: chrome.runtime.Port | null;
  var connectNative: () => void;
}

// 初始化全局变量
globalThis.port = null;

// 初始化消息路由服务（必须先初始化）
const messageRouter = MessageRouterService.getInstance();
console.log('MessageRouterService initialized:', !!messageRouter);

// 初始化后台消息服务
const backgroundMessageService = BackgroundMessageService.getInstance();
console.log('BackgroundMessageService initialized:', !!backgroundMessageService);

// 初始化跨iframe定位后台服务
const crossIframePositionerBackgroundService = CrossIframePositionerBackgroundService.getInstance();
console.log('CrossIframePositionerBackgroundService initialized:', !!crossIframePositionerBackgroundService);

// 初始化iframe树结构服务
const iframeTreeService = IframeTreeService.getInstance();
console.log('IframeTreeService initialized:', !!iframeTreeService);

// 定义连接原生应用的函数并赋值给全局对象
globalThis.connectNative = function() {
  console.log(`Attempting to connect to native host: ${nativeHostName}`);
  try {
    globalThis.port = chrome.runtime.connectNative(nativeHostName);
    console.log("Successfully connected to native host:", globalThis.port);

    globalThis.port.onMessage.addListener((message: any) => {
      console.log("Received native message:", message);
      // Forward the native message to the appropriate content script
      // Correlate responses using the requestId.
      if (message.requestId && message.type === 'passkeyCreateNativeResponse') {
        // Find the tab associated with the request. This might require storing
        // the sender.tab.id when the request first comes in.
        // For now, sending to the active tab as a simplification.
        chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
          const activeTab = tabs[0];
          if (activeTab && activeTab.id) {
            console.log(`Forwarding response for requestId ${message.requestId} to tab ${activeTab.id}`);
            chrome.tabs.sendMessage(activeTab.id, {
              type: 'passkeyCreateResponse',
              requestId: message.requestId,
              success: message.success,
              data: message.data, // Forward data or error
              error: message.error
            }, (response) => {
              if (chrome.runtime.lastError) {
                console.warn(`Error sending message to tab ${activeTab.id}: ${chrome.runtime.lastError.message}`);
              } else {
                console.log(`Response sent to tab ${activeTab.id}, content script response:`, response);
              }
            });
          } else {
            console.error("Could not find active tab to send response to.");
          }
        });
      } else if (message.requestId && message.type === 'passkeyGetNativeResponse') {
        // Handle the response for passkeyGetRequest
        chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
          const activeTab = tabs[0];
          if (activeTab && activeTab.id) {
            console.log(`Forwarding get response for requestId ${message.requestId} to tab ${activeTab.id}`);
            chrome.tabs.sendMessage(activeTab.id, {
              type: 'passkeyGetResponse', // Ensure correct type for content script
              requestId: message.requestId,
              success: message.success,
              data: message.data,
              error: message.error
            }, (response) => {
              // Check for errors when sending the message (e.g., tab closed)
              if (chrome.runtime.lastError) {
                console.warn(`Error sending get message to tab ${activeTab.id}: ${chrome.runtime.lastError.message}`);
              } else {
                console.log(`Get response sent to tab ${activeTab.id}, content script response:`, response);
              }
            });
          } else {
            console.error("Could not find active tab to send get response to.");
          }
        });
      } else if (message.requestId && message.type === 'getPasswordsForDomainResponse') {
        // Handle the response for getPasswordsForDomainRequest
        chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
          const activeTab = tabs[0];
          if (activeTab && activeTab.id) {
            console.log(`Forwarding password list response for requestId ${message.requestId} to tab ${activeTab.id}`);
            chrome.tabs.sendMessage(activeTab.id, {
              type: 'passwordListResponse', // Type for content script
              requestId: message.requestId,
              success: message.success,
              data: message.data, // List of credentials
              error: message.error,
              frameId: message.senderFrameId, // Include the frame ID if available
              href: message.options?.href // Include the href for better frame identification
            }, (response) => {
              // Check for errors when sending the message
              if (chrome.runtime.lastError) {
                console.warn(`Error sending password list to tab ${activeTab.id}: ${chrome.runtime.lastError.message}`);
              } else {
                console.log(`Password list sent to tab ${activeTab.id}, content script response:`, response);
              }
            });
          } else {
            console.error("Could not find active tab to send password list to.");
          }
        });
      } else {
        console.warn("Received unexpected native message format:", message);
      }
    });

    globalThis.port.onDisconnect.addListener(() => {
      const errorMsg = chrome.runtime.lastError?.message || "Unknown reason";
      console.error(`Native host disconnected: ${errorMsg}`);
      globalThis.port = null;

      // 检查是否是权限问题导致的断开
      if (errorMsg.includes("Access to the specified native messaging host is forbidden")) {
        console.error("Permission issue detected. Ensure the native messaging host is correctly configured.");
      }

      // Optional: Implement retry logic with backoff
      // setTimeout(connectNative, 5000); // Example retry after 5 seconds
    });

  } catch (e: any) {
    console.error(`Error connecting to native host ${nativeHostName}:`, e.message);

    // 检查是否是权限问题
    if (e.message.includes("Access to the specified native messaging host is forbidden")) {
      console.error("Permission issue detected. Please verify the native messaging host configuration.");
    }

    globalThis.port = null;
  }
}

// Initial connection attempt
// connectNative();

console.log("Background script event listeners added - v2.");
