import React, { useEffect, useRef, useState } from 'react';
import { List, Typography, Button, Space } from 'antd';
import { UserOutlined, KeyOutlined, CopyOutlined, CloseOutlined, LockOutlined } from '@ant-design/icons';
import type { CredentialOutput } from '../types';

const { Text } = Typography;

// Styles will be handled by Ant Design's StyleProvider

interface PasswordDropdownProps {
  credentials: CredentialOutput[];
  onSelect: (credential: CredentialOutput) => void;
  onClose: () => void;
}

const PasswordDropdown: React.FC<PasswordDropdownProps> = ({
  credentials,
  onSelect,
  onClose,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [visible, setVisible] = useState(false);

  // No need to inject styles manually as we're using StyleProvider

  // Fade in effect
  useEffect(() => {
    // Small delay to allow for DOM positioning before fade in
    const timer = setTimeout(() => {
      setVisible(true);
    }, 50);
    return () => clearTimeout(timer);
  }, []);

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 使用 composedPath 来处理 Shadow DOM 中的事件
      const path = event.composedPath();
      const isClickInside = dropdownRef.current ? path.includes(dropdownRef.current) : false;

      if (!isClickInside) {
        console.debug('点击在下拉菜单外部，关闭下拉菜单');
        // 调用 onClose 属性关闭下拉菜单
        onClose();
      } else {
        console.debug('点击在下拉菜单内部，保持下拉菜单打开');
        // 阻止事件冒泡，防止点击穿透
        event.stopPropagation();
      }
    };

    // 处理 Escape 键关闭下拉菜单
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        console.debug('按下 Escape 键，关闭下拉菜单');
        // 调用 onClose 属性关闭下拉菜单
        onClose();
      }
    };

    // 使用捕获阶段监听事件，确保在事件冒泡之前处理
    document.addEventListener('mousedown', handleClickOutside, true);
    document.addEventListener('keydown', handleEscKey, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
      document.removeEventListener('keydown', handleEscKey, true);
    };
  }, [onClose]);

  // 处理凭证选择
  const handleSelect = (credential: CredentialOutput) => {
    console.debug('选择凭证:', credential.username);
    // 调用 onSelect 属性处理凭证选择
    onSelect(credential);
  };

  // 处理复制密码
  const handleCopy = (e: React.MouseEvent, password: string) => {
    console.debug('复制密码');
    // 阻止事件冒泡，防止触发列表项的点击事件
    e.stopPropagation();
    // 阻止默认行为
    e.preventDefault();
    // 复制密码到剪贴板
    navigator.clipboard.writeText(password)
      .then(() => {
        console.debug('密码已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制密码失败:', err);
      });
  };

  return (
    <div
      ref={dropdownRef}
      style={{
        position: 'relative', // 使用相对定位
        width: '100%', // 使用容器的100%宽度
        maxHeight: '300px',
        overflowY: 'auto',
        backgroundColor: 'white',
        boxShadow: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
        borderRadius: '8px',
        zIndex: 2147483647, // 最大z-index
        border: '1px solid rgba(0, 0, 0, 0.1)',
        opacity: visible ? 1 : 0,
        transition: 'opacity 0.2s ease-out',
        pointerEvents: 'auto', // 确保可以接收鼠标事件
      }}
    >
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 12px',
        borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <LockOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <Text strong>可用密码</Text>
        </div>
        <Button
          type="text"
          icon={<CloseOutlined />}
          size="small"
          onClick={() => {
            // Call the onClose prop to close the dropdown
            onClose();
          }}
          style={{ marginLeft: 'auto' }}
        />
      </div>

      <List
        size="small"
        dataSource={credentials}
        renderItem={(item) => (
          <List.Item
            onClick={(e) => {
              // 阻止事件冒泡，防止点击穿透
              e.stopPropagation();
              // 处理凭证选择
              handleSelect(item);
            }}
            style={{
              cursor: 'pointer',
              padding: '8px 12px',
              pointerEvents: 'auto', // 确保可以接收鼠标事件
              position: 'relative', // 确保定位正确
              zIndex: 2147483647 // 最大z-index
            }}
            className="ant-list-item-hoverable" // 使用 Ant Design 的内置悬停类
            actions={[
              <Button
                key="copy"
                type="text"
                icon={<CopyOutlined />}
                size="small"
                onClick={(e) => handleCopy(e, item.password)}
                title="复制密码"
                style={{ pointerEvents: 'auto' }} // 确保按钮可以接收鼠标事件
              />
            ]}
          >
            <List.Item.Meta
              avatar={<UserOutlined style={{ color: '#1890ff' }} />}
              title={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Text ellipsis style={{ maxWidth: '180px' }}>{item.username}</Text>
                  {item.service_name && (
                    <Text type="secondary" style={{ fontSize: '12px', marginLeft: '4px' }}>
                      ({item.service_name})
                    </Text>
                  )}
                </div>
              }
              description={
                <Space>
                  <KeyOutlined style={{ color: '#52c41a' }} />
                  <Text ellipsis style={{ maxWidth: '150px' }}>
                    ••••••••
                  </Text>
                </Space>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: '没有找到匹配的密码' }}
      />
    </div>
  );
};

export default PasswordDropdown;
