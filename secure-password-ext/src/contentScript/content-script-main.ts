import { FormDetectionService } from '../services/form-detection.service';
import { FormListenerService } from '../services/form-listener.service';
import { PasswordDropdownService } from '../services/password-dropdown.service';
import { IframeIdentifierService } from '../services/iframe-identifier.service';

export class ContentScriptMain {
  private formDetectionService: FormDetectionService = new FormDetectionService();
  private formListenerService: FormListenerService;
  private passwordDropdownService: PasswordDropdownService = PasswordDropdownService.getInstance();
  private iframeIdentifierService: IframeIdentifierService = IframeIdentifierService.getInstance();
  private isInTopFrame: boolean = window === window.top;

  constructor() {
    // Initialize form listener service with form detection service
    this.formListenerService = new FormListenerService(this.formDetectionService);
    this.initialize();
  }

  /**
   * 初始化内容脚本
   */
  private async initialize(): Promise<void> {
    console.log('Secure Password Content Script initializing...');

    // 初始化iframe标识符服务
    await this.iframeIdentifierService.initialize();

    if (this.isInTopFrame) {
      console.log('Content script is running in the top frame');
      this.start();
    } else {
      console.log('Content script is running in an iframe');

      // 检查页面是否包含表单元素
      this.checkForFormsAndInitialize();

      // 设置 MutationObserver 监听 DOM 变化，以便在动态加载表单时重新初始化
      this.setupDomMutationObserver();
    }
  }

  /**
   * 检查页面是否包含表单元素并初始化相关服务
   */
  private checkForFormsAndInitialize(): void {
    // 使用 formDetectionService 检测页面中的表单
    this.formDetectionService.detectForms();

    // 订阅表单检测结果
    const subscription = this.formDetectionService.fieldsChanged$.subscribe(fields => {
      // 创建一个标记，记录服务是否已初始化
      const isInitialized = this.formListenerService.getIsInitialized();

      // 如果检测到表单字段，且服务尚未初始化
      if (fields && fields.length > 0 && !isInitialized) {
        console.log(`检测到 ${fields.length} 个表单字段，初始化密码填充服务...`);

        // 启动服务
        this.start();

        console.log('Secure Password Content Script initialized');
      }
      // 如果没有检测到表单字段，但服务已初始化，则卸载服务
      else if ((!fields || fields.length === 0) && isInitialized) {
        console.log('表单字段已移除，卸载密码填充服务...');

        // 卸载服务
        this.stop();

        console.log('Secure Password Content Script uninitialized');
      }
      // 其他情况，记录当前状态
      else {
        const status = isInitialized ? '已初始化' : '未初始化';
        console.log(`页面中${fields?.length || 0}个表单字段，密码填充服务${status}`);
      }
    });

    // 确保在一定时间后取消订阅，避免内存泄漏
    setTimeout(() => {
      if (!subscription.closed) {
        subscription.unsubscribe();
      }
    }, 10000); // 10秒后如果还没有检测到表单，则取消订阅
  }

  /**
   * 启动内容脚本
   */
  public start(): void {
    // 设置事件监听
    this.setupEventListeners();

    // 初始化服务
    this.formListenerService.initialize();
    this.passwordDropdownService.initialize();
  }

  /**
   * 停止内容脚本服务
   * 执行与start方法相反的操作，卸载各项服务
   */
  public stop(): void {
    // 清理事件监听
    this.cleanupEventListeners();

    // 卸载服务
    this.formListenerService.dispose();

    // 只在顶层页面卸载密码下拉服务
    if (this.isInTopFrame) {
      this.passwordDropdownService.dispose();
    }

    console.log('所有服务已卸载');
  }

  /**
   * 清理事件监听
   */
  private cleanupEventListeners(): void {
    // 这里应该清理在setupEventListeners中设置的所有订阅
    // 实际实现取决于你如何存储这些订阅
    console.log('清理事件监听...');
  }

  /**
   * 设置 DOM 变化监听，用于检测动态加载的表单
   */
  private setupDomMutationObserver(): void {
    // 创建一个 MutationObserver 实例
    const observer = new MutationObserver((mutations) => {
      let shouldDetect = false;

      // 检查变化是否与表单相关
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          // 检查添加的节点
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // 检查是否是表单元素或包含表单元素
              if (element.tagName === 'FORM' ||
                  element.querySelector('form, input[type="password"], input[type="email"], input[type="text"]')) {
                shouldDetect = true;
                break;
              }
            }
          }
        }

        if (shouldDetect) break;
      }

      // 如果检测到表单相关变化，重新检测表单并初始化
      if (shouldDetect) {
        console.log('检测到 DOM 变化，重新检测表单...');
        this.checkForFormsAndInitialize();
      }
    });

    // 开始观察文档变化
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听表单字段焦点事件
    this.formListenerService.fieldFocused$.subscribe(event => {
      this.passwordDropdownService.handleFieldFocus(event);
    });

    // 监听表单字段失焦事件
    this.formListenerService.fieldBlurred$.subscribe(() => {
      // 处理表单字段失焦事件，移除ShadowDOM容器
      this.passwordDropdownService.handleFieldBlur();
    });

    // 监听凭证选择事件
    this.passwordDropdownService.credentialSelected$.subscribe(() => {
      // 可以在这里添加凭证选择后的逻辑，例如记录使用历史
    });

    // 设置消息监听
    this.setupMessageListeners();
  }

  /**
   * 设置消息监听
   */
  private setupMessageListeners(): void {
    // 监听来自后台脚本的消息
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      console.log(globalThis.window.location.origin, `Content script received message: ${message.type}`, message);

      if (message.type === 'CLOSE_PASSWORD_DROPDOWN') {
        console.log('Received request to close password dropdown');
        // 关闭密码下拉菜单
        this.passwordDropdownService.closeDropdown();
        sendResponse({ success: true });
        return true;
      }

      return false;
    });
  }
}

// 初始化内容脚本
new ContentScriptMain();
