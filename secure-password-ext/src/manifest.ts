import { defineManifest } from '@crxjs/vite-plugin'
import packageData from '../package.json'

//@ts-ignore
const isDev = process.env.NODE_ENV == 'development'

export default defineManifest({
  name: `${packageData.displayName || packageData.name}${isDev ? ` ➡️ Dev` : ''}`,
  description: packageData.description,
  version: packageData.version,
  manifest_version: 3,
  icons: {
    16: 'img/logo-16.png',
    32: 'img/logo-32.png',
    48: 'img/logo-48.png',
    128: 'img/logo-128.png',
  },
  action: {
    default_popup: 'popup.html',
    default_icon: 'img/logo-48.png',
  },
  options_page: 'options.html',
  devtools_page: 'devtools.html',
  background: {
    service_worker: 'src/background/index.ts',
    type: 'module',
  },
  content_scripts: [
    {
      matches: ['http://*/*', 'https://*/*'],
      js: [
        'src/contentScript/content-script-main.ts',
      ],
      run_at: 'document_start', // Run script early to intercept API calls
      all_frames: true // Ensure it runs in all frames if necessary
    },
  ],
  side_panel: {
    default_path: 'sidepanel.html',
  },
  web_accessible_resources: [
    {
      // Allow content script to load the injected script
      resources: [
        'img/logo-16.png',
        'img/logo-32.png',
        'img/logo-48.png',
        'img/logo-128.png',
        '_favicon/*'
      ],
      matches: ['<all_urls>'], // Or specify more restricted matches if needed
    },
  ],
  permissions: [
    'sidePanel',
    'storage',
    'nativeMessaging',
    'webNavigation',
    'scripting',
    'activeTab',
    'history',
    'favicon',
  ], // Added scripting permission for iframe position detection
  chrome_url_overrides: {
    newtab: 'newtab.html',
  },
})
