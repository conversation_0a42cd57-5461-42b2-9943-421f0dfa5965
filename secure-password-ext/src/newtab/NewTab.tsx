import React, { useEffect, useState } from 'react';
import { Card, Typography, Spin, Empty, Avatar, Tooltip } from 'antd';
import { useAsyncFn } from 'react-use';
import { getBrowserHistory, HistoryItem } from './historyService';
import { getFaviconUrl, getFaviconSize } from './faviconService';
import { getImageDominantColor, generateGradientBackground } from './colorService';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { firstValueFrom } from 'rxjs';

const { Title, Text } = Typography;

/**
 * 格式化访问时间
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
const formatVisitTime = (timestamp: number): string => {
  try {
    return formatDistanceToNow(timestamp, { addSuffix: true, locale: zhCN });
  } catch (error) {
    console.error('格式化时间出错:', error);
    return '未知时间';
  }
};

interface HistoryCardProps {
  item: HistoryItem;
}

/**
 * 历史记录卡片组件
 * @param item 历史记录项
 */
const HistoryCard: React.FC<HistoryCardProps> = ({ item }) => {
  const [faviconUrl, setFaviconUrl] = useState<string>('');
  const [faviconSize, setFaviconSize] = useState<{ width: number; height: number }>({ width: 48, height: 48 });
  const [isLoading, setIsLoading] = useState(true);
  const [backgroundColor, setBackgroundColor] = useState<string>('#ffffff');
  const [isColorLoading, setIsColorLoading] = useState(true);

  useEffect(() => {
    const loadFavicon = async () => {
      try {
        setIsLoading(true);
        const [url, size] = await Promise.all([
          getFaviconUrl(item.url),
          getFaviconSize(item.url)
        ]);
        setFaviconUrl(url);
        setFaviconSize(size);

        // 获取图标主色调
        setIsColorLoading(true);
        const dominantColor = await getImageDominantColor(url);
        setBackgroundColor(dominantColor);
      } catch (error) {
        console.error('加载网站图标失败:', error);
      } finally {
        setIsLoading(false);
        setIsColorLoading(false);
      }
    };

    loadFavicon();
  }, [item.url]);

  // 计算文字颜色，确保在背景色上清晰可见
  const getTextColor = (bgColor: string): string => {
    // 将背景色转换为 RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // 计算亮度 (YIQ 公式)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;

    // 根据亮度返回黑色或白色
    return yiq >= 128 ? '#000000' : '#ffffff';
  };

  const textColor = getTextColor(backgroundColor);
  const gradientBackground = generateGradientBackground(backgroundColor);

  return (
    <Card
      hoverable
      className="h-full flex flex-col transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-lg"
      onClick={() => window.open(item.url, '_blank')}
      style={{
        background: isColorLoading ? '#ffffff' : gradientBackground,
        color: textColor,
      }}
      bodyStyle={{
        padding: '16px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div className="flex items-start mb-3">
        <Tooltip title={item.title || item.url}>
          <div className="mr-3 flex-shrink-0">
            {isLoading ? (
              <Spin size="small" />
            ) : (
              <Avatar
                src={faviconUrl}
                alt={`${item.title || item.url} favicon`}
                shape="square"
                size={Math.min(faviconSize.width, 48)}
                style={{
                  width: Math.min(faviconSize.width, 48),
                  height: Math.min(faviconSize.height, 48),
                  objectFit: 'contain',
                  backgroundColor: 'transparent',
                  backdropFilter: 'blur(4px)',
                }}
              />
            )}
          </div>
        </Tooltip>
        <div className="flex-grow min-w-0">
          <Title
            level={5}
            ellipsis={{ rows: 2 }}
            className="mb-1 min-h-[24px]"
            style={{ color: textColor }}
            title={item.title || item.url}
          >
            {item.title || item.url}
          </Title>
          <Text
            className="block break-all text-xs"
            style={{
              color: textColor,
              opacity: 0.8,
            }}
            ellipsis
            title={item.url}
          >
            {item.url}
          </Text>
        </div>
      </div>
      <div className="mt-auto flex justify-between text-xs">
        <Text style={{ color: textColor, opacity: 0.8 }}>
          访问次数: {item.visitCount}
        </Text>
        <Text style={{ color: textColor, opacity: 0.8 }}>
          最近访问: {formatVisitTime(item.lastVisitTime)}
        </Text>
      </div>
    </Card>
  );
};

/**
 * 获取历史记录
 * @returns Promise<HistoryItem[]>
 */
const fetchHistory = async (): Promise<HistoryItem[]> => {
  const history$ = getBrowserHistory();
  return firstValueFrom(history$);
};

/**
 * NewTab 页面组件
 */
export const NewTab: React.FC = () => {
  const [{ loading, error, value: historyItems = [] }, fetch] = useAsyncFn(fetchHistory, []);

  React.useEffect(() => {
    fetch();
  }, [fetch]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" tip="加载历史记录中..." fullscreen />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Empty
          description="加载历史记录失败"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen bg-gray-50">
      <Title
        level={2}
        className="mb-6 text-blue-500"
      >
        最近访问
      </Title>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {historyItems.map((item) => (
          <HistoryCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

export default NewTab;
