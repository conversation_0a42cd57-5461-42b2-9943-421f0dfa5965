/**
 * 颜色服务
 * 提供颜色相关的工具函数，包括获取图片主色调、生成渐变背景等
 */

/**
 * 将 RGB 颜色转换为 HSL
 * @param r 红色通道 (0-255)
 * @param g 绿色通道 (0-255)
 * @param b 蓝色通道 (0-255)
 * @returns HSL 颜色值 { h: number, s: number, l: number }
 */
const rgbToHsl = (r: number, g: number, b: number): { h: number; s: number; l: number } => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
};

/**
 * 将 HSL 颜色转换为 RGB
 * @param h 色相 (0-360)
 * @param s 饱和度 (0-100)
 * @param l 亮度 (0-100)
 * @returns RGB 颜色值 { r: number, g: number, b: number }
 */
const hslToRgb = (h: number, s: number, l: number): { r: number; g: number; b: number } => {
  h /= 360;
  s /= 100;
  l /= 100;

  let r: number;
  let g: number;
  let b: number;

  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
};

/**
 * 获取图片的主色调
 * @param imageUrl 图片URL
 * @returns Promise<string> 返回主色调的十六进制颜色值
 */
export const getImageDominantColor = async (imageUrl: string): Promise<string> => {
  try {
    // 创建图片元素
    const img = new Image();
    img.crossOrigin = 'anonymous';

    // 等待图片加载
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageUrl;
    });

    // 创建 canvas 并绘制图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('无法创建 canvas context');
    }

    // 设置 canvas 尺寸为图片尺寸的 1/4，以提高性能
    canvas.width = img.width / 4;
    canvas.height = img.height / 4;
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    // 获取图片数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // 计算颜色直方图
    const colorCounts: { [key: string]: number } = {};
    for (let i = 0; i < data.length; i += 4) {
      // 跳过完全透明的像素
      if (data[i + 3] < 128) continue;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // 将颜色量化为 32 级，减少颜色数量
      const quantizedR = Math.round(r / 8) * 8;
      const quantizedG = Math.round(g / 8) * 8;
      const quantizedB = Math.round(b / 8) * 8;

      const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;
      colorCounts[colorKey] = (colorCounts[colorKey] || 0) + 1;
    }

    // 找出出现次数最多的颜色
    let maxCount = 0;
    let dominantColor = '255,255,255'; // 默认白色

    for (const [color, count] of Object.entries(colorCounts)) {
      if (count > maxCount) {
        maxCount = count;
        dominantColor = color;
      }
    }

    // 将 RGB 转换为 HSL 以调整亮度和饱和度
    const [r, g, b] = dominantColor.split(',').map(Number);
    const hsl = rgbToHsl(r, g, b);

    // 调整亮度和饱和度，使其更适合作为背景色
    hsl.l = Math.min(hsl.l + 20, 95); // 增加亮度
    hsl.s = Math.min(hsl.s + 10, 30); // 降低饱和度

    // 转回 RGB
    const adjustedRgb = hslToRgb(hsl.h, hsl.s, hsl.l);

    // 转换为十六进制
    return `#${adjustedRgb.r.toString(16).padStart(2, '0')}${adjustedRgb.g.toString(16).padStart(2, '0')}${adjustedRgb.b.toString(16).padStart(2, '0')}`;
  } catch (error) {
    console.error('获取图片主色调失败:', error);
    return '#ffffff'; // 返回白色作为默认值
  }
};

/**
 * 生成渐变背景样式
 * @param baseColor 基础颜色（十六进制）
 * @returns CSS 渐变背景样式
 */
export const generateGradientBackground = (baseColor: string): string => {
  // 解析基础颜色
  const r = parseInt(baseColor.slice(1, 3), 16);
  const g = parseInt(baseColor.slice(3, 5), 16);
  const b = parseInt(baseColor.slice(5, 7), 16);

  // 生成稍微深一点的颜色作为渐变终点
  const hsl = rgbToHsl(r, g, b);
  hsl.l = Math.max(hsl.l - 5, 0); // 降低亮度
  const darkerRgb = hslToRgb(hsl.h, hsl.s, hsl.l);
  const darkerColor = `#${darkerRgb.r.toString(16).padStart(2, '0')}${darkerRgb.g.toString(16).padStart(2, '0')}${darkerRgb.b.toString(16).padStart(2, '0')}`;

  // 生成渐变背景
  return `linear-gradient(135deg, ${baseColor} 0%, ${darkerColor} 100%)`;
};
