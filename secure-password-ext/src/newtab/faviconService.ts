/**
 * 获取网站图标的服务
 * 提供获取网站图标的功能，支持多种尺寸和格式
 */

/**
 * 构建网站图标的 URL
 * @param url 网站 URL
 * @param size 图标尺寸
 * @returns string 返回图标的 URL
 */
const buildFaviconUrl = (url: string, size: number = 32): string => {
  try {
    const faviconUrl = new URL(chrome.runtime.getURL('/_favicon/'));
    faviconUrl.searchParams.set('pageUrl', url);
    faviconUrl.searchParams.set('size', size.toString());
    return faviconUrl.toString();
  } catch (error) {
    console.error('构建网站图标 URL 失败:', error);
    return chrome.runtime.getURL('img/logo-48.png');
  }
};

/**
 * 获取网站图标的URL
 * @param url 网站URL
 * @returns Promise<string> 返回图标的URL
 */
export const getFaviconUrl = async (url: string): Promise<string> => {
  try {
    // 尝试获取最大尺寸的图标
    const sizes = [192, 128, 64, 32, 16];

    for (const size of sizes) {
      const faviconUrl = buildFaviconUrl(url, size);
      try {
        const response = await fetch(faviconUrl, { method: 'HEAD' });
        if (response.ok) {
          return faviconUrl;
        }
      } catch (error) {
        console.debug(`无法获取 ${size}x${size} 图标:`, error);
      }
    }

    // 如果都失败了，返回默认图标
    return chrome.runtime.getURL('img/logo-48.png');
  } catch (error) {
    console.error('获取网站图标失败:', error);
    return chrome.runtime.getURL('img/logo-48.png');
  }
};

/**
 * 获取网站图标的最佳尺寸
 * @param url 网站URL
 * @returns Promise<{ width: number; height: number }> 返回图标的最佳尺寸
 */
export const getFaviconSize = async (url: string): Promise<{ width: number; height: number }> => {
  try {
    const faviconUrl = await getFaviconUrl(url);

    // 如果是扩展的默认图标，返回固定尺寸
    if (faviconUrl.includes('img/logo-')) {
      return { width: 48, height: 48 };
    }

    // 从 URL 中提取尺寸参数
    const urlObj = new URL(faviconUrl);
    const size = parseInt(urlObj.searchParams.get('size') || '32', 10);

    // 返回图标的尺寸，但限制最大尺寸为 192x192
    return {
      width: Math.min(size, 192),
      height: Math.min(size, 192)
    };
  } catch (error) {
    console.error('获取图标尺寸失败:', error);
    return { width: 48, height: 48 };
  }
};
