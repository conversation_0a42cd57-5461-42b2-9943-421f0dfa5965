import { from, Observable } from 'rxjs';
import { map, mergeMap, toArray } from 'rxjs/operators';

/**
 * 浏览器历史记录项接口
 */
export interface HistoryItem {
  id: string;
  url: string;
  title: string;
  lastVisitTime: number;
  visitCount: number;
}

/**
 * 获取浏览器历史记录
 * @param days 获取最近几天的历史记录
 * @param maxResults 最大返回结果数
 * @returns Observable<HistoryItem[]>
 */
export const getBrowserHistory = (days: number = 7, maxResults: number = 10): Observable<HistoryItem[]> => {
  const startTime = Date.now() - days * 24 * 60 * 60 * 1000;

  return from(
    chrome.history.search({
      text: '',
      startTime,
      maxResults: 1000, // 获取更多结果以便后续排序
    })
  ).pipe(
    mergeMap((items) => from(items)),
    map((item) => ({
      id: item.id,
      url: item.url || '',
      title: item.title || '',
      lastVisitTime: item.lastVisitTime || 0,
      visitCount: item.visitCount || 0,
    })),
    toArray(),
    map((items) => {
      // 计算综合得分：访问时间权重 0.6，访问次数权重 0.4
      const now = Date.now();
      const maxVisitCount = Math.max(...items.map((item) => item.visitCount));

      return items
        .map((item) => ({
          ...item,
          score:
            (0.6 * (item.lastVisitTime / now)) +
            (0.4 * (item.visitCount / maxVisitCount)),
        }))
        .sort((a, b) => b.score - a.score)
        .slice(0, maxResults);
    })
  );
};
