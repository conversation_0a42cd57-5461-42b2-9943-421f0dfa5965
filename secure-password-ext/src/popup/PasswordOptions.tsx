import React from 'react';
import { Form, InputNumber, Checkbox, Row, Col, Slider } from 'antd';
import type { PasswordOptions } from './hooks/usePasswordGenerator';

interface Props {
  options: PasswordOptions;
  onChange: (opts: Partial<PasswordOptions>) => void;
}

const PasswordOptions: React.FC<Props> = ({ options, onChange }) => {
  return (
    <Form
      layout="vertical"
      className="w-full space-y-2"
      initialValues={options}
    >
      <Form.Item label={<span className="font-semibold">长度</span>} help="值必须在 8 和 100 之间。">
        <Slider
          min={8}
          max={100}
          value={options.length}
          onChange={v => onChange({ length: v })}
        />
        <InputNumber
          min={8}
          max={100}
          value={options.length}
          onChange={v => onChange({ length: Number(v) })}
          className="w-full"
        />
      </Form.Item>
      <Form.Item label={<span className="font-semibold">包含</span>} className="mb-1">
        <Row gutter={[8, 8]}>
          <Col>
            <Checkbox checked={options.useUpper} onChange={e => onChange({ useUpper: e.target.checked })}>A-Z</Checkbox>
          </Col>
          <Col>
            <Checkbox checked={options.useLower} onChange={e => onChange({ useLower: e.target.checked })}>a-z</Checkbox>
          </Col>
          <Col>
            <Checkbox checked={options.useNumber} onChange={e => onChange({ useNumber: e.target.checked })}>0-9</Checkbox>
          </Col>
          <Col>
            <Checkbox checked={options.useSymbol} onChange={e => onChange({ useSymbol: e.target.checked })}>!@#$%^&*</Checkbox>
          </Col>
        </Row>
      </Form.Item>
      <Row gutter={8}>
        <Col span={12}>
          <Form.Item label="数字最少个数" className="mb-1">
            <Slider
              min={0}
              max={options.length}
              value={options.minNumbers}
              onChange={v => onChange({ minNumbers: Number(v) })}
              className="w-full"
            />
            <InputNumber
              min={0}
              max={options.length}
              value={options.minNumbers}
              onChange={v => onChange({ minNumbers: Number(v) })}
              className="w-full"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="符号最少个数" className="mb-1">
            <Slider
              min={0}
              max={options.length}
              value={options.minSymbols}
              onChange={v => onChange({ minSymbols: Number(v) })}
              className="w-full"
            />
            <InputNumber
              min={0}
              max={options.length}
              value={options.minSymbols}
              onChange={v => onChange({ minSymbols: Number(v) })}
              className="w-full"
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item className="mb-0">
        <Checkbox checked={options.avoidAmbiguous} onChange={e => onChange({ avoidAmbiguous: e.target.checked })}>
          避免易混淆的字符
        </Checkbox>
      </Form.Item>
    </Form>
  );
};

export default PasswordOptions;
