import React, { useState } from 'react';
import { <PERSON>, Button, Tooltip, message, Space } from 'antd';
import { CopyOutlined, ReloadOutlined, CheckOutlined } from '@ant-design/icons';

interface Props {
  value: string;
  onRefresh: () => void;
}

const isSymbol = (c: string) => /[!@#$%^&*]/.test(c);
const isNumber = (c: string) => /[0-9]/.test(c);

const PasswordResult: React.FC<Props> = ({ value, onRefresh }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value);
      setCopied(true);
      message.success('已复制到剪贴板');
      setTimeout(() => setCopied(false), 1200);
    } catch {
      message.error('复制失败');
    }
  };

  return (
    <Card
      className="mb-2"
      styles={{
        body: {
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          minHeight: 48,
          background: '#f9fafb',
          borderRadius: 8,
        }
      }}
    >
      <div
        className="flex-1 flex flex-wrap items-center break-all select-all"
        style={{
          fontFamily: 'JetBrains Mono, Fira Mono, Menlo, monospace',
          fontWeight: 400,
          fontSize: 20,
          letterSpacing: '0.05em',
          lineHeight: 1.6,
          wordBreak: 'break-all',
        }}
        title={value}
      >
        {value.split('').map((c, i) => (
          <span
            key={i}
            style={{
              color: isSymbol(c)
                ? '#e53e3e'
                : isNumber(c)
                ? '#2563eb'
                : '#222',
              marginRight: 0,
              fontWeight: 400,
            }}
          >
            {c}
          </span>
        ))}
      </div>
      <Space className="ml-2">
        <Tooltip title="刷新">
          <Button shape="circle" icon={<ReloadOutlined />} onClick={onRefresh} />
        </Tooltip>
        <Tooltip title={copied ? '已复制' : '复制'}>
          <Button
            shape="circle"
            type={copied ? 'primary' : 'default'}
            icon={copied ? <CheckOutlined /> : <CopyOutlined />}
            onClick={handleCopy}
          />
        </Tooltip>
      </Space>
    </Card>
  );
};

export default PasswordResult;
