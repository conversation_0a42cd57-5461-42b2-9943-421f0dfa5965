import React, { useState } from 'react';
import { Tabs, Typography } from 'antd';
import PasswordOptions from './PasswordOptions';
import PasswordResult from './PasswordResult';
import { usePasswordGenerator, PasswordOptions as PwdOptions } from './hooks/usePasswordGenerator';

const { Title } = Typography;

const defaultOptions: PwdOptions = {
  length: 20,
  useUpper: true,
  useLower: true,
  useNumber: true,
  useSymbol: false,
  minNumbers: 1,
  minSymbols: 0,
  avoidAmbiguous: false,
};

export const Popup: React.FC = () => {
  const [tab, setTab] = useState('password');
  const { password, options, setOptions, regenerate } = usePasswordGenerator(defaultOptions);

  // 选项变更后自动刷新密码，并处理符号/数字与最小个数的联动
  const handleOptionsChange = (opts: Partial<PwdOptions>) => {
    let next = { ...options, ...opts };
    let changed = false;

    // useSymbol 与 minSymbols 联动
    if (Object.prototype.hasOwnProperty.call(opts, 'useSymbol')) {
      if (opts.useSymbol) {
        if (next.minSymbols === 0) {
          next.minSymbols = 1;
          changed = true;
        }
      } else {
        if (next.minSymbols !== 0) {
          next.minSymbols = 0;
          changed = true;
        }
      }
    }
    if (Object.prototype.hasOwnProperty.call(opts, 'minSymbols')) {
      if (typeof opts.minSymbols === 'number') {
        if (opts.minSymbols === 0) {
          if (next.useSymbol) {
            next.useSymbol = false;
            changed = true;
          }
        } else if (opts.minSymbols > 0) {
          if (!next.useSymbol) {
            next.useSymbol = true;
            changed = true;
          }
        }
      }
    }

    // useNumber 与 minNumbers 联动
    if (Object.prototype.hasOwnProperty.call(opts, 'useNumber')) {
      if (opts.useNumber) {
        if (next.minNumbers === 0) {
          next.minNumbers = 1;
          changed = true;
        }
      } else {
        if (next.minNumbers !== 0) {
          next.minNumbers = 0;
          changed = true;
        }
      }
    }
    if (Object.prototype.hasOwnProperty.call(opts, 'minNumbers')) {
      if (typeof opts.minNumbers === 'number') {
        if (opts.minNumbers === 0) {
          if (next.useNumber) {
            next.useNumber = false;
            changed = true;
          }
        } else if (opts.minNumbers > 0) {
          if (!next.useNumber) {
            next.useNumber = true;
            changed = true;
          }
        }
      }
    }

    setOptions(next);
    // 只要有关键选项变更就自动刷新
    if (
      changed ||
      Object.keys(opts).some(key => [
        'useUpper', 'useLower', 'useNumber', 'useSymbol', 'avoidAmbiguous',
        'length', 'minNumbers', 'minSymbols'
      ].includes(key))
    ) {
      regenerate();
    }
  };

  return (
    <div className="w-[400px] min-h-[480px] bg-white shadow-xl p-4 flex flex-col border border-gray-100">
      <div className="px-6 pt-5 pb-2 border-b border-gray-100">
        <Title level={4} className="mb-0 text-blue-600">生成器</Title>
      </div>
      <Tabs
        activeKey={tab}
        onChange={setTab}
        className="px-6 pt-2"
        tabBarGutter={32}
        tabBarStyle={{ marginBottom: 0 }}
        items={[
          { label: '密码', key: 'password' },
          { label: '密码短语', key: 'phrase' },
          { label: '用户名', key: 'username' }
        ]}
      />
      <div className="flex-1 px-2 pb-2 pt-6">
        {tab === 'password' && (
          <>
            <PasswordResult value={password} onRefresh={regenerate} />
            <div className="mt-6">
              <PasswordOptions options={options} onChange={handleOptionsChange} />
            </div>
          </>
        )}
        {tab !== 'password' && (
          <div className="text-center text-gray-400 py-16">敬请期待</div>
        )}
      </div>
    </div>
  );
};

export default Popup;
