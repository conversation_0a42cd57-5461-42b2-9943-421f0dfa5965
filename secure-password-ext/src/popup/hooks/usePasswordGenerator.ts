import { useCallback, useState } from 'react';

// 密码生成参数类型
type PasswordOptions = {
  length: number;
  useUpper: boolean;
  useLower: boolean;
  useNumber: boolean;
  useSymbol: boolean;
  minNumbers: number;
  minSymbols: number;
  avoidAmbiguous: boolean;
};

const UPPER = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const LOWER = 'abcdefghijklmnopqrstuvwxyz';
const NUMBER = '0123456789';
const SYMBOL = '!@#$%^&*';
const AMBIGUOUS = 'O0oIl1';

/**
 * 生成随机密码
 * @param options 密码选项
 */
function generatePassword(options: PasswordOptions): string {
  let chars = '';
  if (options.useUpper) chars += UPPER;
  if (options.useLower) chars += LOWER;
  if (options.useNumber) chars += NUMBER;
  if (options.useSymbol) chars += SYMBOL;
  if (options.avoidAmbiguous) chars = chars.split('').filter(c => !AMBIGUOUS.includes(c)).join('');
  if (!chars) return '';

  // 保证最小数字/符号数
  let pwdArr: string[] = [];
  if (options.useNumber && options.minNumbers > 0) {
    for (let i = 0; i < options.minNumbers; i++) {
      pwdArr.push(NUMBER[Math.floor(Math.random() * NUMBER.length)]);
    }
  }
  if (options.useSymbol && options.minSymbols > 0) {
    for (let i = 0; i < options.minSymbols; i++) {
      pwdArr.push(SYMBOL[Math.floor(Math.random() * SYMBOL.length)]);
    }
  }
  // 剩余部分
  while (pwdArr.length < options.length) {
    pwdArr.push(chars[Math.floor(Math.random() * chars.length)]);
  }
  // 打乱顺序
  for (let i = pwdArr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [pwdArr[i], pwdArr[j]] = [pwdArr[j], pwdArr[i]];
  }
  return pwdArr.join('').slice(0, options.length);
}

/**
 * 密码生成器 hook
 */
export function usePasswordGenerator(defaultOptions: PasswordOptions) {
  const [options, setOptions] = useState<PasswordOptions>(defaultOptions);
  const [password, setPassword] = useState<string>('');

  const regenerate = useCallback(() => {
    setPassword(generatePassword(options));
  }, [options]);

  // 选项变更时自动生成
  const updateOptions = useCallback((opts: Partial<PasswordOptions>) => {
    setOptions(prev => ({ ...prev, ...opts }));
  }, []);

  // 自动生成一次
  useState(() => {
    setPassword(generatePassword(options));
  });

  return { password, options, setOptions: updateOptions, regenerate };
}

export type { PasswordOptions };
