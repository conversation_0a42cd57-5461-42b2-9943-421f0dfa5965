import { RuntimeMessage, RuntimeMessageResponse } from './runtime-message.service';
import { CredentialOutput } from '../types';
import { MessageRouterService } from './message-router.service';

/**
 * 消息类型枚举
 * 定义后台脚本和内容脚本之间通信的消息类型
 */
export enum BackgroundMessageType {
  // 表单相关消息
  GET_PASSWORDS_FOR_DOMAIN = 'GET_PASSWORDS_FOR_DOMAIN',
  SAVE_CREDENTIAL = 'SAVE_CREDENTIAL',
  UPDATE_CREDENTIAL = 'UPDATE_CREDENTIAL',
  DELETE_CREDENTIAL = 'DELETE_CREDENTIAL',

  // iframe 焦点事件相关消息
  IFRAME_FOCUS_EVENT = 'IFRAME_FOCUS_EVENT',
  FORWARDED_IFRAME_FOCUS_EVENT = 'FORWARDED_IFRAME_FOCUS_EVENT',
  IFRAME_CREDENTIAL_FILL = 'IFRAME_CREDENTIAL_FILL',
  FORWARDED_IFRAME_CREDENTIAL_FILL = 'FORWARDED_IFRAME_CREDENTIAL_FILL',

  // iframe 位置计算相关消息
  CALCULATE_IFRAME_ELEMENT_POSITION = 'CALCULATE_IFRAME_ELEMENT_POSITION',
  REQUEST_PARENT_FRAME_CALCULATION = 'REQUEST_PARENT_FRAME_CALCULATION',
  CALCULATE_ELEMENT_POSITION_REQUEST = 'CALCULATE_ELEMENT_POSITION_REQUEST',
  CALCULATE_ELEMENT_POSITION_RESPONSE = 'CALCULATE_ELEMENT_POSITION_RESPONSE',

  // iframe 树结构相关消息
  GET_IFRAME_TREE = 'GET_IFRAME_TREE',
  IFRAME_TREE_RESPONSE = 'IFRAME_TREE_RESPONSE',
  GET_CURRENT_FRAME_ID = 'GET_CURRENT_FRAME_ID',

  // 响应消息
  PASSWORD_LIST_RESPONSE = 'PASSWORD_LIST_RESPONSE',
  OPERATION_RESPONSE = 'OPERATION_RESPONSE',

  // 错误和状态消息
  ERROR = 'ERROR',
  STATUS = 'STATUS'
}

/**
 * 密码查询请求接口
 */
export interface PasswordQueryRequest {
  domain: string;
  formId?: string;
  frameId?: string;
  tabId?: number;
}

/**
 * 操作响应接口
 */
export interface OperationResponse {
  operation: string;
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * iframe 焦点事件接口
 * 包含表单字段信息、位置信息和 iframe 信息
 */
export interface IframeFocusEvent {
  field: {
    id: string;
    name: string;
    type: string;
    isPassword: boolean;
    isUsername: boolean;
    form?: {
      id: string;
      action: string;
    };
  };
  position: {
    top: number;
    left: number;
    width: number;
    height: number;
    bottom: number;
    right: number;
    x: number;
    y: number;
  };
  domain: string;
  url: string;
  frameInfo?: {
    frameId: string;
    frameRect: {
      top: number;
      left: number;
      width: number;
      height: number;
      bottom: number;
      right: number;
      x: number;
      y: number;
    };
    tabId?: number;
    frameLevel?: number;
    topFrameUrl?: string;
    isCrossDomain?: boolean; // 标记是否是跨域 iframe
    originalMessageId?: string; // 原始消息ID，用于跟踪消息流程
    originalFrameId?: string | number; // 原始消息FrameId，用于跟踪消息流程
  };
}

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxRetries: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
}

/**
 * 后台消息服务
 * 处理与内容脚本的通信，包括表单数据请求、密码查询和返回逻辑
 */
export class BackgroundMessageService {
  private static instance: BackgroundMessageService;
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelayMs: 500,
    maxDelayMs: 5000,
    backoffFactor: 2
  };

  // 使用单例模式
  private constructor() {
    this.initialize();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): BackgroundMessageService {
    if (!BackgroundMessageService.instance) {
      BackgroundMessageService.instance = new BackgroundMessageService();
    }
    return BackgroundMessageService.instance;
  }

  /**
   * 初始化服务
   * 注册消息处理器到消息路由服务
   */
  private initialize(): void {
    console.log('BackgroundMessageService initializing...');

    // 获取消息路由服务实例
    const messageRouter = MessageRouterService.getInstance();

    // 注册消息处理器
    messageRouter.registerHandler({
      // 定义此服务能处理的消息类型
      messageTypes: [
        BackgroundMessageType.GET_PASSWORDS_FOR_DOMAIN,
        BackgroundMessageType.SAVE_CREDENTIAL,
        BackgroundMessageType.UPDATE_CREDENTIAL,
        BackgroundMessageType.DELETE_CREDENTIAL,
        BackgroundMessageType.IFRAME_FOCUS_EVENT,
        BackgroundMessageType.IFRAME_CREDENTIAL_FILL
      ],

      // 处理消息的方法
      handleMessage: async (message, sender, _sendResponse, signal) => {
        console.log('BackgroundMessageService handling message:', message.type);

        try {
          // 根据消息类型处理不同的请求
          switch (message.type) {
            case BackgroundMessageType.GET_PASSWORDS_FOR_DOMAIN:
              return await this.handleGetPasswordsForDomain(
                message as RuntimeMessage<PasswordQueryRequest>,
                sender,
                signal
              );

            case BackgroundMessageType.SAVE_CREDENTIAL:
              return await this.handleSaveCredential(message, sender, signal);

            case BackgroundMessageType.UPDATE_CREDENTIAL:
              return await this.handleUpdateCredential(message, sender, signal);

            case BackgroundMessageType.DELETE_CREDENTIAL:
              return await this.handleDeleteCredential(message, sender, signal);

            case BackgroundMessageType.IFRAME_FOCUS_EVENT:
              return await this.handleIframeFocusEvent(
                message as RuntimeMessage<IframeFocusEvent>,
                sender
              );

            case BackgroundMessageType.IFRAME_CREDENTIAL_FILL:
              return await this.handleIframeCredentialFill(
                message as RuntimeMessage<any>,
                sender
              );

            default:
              console.warn(`Unknown message type: ${message.type}`);
              return {
                success: false,
                error: `Unknown message type: ${message.type}`,
                messageId: message.messageId
              };
          }
        } catch (error) {
          console.error('Error handling message:', error);
          return {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            messageId: message.messageId
          };
        }
      }
    });

    console.log('BackgroundMessageService initialized');
  }

  /**
   * 处理获取域名密码列表的请求
   */
  private async handleGetPasswordsForDomain(
    message: RuntimeMessage<PasswordQueryRequest>,
    _sender: chrome.runtime.MessageSender, // 使用下划线前缀表示未使用的参数
    signal?: AbortSignal
  ): Promise<RuntimeMessageResponse<CredentialOutput[]>> {
    const { domain } = message.payload || {};

    if (!domain) {
      return {
        success: false,
        error: 'Domain is required',
        messageId: message.messageId
      };
    }

    try {
      // 从本地存储或通过原生消息获取密码列表
      const credentials = await this.getPasswordsWithRetry(domain, this.defaultRetryConfig, signal);
      // 发送密码列表响应
      return {
        success: true,
        data: credentials,
        messageId: message.messageId
      };
    } catch (error) {
      console.error(`Error getting passwords for domain ${domain}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理保存凭证的请求
   */
  private async handleSaveCredential(
    message: RuntimeMessage<any>,
    _sender: chrome.runtime.MessageSender, // 使用下划线前缀表示未使用的参数
    _signal?: AbortSignal // 使用下划线前缀表示未使用的参数
  ): Promise<RuntimeMessageResponse<OperationResponse>> {
    // 实现保存凭证的逻辑
    // 这里是一个示例实现，实际应用中需要与本地存储或原生应用通信
    try {
      // 模拟保存操作
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: {
          operation: 'save',
          success: true,
          message: 'Credential saved successfully'
        },
        messageId: message.messageId
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理更新凭证的请求
   */
  private async handleUpdateCredential(
    message: RuntimeMessage<any>,
    _sender: chrome.runtime.MessageSender, // 使用下划线前缀表示未使用的参数
    _signal?: AbortSignal // 使用下划线前缀表示未使用的参数
  ): Promise<RuntimeMessageResponse<OperationResponse>> {
    // 实现更新凭证的逻辑
    // 这里是一个示例实现
    try {
      // 模拟更新操作
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: {
          operation: 'update',
          success: true,
          message: 'Credential updated successfully'
        },
        messageId: message.messageId
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理删除凭证的请求
   */
  private async handleDeleteCredential(
    message: RuntimeMessage<any>,
    _sender: chrome.runtime.MessageSender, // 使用下划线前缀表示未使用的参数
    _signal?: AbortSignal // 使用下划线前缀表示未使用的参数
  ): Promise<RuntimeMessageResponse<OperationResponse>> {
    // 实现删除凭证的逻辑
    // 这里是一个示例实现
    try {
      // 模拟删除操作
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: {
          operation: 'delete',
          success: true,
          message: 'Credential deleted successfully'
        },
        messageId: message.messageId
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理 iframe 焦点事件
   * 将焦点事件从嵌套 iframe 转发到顶层页面
   * 确保只在顶层页面渲染下拉列表
   * @param message 包含 iframe 焦点事件数据的消息
   * @param sender 消息发送者信息
   * @returns 处理结果
   */
  private async handleIframeFocusEvent(
    message: RuntimeMessage<IframeFocusEvent>,
    sender: chrome.runtime.MessageSender
  ): Promise<RuntimeMessageResponse<any>> {
    try {
      console.log('处理 iframe 焦点事件:', {
        messageId: message.messageId,
        field: message.payload?.field?.id || message.payload?.field?.name,
        isPassword: message.payload?.field?.isPassword,
        isUsername: message.payload?.field?.isUsername,
        domain: message.payload?.domain,
        frameLevel: message.payload?.frameInfo?.frameLevel,
        isCrossDomain: message.payload?.frameInfo?.isCrossDomain
      });

      // 获取发送者的标签页 ID
      const tabId = sender.tab?.id;
      if (!tabId) {
        console.error('无法获取发送者的标签页 ID');
        return {
          success: false,
          error: '无法获取发送者的标签页 ID',
          messageId: message.messageId
        };
      }

      // 确保消息有效，并添加标签页 ID
      if (!message.payload || !message.payload.field || !message.payload.position) {
        console.error('无效的 iframe 焦点事件数据');
        return {
          success: false,
          error: '无效的 iframe 焦点事件数据',
          messageId: message.messageId
        };
      }

      // 检查是否是跨域 iframe
      const isCrossDomain = message.payload.frameInfo?.isCrossDomain || false;
      const frameLevel = message.payload.frameInfo?.frameLevel || 1;
      console.log(`iframe 是否跨域: ${isCrossDomain}, 嵌套层级: ${frameLevel}`);

      // 创建完整的事件对象，确保所有必需字段都有值
      const eventWithTabId: IframeFocusEvent = {
        field: {
          id: message.payload.field.id || '',
          name: message.payload.field.name || '',
          type: message.payload.field.type || 'password',
          isPassword: message.payload.field.isPassword || false,
          isUsername: message.payload.field.isUsername || false,
          form: message.payload.field.form
        },
        position: {
          top: message.payload.position.top || 0,
          left: message.payload.position.left || 0,
          width: message.payload.position.width || 0,
          height: message.payload.position.height || 0,
          bottom: message.payload.position.bottom || 0,
          right: message.payload.position.right || 0,
          x: message.payload.position.x || 0,
          y: message.payload.position.y || 0
        },
        domain: message.payload.domain || '',
        url: message.payload.url || '',
        frameInfo: message.payload.frameInfo ? {
          frameId: message.payload.frameInfo.frameId || `frame_${Date.now()}`,
          frameRect: message.payload.frameInfo.frameRect || {
            top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0
          },
          tabId: tabId,
          frameLevel: frameLevel,
          topFrameUrl: message.payload.frameInfo.topFrameUrl || message.payload.url,
          isCrossDomain: isCrossDomain,
          originalMessageId: message.messageId, // 添加原始消息ID，用于后续跟踪
          originalFrameId: sender.frameId
        } : {
          frameId: `frame_${Date.now()}`,
          frameRect: {
            top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0
          },
          tabId: tabId,
          frameLevel: 1,
          isCrossDomain: false,
          originalMessageId: message.messageId // 添加原始消息ID，用于后续跟踪
        }
      };

      // 将消息转发给顶层页面（frameId=0）
      try {
        console.log('准备转发焦点事件到顶层页面，tabId:', tabId);

        // 使用 chrome.tabs.sendMessage 将消息发送到顶层页面
        await new Promise<void>((resolve, reject) => {
          chrome.tabs.sendMessage(
            tabId,
            {
              type: BackgroundMessageType.FORWARDED_IFRAME_FOCUS_EVENT,
              data: eventWithTabId,
              // 添加额外的元数据，用于顶层页面处理
              meta: {
                isCrossDomain: isCrossDomain,
                frameLevel: frameLevel,
                timestamp: Date.now(),
                originalMessageId: message.messageId, // 添加原始消息ID，用于跟踪
                originalFrameId: sender.frameId
              }
            },
            { frameId: 0 }, // 明确指定发送到顶层页面（frameId=0）
            (response) => {
              if (chrome.runtime.lastError) {
                console.error('转发焦点事件到顶层页面时出错:', chrome.runtime.lastError);
                reject(chrome.runtime.lastError);
              } else {
                console.log('焦点事件已转发到顶层页面，响应:', response);
                resolve();
              }
            }
          );
        });

        return {
          success: true,
          data: {
            message: '焦点事件已成功转发到顶层页面',
            isCrossDomain: isCrossDomain,
            frameLevel: frameLevel,
            tabId: tabId
          },
          messageId: message.messageId
        };
      } catch (error) {
        console.error('转发焦点事件到顶层页面时出错:', error);

        // 尝试不指定frameId再次发送
        try {
          console.log('尝试不指定frameId再次发送消息...');
          await new Promise<void>((resolve, reject) => {
            chrome.tabs.sendMessage(
              tabId,
              {
                type: BackgroundMessageType.FORWARDED_IFRAME_FOCUS_EVENT,
                data: eventWithTabId,
                meta: {
                  isCrossDomain: isCrossDomain,
                  frameLevel: frameLevel,
                  timestamp: Date.now(),
                  originalMessageId: message.messageId,
                  originalFrameId: sender.frameId,
                  isRetry: true
                }
              },
              (response) => {
                if (chrome.runtime.lastError) {
                  console.error('重试转发焦点事件时出错:', chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                } else {
                  console.log('重试成功，焦点事件已转发，响应:', response);
                  resolve();
                }
              }
            );
          });

          return {
            success: true,
            data: {
              message: '焦点事件已通过重试成功转发到顶层页面',
              isCrossDomain: isCrossDomain,
              frameLevel: frameLevel,
              tabId: tabId,
              isRetry: true
            },
            messageId: message.messageId
          };
        } catch (retryError) {
          console.error('重试转发焦点事件也失败:', retryError);
          return {
            success: false,
            error: `转发失败，原始错误: ${error instanceof Error ? error.message : String(error)}，重试错误: ${retryError instanceof Error ? retryError.message : String(retryError)}`,
            messageId: message.messageId
          };
        }
      }
    } catch (error) {
      console.error('处理 iframe 焦点事件时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理 iframe 凭证填充请求
   * 将凭证填充请求从顶层页面转发到特定的 iframe
   * 根据原始 messageId 识别最初发送焦点事件的 iframe
   * @param message 包含凭证数据的消息
   * @param sender 消息发送者信息
   * @returns 处理结果
   */
  private async handleIframeCredentialFill(
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender
  ): Promise<RuntimeMessageResponse<any>> {
    try {
      console.log('处理 iframe 凭证填充请求:', {
        messageId: message.messageId,
        credential: message.payload?.credential?.username,
        frameInfo: message.payload?.frameInfo ? {
          frameId: message.payload.frameInfo.frameId,
          originalFrameId: message.payload.frameInfo.originalFrameId,
          isCrossDomain: message.payload.frameInfo.isCrossDomain,
          frameLevel: message.payload.frameInfo.frameLevel
        } : 'none'
      });

      // 获取发送者的标签页 ID
      const tabId = sender.tab?.id;
      if (!tabId) {
        console.error('无法获取发送者的标签页 ID');
        return {
          success: false,
          error: '无法获取发送者的标签页 ID',
          messageId: message.messageId
        };
      }

      // 获取目标 iframe 信息
      const frameInfo = message.payload.frameInfo;
      if (!frameInfo) {
        console.error('缺少 frameInfo，无法确定目标 iframe');
        return {
          success: false,
          error: '缺少 frameInfo，无法确定目标 iframe',
          messageId: message.messageId
        };
      }

      // 检查是否是跨域 iframe
      const isCrossDomain = frameInfo.isCrossDomain || false;
      const frameId = frameInfo.frameId || '';
      const originalFrameId = frameInfo.originalFrameId || -1;
      const frameLevel = frameInfo.frameLevel || 1;

      console.log(`iframe 凭证填充信息: 跨域=${isCrossDomain}, frameId=${frameId}, originalFrameId=${originalFrameId}, 层级=${frameLevel}`);

      // 添加标签页 ID 到消息中
      const credentialWithTabId = {
        ...message.payload,
        frameInfo: {
          ...frameInfo,
          tabId: tabId,
          isCrossDomain: isCrossDomain
        }
      };

      // 确定目标 frameId（如果有）
      let targetFrameId: number | undefined = undefined;

      // 如果 frameId 是数字字符串，转换为数字
      if (frameId && /^\d+$/.test(frameId)) {
        targetFrameId = parseInt(frameId, 10);
        console.log(`将凭证填充请求发送到特定 frameId: ${targetFrameId}`);
      } else if (frameId && frameId.startsWith('virtual-field-')) {
        // 虚拟字段 ID，可能需要特殊处理
        console.log(`检测到虚拟字段 ID: ${frameId}，将广播到所有 frames`);
      }

      // 将消息转发给目标 iframe
      try {
        // 构建消息对象
        const fillMessage = {
          type: BackgroundMessageType.FORWARDED_IFRAME_CREDENTIAL_FILL,
          data: credentialWithTabId,
          // 添加额外的元数据，用于处理
          meta: {
            isCrossDomain: isCrossDomain,
            frameLevel: frameLevel,
            timestamp: Date.now(),
            originalMessageId: message.messageId,
            originalFrameId: originalFrameId
          }
        };

        console.log(`准备发送凭证填充消息到 tabId=${tabId}${targetFrameId !== undefined ? `, frameId=${targetFrameId}` : ''}`);

        // 使用 chrome.tabs.sendMessage 将消息发送到 iframe
        if (originalFrameId !== undefined) {
          // 发送到特定 frameId
          await new Promise<void>((resolve, reject) => {
            chrome.tabs.sendMessage(
              tabId,
              fillMessage,
              { frameId: originalFrameId }, // 指定目标 frameId
              (response) => {
                if (chrome.runtime.lastError) {
                  console.error(`转发凭证填充请求到 frameId=${targetFrameId} 时出错:`, chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                } else {
                  console.log(`凭证填充请求已成功转发到 frameId=${targetFrameId}，响应:`, response);
                  resolve();
                }
              }
            );
          });
        } else {
          // 广播到所有 frames
          await new Promise<void>((resolve, reject) => {
            chrome.tabs.sendMessage(
              tabId,
              fillMessage,
              (response) => {
                if (chrome.runtime.lastError) {
                  console.error('广播凭证填充请求时出错:', chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                } else {
                  console.log('凭证填充请求已广播，响应:', response);
                  resolve();
                }
              }
            );
          });
        }

        return {
          success: true,
          data: {
            message: '凭证填充请求已成功转发',
            isCrossDomain: isCrossDomain,
            frameId: targetFrameId,
            originalFrameId: originalFrameId,
            frameLevel: frameLevel
          },
          messageId: message.messageId
        };
      } catch (error) {
        console.error('转发凭证填充请求时出错:', error);

        // 尝试不指定 frameId 再次发送（作为备用方案）
        try {
          console.log('尝试广播凭证填充请求...');
          await new Promise<void>((resolve, reject) => {
            chrome.tabs.sendMessage(
              tabId,
              {
                type: BackgroundMessageType.FORWARDED_IFRAME_CREDENTIAL_FILL,
                data: credentialWithTabId,
                meta: {
                  isCrossDomain: isCrossDomain,
                  frameLevel: frameLevel,
                  timestamp: Date.now(),
                  originalMessageId: message.messageId,
                  isRetry: true
                }
              },
              (response) => {
                if (chrome.runtime.lastError) {
                  console.error('广播凭证填充请求时出错:', chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                } else {
                  console.log('凭证填充请求已广播，响应:', response);
                  resolve();
                }
              }
            );
          });

          return {
            success: true,
            data: {
              message: '凭证填充请求已通过广播成功转发',
              isCrossDomain: isCrossDomain,
              isRetry: true
            },
            messageId: message.messageId
          };
        } catch (retryError) {
          console.error('广播凭证填充请求也失败:', retryError);
          return {
            success: false,
            error: `转发失败，原始错误: ${error instanceof Error ? error.message : String(error)}，重试错误: ${retryError instanceof Error ? retryError.message : String(retryError)}`,
            messageId: message.messageId
          };
        }
      }
    } catch (error) {
      console.error('处理 iframe 凭证填充请求时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 使用重试机制获取密码
   * 实现指数退避重试逻辑
   */
  private async getPasswordsWithRetry(
    domain: string,
    retryConfig: RetryConfig,
    signal?: AbortSignal
  ): Promise<CredentialOutput[]> {
    let retryCount = 0;
    let delay = retryConfig.initialDelayMs;

    while (retryCount <= retryConfig.maxRetries) {
      try {
        // 检查是否已取消
        if (signal?.aborted) {
          throw new Error('Operation was aborted');
        }

        // 尝试获取密码
        // 这里是一个示例实现，实际应用中需要与本地存储或原生应用通信
        // 模拟从原生应用获取密码
        const credentials = await this.fetchPasswordsFromNative(domain);
        return credentials;
      } catch (error) {
        retryCount++;

        // 如果已达到最大重试次数，抛出错误
        if (retryCount > retryConfig.maxRetries) {
          throw error;
        }

        // 计算下一次重试的延迟时间（指数退避）
        delay = Math.min(delay * retryConfig.backoffFactor, retryConfig.maxDelayMs);

        console.warn(`Retry ${retryCount}/${retryConfig.maxRetries} for domain ${domain} after ${delay}ms`);

        // 等待延迟时间后重试
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // 这行代码不应该被执行，但为了类型安全添加
    throw new Error(`Failed to get passwords for domain ${domain} after ${retryConfig.maxRetries} retries`);
  }

  /**
   * 从原生应用获取密码
   * 使用现有的原生消息通信机制获取密码
   */
  private async fetchPasswordsFromNative(domain: string): Promise<CredentialOutput[]> {
    return new Promise((resolve, reject) => {
      // 获取后台脚本中的 port 对象
      // 注意：这里假设 port 是在 background/index.ts 中定义的全局变量
      // 在实际应用中，可能需要调整这个逻辑以适应项目结构

      // 直接访问全局变量，而不是通过 window 对象
      // @ts-ignore - 访问在 background/index.ts 中定义的全局变量
      const port = globalThis.port;

      if (!port) {
        console.warn("Native host not connected. Attempting to reconnect...");
        try {
          // 尝试重新连接
          // 这里假设 connectNative 是在 background/index.ts 中定义的全局函数
          // @ts-ignore - 访问在 background/index.ts 中定义的全局函数
          globalThis.connectNative();

          // @ts-ignore - 访问在 background/index.ts 中定义的全局变量
          if (!globalThis.port) {
            reject(new Error("Native host connection failed. Cannot process request."));
            return;
          }
        } catch (error) {
          reject(error);
          return;
        }
      }

      // 获取重新连接后的 port
      // @ts-ignore - 访问在 background/index.ts 中定义的全局变量
      const currentPort = globalThis.port;

      // 确保 port 不为 null
      if (!currentPort) {
        reject(new Error("Native host connection is null after reconnection attempt."));
        return;
      }

      // 生成请求 ID
      const requestId = `pwd_req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // 设置一次性消息监听器来接收响应
      const messageListener = (message: any) => {
        if (message.requestId === requestId && message.type === 'getPasswordsForDomainResponse') {
          // 移除监听器
          currentPort.onMessage.removeListener(messageListener);

          if (message.success) {
            resolve(message.data || []);
          } else {
            reject(new Error(message.error || 'Unknown error'));
          }
        }
      };

      // 添加消息监听器
      currentPort.onMessage.addListener(messageListener);

      // 发送请求
      try {
        currentPort.postMessage({
          type: 'getPasswordsForDomainRequest',
          requestId: requestId,
          options: {
            domain: domain
          },
        });
      } catch (error) {
        // 移除监听器
        currentPort.onMessage.removeListener(messageListener);
        reject(error);
      }

      // 设置超时
      setTimeout(() => {
        // 检查监听器是否仍然存在（如果已经收到响应，监听器应该已被移除）
        if (currentPort) {
          currentPort.onMessage.removeListener(messageListener);
        }
        reject(new Error(`Request timed out after 10000ms`));
      }, 10000);
    });
  }
}
