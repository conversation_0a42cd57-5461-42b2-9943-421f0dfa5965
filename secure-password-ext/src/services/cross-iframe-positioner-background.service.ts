import { RuntimeMessage, RuntimeMessageResponse } from './runtime-message.service';
import { BackgroundMessageType } from './background-message.service';
import { ElementPosition, IframeInfo, PositionCalculationRequest, PositionCalculationResponse } from './cross-iframe-positioner.service';
import { MessageRouterService } from './message-router.service';
import { IframeTreeService, IframeNode, IframeTree } from './iframe-tree.service';

/**
 * 跨iframe定位后台服务
 * 用于在background脚本中处理跨域iframe之间的位置计算请求
 */
export class CrossIframePositionerBackgroundService {
  private static instance: CrossIframePositionerBackgroundService;

  // 存储iframe信息的映射表
  private iframeInfoMap: Map<string, IframeInfo> = new Map();

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    this.setupMessageListeners();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): CrossIframePositionerBackgroundService {
    if (!CrossIframePositionerBackgroundService.instance) {
      CrossIframePositionerBackgroundService.instance = new CrossIframePositionerBackgroundService();
    }
    return CrossIframePositionerBackgroundService.instance;
  }

  /**
   * 设置消息监听器
   * 注册消息处理器到消息路由服务
   */
  private setupMessageListeners(): void {
    // 获取消息路由服务实例
    const messageRouter = MessageRouterService.getInstance();

    // 注册消息处理器
    messageRouter.registerHandler({
      // 定义此服务能处理的消息类型
      messageTypes: [
        BackgroundMessageType.GET_CURRENT_FRAME_ID,
        BackgroundMessageType.CALCULATE_IFRAME_ELEMENT_POSITION,
        BackgroundMessageType.REQUEST_PARENT_FRAME_CALCULATION
      ],

      // 处理消息的方法
      handleMessage: async (message: RuntimeMessage<any>, sender: chrome.runtime.MessageSender, _sendResponse) => {
        console.log('CrossIframePositionerBackgroundService handling message:', message.type);

        // 根据消息类型处理不同的请求
        if (message.type === BackgroundMessageType.CALCULATE_IFRAME_ELEMENT_POSITION) {
          try {
            // 直接返回Promise结果
            return await this.handleCalculateIframeElementPosition(message, sender);
          } catch (error) {
            console.error('处理iframe元素位置计算请求时出错:', error);
            return {
              success: false,
              error: error instanceof Error ? error.message : String(error),
              messageId: message.messageId
            };
          }
        }

        if (message.type === BackgroundMessageType.REQUEST_PARENT_FRAME_CALCULATION) {
          try {
            // 直接返回Promise结果
            return await this.handleRequestParentFrameCalculation(message, sender);
          } catch (error) {
            console.error('处理父iframe位置计算请求时出错:', error);
            return {
              success: false,
              error: error instanceof Error ? error.message : String(error),
              messageId: message.messageId
            };
          }
        }

        if (message.type === BackgroundMessageType.GET_CURRENT_FRAME_ID) {
          try {
            // 直接返回Promise结果
            return this.handleGetCurrentFrameId(message, sender);
          } catch (error) {
            console.error('处理获取当前frameId请求时出错:', error);
            return {
              success: false,
              error: error instanceof Error? error.message : String(error),
              messageId: message.messageId
            };
          }
        }

        // 不处理其他类型的消息，返回错误响应
        return {
          success: false,
          error: `未知消息类型: ${message.type}`,
          messageId: message.messageId
        };
      }
    });

    console.log('CrossIframePositionerBackgroundService message listeners registered');
  }

  /**
   * 处理获取当前frameId的请求
   * @param message 消息对象
   * @param sender 发送者信息
   * @returns RuntimeMessageResponse<{frameId: number}> 响应对象
   */
  private handleGetCurrentFrameId(
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender
  ): RuntimeMessageResponse<{frameId: number}> {
    try {
      // 直接返回sender.frameId
      const frameId = sender.frameId !== undefined ? sender.frameId : -1;

      console.log(`获取当前frameId: ${frameId}, URL: ${sender.url || 'unknown'}`);

      return {
        success: true,
        data: { frameId },
        messageId: message.messageId
      };
    } catch (error) {
      console.error('处理获取当前frameId请求时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理iframe元素位置计算请求
   * @param message 消息对象
   * @param sender 发送者信息
   * @returns Promise<RuntimeMessageResponse<PositionCalculationResponse>> 响应对象
   */
  private async handleCalculateIframeElementPosition(
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender
  ): Promise<RuntimeMessageResponse<PositionCalculationResponse>> {
    try {
      // 获取请求数据
      const { request, elementPosition } = message.payload || {};

      if (!request || !elementPosition) {
        throw new Error('无效的位置计算请求数据');
      }

      // 获取发送者的标签页ID
      const tabId = sender.tab?.id;
      if (!tabId) {
        throw new Error('无法获取发送者的标签页ID');
      }

      // 获取发送者的frameId
      const frameId = sender.frameId !== undefined ? sender.frameId : -1;

      // 获取iframe树结构
      let iframeTree: IframeTree | null = null;
      try {
        // 获取iframe树服务实例
        const iframeTreeService = IframeTreeService.getInstance();

        // 直接从服务实例获取树结构
        const treeMap = (iframeTreeService as any).iframeTreeMap;
        if (treeMap && treeMap instanceof Map && treeMap.has(tabId)) {
          iframeTree = treeMap.get(tabId);
          console.log('成功获取iframe树结构:', iframeTree);
        } else {
          console.warn('无法获取iframe树结构，使用传统方法计算位置');
        }
      } catch (error) {
        console.error('获取iframe树结构时出错:', error);
      }

      // 存储iframe信息
      this.storeIframeInfo({
        frameId: request.frameId,
        frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
        frameLevel: request.frameLevel,
        isCrossDomain: true
      });

      // 如果是顶层页面，直接返回位置
      if (request.frameLevel === 0) {
        return {
          success: true,
          data: {
            position: elementPosition,
            frameInfo: {
              frameId: request.frameId,
              frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
              frameLevel: 0,
              isCrossDomain: false
            },
            requestId: request.requestId,
            success: true
          },
          messageId: message.messageId
        };
      }

      // 如果有iframe树结构，使用树结构计算位置
      if (iframeTree) {
        try {
          // 获取iframe树服务实例
          const iframeTreeService = IframeTreeService.getInstance();

          // 查找当前iframe在树中的节点
          const currentFrameNode = iframeTreeService.findFrameNodeInTree(iframeTree, frameId);

          if (currentFrameNode) {
            console.log('找到当前iframe在树中的节点:', currentFrameNode);

            // 构建从当前iframe到顶层页面的路径
            const pathToTop = iframeTreeService.buildPathToTop(iframeTree, currentFrameNode);
            console.log('从当前iframe到顶层页面的路径:', pathToTop);

            // 使用路径计算位置
            return await this.calculatePositionUsingPath(
              tabId,
              pathToTop,
              elementPosition,
              request,
              message.messageId
            );
          }
        } catch (error) {
          console.error('使用iframe树结构计算位置时出错:', error);
          // 如果使用树结构计算失败，回退到传统方法
        }
      }

      // 如果无法使用树结构或计算失败，使用传统方法
      // 向顶层页面发送位置计算请求
      try {
        // 构建请求消息
        const requestMessage = {
          type: BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST,
          data: {
            request: {
              ...request,
              frameId: frameId // 使用chrome分配的frameId
            },
            elementPosition: elementPosition
          }
        };

        // 发送消息到顶层页面
        const response = await new Promise<any>((resolve, reject) => {
          chrome.tabs.sendMessage(
            tabId,
            requestMessage,
            { frameId: 0 }, // 发送到顶层页面
            (response) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(response);
              }
            }
          );
        });

        // 返回响应
        return {
          success: true,
          data: response,
          messageId: message.messageId
        };
      } catch (error) {
        console.error('向顶层页面发送位置计算请求时出错:', error);

        // 如果无法计算，返回原始位置
        return {
          success: true,
          data: {
            position: elementPosition,
            frameInfo: {
              frameId: request.frameId,
              frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
              frameLevel: request.frameLevel,
              isCrossDomain: true
            },
            requestId: request.requestId,
            success: true
          },
          messageId: message.messageId
        };
      }
    } catch (error) {
      console.error('处理iframe元素位置计算请求时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 处理父iframe位置计算请求
   * @param message 消息对象
   * @param sender 发送者信息
   * @returns Promise<RuntimeMessageResponse<PositionCalculationResponse>> 响应对象
   */
  private async handleRequestParentFrameCalculation(
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender
  ): Promise<RuntimeMessageResponse<PositionCalculationResponse>> {
    try {
      // 获取请求数据
      const { request, elementPosition, targetFrameLevel } = message.payload || {};

      if (!request || !elementPosition || targetFrameLevel === undefined) {
        throw new Error('无效的父iframe位置计算请求数据');
      }

      // 获取发送者的标签页ID
      const tabId = sender.tab?.id;
      if (!tabId) {
        throw new Error('无法获取发送者的标签页ID');
      }

      // 如果目标是顶层页面，直接向顶层页面发送请求
      if (targetFrameLevel === 0) {
        try {
          // 构建请求消息
          const requestMessage = {
            type: BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST,
            data: {
              request: {
                ...request,
                frameLevel: 0
              },
              elementPosition: elementPosition
            }
          };

          // 发送消息到顶层页面
          const response = await new Promise<any>((resolve, reject) => {
            chrome.tabs.sendMessage(
              tabId,
              requestMessage,
              { frameId: 0 }, // 发送到顶层页面
              (response) => {
                if (chrome.runtime.lastError) {
                  reject(chrome.runtime.lastError);
                } else {
                  resolve(response);
                }
              }
            );
          });

          // 返回响应
          return {
            success: true,
            data: response,
            messageId: message.messageId
          };
        } catch (error) {
          console.error('向顶层页面发送位置计算请求时出错:', error);

          // 如果无法计算，返回原始位置
          return {
            success: true,
            data: {
              position: elementPosition,
              frameInfo: {
                frameId: request.frameId,
                frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
                frameLevel: request.frameLevel,
                isCrossDomain: true
              },
              requestId: request.requestId,
              success: true
            },
            messageId: message.messageId
          };
        }
      }

      // 如果目标不是顶层页面，需要找到对应层级的iframe
      // 这里简化处理，直接向顶层页面发送请求
      try {
        // 构建请求消息
        const requestMessage = {
          type: BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST,
          data: {
            request: {
              ...request,
              frameLevel: targetFrameLevel
            },
            elementPosition: elementPosition
          }
        };

        // 发送消息到顶层页面
        const response = await new Promise<any>((resolve, reject) => {
          chrome.tabs.sendMessage(
            tabId,
            requestMessage,
            { frameId: 0 }, // 发送到顶层页面
            (response) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(response);
              }
            }
          );
        });

        // 返回响应
        return {
          success: true,
          data: response,
          messageId: message.messageId
        };
      } catch (error) {
        console.error('向顶层页面发送位置计算请求时出错:', error);

        // 如果无法计算，返回原始位置
        return {
          success: true,
          data: {
            position: elementPosition,
            frameInfo: {
              frameId: request.frameId,
              frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
              frameLevel: targetFrameLevel,
              isCrossDomain: true
            },
            requestId: request.requestId,
            success: true
          },
          messageId: message.messageId
        };
      }
    } catch (error) {
      console.error('处理父iframe位置计算请求时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 存储iframe信息
   * @param iframeInfo iframe信息
   */
  private storeIframeInfo(iframeInfo: IframeInfo): void {
    this.iframeInfoMap.set(String(iframeInfo.frameId), iframeInfo);
  }

  /**
   * 使用iframe路径计算元素位置
   * @param tabId 标签页ID
   * @param pathToTop 从顶层页面到当前iframe的路径
   * @param elementPosition 元素在当前iframe中的位置
   * @param request 原始请求
   * @param messageId 消息ID
   * @returns Promise<RuntimeMessageResponse<PositionCalculationResponse>> 响应对象
   */
  private async calculatePositionUsingPath(
    tabId: number,
    pathToTop: IframeNode[],
    elementPosition: ElementPosition,
    request: PositionCalculationRequest,
    messageId: string
  ): Promise<RuntimeMessageResponse<PositionCalculationResponse>> {
    try {
      // 如果路径为空，返回原始位置
      if (pathToTop.length === 0) {
        return {
          success: true,
          data: {
            position: elementPosition,
            frameInfo: {
              frameId: request.frameId,
              frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
              frameLevel: request.frameLevel,
              isCrossDomain: true
            },
            requestId: request.requestId,
            success: true
          },
          messageId: messageId
        };
      }

      // 如果只有顶层页面，直接返回位置
      if (pathToTop.length === 1 && pathToTop[0].parentFrameId === -1) {
        return {
          success: true,
          data: {
            position: elementPosition,
            frameInfo: {
              frameId: request.frameId,
              frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
              frameLevel: 0,
              isCrossDomain: false
            },
            requestId: request.requestId,
            success: true
          },
          messageId: messageId
        };
      }

      // 从顶层页面开始，逐层计算位置
      let finalPosition = { ...elementPosition };

      // 从倒数第二个节点开始（最后一个是当前iframe）
      // 我们需要获取每个iframe在其父页面中的位置
      for (let i = 0; i < pathToTop.length - 1; i++) {
        const parentNode = pathToTop[i];
        const childNode = pathToTop[i + 1];

        try {
          // 获取子iframe在父页面中的位置
          const iframePosition = await this.getIframePositionInParent(tabId, parentNode.frameId, childNode.frameId);

          if (iframePosition) {
            // 累加位置偏移
            finalPosition = {
              top: finalPosition.top + iframePosition.top,
              left: finalPosition.left + iframePosition.left,
              width: finalPosition.width,
              height: finalPosition.height,
              bottom: finalPosition.bottom + iframePosition.top,
              right: finalPosition.right + iframePosition.left,
              x: finalPosition.x + iframePosition.left,
              y: finalPosition.y + iframePosition.top
            };
          }
        } catch (error) {
          console.error(`获取iframe ${childNode.frameId} 在父页面 ${parentNode.frameId} 中的位置时出错:`, error);
          // 如果出错，继续使用当前位置
        }
      }

      // 返回最终计算的位置
      return {
        success: true,
        data: {
          position: finalPosition,
          frameInfo: {
            frameId: request.frameId,
            frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
            frameLevel: request.frameLevel,
            isCrossDomain: true
          },
          requestId: request.requestId,
          success: true
        },
        messageId: messageId
      };
    } catch (error) {
      console.error('使用iframe路径计算位置时出错:', error);

      // 如果计算失败，返回原始位置
      return {
        success: true,
        data: {
          position: elementPosition,
          frameInfo: {
            frameId: request.frameId,
            frameRect: { top: 0, left: 0, width: 0, height: 0, bottom: 0, right: 0, x: 0, y: 0 },
            frameLevel: request.frameLevel,
            isCrossDomain: true
          },
          requestId: request.requestId,
          success: true
        },
        messageId: messageId
      };
    }
  }

  /**
   * 获取iframe在父页面中的位置
   * @param tabId 标签页ID
   * @param parentFrameId 父iframe的ID
   * @param childFrameId 子iframe的ID
   * @returns Promise<ElementPosition | null> iframe在父页面中的位置
   */
  private async getIframePositionInParent(
    tabId: number,
    parentFrameId: number,
    childFrameId: number
  ): Promise<ElementPosition | null> {
    try {
      // 构建请求消息
      const requestMessage = {
        type: BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST,
        data: {
          request: {
            elementSelector: `iframe[data-secure-password-ext-frame-id="${childFrameId}"]`,
            frameId: parentFrameId,
            frameLevel: 0, // 这里不重要，因为我们直接指定了frameId
            requestId: `get_iframe_position_${parentFrameId}_${childFrameId}_${Date.now()}`
          }
        }
      };

      // 发送消息到父iframe
      const response = await new Promise<any>((resolve, reject) => {
        chrome.tabs.sendMessage(
          tabId,
          requestMessage,
          { frameId: parentFrameId },
          (response) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(response);
            }
          }
        );
      });

      if (response && response.success && response.position) {
        return response.position;
      }

      // 如果无法获取位置，尝试使用默认选择器
      const defaultRequestMessage = {
        type: BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST,
        data: {
          request: {
            elementSelector: 'iframe',
            frameId: parentFrameId,
            frameLevel: 0,
            requestId: `get_iframe_position_default_${parentFrameId}_${childFrameId}_${Date.now()}`
          }
        }
      };

      const defaultResponse = await new Promise<any>((resolve, reject) => {
        chrome.tabs.sendMessage(
          tabId,
          defaultRequestMessage,
          { frameId: parentFrameId },
          (response) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(response);
            }
          }
        );
      });

      if (defaultResponse && defaultResponse.success && defaultResponse.position) {
        return defaultResponse.position;
      }

      return null;
    } catch (error) {
      console.error('获取iframe在父页面中的位置时出错:', error);
      return null;
    }
  }
}
