import { RuntimeMessageService } from './runtime-message.service';
import { BackgroundMessageType } from './background-message.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 位置信息接口
 * 描述元素在页面中的位置
 */
export interface ElementPosition {
  top: number;
  left: number;
  width: number;
  height: number;
  bottom: number;
  right: number;
  x: number;
  y: number;
}

/**
 * iframe信息接口
 * 描述iframe的位置和标识信息
 */
export interface IframeInfo {
  frameId: string | number; // iframe的唯一标识
  frameRect: ElementPosition; // iframe在父页面中的位置
  parentFrameId?: string | number; // 父iframe的ID（如果有）
  frameLevel: number; // iframe嵌套层级，0表示顶层页面
  isCrossDomain: boolean; // 是否是跨域iframe
}

/**
 * 位置计算请求接口
 */
export interface PositionCalculationRequest {
  elementSelector: string; // 要计算位置的元素选择器
  frameId: string | number; // 当前iframe的ID
  frameLevel: number; // 当前iframe的嵌套层级
  requestId: string; // 请求的唯一ID
  parentRequest?: PositionCalculationRequest; // 父请求（如果是嵌套请求）
}

/**
 * 位置计算响应接口
 */
export interface PositionCalculationResponse {
  position: ElementPosition; // 计算出的位置
  frameInfo: IframeInfo; // iframe信息
  requestId: string; // 对应请求的ID
  success: boolean; // 是否成功
  error?: string; // 错误信息（如果有）
}

/**
 * 跨iframe定位服务
 * 用于计算跨域iframe多层嵌套中元素在顶层页面中的位置
 */
export class CrossIframePositionerService {
  private static instance: CrossIframePositionerService;
  private isInIframe: boolean;
  private frameId: string;
  private frameLevel: number;
  private isCrossDomain: boolean;
  // 注意：此属性目前未使用，但保留以供将来扩展
  // private parentFrameInfo: IframeInfo | null = null;

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    // 检查是否在iframe中
    this.isInIframe = window !== window.top;

    // 生成唯一的frameId
    this.frameId = `frame_${uuidv4()}`;

    // 确定iframe嵌套层级
    this.frameLevel = this.determineFrameLevel();

    // 确定是否是跨域iframe
    this.isCrossDomain = this.determineIsCrossDomain();

    // 初始化消息监听
    this.setupMessageListeners();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): CrossIframePositionerService {
    if (!CrossIframePositionerService.instance) {
      CrossIframePositionerService.instance = new CrossIframePositionerService();
    }
    return CrossIframePositionerService.instance;
  }

  /**
   * 计算元素在顶层页面中的位置
   * @param element 要计算位置的元素
   * @returns Promise<ElementPosition> 元素在顶层页面中的位置
   */
  public async calculateElementPositionInTopFrame(element: HTMLElement): Promise<ElementPosition> {
    // 获取元素在当前iframe中的位置
    const elementRect = element.getBoundingClientRect();

    // 如果不在iframe中，直接返回元素位置
    if (!this.isInIframe) {
      return this.convertDOMRectToElementPosition(elementRect);
    }

    // 如果在iframe中，需要计算相对于顶层页面的位置
    return this.calculatePositionRelativeToTop(elementRect);
  }

  /**
   * 将DOMRect转换为ElementPosition
   * @param rect DOMRect对象
   * @returns ElementPosition 位置信息
   */
  private convertDOMRectToElementPosition(rect: DOMRect): ElementPosition {
    return {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height,
      bottom: rect.bottom,
      right: rect.right,
      x: rect.x,
      y: rect.y
    };
  }

  /**
   * 计算元素相对于顶层页面的位置
   * @param elementRect 元素在当前iframe中的位置
   * @returns Promise<ElementPosition> 元素在顶层页面中的位置
   */
  private async calculatePositionRelativeToTop(elementRect: DOMRect): Promise<ElementPosition> {
    // 如果是跨域iframe，通过background中转获取位置
    if (this.isCrossDomain) {
      return this.calculatePositionViaBgScript(elementRect);
    }

    try {
      // 获取当前iframe在父页面中的位置
      const iframeElement = window.frameElement as HTMLIFrameElement;
      if (!iframeElement) {
        throw new Error('无法获取iframe元素');
      }

      const iframeRect = iframeElement.getBoundingClientRect();

      // 计算元素相对于父页面的位置
      const positionInParent: ElementPosition = {
        top: iframeRect.top + elementRect.top,
        left: iframeRect.left + elementRect.left,
        width: elementRect.width,
        height: elementRect.height,
        // 确保bottom考虑元素的高度
        bottom: iframeRect.top + elementRect.top + elementRect.height,
        right: iframeRect.left + elementRect.left + elementRect.width,
        x: iframeRect.left + elementRect.x,
        y: iframeRect.top + elementRect.y
      };

      // 如果父页面是顶层页面，直接返回
      if (window.parent === window.top) {
        return positionInParent;
      }

      // 如果父页面不是顶层页面，递归计算
      // 这里需要通过父页面的CrossIframePositionerService实例计算
      // 由于跨域限制，我们需要通过background脚本中转
      return this.requestParentFrameCalculation(positionInParent);
    } catch (error) {
      console.error('计算元素位置时出错:', error);
      // 如果出错，可能是由于跨域限制，尝试通过background脚本中转
      return this.calculatePositionViaBgScript(elementRect);
    }
  }

  /**
   * 通过background脚本计算元素位置
   * @param elementRect 元素在当前iframe中的位置
   * @returns Promise<ElementPosition> 元素在顶层页面中的位置
   */
  private async calculatePositionViaBgScript(elementRect: DOMRect): Promise<ElementPosition> {
    try {
      // 创建请求ID
      const requestId = uuidv4();

      // 获取当前chrome分配的frameId
      let chromeFrameId: number | undefined;
      try {
        // 尝试从self.frameElement获取frameId
        if (self.frameElement && 'dataset' in self.frameElement) {
          const dataFrameId = (self.frameElement as HTMLIFrameElement).dataset.frameId;
          if (dataFrameId) {
            chromeFrameId = parseInt(dataFrameId, 10);
          }
        }
      } catch (e) {
        // 忽略跨域错误
        console.log('尝试获取frameElement时出现跨域限制');
      }

      // 创建请求对象
      const request: PositionCalculationRequest = {
        elementSelector: '', // 不需要选择器，因为我们已经有了元素位置
        frameId: chromeFrameId !== undefined ? chromeFrameId : this.frameId,
        frameLevel: this.frameLevel,
        requestId: requestId
      };

      // 发送消息到background脚本
      const response = await RuntimeMessageService.sendMessageToBackgroundAsPromise<
        {
          request: PositionCalculationRequest,
          elementPosition: ElementPosition
        },
        PositionCalculationResponse
      >({
        type: BackgroundMessageType.CALCULATE_IFRAME_ELEMENT_POSITION,
        payload: {
          request: request,
          elementPosition: this.convertDOMRectToElementPosition(elementRect)
        }
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || '计算位置失败');
      }

      // 如果响应中包含位置信息，使用响应中的位置
      if (response.data.position) {
        console.log('使用background计算的位置:', response.data.position);
        return response.data.position;
      }

      // 如果没有位置信息，返回原始位置
      return this.convertDOMRectToElementPosition(elementRect);
    } catch (error) {
      console.error('通过background脚本计算位置时出错:', error);
      // 如果计算失败，返回原始位置
      return this.convertDOMRectToElementPosition(elementRect);
    }
  }

  /**
   * 请求父iframe计算位置
   * @param positionInParent 元素在父iframe中的位置
   * @returns Promise<ElementPosition> 元素在顶层页面中的位置
   */
  private async requestParentFrameCalculation(positionInParent: ElementPosition): Promise<ElementPosition> {
    // 由于跨域限制，我们需要通过background脚本中转
    try {
      // 创建请求ID
      const requestId = uuidv4();

      // 创建请求对象
      const request: PositionCalculationRequest = {
        elementSelector: '', // 不需要选择器，因为我们已经有了元素位置
        frameId: this.frameId,
        frameLevel: this.frameLevel,
        requestId: requestId
      };

      // 发送消息到background脚本
      const response = await RuntimeMessageService.sendMessageToBackgroundAsPromise<
        {
          request: PositionCalculationRequest,
          elementPosition: ElementPosition,
          targetFrameLevel: number
        },
        PositionCalculationResponse
      >({
        type: BackgroundMessageType.REQUEST_PARENT_FRAME_CALCULATION,
        payload: {
          request: request,
          elementPosition: positionInParent,
          targetFrameLevel: this.frameLevel - 1 // 父iframe的层级
        }
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || '请求父iframe计算位置失败');
      }

      return response.data.position;
    } catch (error) {
      console.error('请求父iframe计算位置时出错:', error);
      // 如果计算失败，返回在父iframe中的位置
      return positionInParent;
    }
  }

  /**
   * 确定iframe嵌套层级
   * @returns number iframe嵌套层级，0表示顶层页面
   */
  private determineFrameLevel(): number {
    if (!this.isInIframe) {
      return 0;
    }

    // 尝试从URL参数中获取层级信息
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const frameLevel = urlParams.get('frame_level');
      if (frameLevel) {
        const level = parseInt(frameLevel, 10);
        if (!isNaN(level) && level > 0) {
          console.log('从URL参数中获取iframe层级:', level);
          return level;
        }
      }
    } catch (e) {
      console.error('从URL参数获取iframe层级时出错:', e);
    }

    // 尝试从data属性中获取层级信息
    try {
      if (self.frameElement && 'dataset' in self.frameElement) {
        const dataLevel = (self.frameElement as HTMLIFrameElement).dataset.frameLevel;
        if (dataLevel) {
          const level = parseInt(dataLevel, 10);
          if (!isNaN(level) && level > 0) {
            console.log('从data属性中获取iframe层级:', level);
            return level;
          }
        }
      }
    } catch (e) {
      console.log('从data属性获取iframe层级时出现跨域限制');
    }

    // 如果无法从URL或data属性获取，使用传统方法
    let level = 0;
    let currentWindow: Window = window;

    try {
      while (currentWindow !== window.top) {
        level++;
        currentWindow = currentWindow.parent;
      }
    } catch (e) {
      // 如果出现跨域错误，无法继续检查
      console.log('确定iframe层级时出现跨域限制，返回当前已知层级:', level);
    }

    return level;
  }

  /**
   * 确定是否是跨域iframe
   * @returns boolean 是否是跨域iframe
   */
  private determineIsCrossDomain(): boolean {
    if (!this.isInIframe) {
      return false;
    }

    try {
      // 尝试访问父窗口的location.href
      // 如果成功，说明不是跨域iframe
      // 使用void操作符避免未使用变量的警告
      void window.parent.location.href;
      return false;
    } catch (e) {
      // 如果出现错误，说明是跨域iframe
      return true;
    }
  }

  /**
   * 设置消息监听器
   * 处理来自background脚本的消息
   */
  private setupMessageListeners(): void {
    // 监听来自background的位置计算请求
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // 检查消息类型
      if (message.type === BackgroundMessageType.CALCULATE_ELEMENT_POSITION_REQUEST) {
        // 处理位置计算请求
        this.handlePositionCalculationRequest(message.data, sender, sendResponse);
        return true; // 表示会异步发送响应
      }

      return false; // 不处理其他类型的消息
    });
  }

  /**
   * 处理位置计算请求
   * @param data 请求数据
   * @param sender 发送者信息
   * @param sendResponse 发送响应的回调函数
   */
  private async handlePositionCalculationRequest(
    data: any,
    _sender: chrome.runtime.MessageSender, // 使用下划线前缀表示未使用的参数
    sendResponse: (response: any) => void
  ): Promise<void> {
    try {
      // 解析请求数据
      const request = data.request as PositionCalculationRequest;
      const elementSelector = request.elementSelector;

      // 查找元素
      let element: HTMLElement | null = null;
      if (elementSelector) {
        element = document.querySelector(elementSelector) as HTMLElement;
      }

      if (!element && data.elementPosition) {
        // 如果没有找到元素但提供了位置信息，使用位置信息
        const calculatedPosition = this.calculatePositionFromParentData(data.elementPosition);

        // 发送响应
        sendResponse({
          success: true,
          position: calculatedPosition,
          frameInfo: {
            frameId: this.frameId,
            frameLevel: this.frameLevel,
            isCrossDomain: this.isCrossDomain,
            frameRect: this.getIframeRect()
          },
          requestId: request.requestId
        });
        return;
      }

      if (!element) {
        sendResponse({
          success: false,
          error: '找不到指定的元素',
          requestId: request.requestId
        });
        return;
      }

      // 计算元素位置
      const position = await this.calculateElementPositionInTopFrame(element);

      // 发送响应
      sendResponse({
        success: true,
        position: position,
        frameInfo: {
          frameId: this.frameId,
          frameLevel: this.frameLevel,
          isCrossDomain: this.isCrossDomain,
          frameRect: this.getIframeRect()
        },
        requestId: request.requestId
      });
    } catch (error) {
      console.error('处理位置计算请求时出错:', error);
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        requestId: data.request?.requestId || 'unknown'
      });
    }
  }

  /**
   * 根据父iframe提供的数据计算位置
   * @param parentPosition 父iframe中的位置
   * @returns ElementPosition 计算后的位置
   */
  private calculatePositionFromParentData(parentPosition: ElementPosition): ElementPosition {
    // 获取当前iframe的位置
    const iframeRect = this.getIframeRect();

    // 计算最终位置
    return {
      top: iframeRect.top + parentPosition.top,
      left: iframeRect.left + parentPosition.left,
      width: parentPosition.width,
      height: parentPosition.height,
      bottom: iframeRect.top + parentPosition.bottom,
      right: iframeRect.left + parentPosition.right,
      x: iframeRect.left + parentPosition.x,
      y: iframeRect.top + parentPosition.y
    };
  }

  /**
   * 获取当前iframe的位置
   * @returns ElementPosition iframe的位置
   */
  private getIframeRect(): ElementPosition {
    if (!this.isInIframe) {
      // 如果不在iframe中，返回空位置
      return {
        top: 0,
        left: 0,
        width: 0,
        height: 0,
        bottom: 0,
        right: 0,
        x: 0,
        y: 0
      };
    }

    try {
      // 尝试获取iframe元素
      const iframeElement = window.frameElement as HTMLIFrameElement;
      if (iframeElement) {
        return this.convertDOMRectToElementPosition(iframeElement.getBoundingClientRect());
      }
    } catch (e) {
      // 如果出现跨域错误，无法获取frameElement
      console.log('获取iframe位置时出现跨域限制');
    }

    // 如果无法获取iframe位置，返回空位置
    return {
      top: 0,
      left: 0,
      width: 0,
      height: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0
    };
  }
}
