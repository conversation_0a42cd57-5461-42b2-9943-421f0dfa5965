import { Observable, Subject } from 'rxjs';
import { QuerySelectorDeepService } from './query-selector-deep.service';

export interface FormField {
  element: HTMLElement;
  type: string;
  id: string;
  name: string;
  placeholder: string;
  isPassword: boolean;
  isUsername: boolean;
  formId?: string;
}

export interface DetectedForm {
  element: HTMLFormElement;
  id: string;
  fields: FormField[];
  passwordFields: FormField[];
  usernameFields: FormField[];
}

/**
 * 表单检测服务
 * 负责检测页面中的表单和表单字段，包括Shadow DOM中的表单
 */
export class FormDetectionService {
  private forms: DetectedForm[] = [];
  private formFields: FormField[] = [];
  private formsChanged = new Subject<DetectedForm[]>();
  private fieldsChanged = new Subject<FormField[]>();
  private mutationObserver: MutationObserver | null = null;
  private readonly querySelectorService: QuerySelectorDeepService;
  private readonly usernameFieldPatterns = [
    /user(name)?/i,
    /email/i,
    /login/i,
    /log[_-]?in/i,
    /account/i,
    /identifier/i,
    /id/i
  ];
  private readonly excludedFieldTypes = [
    'hidden',
    'submit',
    'reset',
    'button',
    'image',
    'file',
    'checkbox',
    'radio'
  ];
  private readonly debounceTime = 300; // 防抖时间（毫秒）
  private debounceTimer: number | null = null;
  private pendingDetection = false;

  constructor() {
    this.querySelectorService = new QuerySelectorDeepService();
    this.setupMutationObserver();
  }

  /**
   * 获取表单变化的Observable
   */
  get formsChanged$(): Observable<DetectedForm[]> {
    return this.formsChanged.asObservable();
  }

  /**
   * 获取表单字段变化的Observable
   */
  get fieldsChanged$(): Observable<FormField[]> {
    return this.fieldsChanged.asObservable();
  }

  /**
   * 获取当前检测到的所有表单
   */
  getForms(): DetectedForm[] {
    return [...this.forms];
  }

  /**
   * 获取当前检测到的所有表单字段
   */
  getFormFields(): FormField[] {
    return [...this.formFields];
  }

  /**
   * 初始化表单检测
   */
  initialize(): void {
    this.detectForms();

    // 监听Shadow DOM状态变化
    this.querySelectorService.shadowDomStateChanged.subscribe(() => {
      this.detectForms();
    });

    // 监听DOM内容加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.detectForms());
    }

    // 监听页面完全加载完成
    window.addEventListener('load', () => this.detectForms());
  }

  /**
   * 检测页面中的所有表单和表单字段
   */
  detectForms(): void {
    // 使用防抖处理，避免短时间内多次检测
    if (this.debounceTimer !== null) {
      window.clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = window.setTimeout(() => {
      this.performFormDetection();
      this.debounceTimer = null;
    }, this.debounceTime);
  }

  /**
   * 执行表单检测
   */
  private performFormDetection(): void {
    if (this.pendingDetection) {
      return;
    }

    this.pendingDetection = true;

    try {
      // 清空之前的检测结果
      const oldForms = [...this.forms];
      const oldFields = [...this.formFields];

      this.forms = [];
      this.formFields = [];

      // 检测表单元素
      this.detectFormElements();

      // 检测独立的表单字段（不在表单内的输入框）
      this.detectStandaloneFields();

      // 通知变化
      if (this.hasFormsChanged(oldForms, this.forms)) {
        this.formsChanged.next(this.forms);
      }

      if (this.hasFieldsChanged(oldFields, this.formFields)) {
        this.fieldsChanged.next(this.formFields);
      }
    } catch (error) {
      console.error('Error detecting forms:', error);
    } finally {
      this.pendingDetection = false;
    }
  }

  /**
   * 检测表单元素
   */
  private detectFormElements(): void {
    // 使用QuerySelectorDeepService查询所有表单
    const formElements = this.querySelectorService.selectForms();

    for (const formElement of formElements) {
      try {
        // 生成表单ID
        const formId = this.generateFormId(formElement);

        // 检测表单中的字段
        const fields = this.detectFormFields(formElement, formId);

        // 过滤出密码字段和用户名字段
        const passwordFields = fields.filter(field => field.isPassword);
        const usernameFields = fields.filter(field => field.isUsername);

        // 如果表单中有密码字段或用户名字段，则添加到检测结果中
        if (passwordFields.length > 0 || usernameFields.length > 0) {
          this.forms.push({
            element: formElement,
            id: formId,
            fields,
            passwordFields,
            usernameFields
          });

          // 将字段添加到总字段列表中
          this.formFields.push(...fields);
        }
      } catch (error) {
        console.error('Error processing form:', error);
      }
    }
  }

  /**
   * 检测表单中的字段
   * @param formElement 表单元素
   * @param formId 表单ID
   * @returns 表单字段数组
   */
  private detectFormFields(formElement: HTMLFormElement, formId: string): FormField[] {
    const fields: FormField[] = [];

    // 查询表单中的输入元素
    const inputSelector = 'input, textarea, select';
    const inputElements = Array.from(formElement.querySelectorAll(inputSelector)) as HTMLElement[];

    for (const element of inputElements) {
      try {
        const field = this.createFormField(element, formId);
        if (field) {
          fields.push(field);
        }
      } catch (error) {
        console.error('Error processing form field:', error);
      }
    }

    return fields;
  }

  /**
   * 检测独立的表单字段（不在表单内的输入框）
   */
  private detectStandaloneFields(): void {
    // 使用QuerySelectorDeepService查询所有输入元素
    const inputElements = this.querySelectorService.selectFormInputs();

    for (const element of inputElements) {
      try {
        // 检查元素是否已经在表单中
        const isInForm = this.formFields.some(field => field.element === element);

        if (!isInForm) {
          // 检查元素是否在表单内
          const closestForm = element.closest('form');
          if (!closestForm) {
            const field = this.createFormField(element);
            if (field) {
              this.formFields.push(field);

              // 如果是密码字段或用户名字段，创建一个虚拟表单
              if (field.isPassword || field.isUsername) {
                const virtualFormId = 'virtual-form-' + this.generateRandomId();
                field.formId = virtualFormId;

                // 查找附近可能的相关字段
                const relatedFields = this.findRelatedFields(element);
                for (const relatedField of relatedFields) {
                  if (!this.formFields.some(f => f.element === relatedField)) {
                    const relatedFormField = this.createFormField(relatedField, virtualFormId);
                    if (relatedFormField) {
                      this.formFields.push(relatedFormField);
                    }
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error processing standalone field:', error);
      }
    }
  }

  /**
   * 创建表单字段对象
   * @param element 表单元素
   * @param formId 表单ID
   * @returns 表单字段对象或null
   */
  private createFormField(element: HTMLElement, formId?: string): FormField | null {
    // 获取元素类型
    let type = '';
    if (element instanceof HTMLInputElement) {
      type = element.type || 'text';
    } else if (element instanceof HTMLTextAreaElement) {
      type = 'textarea';
    } else if (element instanceof HTMLSelectElement) {
      type = 'select';
    } else {
      type = element.tagName.toLowerCase();
    }

    // 排除不需要处理的字段类型
    if (this.excludedFieldTypes.includes(type)) {
      return null;
    }

    // 获取字段属性
    const id = element.id || '';
    const name = element.getAttribute('name') || '';
    const placeholder = element.getAttribute('placeholder') || '';

    // 判断是否为密码字段
    const isPassword = type === 'password' ||
                      element.getAttribute('autocomplete') === 'current-password' ||
                      element.getAttribute('autocomplete') === 'new-password' ||
                      /password/i.test(id) ||
                      /password/i.test(name) ||
                      /password/i.test(placeholder);

    // 判断是否为用户名字段
    const isUsername = this.isUsernameField(element, id, name, placeholder);

    // 如果既不是密码字段也不是用户名字段，且没有id和name，则跳过
    if (!isPassword && !isUsername && !id && !name) {
      return null;
    }

    return {
      element,
      type,
      id,
      name,
      placeholder,
      isPassword,
      isUsername,
      formId
    };
  }

  /**
   * 判断是否为用户名字段
   * @param element 元素
   * @param id 元素ID
   * @param name 元素name属性
   * @param placeholder 元素placeholder属性
   * @returns 是否为用户名字段
   */
  private isUsernameField(element: HTMLElement, id: string, name: string, placeholder: string): boolean {
    // 检查autocomplete属性
    const autocomplete = element.getAttribute('autocomplete');
    if (autocomplete === 'username' || autocomplete === 'email') {
      return true;
    }

    // 检查字段属性是否匹配用户名模式
    for (const pattern of this.usernameFieldPatterns) {
      if (pattern.test(id) || pattern.test(name) || pattern.test(placeholder)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 查找与元素相关的字段
   * @param element 元素
   * @returns 相关字段数组
   */
  private findRelatedFields(element: HTMLElement): HTMLElement[] {
    const relatedFields: HTMLElement[] = [];

    // 查找同一父元素下的其他输入元素
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.querySelectorAll('input, textarea, select')) as HTMLElement[];
      for (const sibling of siblings) {
        if (sibling !== element && !this.excludedFieldTypes.includes((sibling as HTMLInputElement).type || '')) {
          relatedFields.push(sibling);
        }
      }
    }

    return relatedFields;
  }

  /**
   * 生成表单ID
   * @param formElement 表单元素
   * @returns 表单ID
   */
  private generateFormId(formElement: HTMLFormElement): string {
    // 尝试使用表单的id或name属性
    if (formElement.id) {
      return `form-${formElement.id}`;
    }

    if (formElement.getAttribute('name')) {
      return `form-${formElement.getAttribute('name')}`;
    }

    // 使用表单的action属性
    if (formElement.action) {
      try {
        const url = new URL(formElement.action);
        return `form-${url.hostname}-${url.pathname}`;
      } catch (e) {
        // 忽略URL解析错误
      }
    }

    // 生成随机ID
    return `form-${this.generateRandomId()}`;
  }

  /**
   * 生成随机ID
   * @returns 随机ID
   */
  private generateRandomId(): string {
    return Math.random().toString(36).substring(2, 10);
  }

  /**
   * 检查表单是否发生变化
   * @param oldForms 旧表单数组
   * @param newForms 新表单数组
   * @returns 是否发生变化
   */
  private hasFormsChanged(oldForms: DetectedForm[], newForms: DetectedForm[]): boolean {
    if (oldForms.length !== newForms.length) {
      return true;
    }

    // 检查表单元素是否相同
    const oldFormElements = oldForms.map(form => form.element);
    const newFormElements = newForms.map(form => form.element);

    return !this.areArraysEqual(oldFormElements, newFormElements);
  }

  /**
   * 检查字段是否发生变化
   * @param oldFields 旧字段数组
   * @param newFields 新字段数组
   * @returns 是否发生变化
   */
  private hasFieldsChanged(oldFields: FormField[], newFields: FormField[]): boolean {
    if (oldFields.length !== newFields.length) {
      return true;
    }

    // 检查字段元素是否相同
    const oldFieldElements = oldFields.map(field => field.element);
    const newFieldElements = newFields.map(field => field.element);

    return !this.areArraysEqual(oldFieldElements, newFieldElements);
  }

  /**
   * 检查两个数组是否相等
   * @param array1 数组1
   * @param array2 数组2
   * @returns 是否相等
   */
  private areArraysEqual<T>(array1: T[], array2: T[]): boolean {
    if (array1.length !== array2.length) {
      return false;
    }

    for (let i = 0; i < array1.length; i++) {
      if (array1[i] !== array2[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * 设置DOM变化监听
   */
  private setupMutationObserver(): void {
    // 创建MutationObserver实例
    this.mutationObserver = new MutationObserver((mutations) => {
      let shouldDetect = false;

      // 检查变化是否与表单相关
      for (const mutation of mutations) {
        // 如果是节点添加或删除
        if (mutation.type === 'childList') {
          // 检查添加的节点
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // 检查是否是表单元素或包含表单元素
              if (element.tagName === 'FORM' ||
                  element.querySelector('form, input, textarea, select')) {
                shouldDetect = true;
                break;
              }
            }
          }

          // 检查删除的节点（可能包含表单）
          if (!shouldDetect && mutation.removedNodes.length > 0) {
            shouldDetect = true;
          }
        }

        // 如果是属性变化
        if (!shouldDetect && mutation.type === 'attributes') {
          const element = mutation.target as Element;

          // 检查是否是表单元素或输入元素的相关属性
          if (element.tagName === 'FORM' ||
              element.tagName === 'INPUT' ||
              element.tagName === 'TEXTAREA' ||
              element.tagName === 'SELECT') {

            // 检查变化的属性是否与表单检测相关
            const relevantAttributes = ['type', 'id', 'name', 'placeholder', 'autocomplete'];
            if (relevantAttributes.includes(mutation.attributeName || '')) {
              shouldDetect = true;
            }
          }
        }

        if (shouldDetect) {
          break;
        }
      }

      // 如果变化与表单相关，重新检测表单
      if (shouldDetect) {
        this.detectForms();
      }
    });

    // 开始观察文档变化
    this.mutationObserver.observe(document.documentElement, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['type', 'id', 'name', 'placeholder', 'autocomplete']
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    if (this.debounceTimer !== null) {
      window.clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    this.formsChanged.complete();
    this.fieldsChanged.complete();
    this.querySelectorService.dispose();

    window.removeEventListener('load', () => this.detectForms());
    document.removeEventListener('DOMContentLoaded', () => this.detectForms());
  }
}
