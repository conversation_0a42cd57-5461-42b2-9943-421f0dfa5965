import { Observable, Subject, fromEvent, Subscription } from 'rxjs';
import { debounceTime, filter } from 'rxjs/operators';
import { FormDetectionService, FormField } from './form-detection.service';
import {
  generateUniqueId
} from '../utils/postMessageUtil';
import { BackgroundMessageType } from './background-message.service';
import { RuntimeMessageService, RuntimeMessage } from './runtime-message.service';
import { CrossIframePositionerService } from './cross-iframe-positioner.service';

export interface FormFieldFocusEvent {
  field: FormField;
  position: DOMRect;
  domain: string;
  url: string;
  frameInfo?: {
    frameId: string;
    frameRect: DOMRect;
  };
}

/**
 * 表单监听服务
 * 负责监听表单字段的焦点和失焦事件，并提供字段位置计算
 */
export class FormListenerService {
  private focusedField: FormField | null = null;
  private fieldFocused = new Subject<FormFieldFocusEvent>();
  private fieldBlurred = new Subject<FormField>();
  private documentEventSubscriptions: Subscription[] = [];
  private fieldEventSubscriptions: Map<HTMLElement, Subscription[]> = new Map();
  private readonly debounceTimeMs = 50; // 防抖时间（毫秒）
  private isInIframe = false;
  private frameId: string;

  // 新增：跟踪最近处理的焦点事件，用于去重
  private lastFocusedFieldTimestamp: Map<HTMLElement, number> = new Map();
  private readonly focusDebounceTimeMs = 600; // 焦点事件去重的时间窗口（毫秒）
  private isInitialized = false;

  private crossIframePositionerService: CrossIframePositionerService;

  constructor(private formDetectionService: FormDetectionService) {
    // 生成唯一的框架ID
    this.frameId = this.generateFrameId();

    // 检查是否在iframe中
    this.isInIframe = window !== window.top;

    // 初始化跨iframe定位服务
    this.crossIframePositionerService = CrossIframePositionerService.getInstance();
  }

  /**
   * 获取字段焦点事件的Observable
   */
  get fieldFocused$(): Observable<FormFieldFocusEvent> {
    return this.fieldFocused.asObservable();
  }

  /**
   * 获取字段失焦事件的Observable
   */
  get fieldBlurred$(): Observable<FormField> {
    return this.fieldBlurred.asObservable();
  }

  /**
   * 获取当前焦点字段
   */
  getFocusedField(): FormField | null {
    return this.focusedField;
  }

  /**
   * 检查服务是否已初始化
   * @returns 服务是否已初始化
   */
  public getIsInitialized(): boolean {
    // 这里应该返回一个标志，表示服务是否已初始化
    return this.isInitialized;
  }

  /**
   * 初始化表单字段监听
   */
  initialize(): void {
    try {
      // 设置文档级事件委托
      this.setupDocumentEventDelegation();

      // 监听表单变化，为新表单字段添加事件监听
      this.formDetectionService.fieldsChanged$.subscribe(fields => {
        this.setupFieldListeners(fields);
      });

      // 初始检测
      this.formDetectionService.initialize();

      // 标记服务已初始化
      this.isInitialized = true;

      console.log('FormListenerService initialized');
    } catch (error) {
      console.error('Error initializing FormListenerService:', error);
    }
  }

  /**
   * 设置文档级事件委托
   * 使用事件委托模式减少事件监听器数量
   */
  private setupDocumentEventDelegation(): void {
    try {
      // 使用事件委托监听文档级别的焦点事件
      const focusSubscription = fromEvent<FocusEvent>(document, 'focusin')
        .pipe(
          debounceTime(this.debounceTimeMs),
          filter(event => this.isFormField(event.target as HTMLElement))
        )
        .subscribe(event => {
          const element = event.target as HTMLElement;
          const field = this.findFieldByElement(element);
          if (field) {
            this.handleFieldFocus(field, 'focusin');
          }
        });

      // 使用事件委托监听文档级别的失焦事件
      const blurSubscription = fromEvent<FocusEvent>(document, 'focusout')
        .pipe(
          debounceTime(this.debounceTimeMs),
          filter(event => this.isFormField(event.target as HTMLElement))
        )
        .subscribe(event => {
          const element = event.target as HTMLElement;
          const field = this.findFieldByElement(element);
          if (field) {
            this.handleFieldBlur(field);
          }
        });

      this.documentEventSubscriptions.push(
        focusSubscription,
        blurSubscription,
      );
    } catch (error) {
      console.error('Error setting up document event delegation:', error);
    }
  }

  /**
   * 为表单字段添加事件监听
   * 这是对文档级事件委托的补充，用于处理特定字段的自定义事件
   */
  private setupFieldListeners(fields: FormField[]): void {
    try {
      // 清理旧的事件监听
      this.cleanupFieldEventListeners();

      // 为每个字段添加事件监听
      for (const field of fields) {
        const element = field.element;

        // 跳过已经设置过监听器的元素
        if (this.fieldEventSubscriptions.has(element)) {
          continue;
        }

        const subscriptions: Subscription[] = [];

        // 监听鼠标进入事件，预加载相关资源
        const mouseEnterSubscription = fromEvent<MouseEvent>(element, 'mouseenter')
          .subscribe(() => {
            // 这里可以添加预加载逻辑，例如预加载密码列表
            // 暂时不做实现
          });

        // 在iframe中，不添加点击事件监听器，避免重复触发焦点事件
        // 因为focusin事件已经足够捕获焦点变化
        if (!this.isInIframe) {
          // 只在顶层页面添加点击事件监听
          const clickSubscription = fromEvent<MouseEvent>(element, 'click')
            .subscribe(() => {
              if (document.activeElement === element) {
                this.handleFieldFocus(field, 'click');
              }
            });
          subscriptions.push(clickSubscription);
        }

        subscriptions.push(mouseEnterSubscription);
        this.fieldEventSubscriptions.set(element, subscriptions);
      }
    } catch (error) {
      console.error('Error setting up field listeners:', error);
    }
  }

  /**
   * 处理字段获取焦点事件
   * 添加去重逻辑，避免短时间内重复触发
   * 在iframe中，总是通过background脚本转发焦点事件到顶层页面
   * 使用CrossIframePositionerService计算元素在顶层页面中的位置
   * @param field 表单字段
   * @param eventSource 事件来源，用于调试和日志
   */
  private async handleFieldFocus(field: FormField, eventSource: 'click' | 'focusin' | 'iframe-position-update' | 'scroll-end' | string = 'focusin'): Promise<void> {
    try {
      const element = field.element;
      const currentTime = Date.now();
      const lastFocusTime = this.lastFocusedFieldTimestamp.get(element) || 0;

      // 检查是否在去重时间窗口内
      if (currentTime - lastFocusTime < this.focusDebounceTimeMs) {
        // 在去重时间窗口内，忽略此次焦点事件
        console.debug(`Ignoring duplicate focus event from ${eventSource} for element`, element);
        return;
      }

      // 更新最后焦点时间
      this.lastFocusedFieldTimestamp.set(element, currentTime);

      // 更新当前焦点字段
      this.focusedField = field;

      // 获取当前域名和URL
      const domain = this.getCurrentDomain();
      const url = window.location.href;

      // 如果在iframe中，总是通过background脚本转发焦点事件
      if (this.isInIframe) {
        try {
          // 使用CrossIframePositionerService计算元素在顶层页面中的位置
          const position = await this.crossIframePositionerService.calculateElementPositionInTopFrame(field.element);

          // 创建焦点事件对象
          const focusEvent: FormFieldFocusEvent = {
            field,
            position: this.convertElementPositionToDOMRect(position),
            domain,
            url,
            frameInfo: {
              frameId: this.frameId,
              frameRect: new DOMRect(0, 0, 0, 0) // 这个值会在background中被更新
            }
          };

          // 获取iframe嵌套层级和跨域信息
          const frameLevel = this.crossIframePositionerService['frameLevel']; // 访问私有属性
          const isCrossDomain = this.crossIframePositionerService['isCrossDomain']; // 访问私有属性
          const topWindowUrl = window.location.href; // 使用当前URL，在background中会被更新

          console.debug(`iframe焦点事件: 层级=${frameLevel}, 跨域=${isCrossDomain}, 位置计算完成`);

          // 在iframe中，将焦点事件发送给background
          this.sendFocusEventToBackground(focusEvent, frameLevel, topWindowUrl, isCrossDomain);
        } catch (error) {
          console.error('计算元素在顶层页面中的位置时出错:', error);

          // 如果计算失败，使用传统方法
          const position = this.calculateFieldPosition(field.element);

          // 创建焦点事件对象
          const focusEvent: FormFieldFocusEvent = {
            field,
            position,
            domain,
            url,
            frameInfo: {
              frameId: this.frameId,
              frameRect: new DOMRect(0, 0, 0, 0)
            }
          };

          // 计算iframe嵌套层级
          let frameLevel = 0;
          let currentWindow: Window = window;
          let topWindowUrl = '';
          let isCrossDomain = false;

          try {
            // 尝试遍历iframe层级
            while (currentWindow !== currentWindow.parent) {
              frameLevel++;
              currentWindow = currentWindow.parent;

              try {
                // 尝试访问父窗口的location
                topWindowUrl = currentWindow.location.href;
              } catch (e) {
                // 捕获跨域错误
                console.log('检测到跨域iframe层级');
                isCrossDomain = true;
                break;
              }
            }
          } catch (e) {
            // 捕获其他错误
            console.log('检测iframe层级时出错:', e);
            isCrossDomain = true;
            topWindowUrl = window.location.href;
          }

          // 如果没有成功获取顶层窗口URL，使用当前窗口URL
          if (!topWindowUrl) {
            topWindowUrl = window.location.href;
          }

          console.debug(`iframe焦点事件(传统方法): 层级=${frameLevel}, 跨域=${isCrossDomain}, 顶层URL=${topWindowUrl}`);

          // 在iframe中，将焦点事件发送给background
          this.sendFocusEventToBackground(focusEvent, frameLevel, topWindowUrl, isCrossDomain);
        }
      } else {
        // 在顶层页面中，直接计算位置并触发焦点事件
        const position = this.calculateFieldPosition(field.element);

        // 创建焦点事件对象
        const focusEvent: FormFieldFocusEvent = {
          field,
          position,
          domain,
          url
        };

        // 直接触发焦点事件
        this.fieldFocused.next(focusEvent);
      }

      console.debug(`Field focus event processed from ${eventSource}`, field);
    } catch (error) {
      console.error('Error handling field focus:', error);
    }
  }

  /**
   * 将ElementPosition转换为DOMRect
   * @param position ElementPosition对象
   * @returns DOMRect对象
   */
  private convertElementPositionToDOMRect(position: any): DOMRect {
    return new DOMRect(
      position.left,
      position.top,
      position.width,
      position.height
    );
  }

  /**
   * 处理字段失去焦点事件
   */
  private handleFieldBlur(field: FormField): void {
    try {
      // 如果当前焦点字段是该字段，则清除焦点字段
      if (this.focusedField === field) {
        this.focusedField = null;
      }

      // 发送失焦事件
      this.fieldBlurred.next(field);
    } catch (error) {
      console.error('Error handling field blur:', error);
    }
  }

  /**
   * 计算字段位置
   */
  private calculateFieldPosition(element: HTMLElement): DOMRect {
    try {
      // 获取元素的位置和尺寸
      const rect = element.getBoundingClientRect();

      // 创建新的DOMRect，包含滚动位置
      return new DOMRect(
        rect.left,
        rect.top,
        rect.width,
        rect.height
      );
    } catch (error) {
      console.error('Error calculating field position:', error);
      // 返回默认位置
      return new DOMRect(0, 0, 0, 0);
    }
  }

  /**
   * 获取当前域名
   */
  private getCurrentDomain(): string {
    try {
      return window.location.hostname;
    } catch (error) {
      console.error('Error getting current domain:', error);
      return '';
    }
  }

  /**
   * 检查元素是否为表单字段
   */
  private isFormField(element: HTMLElement): boolean {
    if (!element) {
      return false;
    }

    const tagName = element.tagName.toLowerCase();

    // 检查是否为输入元素
    if (tagName === 'input') {
      const inputType = (element as HTMLInputElement).type.toLowerCase();
      // 排除不需要处理的输入类型
      const excludedTypes = ['submit', 'reset', 'button', 'image', 'file', 'checkbox', 'radio', 'hidden'];
      return !excludedTypes.includes(inputType);
    }

    // 检查是否为文本区域或选择元素
    return tagName === 'textarea' || tagName === 'select';
  }

  /**
   * 根据元素查找对应的表单字段
   */
  private findFieldByElement(element: HTMLElement): FormField | null {
    const fields = this.formDetectionService.getFormFields();
    return fields.find(field => field.element === element) || null;
  }

  /**
   * 生成唯一的框架ID
   */
  private generateFrameId(): string {
    return generateUniqueId();
  }

  /**
   * 清理字段事件监听
   */
  private cleanupFieldEventListeners(): void {
    try {
      // 取消所有字段事件订阅
      for (const subscriptions of this.fieldEventSubscriptions.values()) {
        for (const subscription of subscriptions) {
          subscription.unsubscribe();
        }
      }

      // 清空映射
      this.fieldEventSubscriptions.clear();
    } catch (error) {
      console.error('Error cleaning up field event listeners:', error);
    }
  }

  /**
   * 将焦点事件发送给 background
   * 使用 RuntimeMessageService.sendMessageToBackground 发送消息
   * background 脚本会将消息转发到顶层页面
   * @param event 焦点事件对象
   * @param frameLevel iframe 嵌套层级
   * @param topFrameUrl 顶层页面 URL
   * @param isCrossDomain 是否是跨域 iframe
   */
  private sendFocusEventToBackground(event: FormFieldFocusEvent, frameLevel: number, topFrameUrl: string, isCrossDomain: boolean = false): void {
    try {
      console.debug(`准备发送iframe焦点事件到background: 层级=${frameLevel}, 跨域=${isCrossDomain}`);

      // 创建可序列化的事件对象
      const serializableEvent = this.createSerializableEvent(event, frameLevel, topFrameUrl, isCrossDomain);

      if (!serializableEvent) {
        console.error('创建可序列化事件对象失败，无法发送焦点事件');
        return;
      }

      // 构建消息对象
      const message: Omit<RuntimeMessage<any>, 'messageId'> = {
        type: BackgroundMessageType.IFRAME_FOCUS_EVENT,
        payload: serializableEvent
      };

      console.debug('发送iframe焦点事件到background:', {
        type: message.type,
        field: serializableEvent.field.id || serializableEvent.field.name,
        isPassword: serializableEvent.field.isPassword,
        isUsername: serializableEvent.field.isUsername,
        domain: serializableEvent.domain,
        frameLevel: serializableEvent.frameInfo?.frameLevel,
        isCrossDomain: serializableEvent.frameInfo?.isCrossDomain
      });

      // 使用 RuntimeMessageService 发送消息给 background
      RuntimeMessageService.sendMessageToBackground<any, any>(message)
        .subscribe({
          next: (response: { success: boolean; error?: string; data?: any; messageId: string }) => {
            if (response.success) {
              console.debug('焦点事件已成功发送到 background，响应:', response);
            } else {
              console.error('发送焦点事件到 background 时出错:', response.error);
              // 可以在这里添加重试逻辑
            }
          },
          error: (error: Error) => {
            console.error('发送焦点事件到 background 时出错:', error);
            // 可以在这里添加重试逻辑
          }
        });
    } catch (error) {
      console.error('发送焦点事件到 background 时出错:', error);
    }
  }

  /**
   * 创建可序列化的事件对象
   * DOMRect 对象不能直接序列化，需要转换为普通对象
   * @param event 原始焦点事件对象
   * @param frameLevel iframe 嵌套层级
   * @param topFrameUrl 顶层页面 URL
   * @param isCrossDomain 是否是跨域 iframe
   * @returns 可序列化的事件对象
   */
  private createSerializableEvent(event: FormFieldFocusEvent, frameLevel: number, topFrameUrl: string, isCrossDomain: boolean = false): any {
    try {
      // 转换 DOMRect 为普通对象
      const serializablePosition = {
        top: event.position.top,
        left: event.position.left,
        width: event.position.width,
        height: event.position.height,
        bottom: event.position.bottom,
        right: event.position.right,
        x: event.position.x,
        y: event.position.y
      };

      // 转换字段对象为可序列化对象
      const serializableField = {
        id: event.field.id || '',
        name: event.field.name || '',
        type: event.field.type || '',
        isPassword: event.field.isPassword,
        isUsername: event.field.isUsername,
        // 表单信息可能不存在
        form: undefined
      };

      // 转换 frameInfo 中的 DOMRect
      let serializableFrameInfo = null;
      if (event.frameInfo) {
        serializableFrameInfo = {
          frameId: event.frameInfo.frameId,
          frameRect: {
            top: event.frameInfo.frameRect.top,
            left: event.frameInfo.frameRect.left,
            width: event.frameInfo.frameRect.width,
            height: event.frameInfo.frameRect.height,
            bottom: event.frameInfo.frameRect.bottom,
            right: event.frameInfo.frameRect.right,
            x: event.frameInfo.frameRect.x,
            y: event.frameInfo.frameRect.y
          },
          frameLevel: frameLevel,
          topFrameUrl: topFrameUrl,
          isCrossDomain: isCrossDomain // 添加跨域标志
        };
      }

      // 创建可序列化的事件对象
      return {
        field: serializableField,
        position: serializablePosition,
        domain: event.domain,
        url: event.url,
        frameInfo: serializableFrameInfo
      };
    } catch (error) {
      console.error('创建可序列化事件对象时出错:', error);
      return null;
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    try {
      // 取消文档事件订阅
      for (const subscription of this.documentEventSubscriptions) {
        subscription.unsubscribe();
      }
      this.documentEventSubscriptions = [];

      // 清理字段事件监听
      this.cleanupFieldEventListeners();

      // 清理焦点事件跟踪
      this.lastFocusedFieldTimestamp.clear();

      // 完成Subject
      this.fieldFocused.complete();
      this.fieldBlurred.complete();

      // 标记服务未初始化
      this.isInitialized = false;

      console.log('FormListenerService disposed');
    } catch (error) {
      console.error('Error disposing FormListenerService:', error);
    }
  }
}
