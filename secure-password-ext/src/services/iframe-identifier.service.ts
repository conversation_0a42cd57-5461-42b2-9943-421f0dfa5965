import { RuntimeMessageService } from './runtime-message.service';
import { BackgroundMessageType } from './background-message.service';

/**
 * iframe标识符服务
 * 用于在iframe加载时为其添加标识符，并提供更准确的iframe定位方法
 */
export class IframeIdentifierService {
  private static instance: IframeIdentifierService;
  private isInIframe: boolean;
  private frameId: number | null = null;
  private initialized: boolean = false;

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    // 检查是否在iframe中
    this.isInIframe = window !== window.top;
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): IframeIdentifierService {
    if (!IframeIdentifierService.instance) {
      IframeIdentifierService.instance = new IframeIdentifierService();
    }
    return IframeIdentifierService.instance;
  }

  /**
   * 初始化服务
   * 获取当前iframe的frameId，并为iframe元素添加标识符
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 获取当前frameId
      this.frameId = await this.getCurrentFrameId();

      if (this.isInIframe && this.frameId !== null) {
        // 为当前iframe添加标识符
        this.markCurrentIframe();
      }

      // 如果是顶层页面，为所有iframe添加标识符
      if (!this.isInIframe) {
        this.markAllIframes();
      }

      this.initialized = true;
      console.log(`IframeIdentifierService initialized, frameId: ${this.frameId}`);
    } catch (error) {
      console.error('初始化IframeIdentifierService时出错:', error);
    }
  }

  /**
   * 获取当前iframe的frameId
   * @returns Promise<number | null> 当前iframe的frameId，如果不在iframe中或获取失败则返回null
   */
  private async getCurrentFrameId(): Promise<number | null> {
    if (!this.isInIframe) {
      return 0; // 顶层页面的frameId为0
    }

    try {
      // 向background发送消息，获取当前frameId
      const response = await RuntimeMessageService.sendMessageToBackgroundAsPromise<
        { url: string },
        { frameId: number }
      >({
        type: BackgroundMessageType.GET_CURRENT_FRAME_ID,
        payload: { url: window.location.href }
      });

      if (response.success && response.data && typeof response.data.frameId === 'number') {
        return response.data.frameId;
      }
    } catch (error) {
      console.error('获取当前frameId时出错:', error);
    }

    return null;
  }

  /**
   * 为当前iframe添加标识符
   * 在iframe元素上添加data-frame-id属性
   */
  private markCurrentIframe(): void {
    try {
      // 向父页面发送消息，请求为当前iframe添加标识符
      window.parent.postMessage({
        action: 'MARK_IFRAME',
        frameId: this.frameId,
        url: window.location.href
      }, '*');

      console.log(`已请求父页面为当前iframe添加标识符, frameId: ${this.frameId}`);
    } catch (error) {
      console.error('请求父页面为当前iframe添加标识符时出错:', error);
    }
  }

  /**
   * 为所有iframe添加标识符
   * 监听来自子iframe的消息，为其添加标识符
   */
  private markAllIframes(): void {
    // 监听来自子iframe的消息
    window.addEventListener('message', (event) => {
      try {
        const { action, frameId, url } = event.data || {};

        if (action === 'MARK_IFRAME' && typeof frameId === 'number') {
          // 查找对应的iframe元素
          const iframes = document.querySelectorAll('iframe');
          let found = false;

          for (const iframe of Array.from(iframes)) {
            try {
              // 尝试通过URL匹配
              if (iframe.src === url || iframe.contentWindow?.location.href === url) {
                // 为iframe添加标识符
                iframe.setAttribute('data-secure-password-ext-frame-id', String(frameId));
                // 添加时间戳作为额外验证
                iframe.setAttribute('data-secure-password-ext-frame-timestamp', String(Date.now()));
                // 添加混淆值作为额外验证
                iframe.setAttribute('data-secure-password-ext-frame-hash', this.generateFrameHash(frameId, url));
                console.log(`已为iframe添加标识符, frameId: ${frameId}, url: ${url}`);
                found = true;
                break;
              }
            } catch (e) {
              // 忽略跨域错误
            }
          }

          if (!found) {
            console.warn(`未找到对应的iframe, frameId: ${frameId}, url: ${url}`);
          }
        }
      } catch (error) {
        console.error('处理iframe标识消息时出错:', error);
      }
    });
  }

  /**
   * 生成iframe的混淆哈希值
   * @param frameId iframe的ID
   * @param url iframe的URL
   * @returns string 混淆后的哈希值
   */
  private generateFrameHash(frameId: number, url: string): string {
    // 简单的混淆算法，实际应用中可以使用更复杂的算法
    const baseString = `${frameId}-${url}-${Date.now()}`;
    let hash = 0;

    for (let i = 0; i < baseString.length; i++) {
      const char = baseString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    // 转换为16进制字符串并添加前缀
    return 'sp-' + Math.abs(hash).toString(16).padStart(8, '0');
  }

  /**
   * 获取指定frameId的iframe元素
   * @param frameId 要查找的frameId
   * @returns HTMLIFrameElement | null 找到的iframe元素或null
   */
  public static findIframeByFrameId(frameId: number): HTMLIFrameElement | null {
    try {
      // 首先尝试使用主要标识符查找
      const iframe = document.querySelector(`iframe[data-secure-password-ext-frame-id="${frameId}"]`);
      if (iframe) {
        return iframe as HTMLIFrameElement;
      }

      // 如果找不到，尝试遍历所有iframe，检查是否有匹配的标识符
      const iframes = document.querySelectorAll('iframe');
      for (const frame of Array.from(iframes)) {
        try {
          // 检查是否有我们的标识符
          const frameIdAttr = frame.getAttribute('data-secure-password-ext-frame-id');
          if (frameIdAttr && parseInt(frameIdAttr, 10) === frameId) {
            return frame as HTMLIFrameElement;
          }

          // 检查是否有我们的其他标识符
          const frameHashAttr = frame.getAttribute('data-secure-password-ext-frame-hash');
          if (frameHashAttr && frameHashAttr.startsWith('sp-')) {
            // 这可能是我们标记的iframe，但标识符被修改了
            // 可以通过其他方式验证，如检查URL等
            console.log(`找到可能的iframe，hash: ${frameHashAttr}`);
          }
        } catch (e) {
          // 忽略跨域错误
        }
      }

      // 如果还是找不到，尝试使用其他方法，如位置、大小等特征
      console.warn(`无法通过标识符找到frameId为${frameId}的iframe，尝试使用其他方法`);

      return null;
    } catch (error) {
      console.error(`查找frameId为${frameId}的iframe时出错:`, error);
      return null;
    }
  }

  /**
   * 获取指定frameId的iframe元素位置
   * @param frameId 要查找的frameId
   * @returns DOMRect | null iframe的位置，如果找不到则返回null
   */
  public static getIframePosition(frameId: number): DOMRect | null {
    const iframe = IframeIdentifierService.findIframeByFrameId(frameId);
    if (iframe) {
      return iframe.getBoundingClientRect();
    }

    // 如果找不到iframe，尝试使用消息通信获取位置
    console.warn(`无法找到frameId为${frameId}的iframe，无法获取位置`);
    return null;
  }

  /**
   * 获取当前frameId
   * @returns number | null 当前frameId，如果未初始化或获取失败则返回null
   */
  public getFrameId(): number | null {
    return this.frameId;
  }


}
