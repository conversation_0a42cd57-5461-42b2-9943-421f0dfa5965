import { MessageRouterService } from './message-router.service';
import { BackgroundMessageType } from './background-message.service';
import { RuntimeMessage, RuntimeMessageResponse } from './runtime-message.service';

/**
 * iframe树节点接口
 * 表示iframe的层级结构
 */
export interface IframeNode {
  frameId: number;
  parentFrameId: number;
  url: string;
  children: IframeNode[];
  level?: number; // 嵌套层级，0表示顶层页面
}

/**
 * iframe树结构接口
 * 以frameId为键，存储所有iframe节点
 */
export interface IframeTree {
  [frameId: number]: IframeNode;
}

/**
 * iframe树查询结果接口
 */
export interface IframeTreeQueryResult {
  success: boolean;
  tree?: IframeTree;
  error?: string;
}

/**
 * iframe树服务
 * 用于构建和管理当前网页的iframe树结构
 */
export class IframeTreeService {
  private static instance: IframeTreeService;

  // 存储每个标签页的iframe树结构
  private iframeTreeMap: Map<number, IframeTree> = new Map();

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    this.setupNavigationListener();
    this.setupMessageListeners();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): IframeTreeService {
    if (!IframeTreeService.instance) {
      IframeTreeService.instance = new IframeTreeService();
    }
    return IframeTreeService.instance;
  }

  /**
   * 设置导航监听器
   * 监听网页导航完成事件，构建iframe树结构
   */
  private setupNavigationListener(): void {
    // 监听主框架导航完成事件
    chrome.webNavigation.onCompleted.addListener((details) => {
      // 当主框架导航完成时，获取所有框架
      if (details.frameId === 0) {
        this.buildIframeTree(details.tabId);
      }
    }, { url: [{ schemes: ['http', 'https'] }] });

    // 监听框架导航完成事件
    chrome.webNavigation.onDOMContentLoaded.addListener((details) => {
      // 当任何框架的DOM加载完成时，更新iframe树结构
      this.updateIframeTree(details.tabId, details.frameId, details.url);
    }, { url: [{ schemes: ['http', 'https'] }] });

    console.log('IframeTreeService navigation listeners registered');
  }

  /**
   * 设置消息监听器
   * 注册消息处理器到消息路由服务
   */
  private setupMessageListeners(): void {
    // 获取消息路由服务实例
    const messageRouter = MessageRouterService.getInstance();

    // 注册消息处理器
    messageRouter.registerHandler({
      // 定义此服务能处理的消息类型
      messageTypes: [
        BackgroundMessageType.GET_IFRAME_TREE,
        BackgroundMessageType.IFRAME_FOCUS_EVENT
      ],

      // 处理消息的方法
      handleMessage: async (message: RuntimeMessage<any>, sender: chrome.runtime.MessageSender, _sendResponse) => {
        console.log('IframeTreeService handling message:', message.type);

        // 根据消息类型处理不同的请求
        if (message.type === BackgroundMessageType.GET_IFRAME_TREE) {
          return this.handleGetIframeTree(message, sender);
        }

        if (message.type === BackgroundMessageType.IFRAME_FOCUS_EVENT) {
          // 在处理iframe焦点事件前，确保我们有最新的iframe树结构
          if (sender.tab?.id) {
            await this.buildIframeTree(sender.tab.id);
          }
          // 继续正常处理流程，返回成功响应
          return {
            success: true,
            data: { message: 'iframe树结构已更新' },
            messageId: message.messageId
          };
        }

        // 不处理其他类型的消息，返回错误响应
        return {
          success: false,
          error: `未知消息类型: ${message.type}`,
          messageId: message.messageId
        };
      }
    });

    console.log('IframeTreeService message listeners registered');
  }

  /**
   * 构建iframe树结构
   * @param tabId 标签页ID
   */
  private async buildIframeTree(tabId: number): Promise<void> {
    try {
      // 获取标签页中的所有框架
      const frames = await this.getAllFrames(tabId);

      if (!frames || frames.length === 0) {
        console.log(`标签页 ${tabId} 中没有找到框架`);
        return;
      }

      // 构建树形结构
      const frameTree: IframeTree = {};
      const frameMap: { [frameId: number]: IframeNode } = {};

      // 初始化框架映射
      frames.forEach(frame => {
        frameMap[frame.frameId] = {
          frameId: frame.frameId,
          parentFrameId: frame.parentFrameId,
          url: frame.url,
          children: [],
          level: this.calculateFrameLevel(frame.frameId, frame.parentFrameId, frames)
        };
      });

      // 构建父子关系
      frames.forEach(frame => {
        if (frame.parentFrameId !== -1 && frameMap[frame.parentFrameId]) {
          frameMap[frame.parentFrameId].children.push(frameMap[frame.frameId]);
        } else {
          frameTree[frame.frameId] = frameMap[frame.frameId];
        }
      });

      // 存储树形结构
      this.iframeTreeMap.set(tabId, frameTree);

      console.log(`标签页 ${tabId} 的iframe树结构已构建:`, frameTree);
    } catch (error) {
      console.error(`构建标签页 ${tabId} 的iframe树结构时出错:`, error);
    }
  }

  /**
   * 更新iframe树结构
   * @param tabId 标签页ID
   * @param frameId 框架ID
   * @param url 框架URL
   */
  private async updateIframeTree(tabId: number, frameId: number, url: string): Promise<void> {
    try {
      // 获取当前的iframe树结构
      const tree = this.iframeTreeMap.get(tabId);

      if (!tree) {
        // 如果树结构不存在，重新构建
        await this.buildIframeTree(tabId);
        return;
      }

      // 查找并更新框架节点
      this.updateFrameNode(tree, frameId, url);

      console.log(`标签页 ${tabId} 的iframe树结构已更新，frameId=${frameId}, url=${url}`);
    } catch (error) {
      console.error(`更新标签页 ${tabId} 的iframe树结构时出错:`, error);
    }
  }

  /**
   * 更新框架节点
   * @param tree iframe树结构
   * @param frameId 框架ID
   * @param url 框架URL
   */
  private updateFrameNode(tree: IframeTree, frameId: number, url: string): void {
    // 直接查找顶层节点
    if (tree[frameId]) {
      tree[frameId].url = url;
      return;
    }

    // 递归查找子节点
    Object.values(tree).forEach(node => {
      this.updateFrameNodeRecursive(node, frameId, url);
    });
  }

  /**
   * 递归更新框架节点
   * @param node 当前节点
   * @param frameId 要更新的框架ID
   * @param url 新的URL
   * @returns 是否找到并更新了节点
   */
  private updateFrameNodeRecursive(node: IframeNode, frameId: number, url: string): boolean {
    if (node.frameId === frameId) {
      node.url = url;
      return true;
    }

    for (const child of node.children) {
      if (this.updateFrameNodeRecursive(child, frameId, url)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 计算框架嵌套层级
   * @param frameId 框架ID
   * @param parentFrameId 父框架ID
   * @param frames 所有框架列表
   * @returns 嵌套层级，0表示顶层页面
   */
  private calculateFrameLevel(frameId: number, parentFrameId: number, frames: chrome.webNavigation.GetAllFrameResultDetails[]): number {
    if (parentFrameId === -1) {
      return 0; // 顶层页面
    }

    let level = 1;
    let currentParentId = parentFrameId;

    while (currentParentId !== -1) {
      level++;
      const parentFrame = frames.find(f => f.frameId === currentParentId);
      if (!parentFrame) break;
      currentParentId = parentFrame.parentFrameId;
    }

    return level;
  }

  /**
   * 获取标签页中的所有框架
   * @param tabId 标签页ID
   * @returns Promise<chrome.webNavigation.GetAllFrameResultDetails[]> 框架列表
   */
  private getAllFrames(tabId: number): Promise<chrome.webNavigation.GetAllFrameResultDetails[]> {
    return new Promise((resolve, reject) => {
      chrome.webNavigation.getAllFrames({ tabId }, (frames) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }

        if (!frames) {
          resolve([]);
          return;
        }

        resolve(frames);
      });
    });
  }

  /**
   * 处理获取iframe树结构请求
   * @param message 消息对象
   * @param sender 发送者信息
   * @returns Promise<RuntimeMessageResponse<IframeTreeQueryResult>> 响应对象
   */
  private async handleGetIframeTree(
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender
  ): Promise<RuntimeMessageResponse<IframeTreeQueryResult>> {
    try {
      // 获取请求的标签页ID
      const tabId = message.payload?.tabId || sender.tab?.id;

      if (!tabId) {
        throw new Error('无法获取标签页ID');
      }

      // 获取iframe树结构
      let tree = this.iframeTreeMap.get(tabId);

      // 如果树结构不存在，尝试构建
      if (!tree) {
        await this.buildIframeTree(tabId);
        tree = this.iframeTreeMap.get(tabId);
      }

      return {
        success: true,
        data: {
          success: true,
          tree: tree || {}
        },
        messageId: message.messageId
      };
    } catch (error) {
      console.error('处理获取iframe树结构请求时出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messageId: message.messageId
      };
    }
  }

  /**
   * 在iframe树中查找指定frameId的节点
   * @param tree iframe树结构
   * @param frameId 要查找的frameId
   * @returns IframeNode | null 找到的节点或null
   */
  public findFrameNodeInTree(tree: IframeTree, frameId: number): IframeNode | null {
    // 直接查找顶层节点
    if (tree[frameId]) {
      return tree[frameId];
    }

    // 递归查找子节点
    for (const node of Object.values(tree)) {
      const foundNode = this.findFrameNodeRecursive(node, frameId);
      if (foundNode) {
        return foundNode;
      }
    }

    return null;
  }

  /**
   * 递归查找iframe节点
   * @param node 当前节点
   * @param frameId 要查找的frameId
   * @returns IframeNode | null 找到的节点或null
   */
  private findFrameNodeRecursive(node: IframeNode, frameId: number): IframeNode | null {
    if (node.frameId === frameId) {
      return node;
    }

    for (const child of node.children) {
      const foundNode = this.findFrameNodeRecursive(child, frameId);
      if (foundNode) {
        return foundNode;
      }
    }

    return null;
  }

  /**
   * 构建从当前iframe到顶层页面的路径
   * @param tree iframe树结构
   * @param currentNode 当前iframe节点
   * @returns IframeNode[] 从当前iframe到顶层页面的路径（从顶层到当前节点的顺序）
   */
  public buildPathToTop(tree: IframeTree, currentNode: IframeNode): IframeNode[] {
    const path: IframeNode[] = [];
    let node: IframeNode | null = currentNode;

    // 将当前节点添加到路径
    path.unshift(node);

    // 向上查找父节点，直到找到顶层页面
    while (node && node.parentFrameId !== -1) {
      const parentNode = this.findFrameNodeInTree(tree, node.parentFrameId);
      if (parentNode) {
        path.unshift(parentNode);
        node = parentNode;
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * 构建从顶层页面到当前iframe的路径
   * @param tree iframe树结构
   * @param currentNode 当前iframe节点
   * @returns IframeNode[] 从顶层页面到当前iframe的路径（从当前节点到顶层的顺序）
   */
  public buildTopToPath(tree: IframeTree, currentNode: IframeNode): IframeNode[] {
    return this.buildPathToTop(tree, currentNode).reverse();
  }
}
