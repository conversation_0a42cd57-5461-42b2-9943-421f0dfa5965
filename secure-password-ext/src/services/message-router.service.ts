import { RuntimeMessageService, RuntimeMessage, RuntimeMessageResponse } from './runtime-message.service';
import { BackgroundMessageType } from './background-message.service';

/**
 * 消息处理器接口
 * 定义消息处理器的结构
 */
export interface MessageHandler {
  // 处理器能处理的消息类型列表
  messageTypes: string[];

  // 处理消息的方法
  handleMessage: (
    message: RuntimeMessage<any>,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response: RuntimeMessageResponse<any>) => void,
    signal?: AbortSignal
  ) => boolean | void | Promise<RuntimeMessageResponse<any>>;
}

/**
 * 消息路由服务
 * 负责将消息路由到正确的处理器
 */
export class MessageRouterService {
  private static instance: MessageRouterService;
  private handlers: MessageHandler[] = [];
  private initialized = false;

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {}

  /**
   * 获取服务实例
   */
  public static getInstance(): MessageRouterService {
    if (!MessageRouterService.instance) {
      MessageRouterService.instance = new MessageRouterService();
    }
    return MessageRouterService.instance;
  }

  /**
   * 注册消息处理器
   * @param handler 消息处理器
   */
  public registerHandler(handler: MessageHandler): void {
    // 检查是否已经注册了相同的处理器
    const existingHandler = this.handlers.find(h =>
      h === handler ||
      h.messageTypes.some(type => handler.messageTypes.includes(type))
    );

    if (existingHandler) {
      console.warn('消息处理器已注册或存在处理相同消息类型的处理器');
      // 合并消息类型
      existingHandler.messageTypes = [...new Set([...existingHandler.messageTypes, ...handler.messageTypes])];
      return;
    }

    // 添加新的处理器
    this.handlers.push(handler);
    console.log(`已注册处理器，处理消息类型: ${handler.messageTypes.join(', ')}`);

    // 如果已经初始化，则不需要再次初始化
    if (!this.initialized) {
      this.initialize();
    }
  }

  /**
   * 初始化消息路由服务
   * 设置全局消息监听器
   */
  private initialize(): void {
    if (this.initialized) {
      return;
    }

    // 设置全局消息监听器
    RuntimeMessageService.onMessage((message, sender, sendResponse, signal) => {
      console.log('MessageRouterService received message:', message.type);

      // 查找能处理该消息类型的处理器
      const handler = this.handlers.find(h => h.messageTypes.includes(message.type));

      if (handler) {
        try {
          // 调用处理器处理消息
          try {
            const result = handler.handleMessage(message, sender, sendResponse, signal);

            // 如果处理器返回 Promise
            if (result instanceof Promise) {
              // 使用 Promise 处理异步结果
              result
                .then(response => {
                  if (response && !signal?.aborted) {
                    sendResponse(response);
                  }
                })
                .catch(error => {
                  if (!signal?.aborted) {
                    console.error('处理消息时出错:', error);
                    sendResponse({
                      success: false,
                      error: error instanceof Error ? error.message : String(error),
                      messageId: message.messageId
                    });
                  }
                });

              return true; // 表示会异步发送响应
            }

            // 如果处理器返回 true，表示它会自己处理异步响应
            if (result === true) {
              return true;
            }

            // 如果处理器返回 undefined 或 false，表示已同步处理完成
            return false;
          } catch (error) {
            // 处理同步错误
            console.error('处理消息时出错:', error);
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : String(error),
              messageId: message.messageId
            });
            return false;
          }
        } catch (error) {
          console.error('处理消息时出错:', error);
          sendResponse({
            success: false,
            error: error instanceof Error ? error.message : String(error),
            messageId: message.messageId
          });
          return false;
        }
      } else {
        // 没有找到处理器，返回未知消息类型错误
        console.warn(`未找到处理消息类型 ${message.type} 的处理器`);
        sendResponse({
          success: false,
          error: `未知消息类型: ${message.type}`,
          messageId: message.messageId
        });
        return false;
      }
    });

    this.initialized = true;
    console.log('MessageRouterService initialized');
  }

  /**
   * 获取已注册的消息类型列表
   * @returns 已注册的消息类型列表
   */
  public getRegisteredMessageTypes(): string[] {
    return this.handlers.reduce((types, handler) => {
      return [...types, ...handler.messageTypes];
    }, [] as string[]);
  }
}
