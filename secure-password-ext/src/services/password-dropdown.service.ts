import { Observable, Subject, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { RuntimeMessageService, RuntimeMessage } from './runtime-message.service';
import { FormFieldFocusEvent } from './form-listener.service';
import { FormField } from './form-detection.service';
import { BackgroundMessageType, IframeFocusEvent } from './background-message.service';
import { ShadowDomService } from './shadow-dom.service';
import type { CredentialOutput } from '../types';
import * as React from 'react';
import * as ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import { StyleProvider } from '@ant-design/cssinjs';
import zhCN from 'antd/lib/locale/zh_CN';
import PasswordDropdown from '../components/PasswordDropdown';

export interface Credential {
  id: string;
  username: string;
  password: string;
  serviceName: string;
  url: string;
}

export interface DropdownPosition {
  top: number;
  left: number;
  width: number;
  height?: number;
}

export interface PasswordFillRequest {
  fieldId: string;
  credential: Credential;
  frameInfo?: {
    frameId: string;
    frameRect: DOMRect;
  };
}

/**
 * 密码下拉菜单服务
 * 负责处理密码列表获取、显示下拉菜单、填充密码等功能
 */
export class PasswordDropdownService {
  private static instance: PasswordDropdownService;
  private dropdownOpened = new Subject<DropdownPosition>();
  private dropdownClosed = new Subject<void>();
  private credentialSelected = new Subject<Credential>();
  private currentField: FormField | null = null;
  private currentPosition: DropdownPosition | null = null;
  private dropdownVisible = false;
  private dropdownElement: HTMLElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private shadowDomService: ShadowDomService;
  private dropdownRoot: ReactDOM.Root | null = null;

  // 存储最后的凭证和位置以便重用
  private lastCredentials: CredentialOutput[] = [];
  private lastTargetFieldId: string | null = null;
  private lastIframeData: { frameId: number, frameRect: DOMRect | null } | undefined = undefined;

  /**
   * 获取PasswordDropdownService的单例实例
   */
  public static getInstance(): PasswordDropdownService {
    if (!PasswordDropdownService.instance) {
      PasswordDropdownService.instance = new PasswordDropdownService();
    }
    return PasswordDropdownService.instance;
  }

  /**
   * 私有构造函数，强制使用单例模式
   */
  private constructor() {
    this.shadowDomService = new ShadowDomService();
  }

  /**
   * 获取下拉菜单打开事件的Observable
   */
  get dropdownOpened$(): Observable<DropdownPosition> {
    return this.dropdownOpened.asObservable();
  }

  /**
   * 获取下拉菜单关闭事件的Observable
   */
  get dropdownClosed$(): Observable<void> {
    return this.dropdownClosed.asObservable();
  }

  /**
   * 获取凭证选择事件的Observable
   */
  get credentialSelected$(): Observable<Credential> {
    return this.credentialSelected.asObservable();
  }

  /**
   * 初始化密码下拉菜单服务
   */
  initialize(): void {
    // 不再立即创建Shadow DOM容器，而是在表单字段获取焦点时创建

    // 监听点击事件，处理下拉菜单外部点击关闭
    document.addEventListener('click', this.handleDocumentClick.bind(this));

    // 监听窗口大小变化，更新下拉菜单位置
    // window.addEventListener('resize', this.handleWindowResize.bind(this));

    // 监听滚动事件，更新下拉菜单位置
    // window.addEventListener('scroll', this.handleWindowScroll.bind(this), true);

    // 设置消息监听，接收来自 background 的焦点事件消息
    this.setupMessageListener();

    console.log('PasswordDropdownService initialized');
  }

  /**
   * 设置消息监听
   * 监听来自 background 的焦点事件消息
   */
  private setupMessageListener(): void {
    try {
      const isInIframe = window !== window.top;

      // 监听来自 background 的消息
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        try {
          // 检查消息类型
          if (
            !isInIframe &&
            message.type === BackgroundMessageType.FORWARDED_IFRAME_FOCUS_EVENT
          ) {
            console.debug('收到来自 background 的转发 iframe 焦点事件:', message.data);

            // 处理来自 iframe 的焦点事件
            this.handleIframeFocusEvent(message.data);

            // 发送响应
            sendResponse({ status: 'received' });
            return true;
          } else if (
            isInIframe &&
            message.type === BackgroundMessageType.FORWARDED_IFRAME_CREDENTIAL_FILL
          ) {
            console.log('处理转发的 iframe 凭证填充请求:', message);
            this.handleCredentialFillRequest(message.data);
            sendResponse({ status: 'received' });
            return true;
          }
        } catch (error) {
          console.error('处理来自 background 的转发 iframe 焦点事件时出错:', error);
          sendResponse({ status: 'error', error: error instanceof Error ? error.message : String(error) });
          return false;
        }
      });

      console.debug('已设置 iframe 焦点事件消息监听器');
    } catch (error) {
      console.error('设置消息监听器时出错:', error);
    }
  }

  /**
   * 处理来自 iframe 的焦点事件
   * 在顶层页面中渲染密码下拉列表
   * @param eventData 来自 iframe 的焦点事件数据
   */
  private handleIframeFocusEvent(eventData: IframeFocusEvent): void {
    try {
      // 只处理密码字段或用户名字段
      if (!eventData.field.isPassword && !eventData.field.isUsername) {
        return;
      }

      console.debug('处理来自 iframe 的焦点事件:', eventData);

      // 检查是否是跨域 iframe
      const isCrossDomain = eventData.frameInfo?.isCrossDomain || false;
      console.log(`处理${isCrossDomain ? '跨域' : '同域'} iframe 焦点事件`);

      // 创建一个虚拟的表单字段对象
      // 创建一个虚拟的 DOM 元素，因为 FormField 需要一个 HTMLElement
      // 在虚拟字段ID中包含时间戳和原始消息ID（如果有）
      const timestamp = Date.now();
      const originalMessageId = eventData.frameInfo?.originalMessageId || '';
      const originalFrameId = eventData.frameInfo?.originalFrameId || '';

      const virtualElement = document.createElement('div');
      virtualElement.id = originalMessageId
          ? `virtual-field-${timestamp}-${originalMessageId}-${originalFrameId}`
          : `virtual-field-${timestamp}`;

      const virtualField: FormField = {
        id: eventData.field.id || '',
        name: eventData.field.name || '',
        type: eventData.field.type || 'text',
        isPassword: eventData.field.isPassword || false,
        isUsername: eventData.field.isUsername || false,
        element: virtualElement,
        placeholder: '' // 添加必需的 placeholder 属性
      };

      // 保存当前字段信息
      this.currentField = virtualField;

      // 计算下拉菜单位置，考虑 iframe 嵌套
      const position = this.calculateIframeElementPosition(eventData.position, eventData.frameInfo);
      this.currentPosition = position;

      // 确保 ShadowDOM 容器已创建
      if (!this.shadowRoot) {
        this.createShadowDomContainer();
      }

      // 获取当前域名的密码列表
      // 如果是跨域 iframe，使用 iframe 的域名而不是顶层页面的域名
      const domain = eventData.domain || '';

      this.getPasswordsForDomain(domain)
        .pipe(
          tap(credentials => {
            // 如果有可用的凭证，显示下拉菜单
            if (credentials && credentials.length > 0) {
              this.showDropdown(position, credentials);
            }
          }),
          catchError(error => {
            console.error('获取域名密码列表时出错:', error);
            return of([]);
          })
        )
        .subscribe();
    } catch (error) {
      console.error('处理 iframe 焦点事件时出错:', error);
    }
  }

  /**
   * 计算 iframe 中元素的绝对位置
   * @param elementPosition 元素在 iframe 中的位置
   * @param frameInfo iframe 信息
   * @returns 元素在顶层页面中的绝对位置
   */
  private calculateIframeElementPosition(elementPosition: any, frameInfo: any): DropdownPosition {
    try {
      // 如果没有 frameInfo，直接返回元素位置
      if (!frameInfo || !frameInfo.frameRect) {
        return elementPosition;
      }

      // 计算元素在顶层页面中的绝对位置
      const frameRect = frameInfo.frameRect;

      // 基本位置计算
      let top = frameRect.top + elementPosition.top;
      let left = frameRect.left + elementPosition.left;

      // 考虑页面滚动
      top += window.scrollY;
      left += window.scrollX;

      // 获取元素高度，如果没有提供，使用默认值
      const height = elementPosition.height || 30; // 默认高度为30px

      // 创建位置对象，确保下拉菜单显示在表单元素下方
      return {
        top: top + height, // 将下拉菜单放在表单元素下方
        left,
        width: elementPosition.width,
        height
      };
    } catch (error) {
      console.error('计算 iframe 元素位置时出错:', error);
      return elementPosition;
    }
  }

  /**
   * 处理表单字段获取焦点事件
   * 当用户点击或聚焦于表单字段时，创建ShadowDOM容器并显示密码下拉菜单
   * 确保只在顶层页面渲染下拉列表
   * @param event 焦点事件，可以是普通焦点事件或iframe焦点事件
   */
  handleFieldFocus(event: FormFieldFocusEvent): void {
    try {
      // 检查当前是否在顶层页面
      if (window !== window.top) {
        console.debug('不在顶层页面，忽略焦点事件，让background转发到顶层页面处理');
        return;
      }

      console.log('处理字段焦点事件:', {
        field: event.field?.id || event.field?.name,
        isPassword: event.field?.isPassword,
        isUsername: event.field?.isUsername,
        domain: event.domain,
      });


      // 只处理密码字段或用户名字段
      if (!event.field.isPassword && !event.field.isUsername) {
        console.debug('非密码或用户名字段，忽略焦点事件');
        return;
      }

      // 确保ShadowDOM容器已创建
      if (!this.shadowRoot) {
        console.debug('创建ShadowDOM容器');
        this.createShadowDomContainer();
      }

        // 处理普通焦点事件（顶层页面）
      console.debug('处理顶层页面的焦点事件');

      // 保存当前字段和位置信息
      this.currentField = event.field;

      // 计算下拉菜单位置
      const position = this.calculateDropdownPosition(event.position);
      this.currentPosition = position;

      // 获取当前域名的密码列表
      const domain = event.domain || '';
      console.debug(`获取域名 ${domain} 的密码列表`);

      this.getPasswordsForDomain(domain)
        .pipe(
          tap(credentials => {
            // 如果有可用的凭证，显示下拉菜单
            if (credentials && credentials.length > 0) {
              console.debug(`找到 ${credentials.length} 个凭证，显示下拉菜单`);
              this.showDropdown(position, credentials);
            } else {
              console.debug(`未找到域名 ${domain} 的凭证`);
            }
          }),
          catchError(error => {
            console.error('获取域名密码列表时出错:', error);
            return of([]);
          })
        )
        .subscribe();
    } catch (error) {
      console.error('处理字段焦点事件时出错:', error);
    }
  }

  /**
   * 获取当前密码列表的真实的高度和宽度
   */
  getCurrentDropdownElementRect(): { width: number, height: number } {
    if (!this.dropdownElement) {
      return {
        width: 300,
        height: 300
      };
    }

    const rect = this.dropdownElement.getBoundingClientRect();
    return {
      width: rect.width,
      height: rect.height
    };
  }

  /**
   * 获取指定域名的密码列表
   * 从后台脚本获取匹配当前网站的密码列表
   */
  getPasswordsForDomain(domain: string): Observable<Credential[]> {
    // 构建消息
    const message: Omit<RuntimeMessage<{ domain: string }>, 'messageId'> = {
      type: 'GET_PASSWORDS_FOR_DOMAIN',
      payload: { domain }
    };

    // 发送消息到后台脚本
    return new Observable<Credential[]>(observer => {
      RuntimeMessageService.sendMessageToBackground<{ domain: string }, Credential[]>(message)
        .subscribe({
          next: response => {
            if (response.success && response.data) {
              observer.next(response.data);
            } else {
              console.error('Error getting passwords:', response.error);
              observer.next([]);
            }
            observer.complete();
          },
          error: error => {
            console.error('Error sending message to background:', error);
            observer.next([]);
            observer.complete();
          }
        });
    });
  }

  /**
   * 显示密码下拉菜单
   * 创建并显示密码下拉菜单UI
   */
  private showDropdown(position: DropdownPosition, credentials: Credential[]): void {
    if (!this.currentField || !this.shadowRoot) {
      return;
    }

    // 如果下拉菜单已经显示，先关闭
    if (this.dropdownVisible) {
      this.closeDropdown();
    }

    // 创建下拉菜单元素
    this.createDropdownElement(credentials);

    // 设置下拉菜单位置
    this.positionDropdown(position);

    // 标记下拉菜单为可见
    this.dropdownVisible = true;

    // 触发下拉菜单打开事件
    this.dropdownOpened.next(position);

    console.log('Password dropdown opened with', credentials.length, 'credentials');
  }

  /**
   * 创建下拉菜单元素
   */
  private createDropdownElement(credentials: Credential[]): void {
    if (!this.shadowRoot) {
      return;
    }

    try {
      // 将 Credential 转换为 CredentialOutput 格式
      const credentialOutputs: CredentialOutput[] = credentials.map(cred => ({
        id: parseInt(cred.id) || 0,
        service_name: cred.serviceName,
        username: cred.username,
        password: cred.password,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      // 创建容器元素
      const container = document.createElement('div');
      container.className = 'password-dropdown-container';
      container.style.cssText = `
        position: absolute;
        width: 100%;
        z-index: 2147483647;
        pointer-events: auto !important; /* 确保下拉菜单可以接收鼠标事件 */
      `;

      // 添加调试日志
      console.debug('创建下拉菜单容器，设置 pointer-events: auto');

      // 将容器添加到Shadow DOM
      this.shadowRoot.appendChild(container);
      this.dropdownElement = container;

      // 创建React根元素
      this.dropdownRoot = ReactDOM.createRoot(container);

      // 创建React元素
      const passwordDropdownElement = React.createElement(
        React.StrictMode,
        null,
        React.createElement(
          StyleProvider,
          { container: this.shadowRoot }, // 使用shadow root作为样式容器
          React.createElement(
            ConfigProvider,
            { locale: zhCN },
            React.createElement(PasswordDropdown, {
              credentials: credentialOutputs,
              onSelect: (credential) => {
                // 转换回 Credential 格式
                const cred: Credential = {
                  id: credential.id.toString(),
                  username: credential.username,
                  password: credential.password,
                  serviceName: credential.service_name,
                  url: ''
                };
                this.selectCredential(cred);
              },
              onClose: () => {
                this.closeDropdown();
              }
            })
          )
        )
      );

      // 渲染下拉菜单
      this.dropdownRoot.render(passwordDropdownElement);
      console.log(`PasswordDropdownService: 已渲染密码下拉菜单，包含 ${credentials.length} 个凭证`);
    } catch (error) {
      console.error('创建下拉菜单元素时出错:', error);
    }
  }

  /**
   * 选择凭证
   * 当用户选择一个凭证时，填充到表单并关闭下拉菜单
   */
  private selectCredential(credential: Credential): void {
    if (!this.currentField) {
      return;
    }

    try {
      // 保存当前字段的引用，因为在关闭下拉菜单后可能会清除 currentField
      const fieldElement = this.currentField.element;

      // 触发凭证选择事件
      this.credentialSelected.next(credential);

      // 填充凭证到表单
      this.fillCredential(credential, fieldElement);

      // 关闭下拉菜单
      this.closeDropdown();

      // 清除当前字段引用，模拟失焦
      this.currentField = null;

      // 触发字段失焦处理，延迟移除ShadowDOM容器
      this.handleFieldBlur();
    } catch (error) {
      console.error('Error selecting credential:', error);
      // 确保下拉菜单关闭，即使发生错误
      this.closeDropdown();
    }
  }

  /**
   * 填充凭证到表单
   * 将用户名和密码填充到对应的表单字段
   * 支持处理虚拟字段（来自 iframe）
   */
  fillCredential(credential: Credential, targetField: HTMLElement): void {
    if (!targetField) {
      return;
    }

    // 检查是否是虚拟字段（来自 iframe）
    const isVirtualField = targetField.id && targetField.id.startsWith('virtual-field-');

    if (isVirtualField) {
      console.debug('检测到虚拟字段，尝试通过 background 发送填充凭证消息到 iframe');

      // 如果是虚拟字段，通过 background 发送消息到 iframe
      this.sendCredentialToIframe(credential, targetField);
      return;
    }

    // 以下是处理实际 DOM 元素的逻辑
    // 获取当前字段类型
    const isPassword = this.isPasswordField(targetField);
    const isUsername = this.isUsernameField(targetField);

    // 填充当前字段
    if (isPassword) {
      this.fillInputValue(targetField, credential.password);
    } else if (isUsername) {
      this.fillInputValue(targetField, credential.username);
    }

    // 尝试查找并填充相关字段
    if (isPassword) {
      // 如果当前是密码字段，尝试查找并填充用户名字段
      const usernameField = this.findRelatedField(targetField, true);
      if (usernameField) {
        this.fillInputValue(usernameField, credential.username);
      }
    } else if (isUsername) {
      // 如果当前是用户名字段，尝试查找并填充密码字段
      const passwordField = this.findRelatedField(targetField, false);
      if (passwordField) {
        this.fillInputValue(passwordField, credential.password);
      }
    }

    console.log('凭证已填充');
  }

  /**
   * 通过 background 发送凭证到 iframe
   * 根据原始焦点事件中的 frameInfo 信息，将凭证发送到正确的 iframe
   * @param credential 要填充的凭证
   */
  private sendCredentialToIframe(credential: Credential, targetField: HTMLElement): void {
    try {
      console.debug('开始发送凭证到 iframe');

      // 获取当前保存的 frameInfo
      let frameInfo = null;
      let isCrossDomain = false;
      let frameLevel = 0;
      let originalMessageId = '';
      let originalFrameId = -1;

      // 检查是否有当前字段和位置信息
      if (this.currentField && this.currentPosition) {
        // 检查当前字段是否是虚拟字段（来自 iframe）
        const isVirtualField = this.currentField.element.id && this.currentField.element.id.startsWith('virtual-field-');

        if (isVirtualField) {
          // 这是一个来自 iframe 的虚拟字段，可能是跨域的
          isCrossDomain = true;

          // 从虚拟字段ID中提取原始消息ID（如果有）
          const virtualFieldId = this.currentField.element.id;
          const messageIdMatch = virtualFieldId.match(/virtual-field-(\d+)-(.+)-(\d+)$/);

          if (messageIdMatch && messageIdMatch[2]) {
            originalMessageId = messageIdMatch[2];
            console.debug(`从虚拟字段ID中提取到原始消息ID: ${originalMessageId}`);
          }
          if (messageIdMatch && messageIdMatch[3]) {
            originalFrameId = +messageIdMatch[3];
            console.debug(`从虚拟字段ID中提取到原始iframe ID: ${originalFrameId}`);
          }

          console.debug('检测到虚拟字段，可能是跨域 iframe');
        }

        // 使用安全的常量值 -1 代替 chrome.tabs.TAB_ID_NONE
        const SAFE_TAB_ID_NONE = -1;

        // 尝试从当前位置信息中获取 frameLevel
        if (this.currentPosition.hasOwnProperty('frameLevel')) {
          // @ts-ignore - 动态属性
          frameLevel = this.currentPosition.frameLevel || 0;
        }

        // 创建可序列化的字段信息，替代直接传递DOM元素
        const serializableField = {
          id: this.currentField.id || '',
          name: this.currentField.name || '',
          type: this.currentField.type || '',
          isPassword: this.currentField.isPassword || false,
          isUsername: this.currentField.isUsername || false,
          // 添加额外的标识信息，帮助iframe中定位正确的字段
          elementId: this.currentField.element.id || '',
          elementName: this.currentField.element.getAttribute('name') || '',
          elementType: this.currentField.element.getAttribute('type') || '',
          elementSelector: this.getUniqueSelector(this.currentField.element)
        };

        frameInfo = {
          tabId: SAFE_TAB_ID_NONE, // 使用安全常量，将在 background 中获取正确的 tabId
          frameId: isVirtualField ? this.currentField.element.id : '',
          frameRect: {
            top: this.currentPosition.top || 0,
            left: this.currentPosition.left || 0,
            width: this.currentPosition.width || 0,
            height: this.currentPosition.height || 0,
            bottom: (this.currentPosition.top || 0) + (this.currentPosition.height || 0),
            right: (this.currentPosition.left || 0) + (this.currentPosition.width || 0),
            x: this.currentPosition.left || 0,
            y: this.currentPosition.top || 0
          },
          isCrossDomain: isCrossDomain,
          frameLevel: frameLevel,
          originalMessageId: originalMessageId,
          originalFrameId: originalFrameId
        };

        console.debug('已创建 frameInfo:', JSON.stringify({
          tabId: frameInfo.tabId,
          frameId: frameInfo.frameId,
          isCrossDomain: frameInfo.isCrossDomain,
          frameLevel: frameInfo.frameLevel,
          originalMessageId: frameInfo.originalMessageId,
          originalFrameId: frameInfo.originalFrameId
        }));

        // 构建消息对象
        const message: Omit<RuntimeMessage<any>, 'messageId'> = {
          type: BackgroundMessageType.IFRAME_CREDENTIAL_FILL,
          payload: {
            credential,
            targetFieldInfo: serializableField, // 使用可序列化的字段信息替代DOM元素
            frameInfo,
            isCrossDomain: isCrossDomain,
            timestamp: Date.now()
          }
        };

        console.debug('准备发送消息到 background:', message.type);

        // 使用 RuntimeMessageService 发送消息给 background
        RuntimeMessageService.sendMessageToBackground<any, any>(message)
          .subscribe({
            next: (response: { success: boolean; error?: string; data?: any; messageId: string }) => {
              if (response.success) {
                console.debug('凭证已成功发送到 background，响应:', response);

                // 如果响应中包含了目标 frameId，记录下来
                if (response.data && response.data.frameId !== undefined) {
                  console.debug(`凭证已发送到 frameId=${response.data.frameId}`);
                }
              } else {
                console.error('发送凭证到 background 时出错:', response.error);
              }
            },
            error: (error: Error) => {
              console.error('发送凭证到 background 时出错:', error);
              // 提供更详细的错误信息
              if (error instanceof Error) {
                console.error('错误详情:', error.message);
                console.error('错误堆栈:', error.stack);
              }
            }
          });
      } else {
        console.error('无法发送凭证到 iframe：缺少当前字段或位置信息');
      }
    } catch (error) {
      console.error('发送凭证到 iframe 时出错:', error);
      // 提供更详细的错误信息
      if (error instanceof Error) {
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
      }
    }
  }

  /**
   * 填充输入框值
   */
  private fillInputValue(element: HTMLElement, value: string): void {
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      if (typeof element?.click !== 'function') {
        element.click();
      }
      // 设置元素值
      element.value = value;

      // 触发输入事件，确保表单验证和其他事件监听器能够响应
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      const changeEvent = new Event('change', { bubbles: true });
      element.dispatchEvent(changeEvent);
    }
  }

  /**
   * 查找相关字段
   * @param currentField 当前字段
   * @param findUsername 是否查找用户名字段（true表示查找用户名字段，false表示查找密码字段）
   */
  private findRelatedField(currentField: HTMLElement, findUsername: boolean): HTMLElement | null {
    // 尝试在同一表单中查找
    const form = currentField.closest('form');
    if (form) {
      const selector = findUsername ?
        'input[type="text"], input[type="email"], input:not([type])' :
        'input[type="password"]';

      const fields = Array.from(form.querySelectorAll(selector)) as HTMLElement[];
      if (fields.length > 0) {
        // 过滤出不是display:none的字段
        const filteredFields = fields.filter(field => {
          const computedStyle = window.getComputedStyle(field);
          return computedStyle.display !== 'none';
        });
        const [first_match = null] = filteredFields;
        return first_match
      }

      return null;
    }

    // 如果不在表单中，尝试查找附近的字段
    const parent = currentField.parentElement;
    if (parent) {
      const selector = findUsername ?
        'input[type="text"], input[type="email"], input:not([type])' :
        'input[type="password"]';

      const fields = Array.from(parent.querySelectorAll(selector)) as HTMLElement[];
      // 过滤掉当前字段
      const filteredFields = fields.filter(field => field !== currentField)
      .filter(field => window.getComputedStyle(field).display !== 'none');

      return filteredFields.length > 0 ? filteredFields[0] : null;
    }

    return null;
  }

  /**
   * 判断是否为密码字段
   */
  private isPasswordField(element: HTMLElement): boolean {
    if (element instanceof HTMLInputElement) {
      // 检查类型
      if (element.type === 'password') {
        return true;
      }

      // 检查属性
      const id = element.id || '';
      const name = element.name || '';
      const placeholder = element.placeholder || '';
      const autocomplete = element.getAttribute('autocomplete') || '';

      return autocomplete === 'current-password' ||
             autocomplete === 'new-password' ||
             /password/i.test(id) ||
             /password/i.test(name) ||
             /password/i.test(placeholder);
    }

    return false;
  }

  /**
   * 判断是否为用户名字段
   */
  private isUsernameField(element: HTMLElement): boolean {
    if (element instanceof HTMLInputElement) {
      // 检查类型
      if (element.type === 'email') {
        return true;
      }

      // 检查属性
      const id = element.id || '';
      const name = element.name || '';
      const placeholder = element.placeholder || '';
      const autocomplete = element.getAttribute('autocomplete') || '';

      return autocomplete === 'username' ||
             autocomplete === 'email' ||
             /user(name)?/i.test(id) ||
             /user(name)?/i.test(name) ||
             /user(name)?/i.test(placeholder) ||
             /email/i.test(id) ||
             /email/i.test(name) ||
             /email/i.test(placeholder) ||
             /login/i.test(id) ||
             /login/i.test(name) ||
             /login/i.test(placeholder);
    }

    return false;
  }

  /**
   * 关闭密码下拉菜单
   * 在关闭下拉菜单时保存必要的状态，以便下次 focusin 时能够恢复
   */
  closeDropdown(): void {
    if (!this.dropdownVisible || !this.shadowRoot || !this.dropdownElement) {
      return;
    }

    try {
      // 保存必要的状态信息，以便下次 focusin 时能够恢复
      // 当前我们已经在类属性中保存了 currentField 和 currentPosition

      // 卸载 React 组件
      if (this.dropdownRoot) {
        this.dropdownRoot.unmount();
        this.dropdownRoot = null;
      }

      // 从Shadow DOM中移除下拉菜单
      if (this.dropdownElement && this.shadowRoot.contains(this.dropdownElement)) {
        this.shadowRoot.removeChild(this.dropdownElement);
      }
      this.dropdownElement = null;

      // 标记下拉菜单为不可见
      this.dropdownVisible = false;

      // 触发下拉菜单关闭事件
      this.dropdownClosed.next();

      console.log('Password dropdown closed');
    } catch (error) {
      console.error('Error closing dropdown:', error);

      // 确保状态重置，即使发生错误
      this.dropdownElement = null;
      this.dropdownVisible = false;
      this.dropdownRoot = null;
    }
  }

  /**
   * 创建Shadow DOM容器
   * 仅在表单元素触发 focusin 事件后才创建并插入到 DOM 树中
   * @returns {boolean} 创建是否成功
   */
  private createShadowDomContainer(): boolean {
    try {
      // 如果已经存在 shadowRoot，则不需要重新创建
      if (this.shadowRoot) {
        return true;
      }

      // 使用 ShadowDomService 创建 Shadow DOM
      this.shadowRoot = this.shadowDomService.getShadowRoot();

      console.log('ShadowDOM container created and attached to DOM');
      return true;
    } catch (error) {
      console.error('Error creating ShadowDOM container:', error);
      return false;
    }
  }

  /**
   * 计算下拉菜单位置
   * 确保下拉菜单显示在表单元素下方
   */
  private calculateDropdownPosition(fieldRect: DOMRect): DropdownPosition {
    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 获取表单元素的高度
    const fieldHeight = fieldRect.height || 30; // 如果没有高度，使用默认值30px

    // 计算下拉菜单的理想位置（在字段下方）
    // 确保考虑表单元素的高度
    let top = fieldRect.top + fieldHeight + window.scrollY;
    let left = fieldRect.left + window.scrollX;

    // 下拉菜单的尺寸
    const { width: dropdownWidth, height: dropdownHeight } = this.getCurrentDropdownElementRect();

    // 确保下拉菜单不会超出视口右侧
    if (left + dropdownWidth > viewportWidth) {
      left = viewportWidth - dropdownWidth - 10;
    }

    // 确保下拉菜单不会超出视口底部
    // 如果超出，则将其放置在字段上方
    if (top + dropdownHeight > viewportHeight + window.scrollY) {
      top = fieldRect.top + window.scrollY - dropdownHeight;
    }

    // 确保位置不为负
    top = Math.max(window.scrollY, top);
    left = Math.max(window.scrollX, left);

    return {
      top,
      left,
      width: dropdownWidth,
      height: dropdownHeight
    };
  }

  /**
   * 定位下拉菜单
   */
  private positionDropdown(position: DropdownPosition): void {
    if (!this.dropdownElement) {
      return;
    }

    this.dropdownElement.style.top = `${position.top}px`;
    this.dropdownElement.style.left = `${position.left}px`;
    this.dropdownElement.style.width = `${position.width}px`;

    if (position.height) {
      this.dropdownElement.style.maxHeight = `${position.height}px`;
    }
  }

  /**
   * 处理文档点击事件
   * 当用户点击下拉菜单外部时关闭下拉菜单并可能移除ShadowDOM容器
   */
  private handleDocumentClick(event: MouseEvent): void {
    try {
      if (!this.dropdownVisible || !this.dropdownElement) {
        return;
      }

      // 检查点击是否在下拉菜单内
      const path = event.composedPath();
      const isClickInside = path.includes(this.dropdownElement);

      // 检查点击是否在当前表单字段内
      const isClickOnField = this.currentField && path.includes(this.currentField.element);
      if (isClickOnField) {
        return;
      }

      if (!isClickInside) {
        // 关闭下拉菜单
        this.closeDropdown();

        // 如果点击不在当前表单字段上，则清除当前字段引用并触发失焦处理
        if (!isClickOnField) {
          this.currentField = null;
          this.handleFieldBlur();
        }
      }
    } catch (error) {
      console.error('Error handling document click:', error);
      // 确保下拉菜单关闭，即使发生错误
      this.closeDropdown();
    }
  }

  /**
   * 处理窗口大小变化事件
   */
  private handleWindowResize(): void {
    if (this.dropdownVisible && this.currentPosition && this.currentField) {
      // 重新计算位置
      const fieldRect = this.currentField.element.getBoundingClientRect();
      const newPosition = this.calculateDropdownPosition(fieldRect);

      // 更新位置
      this.positionDropdown(newPosition);
      this.currentPosition = newPosition;
    }
  }

  /**
   * 处理窗口滚动事件
   */
  private handleWindowScroll(): void {
    if (this.dropdownVisible && this.currentPosition && this.currentField) {
      // 重新计算位置
      const fieldRect = this.currentField.element.getBoundingClientRect();
      const newPosition = this.calculateDropdownPosition(fieldRect);

      // 更新位置
      this.positionDropdown(newPosition);
      this.currentPosition = newPosition;
    }
  }

  /**
   * 移除Shadow DOM容器
   * 当表单元素触发 blur 事件后，将之前创建的 ShadowDOM 容器从 DOM 树中移除
   */
  private removeShadowDomContainer(): void {
    try {
      // 首先关闭下拉菜单（如果打开的话）
      if (this.dropdownVisible) {
        this.closeDropdown();
      }

      // 使用 ShadowDomService 销毁 Shadow DOM
      this.shadowDomService.destroy();
      this.shadowRoot = null;
      console.log('ShadowDOM container removed from DOM');
    } catch (error) {
      console.error('Error removing ShadowDOM container:', error);
    }
  }

  /**
   * 处理表单字段失去焦点事件
   * 当用户从表单字段移开焦点时，移除ShadowDOM容器
   */
  handleFieldBlur(): void {
    try {
      // 设置延迟，以便在用户点击下拉菜单时不会立即移除容器
      setTimeout(() => {
        // 如果下拉菜单已关闭且没有当前字段，则移除ShadowDOM容器
        if (!this.dropdownVisible && !this.currentField) {
          this.removeShadowDomContainer();
        }
      }, 300); // 300毫秒延迟，给用户足够的时间点击下拉菜单
    } catch (error) {
      console.error('Error handling field blur:', error);
    }
  }

  /**
   * 卸载服务，清理资源
   */
  public dispose(): void {
    // 关闭下拉菜单
    this.closeDropdown();

    // 清理所有事件监听器和订阅
    this.cleanupEventListeners();

    // 移除ShadowDOM容器
    this.removeShadowDomContainer();

    console.log('PasswordDropdownService disposed');
  }

  /**
   * 清理事件监听器和订阅
   */
  private cleanupEventListeners(): void {
    // 清理所有事件监听器和订阅
    console.log('清理PasswordDropdownService事件监听...');
    document.removeEventListener('click', this.handleDocumentClick.bind(this));
    window.removeEventListener('resize', this.handleWindowResize.bind(this));
    window.removeEventListener('scroll', this.handleWindowScroll.bind(this), true);
    this.dropdownOpened.complete();
    this.dropdownClosed.complete();
    this.credentialSelected.complete();
  }

  /**
   * 获取元素的唯一选择器
   * 用于在iframe中重新定位元素
   * @param element 目标元素
   * @returns 唯一选择器字符串
   */
  private getUniqueSelector(element: HTMLElement): string {
    try {
      // 尝试使用现代API获取选择器
      if (element.id) {
        return `#${element.id}`;
      }

      // 构建基于属性的选择器
      let selector = element.tagName.toLowerCase();

      // 添加类名
      if (element.className) {
        const classNames = element.className.split(/\s+/).filter(Boolean);
        if (classNames.length > 0) {
          selector += `.${classNames.join('.')}`;
        }
      }

      // 添加name属性
      if (element.getAttribute('name')) {
        selector += `[name="${element.getAttribute('name')}"]`;
      }

      // 添加type属性
      if (element.getAttribute('type')) {
        selector += `[type="${element.getAttribute('type')}"]`;
      }

      // 添加placeholder属性
      if (element.getAttribute('placeholder')) {
        selector += `[placeholder="${element.getAttribute('placeholder')}"]`;
      }

      // 如果是表单内的元素，添加表单上下文
      const form = element.closest('form');
      if (form) {
        if (form.id) {
          return `#${form.id} ${selector}`;
        } else if (form.getAttribute('name')) {
          return `form[name="${form.getAttribute('name')}"] ${selector}`;
        } else if (form.getAttribute('action')) {
          return `form[action="${form.getAttribute('action')}"] ${selector}`;
        }
      }

      return selector;
    } catch (error) {
      console.error('生成唯一选择器时出错:', error);
      return '';
    }
  }

  /**
   * 处理来自background的凭证填充请求
   * 在iframe中找到并填充正确的表单字段
   * @param data 包含凭证和目标字段信息的数据
   */
  public handleCredentialFillRequest(data: any): void {
    try {
      if (!data || !data.credential || !data.targetFieldInfo) {
        console.error('无效的凭证填充请求数据');
        return;
      }

      console.debug('处理凭证填充请求:', {
        username: data.credential.username,
        isPassword: data.targetFieldInfo.isPassword,
        isUsername: data.targetFieldInfo.isUsername,
        selector: data.targetFieldInfo.elementSelector
      });

      // 尝试使用多种方式找到目标字段
      let targetElement: HTMLElement | null = null;

      // 1. 尝试通过ID查找
      if (data.targetFieldInfo.id) {
        targetElement = document.querySelector(`#${data.targetFieldInfo.id}`);
      } else if (data.targetFieldInfo.elementId) {
        targetElement = document.getElementById(data.targetFieldInfo.elementId);
      }

      // 2. 如果没找到，尝试通过name和type属性查找
      if (!targetElement && data.targetFieldInfo.elementName && data.targetFieldInfo.elementType) {
        const elements = document.querySelectorAll(
          `input[name="${data.targetFieldInfo.elementName}"][type="${data.targetFieldInfo.elementType}"]`
        );
        if (elements.length > 0) {
          targetElement = elements[0] as HTMLElement;
        }
      }

      // 3. 如果还没找到，尝试通过选择器查找
      if (!targetElement && data.targetFieldInfo.elementSelector) {
        try {
          const elements = document.querySelectorAll(data.targetFieldInfo.elementSelector);
          if (elements.length > 0) {
            targetElement = elements[0] as HTMLElement;
          }
        } catch (e) {
          console.debug('通过选择器查找元素失败:', e);
        }
      }

      // 4. 如果还没找到，尝试查找符合类型的输入字段
      if (!targetElement) {
        // 查找所有密码或用户名字段
        const selector = data.targetFieldInfo.isPassword
          ? 'input[type="password"]'
          : 'input[type="text"], input[type="email"], input:not([type])';

        const elements = Array.from(document.querySelectorAll(selector));

        // 如果只有一个匹配的字段，直接使用
        if (elements.length === 1) {
          targetElement = elements[0] as HTMLElement;
        }
        // 如果有多个匹配的字段，尝试找到当前聚焦的字段
        else if (elements.length > 1) {
          // 优先使用当前聚焦的字段
          const focusedElement = elements.find(el => el === document.activeElement);
          if (focusedElement) {
            targetElement = focusedElement as HTMLElement;
          } else {
            // 否则使用第一个字段
            targetElement = elements[0] as HTMLElement;
          }
        }
      }

      // 如果找到了目标字段，填充凭证
      if (targetElement) {
        console.debug('找到目标字段，准备填充凭证');

        // 创建一个FormField对象
        const field: FormField = {
          id: targetElement.id || '',
          name: targetElement.getAttribute('name') || '',
          type: targetElement.getAttribute('type') || 'text',
          isPassword: data.targetFieldInfo.isPassword,
          isUsername: data.targetFieldInfo.isUsername,
          element: targetElement,
          placeholder: targetElement.getAttribute('placeholder') || ''
        };

        // 填充凭证
        this.fillCredentialToField(data.credential, field);
      } else {
        console.error('无法找到目标字段');
      }
    } catch (error) {
      console.error('处理凭证填充请求时出错:', error);
    }
  }

  /**
   * 填充凭证到指定字段
   * @param credential 凭证信息
   * @param field 目标字段
   */
  private fillCredentialToField(credential: Credential, field: FormField): void {
    try {
      // 获取当前字段类型
      const isPassword = field.isPassword;
      const isUsername = field.isUsername;
      const element = field.element;

      // 填充当前字段
      if (isPassword) {
        this.fillInputValue(element, credential.password);
      } else if (isUsername) {
        this.fillInputValue(element, credential.username);
      }

      // 尝试查找并填充相关字段
      if (isPassword) {
        // 如果当前是密码字段，尝试查找并填充用户名字段
        const usernameField = this.findRelatedField(element, true);
        if (usernameField) {
          this.fillInputValue(usernameField, credential.username);
        }
      } else if (isUsername) {
        // 如果当前是用户名字段，尝试查找并填充密码字段
        const passwordField = this.findRelatedField(element, false);
        if (passwordField) {
          this.fillInputValue(passwordField, credential.password);
        }
      }

      console.log('凭证已填充到iframe中的字段');
    } catch (error) {
      console.error('填充凭证到字段时出错:', error);
    }
  }
}
