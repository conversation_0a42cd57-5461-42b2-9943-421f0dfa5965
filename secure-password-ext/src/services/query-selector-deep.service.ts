import { Observable, Subject } from 'rxjs';

/**
 * 查询策略枚举
 */
export enum SelectorStrategy {
  STANDARD = 'standard',
  TREE_WALKER = 'tree_walker'
}

/**
 * 深度选择器服务接口
 */
export interface IQuerySelectorDeepService {
  selectElements<T extends Element>(
    root: Document | ShadowRoot | Element,
    selector: string,
    nodeFilter: (node: Node) => boolean,
    observer?: MutationObserver,
    forceStandardQuery?: boolean
  ): T[];
  checkForShadowDom(): void;
  containsShadowDom(): boolean;
  shadowDomStateChanged: Observable<boolean>;
}

/**
 * 深度选择器服务实现
 * 用于高效查询页面中的元素，包括处理Shadow DOM
 */
export class QuerySelectorDeepService implements IQuerySelectorDeepService {
  private _containsShadowDom = false;
  private _shadowDomStateChanged = new Subject<boolean>();
  private readonly MAX_RECURSION_DEPTH = 10;

  // 不需要检查的元素类型
  private excludedElements = new Set([
    "svg", "script", "noscript", "head", "style", "link",
    "meta", "title", "base", "img", "picture", "video",
    "audio", "object", "source", "track", "param", "map", "area"
  ]);

  constructor() {
    this.initialize();
  }

  /**
   * 获取Shadow DOM变化的Observable
   */
  get shadowDomStateChanged(): Observable<boolean> {
    return this._shadowDomStateChanged.asObservable();
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    if (document.readyState === "complete") {
      this.checkForShadowDom();
    } else {
      window.addEventListener("load", () => this.checkForShadowDom());
    }
  }

  /**
   * 检测页面是否包含Shadow DOM
   */
  checkForShadowDom(): void {
    const foundShadowDom = this.findFirstShadowRoot(document.body) !== null;
    if (foundShadowDom !== this._containsShadowDom) {
      this._containsShadowDom = foundShadowDom;
      this._shadowDomStateChanged.next(foundShadowDom);
    }
  }

  /**
   * 返回页面是否包含Shadow DOM
   */
  containsShadowDom(): boolean {
    return this._containsShadowDom;
  }

  /**
   * 选择元素
   */
  selectElements<T extends Element>(
    root: Document | ShadowRoot | Element,
    selector: string,
    nodeFilter: (node: Node) => boolean,
    observer?: MutationObserver,
    forceStandardQuery?: boolean
  ): T[] {
    // 选择查询策略
    if (!forceStandardQuery && this.containsShadowDom()) {
      return this.selectElementsWithTreeWalker<T>(
        root,
        nodeFilter,
        this.excludedElements,
        observer
      );
    }

    // 使用标准查询，如果失败则回退到TreeWalker
    try {
      return this.selectElementsInShadowDom<T>(root, selector, observer);
    } catch (error) {
      console.warn('Standard query failed, using TreeWalker instead', error);
      return this.selectElementsWithTreeWalker<T>(
        root,
        nodeFilter,
        this.excludedElements,
        observer
      );
    }
  }

  /**
   * 在Shadow DOM中选择元素
   */
  private selectElementsInShadowDom<T extends Element>(
    root: Document | ShadowRoot | Element,
    selector: string,
    observer?: MutationObserver
  ): T[] {
    // 查询当前根元素
    let elements = this.selectElementsInRoot<T>(root, selector);

    // 查找所有Shadow Roots
    const shadowRoots = this.findAllShadowRoots(root);
    for (const shadowRoot of shadowRoots) {
      elements = elements.concat(this.selectElementsInRoot<T>(shadowRoot, selector));

      // 设置观察器
      if (observer) {
        observer.observe(shadowRoot, {
          attributes: true,
          childList: true,
          subtree: true
        });
      }
    }

    return elements;
  }

  /**
   * 在根元素中选择元素
   */
  private selectElementsInRoot<T extends Element>(
    root: Document | ShadowRoot | Element,
    selector: string
  ): T[] {
    if (!root || !root.querySelector) {
      return [];
    }

    try {
      // 优化查询
      if (!root.querySelector(selector)) {
        return [];
      }
      return Array.from(root.querySelectorAll(selector)) as T[];
    } catch (error) {
      console.warn('Error selecting elements:', error);
      return [];
    }
  }

  /**
   * 查找所有Shadow Roots
   */
  private findAllShadowRoots(
    root: Document | ShadowRoot | Element,
    depth: number = 0
  ): ShadowRoot[] {
    if (!this._containsShadowDom || !root) {
      return [];
    }

    // 防止递归过深
    if (depth >= this.MAX_RECURSION_DEPTH) {
      throw new Error("Maximum recursion depth reached");
    }

    // 查找当前层级的Shadow Roots
    let shadowRoots = this.findDirectShadowRoots(root);

    // 递归查找下一层级
    const currentRoots = [...shadowRoots]; // 创建副本避免修改原数组
    for (const shadowRoot of currentRoots) {
      const childRoots = this.findAllShadowRoots(shadowRoot, depth + 1);
      shadowRoots = shadowRoots.concat(childRoots);
    }

    return shadowRoots;
  }

  /**
   * 查找直接的Shadow Roots
   */
  private findDirectShadowRoots(
    root: Document | ShadowRoot | Element
  ): ShadowRoot[] {
    if (!root) {
      return [];
    }

    const shadowRoots: ShadowRoot[] = [];
    try {
      // 查找可能包含Shadow DOM的元素
      const elements = root.querySelectorAll('*');
      for (let i = 0; i < elements.length; i++) {
        const shadowRoot = this.extractShadowRoot(elements[i]);
        if (shadowRoot) {
          shadowRoots.push(shadowRoot);
        }
      }
    } catch (error) {
      console.warn('Error finding shadow roots:', error);
    }

    return shadowRoots;
  }

  /**
   * 查找第一个Shadow Root
   */
  private findFirstShadowRoot(
    root: Document | ShadowRoot | Element
  ): ShadowRoot | null {
    if (!root) {
      return null;
    }

    try {
      // 查找可能包含Shadow DOM的元素
      const elements = root.querySelectorAll('*');
      for (let i = 0; i < elements.length; i++) {
        const shadowRoot = this.extractShadowRoot(elements[i]);
        if (shadowRoot) {
          return shadowRoot;
        }
      }
    } catch (error) {
      console.warn('Error finding first shadow root:', error);
    }

    return null;
  }

  /**
   * 提取元素的Shadow Root
   */
  private extractShadowRoot(element: Element): ShadowRoot | null {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
      return null;
    }

    // 尝试获取开放的Shadow Root
    if (element.shadowRoot) {
      return element.shadowRoot;
    }

    // 尝试使用Chrome扩展API
    if (typeof chrome !== 'undefined' && 'dom' in chrome && 'openOrClosedShadowRoot' in (chrome as any).dom) {
      try {
        return (chrome as any).dom.openOrClosedShadowRoot(element);
      } catch {
        // 忽略错误
      }
    }

    return null;
  }

  /**
   * 使用TreeWalker选择元素
   */
  private selectElementsWithTreeWalker<T extends Element>(
    rootNode: Node,
    nodeFilter: (node: Node) => boolean,
    excludedElements: Set<string>,
    observer?: MutationObserver
  ): T[] {
    const results: T[] = [];

    this.traverseWithTreeWalker(
      rootNode,
      results,
      nodeFilter,
      excludedElements,
      observer
    );

    return results;
  }

  /**
   * 使用TreeWalker遍历DOM
   */
  private traverseWithTreeWalker<T extends Element>(
    rootNode: Node,
    results: T[],
    nodeFilter: (node: Node) => boolean,
    excludedElements: Set<string>,
    observer?: MutationObserver
  ): void {
    if (!rootNode || !document.createTreeWalker) {
      return;
    }

    try {
      // 创建TreeWalker
      const filter = {
        acceptNode: (node: Node) =>
          excludedElements.has(node.nodeName?.toLowerCase())
            ? NodeFilter.FILTER_REJECT
            : NodeFilter.FILTER_ACCEPT
      };

      const treeWalker = document.createTreeWalker(
        rootNode,
        NodeFilter.SHOW_ELEMENT,
        filter
      );

      let currentNode = treeWalker.currentNode;

      // 遍历节点
      while (currentNode) {
        if (nodeFilter(currentNode)) {
          results.push(currentNode as unknown as T);
        }

        // 检查Shadow DOM
        const shadowRoot = this.extractShadowRoot(currentNode as Element);
        if (shadowRoot) {
          if (observer) {
            observer.observe(shadowRoot, {
              attributes: true,
              childList: true,
              subtree: true
            });
          }

          // 递归遍历Shadow DOM
          this.traverseWithTreeWalker(
            shadowRoot,
            results,
            nodeFilter,
            excludedElements,
            observer
          );
        }

        const nextNode = treeWalker.nextNode();
        if (nextNode === null) {
          break;
        }
        currentNode = nextNode;
      }
    } catch (error) {
      console.warn('Error in TreeWalker traversal:', error);
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    window.removeEventListener("load", () => this.checkForShadowDom());
    this._shadowDomStateChanged.complete();
  }

  /**
   * 便捷方法：选择所有表单
   */
  selectForms(): HTMLFormElement[] {
    return this.selectElements<HTMLFormElement>(
      document.documentElement,
      'form',
      (node) => node.nodeName.toLowerCase() === 'form'
    );
  }

  /**
   * 便捷方法：选择所有表单项
   */
  selectFormInputs(): HTMLElement[] {
    return this.selectElements<HTMLElement>(
      document.documentElement,
      'input, textarea, select, button',
      (node) => {
        const name = node.nodeName.toLowerCase();
        return name === 'input' || name === 'textarea' ||
               name === 'select' || name === 'button';
      }
    );
  }

  /**
   * 便捷方法：选择所有密码字段
   */
  selectPasswordFields(): HTMLInputElement[] {
    return this.selectElements<HTMLInputElement>(
      document.documentElement,
      'input[type="password"]',
      (node) => {
        return node.nodeName.toLowerCase() === 'input' &&
               (node as HTMLInputElement).type === 'password';
      }
    );
  }

  /**
   * 便捷方法：获取所有表单和表单项
   */
  selectAllFormElements(): { forms: HTMLFormElement[], inputs: HTMLElement[] } {
    return {
      forms: this.selectForms(),
      inputs: this.selectFormInputs()
    };
  }
}
