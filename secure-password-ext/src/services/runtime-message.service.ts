import { Observable, throwError, Subscriber } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

export interface RuntimeMessage<T = any> {
  type: string;
  payload?: T;
  messageId: string; // 消息 ID，用于跟踪和取消
}

export interface RuntimeMessageResponse<R = any> {
  success: boolean;
  data?: R;
  error?: string;
  messageId: string; // 响应中包含原始消息 ID
}

const DEFAULT_TIMEOUT = 5000 * 10; // 默认超时时间 5 秒
export const CANCEL_REQUEST_TYPE = 'INTERNAL_CANCEL_REQUEST';

export class AbortError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AbortError';
  }
}

// 用于在 background 存储可取消操作的 AbortController
const cancellableOperations = new Map<string, AbortController>();

export class RuntimeMessageService {
  private static generateMessageId(): string {
    return uuidv4();
  }

  /**
   * 从 Content Script 或 Popup 等发送消息到 Background Script (Observable)
   * 支持通过取消订阅来发送 CANCEL_REQUEST 到 background。
   */
  static sendMessageToBackground<T, R>(
    messagePayload: Omit<RuntimeMessage<T>, 'messageId'>, // 传入时不包含 messageId
    responseTimeout: number = DEFAULT_TIMEOUT
  ): Observable<RuntimeMessageResponse<R>> {
    const messageId = this.generateMessageId();
    const message: RuntimeMessage<T> = { ...messagePayload, messageId };

    return new Observable<RuntimeMessageResponse<R>>((observer: Subscriber<RuntimeMessageResponse<R>>) => {
      let messageSent = false;
      const handleResponse = (response: RuntimeMessageResponse<R> | undefined) => {
        if (chrome.runtime.lastError) {
          if (!observer.closed) {
            observer.error(
              `Error sending message to background (ID: ${messageId}): ${chrome.runtime.lastError.message}`
            );
          }
          return;
        }
        if (response === undefined) {
          if (!observer.closed) {
            observer.error(`No response for message (ID: ${messageId}) from background.`);
          }
          return;
        }
        // 确保响应对应的是当前消息
        if (response.messageId === messageId) {
            if (!observer.closed) {
                observer.next(response);
                observer.complete();
            }
        } else {
            // console.warn(`Received response for mismatched messageId. Expected ${messageId}, got ${response.messageId}`);
        }
      };

      try {
        // console.log(`Sending message to background (ID: ${messageId}):`, message);
        chrome.runtime.sendMessage(message, handleResponse);
        messageSent = true;
      } catch (error: any) {
        if (!observer.closed) {
            observer.error(`Failed to send message (ID: ${messageId}): ${error.message}`);
        }
        return;
      }

      // Teardown logic for when the Observable is unsubscribed
      return () => {
        if (messageSent && !observer.closed) { // 只有当消息已发送且订阅者主动取消时
          // console.log(`Unsubscribed from message (ID: ${messageId}). Sending CANCEL_REQUEST.`);
          const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
            type: CANCEL_REQUEST_TYPE,
            payload: { originalMessageId: messageId },
            messageId: this.generateMessageId(), // Cancel请求本身也需要一个ID
          };
          // 发送取消请求，通常是 fire-and-forget
          try {
            chrome.runtime.sendMessage(cancelMessage, (response) => {
              if (chrome.runtime.lastError) {
                // console.warn(`Error sending CANCEL_REQUEST for ${messageId}: ${chrome.runtime.lastError.message}`);
              } else {
                // console.log(`CANCEL_REQUEST for ${messageId} acknowledged:`, response);
              }
            });
          } catch (e) {
            // console.warn(`Failed to send CANCEL_REQUEST for ${messageId}:`, e);
          }
        }
      };
    }).pipe(
      timeout(responseTimeout),
      catchError(err => {
        if (err.name === 'TimeoutError') {
          return throwError(() => new Error(`Request (ID: ${messageId}) to background timed out after ${responseTimeout}ms`));
        }
        return throwError(() => err);
      })
    );
  }

  /**
   * [Promise 版本] 从 Content Script 或 Popup 等发送消息到 Background Script
   * AbortSignal 优先于发送 CANCEL_REQUEST，因为 AbortSignal 是更直接的本地取消。
   */
  static async sendMessageToBackgroundAsPromise<T, R>(
    messagePayload: Omit<RuntimeMessage<T>, 'messageId'>,
    responseTimeout: number = DEFAULT_TIMEOUT,
    signal?: AbortSignal
  ): Promise<RuntimeMessageResponse<R>> {
    const messageId = this.generateMessageId();
    const message: RuntimeMessage<T> = { ...messagePayload, messageId };

    if (signal?.aborted) {
      return Promise.reject(new AbortError(`Request (ID: ${messageId}) aborted before sending.`));
    }

    return new Promise<RuntimeMessageResponse<R>>((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | null = null;
      let settled = false;

      const cleanupAndSettle = (settleFn: Function, value: any) => {
        if (settled) return;
        settled = true;
        if (timeoutId) clearTimeout(timeoutId);
        signal?.removeEventListener('abort', handleAbort);
        settleFn(value);
      };

      const handleAbort = () => {
        // 发送 CANCEL_REQUEST 到 background
        const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
            type: CANCEL_REQUEST_TYPE,
            payload: { originalMessageId: messageId },
            messageId: this.generateMessageId(),
        };
        try { chrome.runtime.sendMessage(cancelMessage); } catch (e) { /* ignore */ }
        cleanupAndSettle(reject, new AbortError(`Request (ID: ${messageId}) aborted.`));
      };

      signal?.addEventListener('abort', handleAbort, { once: true });

      try {
        chrome.runtime.sendMessage(message, (response: RuntimeMessageResponse<R> | undefined) => {
          if (signal?.aborted && !settled) {
            // 如果在等待响应时中止，handleAbort 应该已经处理了
            return;
          }
          if (chrome.runtime.lastError) {
            cleanupAndSettle(reject, new Error(`Error sending message (ID: ${messageId}): ${chrome.runtime.lastError.message}`));
            return;
          }
          if (response === undefined) {
            cleanupAndSettle(reject, new Error(`No response for message (ID: ${messageId}).`));
            return;
          }
          if (response.messageId !== messageId) {
            // console.warn(`Mismatched response ID. Expected ${messageId}, got ${response.messageId}`);
            // 决定是否 reject 或忽略，这里选择忽略，等待正确的响应或超时
            return;
          }
          cleanupAndSettle(resolve, response);
        });
      } catch (error: any) {
        cleanupAndSettle(reject, new Error(`Failed to send message (ID: ${messageId}): ${error.message}`));
        return;
      }

      timeoutId = setTimeout(() => {
        if (signal?.aborted && !settled) return; // Abort 优先
         // 发送 CANCEL_REQUEST 到 background (因为超时也意味着我们不再关心这个请求了)
        const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
            type: CANCEL_REQUEST_TYPE,
            payload: { originalMessageId: messageId },
            messageId: this.generateMessageId(),
        };
        try { chrome.runtime.sendMessage(cancelMessage); } catch (e) { /* ignore */ }
        cleanupAndSettle(reject, new Error(`Request (ID: ${messageId}) timed out after ${responseTimeout}ms.`));
      }, responseTimeout);
    });
  }

  /**
   * 从 Content Script 或 Popup 等发送消息到特定标签页的 Content Script (Observable)
   * 支持通过取消订阅来发送 CANCEL_REQUEST 到目标标签页。
   */
  static sendMessageToTab<T, R>(
    tabId: number,
    messagePayload: Omit<RuntimeMessage<T>, 'messageId'>,
    responseTimeout: number = DEFAULT_TIMEOUT,
    frameId?: number // 可选参数，指定目标 frameId
  ): Observable<RuntimeMessageResponse<R>> {
    const messageId = this.generateMessageId();
    const message: RuntimeMessage<T> = { ...messagePayload, messageId };

    return new Observable<RuntimeMessageResponse<R>>((observer: Subscriber<RuntimeMessageResponse<R>>) => {
      let messageSent = false;
      const handleResponse = (response: RuntimeMessageResponse<R> | undefined) => {
        if (chrome.runtime.lastError) {
          if (!observer.closed) {
            observer.error(
              `Error sending message to tab ${tabId}${frameId !== undefined ? ` (frameId: ${frameId})` : ''} (ID: ${messageId}): ${chrome.runtime.lastError.message}`
            );
          }
          return;
        }
        if (response === undefined) {
          if (!observer.closed) {
            observer.error(`No response for message (ID: ${messageId}) from tab ${tabId}.`);
          }
          return;
        }
        // 确保响应对应的是当前消息
        if (response.messageId === messageId) {
          if (!observer.closed) {
            observer.next(response);
            observer.complete();
          }
        } else {
          // console.warn(`Received response for mismatched messageId. Expected ${messageId}, got ${response.messageId}`);
        }
      };

      try {
        // console.log(`Sending message to tab ${tabId}${frameId !== undefined ? ` (frameId: ${frameId})` : ''} (ID: ${messageId}):`, message);
        if (frameId !== undefined) {
          // 发送到特定 frameId
          chrome.tabs.sendMessage(tabId, message, { frameId }, handleResponse);
        } else {
          // 发送到标签页的主 frame
          chrome.tabs.sendMessage(tabId, message, handleResponse);
        }
        messageSent = true;
      } catch (error: any) {
        if (!observer.closed) {
          observer.error(`Failed to send message to tab ${tabId} (ID: ${messageId}): ${error.message}`);
        }
        return;
      }

      // Teardown logic for when the Observable is unsubscribed
      return () => {
        if (messageSent && !observer.closed) { // 只有当消息已发送且订阅者主动取消时
          // console.log(`Unsubscribed from message to tab ${tabId} (ID: ${messageId}). Sending CANCEL_REQUEST.`);
          const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
            type: CANCEL_REQUEST_TYPE,
            payload: { originalMessageId: messageId },
            messageId: this.generateMessageId(), // Cancel请求本身也需要一个ID
          };
          // 发送取消请求，通常是 fire-and-forget
          try {
            if (frameId !== undefined) {
              chrome.tabs.sendMessage(tabId, cancelMessage, { frameId }, (response) => {
                if (chrome.runtime.lastError) {
                  // console.warn(`Error sending CANCEL_REQUEST for ${messageId} to tab ${tabId} (frameId: ${frameId}): ${chrome.runtime.lastError.message}`);
                } else {
                  // console.log(`CANCEL_REQUEST for ${messageId} to tab ${tabId} (frameId: ${frameId}) acknowledged:`, response);
                }
              });
            } else {
              chrome.tabs.sendMessage(tabId, cancelMessage, (response) => {
                if (chrome.runtime.lastError) {
                  // console.warn(`Error sending CANCEL_REQUEST for ${messageId} to tab ${tabId}: ${chrome.runtime.lastError.message}`);
                } else {
                  // console.log(`CANCEL_REQUEST for ${messageId} to tab ${tabId} acknowledged:`, response);
                }
              });
            }
          } catch (e) {
            // console.warn(`Failed to send CANCEL_REQUEST for ${messageId} to tab ${tabId}:`, e);
          }
        }
      };
    }).pipe(
      timeout(responseTimeout),
      catchError(err => {
        if (err.name === 'TimeoutError') {
          return throwError(() => new Error(`Request (ID: ${messageId}) to tab ${tabId} timed out after ${responseTimeout}ms`));
        }
        return throwError(() => err);
      })
    );
  }

  /**
   * [Promise 版本] 从 Content Script 或 Popup 等发送消息到特定标签页的 Content Script
   * AbortSignal 优先于发送 CANCEL_REQUEST，因为 AbortSignal 是更直接的本地取消。
   */
  static async sendMessageToTabAsPromise<T, R>(
    tabId: number,
    messagePayload: Omit<RuntimeMessage<T>, 'messageId'>,
    responseTimeout: number = DEFAULT_TIMEOUT,
    signal?: AbortSignal,
    frameId?: number // 可选参数，指定目标 frameId
  ): Promise<RuntimeMessageResponse<R>> {
    const messageId = this.generateMessageId();
    const message: RuntimeMessage<T> = { ...messagePayload, messageId };

    if (signal?.aborted) {
      return Promise.reject(new AbortError(`Request (ID: ${messageId}) to tab ${tabId} aborted before sending.`));
    }

    return new Promise<RuntimeMessageResponse<R>>((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | null = null;
      let settled = false;

      const cleanupAndSettle = (settleFn: Function, value: any) => {
        if (settled) return;
        settled = true;
        if (timeoutId) clearTimeout(timeoutId);
        signal?.removeEventListener('abort', handleAbort);
        settleFn(value);
      };

      const handleAbort = () => {
        // 发送 CANCEL_REQUEST 到目标标签页
        const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
          type: CANCEL_REQUEST_TYPE,
          payload: { originalMessageId: messageId },
          messageId: this.generateMessageId(),
        };
        try {
          if (frameId !== undefined) {
            chrome.tabs.sendMessage(tabId, cancelMessage, { frameId });
          } else {
            chrome.tabs.sendMessage(tabId, cancelMessage);
          }
        } catch (e) { /* ignore */ }
        cleanupAndSettle(reject, new AbortError(`Request (ID: ${messageId}) to tab ${tabId} aborted.`));
      };

      signal?.addEventListener('abort', handleAbort, { once: true });

      try {
        const sendMessageCallback = (response: RuntimeMessageResponse<R> | undefined) => {
          if (signal?.aborted && !settled) {
            // 如果在等待响应时中止，handleAbort 应该已经处理了
            return;
          }
          if (chrome.runtime.lastError) {
            cleanupAndSettle(reject, new Error(`Error sending message to tab ${tabId}${frameId !== undefined ? ` (frameId: ${frameId})` : ''} (ID: ${messageId}): ${chrome.runtime.lastError.message}`));
            return;
          }
          if (response === undefined) {
            cleanupAndSettle(reject, new Error(`No response for message (ID: ${messageId}) from tab ${tabId}.`));
            return;
          }
          if (response.messageId !== messageId) {
            // console.warn(`Mismatched response ID. Expected ${messageId}, got ${response.messageId}`);
            // 决定是否 reject 或忽略，这里选择忽略，等待正确的响应或超时
            return;
          }
          cleanupAndSettle(resolve, response);
        };

        if (frameId !== undefined) {
          chrome.tabs.sendMessage(tabId, message, { frameId }, sendMessageCallback);
        } else {
          chrome.tabs.sendMessage(tabId, message, sendMessageCallback);
        }
      } catch (error: any) {
        cleanupAndSettle(reject, new Error(`Failed to send message to tab ${tabId} (ID: ${messageId}): ${error.message}`));
        return;
      }

      timeoutId = setTimeout(() => {
        if (signal?.aborted && !settled) return; // Abort 优先
        // 发送 CANCEL_REQUEST 到目标标签页 (因为超时也意味着我们不再关心这个请求了)
        const cancelMessage: RuntimeMessage<{ originalMessageId: string }> = {
          type: CANCEL_REQUEST_TYPE,
          payload: { originalMessageId: messageId },
          messageId: this.generateMessageId(),
        };
        try {
          if (frameId !== undefined) {
            chrome.tabs.sendMessage(tabId, cancelMessage, { frameId });
          } else {
            chrome.tabs.sendMessage(tabId, cancelMessage);
          }
        } catch (e) { /* ignore */ }
        cleanupAndSettle(reject, new Error(`Request (ID: ${messageId}) to tab ${tabId} timed out after ${responseTimeout}ms.`));
      }, responseTimeout);
    });
  }


  /**
   * 在 Background Script 或 Content Script 中监听消息
   * Background Script 需要处理 CANCEL_REQUEST_TYPE。
   */
  static onMessage<T, R>(
    handler: (
      message: RuntimeMessage<T>,
      sender: chrome.runtime.MessageSender,
      sendResponse: (response: RuntimeMessageResponse<R>) => void,
      signal?: AbortSignal // Background handler 可以接收一个 signal 来中止其操作
    ) => boolean | void | Promise<RuntimeMessageResponse<R>>
  ): void {
    chrome.runtime.onMessage.addListener((rawMessage, sender, sendResponseCallback) => {
      if (typeof rawMessage !== 'object' || rawMessage === null || typeof rawMessage.type !== 'string' || typeof rawMessage.messageId !== 'string') {
        return false;
      }

      const message = rawMessage as RuntimeMessage<any>; // 接收时 payload 类型未知

      // 封装 sendResponse 以确保 messageId 被包含
      const sendResponse = (response: Partial<RuntimeMessageResponse<any>>) => {
        sendResponseCallback({
            success: response.success || false,
            data: response.data,
            error: response.error,
            messageId: message.messageId
        });
      }

      if (message.type === CANCEL_REQUEST_TYPE) {
        const originalMessageId = message.payload?.originalMessageId;
        if (originalMessageId) {
          const operationToCancel = cancellableOperations.get(originalMessageId);
          if (operationToCancel) {
            // console.log(`Background: Received CANCEL_REQUEST for operation ${originalMessageId}. Aborting.`);
            operationToCancel.abort();
            cancellableOperations.delete(originalMessageId);
            sendResponse({ success: true, data: { status: 'cancel_acknowledged' } });
          } else {
            // console.log(`Background: Received CANCEL_REQUEST for ${originalMessageId}, but no active operation found.`);
            sendResponse({ success: false, error: 'operation_not_found_or_already_completed' });
          }
        }
        return false; // 通常取消请求不需要异步响应
      }

      let abortController: AbortController | undefined;
      if (sender.tab) { // 仅当消息来自 content script 或其他有意义的发送者时，才创建可取消操作
                       // 或者根据业务逻辑决定何时创建 AbortController
        abortController = new AbortController();
        cancellableOperations.set(message.messageId, abortController);
      }

      const signal = abortController?.signal;

      try {
        try {
          const result = handler(message as RuntimeMessage<T>, sender, sendResponse as (response: RuntimeMessageResponse<R>) => void, signal);

          // 处理异步函数返回的Promise
          if (result instanceof Promise) {
            result
              .then(response => {
                if (!signal?.aborted) { // 只有当操作未被取消时才发送响应
                  sendResponse(response === undefined ? { success: true } : response);
                }
              })
              .catch(error => {
                if (!signal?.aborted) {
                  sendResponse({
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                  });
                }
              })
              .finally(() => {
                cancellableOperations.delete(message.messageId);
              });
            return true; // 表示会异步发送响应
          }
          // 处理同步函数返回的响应对象
          else if (result && typeof result === 'object' && ('success' in result || 'error' in result)) {
            sendResponse(result);
            cancellableOperations.delete(message.messageId);
            return false; // 表示已同步发送响应
          }
          // 处理返回true的情况（表示处理器会自己调用sendResponse）
          else if (result === true) {
            // 如果 handler 返回 true，它负责在完成时清理 cancellableOperations
            // 或者我们在这里添加一个默认的超时来清理它，以防 handler 忘记
            // setTimeout(() => cancellableOperations.delete(message.messageId), someDefaultOperationTimeout);
            return true;
          }
          // 同步完成或未返回 true
          cancellableOperations.delete(message.messageId);
          return false;
        } catch (error) {
          // 处理同步错误
          console.error('处理消息时出错:', error);
          sendResponse({
            success: false,
            error: error instanceof Error ? error.message : String(error),
          });
          cancellableOperations.delete(message.messageId);
          return false;
        }

      } catch (error) {
        // 处理外层错误（通常不会发生）
        cancellableOperations.delete(message.messageId);
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
        return false;
      }
    });
  }
}

// ------------------- 调用示例 (Background Script) -------------------
/*
import { RuntimeMessageService, RuntimeMessage, RuntimeMessageResponse, AbortError, CANCEL_REQUEST_TYPE } from './services/runtime-message.service';

interface LongTaskPayload { duration: number };
interface LongTaskResponse { result: string };

RuntimeMessageService.onMessage<LongTaskPayload, LongTaskResponse>(
  async (message, sender, sendResponse, signal) => {
    console.log(`Background: Received message type ${message.type} (ID: ${message.messageId})`);

    if (message.type === 'DO_LONG_TASK') {
      const duration = message.payload?.duration || 2000;
      console.log(`Background: Starting long task (ID: ${message.messageId}) for ${duration}ms.`);

      return new Promise<RuntimeMessageResponse<LongTaskResponse>>((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          console.log(`Background: Long task (ID: ${message.messageId}) completed.`);
          resolve({ success: true, data: { result: `Task ${message.messageId} done after ${duration}ms` }, messageId: message.messageId });
        }, duration);

        // 监听取消信号
        signal?.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          console.log(`Background: Long task (ID: ${message.messageId}) aborted by signal.`);
          // 注意：这里不应该再调用 sendResponse，因为 Promise 会被外部的 onMessage 包装器处理
          // 如果直接 reject，外部的 catch 会发送错误响应
          reject(new AbortError(`Task ${message.messageId} was cancelled.`));
        }, { once: true });
      });
    }
    // 其他消息类型...
    sendResponse({ success: false, error: 'Unknown message type', messageId: message.messageId });
    return false;
  }
);
*/

// ------------------- 调用示例 (Content Script / Popup) -------------------
/*
import { RuntimeMessageService, RuntimeMessage, RuntimeMessageResponse, AbortError } from './services/runtime-message.service';
import { Subscription } from 'rxjs';

// --- Observable Cancellation Example ---
const longTaskMsgPayload = { type: 'DO_LONG_TASK', payload: { duration: 5000 } }; // 5秒任务

console.log('Content Script: Sending DO_LONG_TASK (Observable)...');
const subscription: Subscription = RuntimeMessageService.sendMessageToBackground<any, any>(longTaskMsgPayload, 7000)
  .subscribe({
    next: (response) => {
      console.log('Content Script (Observable): Task response:', response);
    },
    error: (err) => {
      console.error('Content Script (Observable): Task error:', err.message);
    }
  });

// 在 2 秒后取消 Observable 请求
setTimeout(() => {
  if (!subscription.closed) {
    console.log('Content Script (Observable): Unsubscribing from DO_LONG_TASK...');
    subscription.unsubscribe(); // 这会触发发送 CANCEL_REQUEST
  }
}, 2000);


// --- Promise Cancellation Example with AbortController ---
const longTaskPromisePayload = { type: 'DO_LONG_TASK', payload: { duration: 5000 } };
const abortController = new AbortController();

async function sendLongTaskWithPromise() {
  console.log('Content Script: Sending DO_LONG_TASK (Promise)...');
  try {
    const response = await RuntimeMessageService.sendMessageToBackgroundAsPromise<any, any>(
      longTaskPromisePayload,
      7000, // timeout
      abortController.signal // AbortSignal
    );
    console.log('Content Script (Promise): Task response:', response);
  } catch (error: any) {
    if (error instanceof AbortError) {
      console.warn('Content Script (Promise): Task aborted:', error.message);
    } else {
      console.error('Content Script (Promise): Task error:', error.message);
    }
  }
}

sendLongTaskWithPromise();

// 在 2 秒后取消 Promise 请求
setTimeout(() => {
  console.log('Content Script (Promise): Aborting DO_LONG_TASK...');
  abortController.abort(); // 这会 reject Promise 并尝试发送 CANCEL_REQUEST
}, 2000);
*/
