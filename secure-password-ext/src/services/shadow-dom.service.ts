// secure-password-ext/src/services/shadow-dom.service.ts

/**
 * Service for managing Shadow DOM elements.
 * This allows us to create isolated DOM elements that won't be affected by the page's styles.
 */
export class ShadowDomService {
  private shadowRoot: ShadowRoot | null = null;
  private shadowHost: HTMLElement | null = null;

  /**
   * Creates a shadow DOM host and attaches it to the top-level document body.
   * @returns The shadow DOM root
   */
  createShadowDom(): ShadowRoot {
    if (this.shadowRoot) {
      return this.shadowRoot;
    }

    // Get the top-level document (in case we're in an iframe)
    const topDocument = this.getTopDocument();

    // Create a host element for the shadow DOM
    this.shadowHost = topDocument.createElement('div');
    this.shadowHost.id = 'secure-password-shadow-host';
    this.shadowHost.style.cssText = `
      all: initial !importamt;
      position: fixed !importamt;
      top: 0 !importamt;
      left: 0 !importamt;
      width: 0 !importamt;
      height: 0 !importamt;
      overflow: visible !importamt;
      z-index: 2147483647 !importamt; /* Highest possible z-index */
      pointer-events: none !importamt; /* 宿主元素不接收鼠标事件，但其子元素可以 */
      transform: translateZ(0) !importamt; /* Force GPU acceleration */
      will-change: transform !importamt; /* Hint to browser for optimization */
    `;

    // Append the host to the top-level document body
    topDocument.body.appendChild(this.shadowHost);

    // Create a shadow DOM with 'closed' mode
    this.shadowRoot = this.shadowHost.attachShadow({ mode: 'open' });

    // Add base styles to the shadow DOM
    const style = topDocument.createElement('style');
    style.textContent = `
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.85);
      }
    `;

    this.shadowRoot.appendChild(style);
    return this.shadowRoot;
  }

  /**
   * Gets the top-level document (in case we're in an iframe)
   * @returns The top-level document
   */
  private getTopDocument(): Document {
    try {
      // Try to access top document (will throw if cross-origin)
      if (window.top && window.top.document) {
        return window.top.document;
      }
    } catch (e) {
      console.log("ShadowDomService: Cannot access top document, using current document");
    }
    return document;
  }

  /**
   * Gets the shadow DOM root, creating it if it doesn't exist.
   * @returns The shadow DOM root
   */
  getShadowRoot(): ShadowRoot {
    return this.shadowRoot || this.createShadowDom();
  }

  /**
   * Gets the shadow host element.
   * @returns The shadow host element or null if it doesn't exist
   */
  getShadowHost(): HTMLElement | null {
    if (!this.shadowHost) {
      this.createShadowDom();
    }
    return this.shadowHost;
  }

  /**
   * Creates a container element within the shadow DOM.
   * @returns The container element
   */
  createContainer(): HTMLElement {
    // Get the top-level document
    const topDocument = this.getTopDocument();

    const container = topDocument.createElement('div');
    container.className = 'secure-password-dropdown-container';
    container.style.opacity = '0';
    container.style.transition = 'opacity 0.2s ease-out';

    // Add the container to the shadow DOM
    this.getShadowRoot().appendChild(container);

    // Trigger reflow to enable transition
    setTimeout(() => {
      container.style.opacity = '1';
    }, 50);

    return container;
  }

  /**
   * Removes a container element from the shadow DOM.
   * @param container The container element to remove
   */
  removeContainer(container: HTMLElement): void {
    if (container && this.shadowRoot && this.shadowRoot.contains(container)) {
      // Fade out before removing
      container.style.opacity = '0';
      setTimeout(() => {
        if (this.shadowRoot && this.shadowRoot.contains(container)) {
          this.shadowRoot.removeChild(container);
        }
      }, 200);
    }
  }

  /**
   * Destroys the shadow DOM and removes the host element.
   */
  destroy(): void {
    try {
      // Try to get the top document
      const topDocument = this.getTopDocument();

      if (this.shadowHost && topDocument.body.contains(this.shadowHost)) {
        topDocument.body.removeChild(this.shadowHost);
      }
    } catch (e) {
      console.error("ShadowDomService: Error removing shadow host", e);
    }

    this.shadowRoot = null;
    this.shadowHost = null;
  }
}
