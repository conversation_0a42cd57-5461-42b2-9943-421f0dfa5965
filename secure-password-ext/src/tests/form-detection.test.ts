import { FormDetectionService, FormField, DetectedForm } from '../services/form-detection.service';

/**
 * 表单检测服务测试
 * 
 * 这个测试文件用于验证FormDetectionService的功能
 * 可以在浏览器控制台中运行以下代码来测试:
 * 
 * ```
 * const test = new FormDetectionTest();
 * test.runTests();
 * ```
 */
export class FormDetectionTest {
  private formDetectionService: FormDetectionService;
  private testResults: { name: string; passed: boolean; message: string }[] = [];

  constructor() {
    this.formDetectionService = new FormDetectionService();
    console.log('FormDetectionTest initialized');
  }

  /**
   * 运行所有测试
   */
  runTests(): void {
    console.log('Running FormDetectionService tests...');
    
    this.testInitialization();
    this.testFormDetection();
    this.testFieldDetection();
    this.testPasswordFieldDetection();
    this.testUsernameFieldDetection();
    this.testStandaloneFieldDetection();
    
    this.reportResults();
  }

  /**
   * 测试初始化
   */
  private testInitialization(): void {
    try {
      this.formDetectionService.initialize();
      this.recordTestResult('Initialization', true, 'Service initialized successfully');
    } catch (error) {
      this.recordTestResult('Initialization', false, `Error: ${error}`);
    }
  }

  /**
   * 测试表单检测
   */
  private testFormDetection(): void {
    try {
      // 创建测试表单
      const testForm = this.createTestForm();
      document.body.appendChild(testForm);
      
      // 检测表单
      this.formDetectionService.detectForms();
      
      // 等待检测完成
      setTimeout(() => {
        const forms = this.formDetectionService.getForms();
        const passed = forms.length > 0;
        
        this.recordTestResult(
          'Form Detection', 
          passed, 
          passed ? `Detected ${forms.length} forms` : 'Failed to detect forms'
        );
        
        // 清理测试表单
        document.body.removeChild(testForm);
      }, 500);
    } catch (error) {
      this.recordTestResult('Form Detection', false, `Error: ${error}`);
    }
  }

  /**
   * 测试字段检测
   */
  private testFieldDetection(): void {
    try {
      // 创建测试表单
      const testForm = this.createTestForm();
      document.body.appendChild(testForm);
      
      // 检测表单
      this.formDetectionService.detectForms();
      
      // 等待检测完成
      setTimeout(() => {
        const fields = this.formDetectionService.getFormFields();
        const passed = fields.length > 0;
        
        this.recordTestResult(
          'Field Detection', 
          passed, 
          passed ? `Detected ${fields.length} fields` : 'Failed to detect fields'
        );
        
        // 清理测试表单
        document.body.removeChild(testForm);
      }, 500);
    } catch (error) {
      this.recordTestResult('Field Detection', false, `Error: ${error}`);
    }
  }

  /**
   * 测试密码字段检测
   */
  private testPasswordFieldDetection(): void {
    try {
      // 创建测试表单
      const testForm = this.createTestForm();
      document.body.appendChild(testForm);
      
      // 检测表单
      this.formDetectionService.detectForms();
      
      // 等待检测完成
      setTimeout(() => {
        const fields = this.formDetectionService.getFormFields();
        const passwordFields = fields.filter(field => field.isPassword);
        const passed = passwordFields.length > 0;
        
        this.recordTestResult(
          'Password Field Detection', 
          passed, 
          passed ? `Detected ${passwordFields.length} password fields` : 'Failed to detect password fields'
        );
        
        // 清理测试表单
        document.body.removeChild(testForm);
      }, 500);
    } catch (error) {
      this.recordTestResult('Password Field Detection', false, `Error: ${error}`);
    }
  }

  /**
   * 测试用户名字段检测
   */
  private testUsernameFieldDetection(): void {
    try {
      // 创建测试表单
      const testForm = this.createTestForm();
      document.body.appendChild(testForm);
      
      // 检测表单
      this.formDetectionService.detectForms();
      
      // 等待检测完成
      setTimeout(() => {
        const fields = this.formDetectionService.getFormFields();
        const usernameFields = fields.filter(field => field.isUsername);
        const passed = usernameFields.length > 0;
        
        this.recordTestResult(
          'Username Field Detection', 
          passed, 
          passed ? `Detected ${usernameFields.length} username fields` : 'Failed to detect username fields'
        );
        
        // 清理测试表单
        document.body.removeChild(testForm);
      }, 500);
    } catch (error) {
      this.recordTestResult('Username Field Detection', false, `Error: ${error}`);
    }
  }

  /**
   * 测试独立字段检测
   */
  private testStandaloneFieldDetection(): void {
    try {
      // 创建独立字段
      const standaloneField = document.createElement('input');
      standaloneField.type = 'password';
      standaloneField.id = 'standalone-password';
      document.body.appendChild(standaloneField);
      
      // 检测表单
      this.formDetectionService.detectForms();
      
      // 等待检测完成
      setTimeout(() => {
        const fields = this.formDetectionService.getFormFields();
        const standaloneFields = fields.filter(field => !field.formId || field.formId.startsWith('virtual-form-'));
        const passed = standaloneFields.length > 0;
        
        this.recordTestResult(
          'Standalone Field Detection', 
          passed, 
          passed ? `Detected ${standaloneFields.length} standalone fields` : 'Failed to detect standalone fields'
        );
        
        // 清理独立字段
        document.body.removeChild(standaloneField);
      }, 500);
    } catch (error) {
      this.recordTestResult('Standalone Field Detection', false, `Error: ${error}`);
    }
  }

  /**
   * 创建测试表单
   */
  private createTestForm(): HTMLFormElement {
    const form = document.createElement('form');
    form.id = 'test-form';
    form.innerHTML = `
      <div>
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" placeholder="Enter your username">
      </div>
      <div>
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" placeholder="Enter your email">
      </div>
      <div>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" placeholder="Enter your password">
      </div>
      <div>
        <button type="submit">Submit</button>
      </div>
    `;
    return form;
  }

  /**
   * 记录测试结果
   */
  private recordTestResult(name: string, passed: boolean, message: string): void {
    this.testResults.push({ name, passed, message });
    console.log(`Test: ${name} - ${passed ? 'PASSED' : 'FAILED'}: ${message}`);
  }

  /**
   * 报告测试结果
   */
  private reportResults(): void {
    console.log('--- FormDetectionService Test Results ---');
    
    const passedTests = this.testResults.filter(result => result.passed);
    const failedTests = this.testResults.filter(result => !result.passed);
    
    console.log(`Total Tests: ${this.testResults.length}`);
    console.log(`Passed: ${passedTests.length}`);
    console.log(`Failed: ${failedTests.length}`);
    
    if (failedTests.length > 0) {
      console.log('Failed Tests:');
      failedTests.forEach(test => {
        console.log(`- ${test.name}: ${test.message}`);
      });
    }
    
    console.log('--- End of Test Results ---');
  }
}

// 如果在浏览器环境中，可以直接运行测试
if (typeof window !== 'undefined') {
  console.log('FormDetectionTest available. Run "new FormDetectionTest().runTests()" to test.');
}
