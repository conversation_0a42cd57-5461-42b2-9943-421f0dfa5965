// Types for the secure-password extension

export interface CustomField {
  title: string;
  type: 'text' | 'password' | 'textarea';
  value: string;
}

export interface CredentialInput {
  service_name: string;
  username: string;
  password: string;
  notes?: string;
  custom_fields?: CustomField[];
}

export interface CredentialOutput {
  id: number;
  service_name: string;
  username: string;
  password: string;
  notes?: string;
  custom_fields?: CustomField[];
  created_at: string;
  updated_at: string;
}

export interface PasswordHistoryEntry {
  id: number;
  old_value: string;
  changed_at: string;
}

export interface PasswordDropdownPosition {
  left: number;
  top: number;
  width: number;
}
