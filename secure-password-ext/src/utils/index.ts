interface ChromeRuntimeSendMessagePromiseResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export function chromeRuntimeSendMessagePromise<T = any>(
  message: any,
  options: {
    timeout?: number; // 超时时间（毫秒）
    validateResponse?: (response: any) => boolean; // 响应验证函数
  } = {}
): Promise<T> {
  const { timeout = 5000, validateResponse } = options;

  return new Promise<T>((resolve, reject) => {
    // 检查 Chrome Runtime 是否可用
    if (!chrome?.runtime?.sendMessage) {
      reject(new Error('Chrome runtime not available'));
      return;
    }

    // 设置超时处理
    const timeoutId = setTimeout(() => {
      reject(new Error(`Message timeout after ${timeout}ms`));
    }, timeout);

    try {
      chrome.runtime.sendMessage(message, (response: ChromeRuntimeSendMessagePromiseResponse<T>) => {
        clearTimeout(timeoutId);

        // 检查运行时错误
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        // 检查响应格式
        if (!response) {
          reject(new Error('Empty response received'));
          return;
        }

        // 自定义响应验证
        if (validateResponse && !validateResponse(response)) {
          reject(new Error('Invalid response format'));
          return;
        }

        // 处理业务层面的错误
        if (!response.success) {
          reject(new Error(response.error || 'Unknown error occurred'));
          return;
        }

        resolve(response.data as T);
      });
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}
