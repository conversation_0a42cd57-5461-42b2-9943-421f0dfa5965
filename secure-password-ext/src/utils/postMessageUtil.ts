/**
 * @file postMessageUtil.ts
 * @description 使用 Promise 和 async/await 封装 window.postMessage，简化跨窗口通信 (TypeScript 版本)。
 */

// 消息命名空间，用于区分此工具发送的消息，避免与其他 postMessage 使用者冲突
const MESSAGE_NAMESPACE = 'secure-password-ext-message-channel';
// 默认请求超时时间 (毫秒)
const DEFAULT_TIMEOUT = 5000;

/**
 * 消息负载可以是任何可序列化的类型
 */
type MessagePayload = any;

/**
 * 消息处理函数类型
 * @template T - 期望从处理器返回的数据类型
 * @param payload - 消息的有效负载
 * @param event - 原始的 MessageEvent 对象
 * @returns 可以是直接的值，也可以是 Promise 解析后的值
 */
export type MessageHandler<T = MessagePayload, R = MessagePayload> = (
  payload: T,
  event: MessageEvent
) => R | Promise<R>;

/**
 * 内部消息结构
 */
interface Message<T = MessagePayload> {
  namespace: typeof MESSAGE_NAMESPACE;
  id: string;
  type: 'request' | 'response';
  action: string;
  payload?: T;
  error?: string;
}

/**
 * 生成唯一的ID字符串
 * @returns {string} 唯一的ID
 */
export function generateUniqueId(): string {
  return `${MESSAGE_NAMESPACE}-${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * 监听传入的消息。
 * @template ReqPayload - 请求负载的类型
 * @template ResPayload - 响应负载的类型
 * @param {Window} currentWindow - 当前窗口对象 (例如 window, self)
 * @param {string} expectedOrigin - 期望接收消息的来源 origin。 '*' 表示接受来自任何源的消息 (强烈不推荐在生产环境中使用)。
 * @param {MessageHandler<ReqPayload, ResPayload>} handler - 消息处理器函数。
 * @returns {() => void} 一个取消函数，调用它可以移除事件监听器。
 */
export function listenForPostMessages<ReqPayload = MessagePayload, ResPayload = MessagePayload>(
  currentWindow: Window,
  expectedOrigin: string,
  handler: MessageHandler<ReqPayload, ResPayload>
): () => void {
  if (!currentWindow || typeof currentWindow.addEventListener !== 'function') {
    console.error(`[${MESSAGE_NAMESPACE}] 无效的 currentWindow 对象提供给 listenForMessages。`);
    return () => {}; // 返回一个无操作的取消函数
  }
  if (typeof expectedOrigin !== 'string') {
    console.error(`[${MESSAGE_NAMESPACE}] expectedOrigin 必须是一个字符串。`);
    return () => {};
  }
  if (typeof handler !== 'function') {
    console.error(`[${MESSAGE_NAMESPACE}] handler 必须是一个函数。`);
    return () => {};
  }

  const messageListener = async (event: MessageEvent) => {
    // 1. 校验消息来源 (origin)
    // 对于 file:// 协议，event.origin 可能是 "null"，需要谨慎处理
    if (expectedOrigin !== '*' && event.origin !== expectedOrigin && !(expectedOrigin === 'file://' && event.origin === null)) {
      // console.warn(`[${MESSAGE_NAMESPACE}] 消息被忽略：来源 (${event.origin}) 与期望 (${expectedOrigin}) 不匹配。`);
      return;
    }

    const message = event.data as Message<ReqPayload>;

    // 2. 校验消息格式和命名空间
    if (typeof message !== 'object' || message === null || message.namespace !== MESSAGE_NAMESPACE) {
      // console.debug(`[${MESSAGE_NAMESPACE}] 消息被忽略：命名空间不匹配或格式无效。`, message);
      return;
    }

    const { id, type, action, payload } = message;

    // 3. 只处理 "request" 类型的消息
    if (type === 'request') {
      // console.debug(`[${MESSAGE_NAMESPACE}] 接收到请求 (ID: ${id}, Action: ${action}):`, payload);
      try {
        // payload 可能为 undefined，如果请求没有负载
        const result = await handler(payload as ReqPayload, event);
        // 发送成功响应
        if (event.source && typeof (event.source as Window).postMessage === 'function') {
          (event.source as Window).postMessage({
            namespace: MESSAGE_NAMESPACE,
            id: id, // 使用原始请求的 ID
            type: 'response',
            action: action, // 保留原始 action，便于调试或特定逻辑
            payload: result,
          } as Message<ResPayload>, event.origin === 'null' ? '*' : event.origin); // 回复到原始发送方
          // console.debug(`[${MESSAGE_NAMESPACE}] 已发送响应 (ID: ${id}):`, result);
        } else {
          // console.warn(`[${MESSAGE_NAMESPACE}] 无法发送响应：event.source 或 event.source.postMessage 无效。`);
        }
      } catch (error: any) {
        // console.error(`[${MESSAGE_NAMESPACE}] 处理请求时发生错误 (ID: ${id}, Action: ${action}):`, error);
        // 发送错误响应
        if (event.source && typeof (event.source as Window).postMessage === 'function') {
          (event.source as Window).postMessage({
            namespace: MESSAGE_NAMESPACE,
            id: id,
            type: 'response',
            action: action,
            error: error instanceof Error ? error.message : String(error),
          } as Message<never>, event.origin === 'null' ? '*' : event.origin);
        }
      }
    }
  };

  currentWindow.addEventListener('message', messageListener);

  // 返回一个取消函数
  return () => {
    currentWindow.removeEventListener('message', messageListener);
    // console.log(`[${MESSAGE_NAMESPACE}] 已为 origin "${expectedOrigin}" 移除消息监听器。`);
  };
}

/**
 * 发送消息到目标窗口并等待响应。
 * @template ReqPayload - 请求负载的类型
 * @template ResPayload - 期望的响应负载类型
 * @param {Window} targetWindow - 目标窗口对象。
 * @param {string} targetOrigin - 目标窗口的源。 '*' 表示任意来源 (强烈建议指定明确的源)。
 * @param {string} action - 操作类型/名称。
 * @param {ReqPayload} [payload] - 要发送的数据。
 * @param {number} [timeout=DEFAULT_TIMEOUT] - 等待响应的超时时间 (毫秒)。
 * @returns {Promise<ResPayload>} 返回一个 Promise，resolve 时为响应数据，reject 时为错误或超时。
 */
export function sendPostMessagePromise<ReqPayload = MessagePayload, ResPayload = MessagePayload>(
  targetWindow: Window,
  targetOrigin: string,
  action: string,
  payload?: ReqPayload,
  timeout: number = DEFAULT_TIMEOUT
): Promise<ResPayload> {
  return new Promise<ResPayload>((resolve, reject) => {
    if (!targetWindow || typeof targetWindow.postMessage !== 'function') {
      return reject(new Error(`[${MESSAGE_NAMESPACE}] 无效的 targetWindow 或 targetWindow.postMessage 不可用。`));
    }
    if (typeof targetOrigin !== 'string') {
        return reject(new Error(`[${MESSAGE_NAMESPACE}] targetOrigin 必须是一个字符串。`));
    }
    if (typeof action !== 'string' || action.trim() === '') {
        return reject(new Error(`[${MESSAGE_NAMESPACE}] action 必须是一个非空字符串。`));
    }

    const messageId = generateUniqueId();
    let timeoutId: number | undefined = undefined; // NodeJS.Timeout in Node, number in browser

    const responseListener = (event: MessageEvent) => {
      // 1. 校验响应来源
      if (targetOrigin !== '*' && event.origin !== targetOrigin && !(targetOrigin === 'file://' && event.origin === null)) {
        // console.warn(`[${MESSAGE_NAMESPACE}] 响应被忽略 (ID: ${messageId}): 来源 (${event.origin}) 与目标 (${targetOrigin}) 不匹配。`);
        return;
      }

      const response = event.data as Message<ResPayload>;

      // 2. 校验响应格式、命名空间和 ID
      if (
        typeof response === 'object' &&
        response !== null &&
        response.namespace === MESSAGE_NAMESPACE &&
        response.type === 'response' &&
        response.id === messageId
      ) {
        // console.debug(`[${MESSAGE_NAMESPACE}] 接收到响应 (ID: ${messageId}):`, response);
        if (typeof timeoutId === 'number') { // Check if timeoutId is a browser timeout ID
            clearTimeout(timeoutId);
        }
        window.removeEventListener('message', responseListener); // 清理监听器

        if (response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response.payload as ResPayload);
        }
      }
    };

    window.addEventListener('message', responseListener);

    const requestMessage: Message<ReqPayload> = {
      namespace: MESSAGE_NAMESPACE,
      id: messageId,
      type: 'request',
      action: action,
      payload: payload,
    };

    // console.debug(`[${MESSAGE_NAMESPACE}] 发送请求到 ${targetOrigin} (ID: ${messageId}, Action: ${action}):`, requestMessage);
    try {
      targetWindow.postMessage(requestMessage, targetOrigin);
    } catch (error: any) {
      // console.error(`[${MESSAGE_NAMESPACE}] 发送消息失败 (ID: ${messageId}):`, error);
      window.removeEventListener('message', responseListener);
      if (typeof timeoutId === 'number') {
        clearTimeout(timeoutId);
      }
      return reject(new Error(`[${MESSAGE_NAMESPACE}] 发送消息失败: ${error.message}`));
    }

    timeoutId = window.setTimeout(() => { // window.setTimeout returns a number
      window.removeEventListener('message', responseListener);
      reject(new Error(`[${MESSAGE_NAMESPACE}] 消息超时 (ID: ${messageId}, Action: "${action}")，等待时间 ${timeout}ms`));
    }, timeout);
  });
}

// 示例
// listenForPostMessages(window, '*', (payload, event) => {
//   console.log('Received message:', payload);
//   return { status: 'success', message: 'Hello from background!' };
// });
//
// sendPostMessagePromise(window, '*', 'test', { message: 'Hello from content script!' })
//   .then(response => {
//     console.log('Response from background:', response);
//   })
//   .catch(error => {
//     console.error('Error:', error);
//   });

