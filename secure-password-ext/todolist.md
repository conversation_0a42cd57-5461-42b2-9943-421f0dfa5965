密码管理Chrome扩展 - 待办事项清单

如果参考了 bitwarden-clients 目录下的代码实现，因为此目录下的代码使用 PL 3.0 license，需要保证在不违背 GPL 3.0 license 的情况下，可以闭源使用，再实现你要编写的代码。

已实现的核心服务
✅  RuntimeMessageService - 处理扩展内部通信，支持可取消的消息
✅  QuerySelectorDeepService - 高效查询DOM元素，包括Shadow DOM支持
待办事项
1. 创建表单检测服务 (FormDetectionService)
定义表单和表单字段接口
实现表单检测逻辑
添加MutationObserver监听DOM变化
实现Shadow DOM中表单的检测
添加表单变化通知机制
2. 创建表单监听服务 (FormListenerService)
实现表单字段焦点事件监听
实现表单字段失焦事件监听
添加事件委托优化
处理iframe中的表单
实现表单字段位置计算
3. 创建密码下拉菜单服务 (PasswordDropdownService)
实现密码列表获取逻辑
创建密码下拉菜单UI组件
实现密码填充功能
添加密码选择事件处理
实现下拉菜单定位逻辑
4. 创建内容脚本主入口 (ContentScriptMain)
初始化各服务
设置服务间事件监听
实现消息通信处理
添加错误处理和恢复机制
实现资源清理逻辑
4. 创建内容脚本主入口 (ContentScriptMain)
初始化各服务
设置服务间事件监听
实现消息通信处理
添加错误处理和恢复机制
实现资源清理逻辑

5. 创建后台脚本服务 (BackgroundServices)
5.1
创建消息处理服务 (BackgroundMessageService)
实现与内容脚本的通信接口
处理来自内容脚本的表单数据请求
实现密码查询和返回逻辑
添加错误处理和重试机制
5.2
创建安全通信服务 (SecureCommunicationService)
实现与原生应用的安全通信
添加消息加密和解密功能
实现会话管理和认证
处理连接中断和恢复
6. 优化现有服务
完善 QuerySelectorDeepService 的性能优化
添加查询结果缓存机制
优化Shadow DOM查询策略
实现增量DOM更新
增强 RuntimeMessageService 的错误处理
添加消息重试机制
完善超时处理
增强错误日志
性能优化任务
使用 IntersectionObserver 只处理可见区域的表单
使用 requestIdleCallback 在浏览器空闲时执行非关键操作
使用事件委托减少事件监听器数量
实现表单检测的延迟加载和懒初始化
健壮性增强任务
添加详细的错误边界和异常处理
实现服务的自动恢复机制
添加详细日志以便调试
使用类型安全的接口和消息格式
测试任务
创建单元测试
创建集成测试
测试各种复杂网站场景
测试性能和内存使用
实现顺序
首先实现 FormDetectionService
然后实现 FormListenerService
接着实现 PasswordDropdownService
最后整合到 ContentScriptMain
