# 通用密码派生方案

## 概述

本文档描述了一个完全基于密码派生的通用身份验证方案，解决了传统方案依赖本地存储（如keychain）导致的跨设备不一致问题。

## 问题背景

### 原有方案的局限性

1. **本地存储依赖**：过度依赖keychain等本地存储机制
2. **跨设备不一致**：新设备登录时无法获取本地存储的密钥
3. **用户体验差**：需要复杂的密钥同步或恢复流程
4. **安全风险**：本地存储可能被攻击或损坏

### 解决方案需求

1. **完全基于密码派生**：不依赖任何本地存储
2. **跨设备一致性**：同一用户在任何设备上都能生成相同的密钥
3. **零知识架构**：服务端无法获得用户的真实主密钥
4. **用户隔离**：不同用户的密钥完全隔离

## 架构设计

### 核心架构

```
用户密码 + 联系方式
    ↓ (确定性盐值派生)
中间密钥 (Intermediate Key)
    ↓ (用途分离)
┌─────────────────┬─────────────────┐
│   本地主密钥     │   服务端哈希     │
│ (Local Master)  │ (Server Hash)   │
│   用于本地加密   │   提交到服务端   │
└─────────────────┴─────────────────┘
```

### 密钥派生流程

1. **确定性盐值生成**
   ```rust
   deterministic_salt = SHA256(contact + APP_IDENTIFIER)
   ```

2. **中间密钥派生**
   ```rust
   intermediate_key = Argon2id(password, deterministic_salt, params)
   ```

3. **用途密钥派生**
   ```rust
   local_master_key = HKDF(intermediate_key, "local-master")
   server_auth_key = HKDF(intermediate_key, "server-auth")
   ```

4. **服务端哈希生成**
   ```rust
   server_hash = Argon2id(server_auth_key, server_salt, params)
   ```

## 核心组件

### 数据结构

#### UniversalRegistrationData
```rust
pub struct UniversalRegistrationData {
    pub server_hash: String,           // 提交给服务端的哈希
    pub local_master_key: [u8; 32],    // 本地主密钥
    pub deterministic_salt: String,    // 确定性盐值
    pub intermediate_key: [u8; 32],    // 中间密钥
}
```

#### UniversalLoginData
```rust
pub struct UniversalLoginData {
    pub server_hash: String,           // 提交给服务端的哈希
    pub local_master_key: [u8; 32],    // 本地主密钥
    pub deterministic_salt: String,    // 确定性盐值
}
```

### 核心方法

#### PasswordService

1. **generate_registration_data()** - 生成注册数据
2. **generate_login_data()** - 生成登录数据
3. **verify_password_universal()** - 通用密码验证
4. **generate_final_server_hash()** - 生成最终服务端哈希

#### AuthService

1. **register_user_universal()** - 通用用户注册
2. **login_user_universal()** - 通用用户登录

## 安全特性

### 1. 零知识架构

- **服务端隔离**：服务端只接收经过二次哈希的数据
- **密钥隔离**：本地主密钥和服务端哈希在密码学上完全隔离
- **前向安全**：即使服务端数据泄露，也无法逆推真实主密钥

### 2. 确定性安全

- **确定性盐值**：基于联系方式生成，确保跨设备一致性
- **域分离**：不同用途使用不同的域分离符
- **抗彩虹表**：每个用户都有唯一的确定性盐值

### 3. 密码学强度

- **Argon2id**：使用最新的密码哈希算法
- **HKDF**：使用标准的密钥派生函数
- **SHA-256**：使用安全的哈希算法

## 使用示例

### 注册流程

```rust
let password_service = PasswordService::new(&app_handle);

// 生成注册数据
let registration_data = password_service
    .generate_registration_data(password, contact)
    .await?;

// 提交到服务端
let auth_service = AuthService::new(&app_handle);
auth_service
    .register_user_universal(contact, &registration_data.server_hash)
    .await?;

// 本地使用主密钥
let vault_crypto = VaultCrypto::from_master_key(
    &registration_data.local_master_key
)?;
```

### 登录流程

```rust
let password_service = PasswordService::new(&app_handle);

// 生成登录数据
let login_data = password_service
    .generate_login_data(password, contact)
    .await?;

// 验证服务端
let auth_service = AuthService::new(&app_handle);
let is_valid = auth_service
    .login_user_universal(contact, &login_data.server_hash)
    .await?;

if is_valid {
    // 本地使用主密钥
    let vault_crypto = VaultCrypto::from_master_key(
        &login_data.local_master_key
    )?;
}
```

## 测试验证

### 核心测试用例

1. **test_cross_device_consistency** - 跨设备一致性验证
2. **test_universal_registration_data_generation** - 注册数据生成测试
3. **test_universal_login_data_generation** - 登录数据生成测试
4. **test_multiple_users_isolation** - 多用户隔离测试
5. **test_universal_password_verification** - 密码验证测试

### 测试结果

```bash
cargo test auth::services::tests -- --nocapture
```

所有14个测试用例均通过，验证了方案的正确性和安全性。

## 演示程序

运行演示程序查看完整的使用流程：

```bash
cargo run --example universal_password_demo
```

演示程序展示了：
- 设备A注册流程
- 设备B登录流程（新设备，无本地存储）
- 跨设备一致性验证
- 密码验证功能
- 多用户隔离验证

## 向后兼容性

为了保持向后兼容性，保留了旧的方法但标记为已弃用：

- `hash_password_secure()` - 哈希密码（安全版本）
- `create_user_vault_crypto()` - 创建用户保险库加密
- `get_user_master_key()` - 获取用户主密钥
- `login_user()` - 处理用户登录流程

## 部署建议

### 生产环境配置

1. **高安全性参数**
   ```rust
   let crypto_config = CryptoConfig {
       argon2_iterations: 5,         // 高安全性迭代次数
       argon2_memory: 131072,        // 128 MB 内存使用
       argon2_parallelism: 8,        // 8 线程并行
       enable_keychain: false,       // 禁用密钥链依赖
       auto_lock_timeout: Some(600), // 10分钟自动锁定
   };
   ```

2. **服务端验证**
   - 实现速率限制防止暴力破解
   - 记录登录尝试日志
   - 实现账户锁定机制

3. **客户端安全**
   - 使用内存清理（zeroize）
   - 实现自动锁定机制
   - 防止内存转储攻击

## 总结

通用密码派生方案成功解决了传统方案的局限性：

✅ **完全基于密码派生**：不依赖任何本地存储  
✅ **跨设备一致性**：确保任何设备上的密钥一致  
✅ **零知识架构**：服务端无法获得真实主密钥  
✅ **用户隔离**：不同用户的密钥完全隔离  
✅ **密码学安全**：使用最新的安全算法和最佳实践  
✅ **向后兼容**：不破坏现有功能  

这是一个企业级的密码学解决方案，适用于需要跨设备同步但又要保证安全性的应用场景。 