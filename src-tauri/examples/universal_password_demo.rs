/// 通用密码派生方案演示
///
/// 此演示展示了如何使用完全基于密码派生的通用方案，
/// 不依赖任何本地存储，适配跨设备使用场景。
use secure_password_lib::auth::services::PasswordService;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();

    println!("=== 通用密码派生方案演示 ===\n");

    // 模拟用户信息
    let contact = "<EMAIL>";
    let password = "DemoPassword123!";

    println!("用户信息:");
    println!("联系方式: {}", contact);
    println!("密码: {}", password);
    println!();

    // 创建密码服务
    let password_service = PasswordService {};

    // === 场景1: 设备A注册 ===
    println!("=== 场景1: 设备A注册 ===");
    let registration_data = password_service
        .generate_registration_data(password.to_string(), contact.to_string())
        .await?;

    println!("注册数据生成成功:");
    println!("服务端哈希: {}", &registration_data.server_hash[..50]);
    println!("确定性盐值: {}", registration_data.deterministic_salt);
    println!(
        "本地主密钥: {}",
        hex::encode(&registration_data.local_master_key[..8])
    );
    println!();

    // === 场景2: 设备B登录（新设备，无本地存储） ===
    println!("=== 场景2: 设备B登录（新设备，无本地存储） ===");
    let login_data = password_service
        .generate_login_data(password.to_string(), contact.to_string())
        .await?;

    println!("登录数据生成成功:");
    println!("服务端哈希: {}", &login_data.server_hash[..50]);
    println!("确定性盐值: {}", login_data.deterministic_salt);
    println!(
        "本地主密钥: {}",
        hex::encode(&login_data.local_master_key[..8])
    );
    println!();

    // === 验证跨设备一致性 ===
    println!("=== 跨设备一致性验证 ===");
    let server_hash_match = registration_data.server_hash == login_data.server_hash;
    let local_key_match = registration_data.local_master_key == login_data.local_master_key;
    let salt_match = registration_data.deterministic_salt == login_data.deterministic_salt;

    println!(
        "服务端哈希一致性: {}",
        if server_hash_match { "✓" } else { "✗" }
    );
    println!(
        "本地主密钥一致性: {}",
        if local_key_match { "✓" } else { "✗" }
    );
    println!("确定性盐值一致性: {}", if salt_match { "✓" } else { "✗" });
    println!();

    // === 密码验证 ===
    println!("=== 密码验证 ===");
    let correct_password_valid = password_service
        .verify_password_universal(
            password.to_string(),
            contact,
            &registration_data.server_hash,
        )
        .await?;

    let wrong_password_valid = password_service
        .verify_password_universal(
            "WrongPassword123!".to_string(),
            contact,
            &registration_data.server_hash,
        )
        .await?;

    println!(
        "正确密码验证: {}",
        if correct_password_valid { "✓" } else { "✗" }
    );
    println!(
        "错误密码验证: {}",
        if !wrong_password_valid { "✓" } else { "✗" }
    );
    println!();

    // === 多用户隔离验证 ===
    println!("=== 多用户隔离验证 ===");
    let user2_contact = "<EMAIL>";
    let user2_data = password_service
        .generate_registration_data(password.to_string(), user2_contact.to_string())
        .await?;

    let users_isolated = registration_data.server_hash != user2_data.server_hash;
    println!(
        "用户1: {} - 哈希: {}",
        contact,
        &registration_data.server_hash[..20]
    );
    println!(
        "用户2: {} - 哈希: {}",
        user2_contact,
        &user2_data.server_hash[..20]
    );
    println!("用户隔离: {}", if users_isolated { "✓" } else { "✗" });
    println!();

    // === 总结 ===
    println!("=== 总结 ===");
    println!("✓ 完全基于密码派生，不依赖本地存储");
    println!("✓ 跨设备一致性保证");
    println!("✓ 零知识架构，服务端无法获得真实主密钥");
    println!("✓ 用户间完全隔离");
    println!("✓ 密码验证功能正常");
    println!();
    println!("通用密码派生方案演示完成！");

    Ok(())
}
