<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.secure_password" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- 状态栏完全透明 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <!-- 状态栏内容为浅色（适用于深色背景） -->
        <item name="android:windowLightStatusBar">false</item>
        <!-- 全屏显示，内容延伸到状态栏 -->
        <item name="android:windowTranslucentStatus">true</item>
        <!-- 导航栏完全透明 -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <!-- 启用边到边显示 -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <!-- 确保窗口绘制在系统栏后面 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <!-- 启用沉浸式模式 -->
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
</resources>
