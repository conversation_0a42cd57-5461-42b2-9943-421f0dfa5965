// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		05837A89037011117E93B973 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 80E8B7EE2B49953BEF7B28EF /* Metal.framework */; };
		0D23D317F6A6611FD0056E2E /* libapp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = CB42161E9EF60D7619D1FE2E /* libapp.a */; };
		0EB360487ADF430644DF7DC6 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E1EB3454FAD3DD97A3AF0259 /* CoreGraphics.framework */; };
		352BD46C166BC84EAB78B0E9 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7CA017E727EF141E342A7BD /* UIKit.framework */; };
		39AD7EB755EC6F76A594DED4 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 804EB57A0C6A5B45C878314A /* LaunchScreen.storyboard */; };
		524C0080385C3F3B413C0D96 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 5D7A890A99526E27FD40EFE0 /* assets */; };
		67FBC0814BA0197F675B049D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 53447B133BF54615B248A129 /* Assets.xcassets */; };
		8C3336A5E731FF6ABBD6010D /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9C4251B6499B2083526E6617 /* MetalKit.framework */; };
		A5E905114014BFA94A561BFE /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6C889A273AD267D44372A9BA /* WebKit.framework */; };
		AA5DB6AA53461BD2088EB5B4 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = 7541448DB67DD6333089082C /* main.mm */; };
		C1711BCEBAC860EBFC7481C3 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66386C263434690C12BE23ED /* Security.framework */; };
		E6AFAC2903E8762F208A55EF /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C721EA740F42CDFF59F4B0BA /* QuartzCore.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		01BF9DA97481E76AA5E46A75 /* errors.rs */ = {isa = PBXFileReference; path = errors.rs; sourceTree = "<group>"; };
		02D981C872F2489808CE0C38 /* test_remote_auth.rs */ = {isa = PBXFileReference; path = test_remote_auth.rs; sourceTree = "<group>"; };
		033729ADC95B53BE9AAE0BD0 /* REGISTRATION_FLOW_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REGISTRATION_FLOW_IMPLEMENTATION.md; sourceTree = "<group>"; };
		0661CD3E22CD487F440AB5A9 /* vault_crypto.rs */ = {isa = PBXFileReference; path = vault_crypto.rs; sourceTree = "<group>"; };
		06C3E43673F1864E5A083C2F /* state.rs */ = {isa = PBXFileReference; path = state.rs; sourceTree = "<group>"; };
		0A24F80609D277E3477BDF63 /* integration_tests.rs */ = {isa = PBXFileReference; path = integration_tests.rs; sourceTree = "<group>"; };
		0C618521CB18B84FC80EBD04 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		0D40BB5C168B0045EFD13353 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		0DDC48AA8A009C44DF41E554 /* database.rs */ = {isa = PBXFileReference; path = database.rs; sourceTree = "<group>"; };
		0F169AC8186496EC78CCD1AC /* CONTACT_BASED_SALT_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = CONTACT_BASED_SALT_IMPLEMENTATION.md; sourceTree = "<group>"; };
		15B2985EEF1CB83592B591FA /* SOFT_DELETE_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = SOFT_DELETE_IMPLEMENTATION.md; sourceTree = "<group>"; };
		164E0AA0ECFEC4ACE78BE057 /* IMPLEMENTATION_GUIDE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = IMPLEMENTATION_GUIDE.md; sourceTree = "<group>"; };
		16AD4FA8A344FC11E5180B3E /* models.rs */ = {isa = PBXFileReference; path = models.rs; sourceTree = "<group>"; };
		16B6D481F9A95ACACCB546B4 /* REMOTE_AUTH_GUIDE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REMOTE_AUTH_GUIDE.md; sourceTree = "<group>"; };
		16CEF088506E3699A747AF71 /* HTTP_AUTH_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = HTTP_AUTH_IMPLEMENTATION.md; sourceTree = "<group>"; };
		182FAB9E0D33BBB36372A032 /* examples.rs */ = {isa = PBXFileReference; path = examples.rs; sourceTree = "<group>"; };
		1A3D3758F64F26D279AEC572 /* REMOTE_KEYS_SYNC_USAGE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REMOTE_KEYS_SYNC_USAGE.md; sourceTree = "<group>"; };
		1B9FB4D7C4730A6D2FC9C4A1 /* examples.rs */ = {isa = PBXFileReference; path = examples.rs; sourceTree = "<group>"; };
		1D65C4A5C1BD4E6DB5AFF43F /* orm_commands.rs */ = {isa = PBXFileReference; path = orm_commands.rs; sourceTree = "<group>"; };
		2106769CD93AC64699F5BDB4 /* three_way_merge_tests.rs */ = {isa = PBXFileReference; path = three_way_merge_tests.rs; sourceTree = "<group>"; };
		2118AEC5CA795E528E08D84D /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		213A88065EA7FC3F1E46EA13 /* errors.rs */ = {isa = PBXFileReference; path = errors.rs; sourceTree = "<group>"; };
		21FB0CEE305E14CEAD1A5FD8 /* three_way_merge.rs */ = {isa = PBXFileReference; path = three_way_merge.rs; sourceTree = "<group>"; };
		24B8A43D85663F264D2C07BF /* README_PURE_VECTOR_CLOCK.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_PURE_VECTOR_CLOCK.md; sourceTree = "<group>"; };
		24C2D354D26CC7A96018243C /* VECTOR_CLOCK_IMPLEMENTATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = VECTOR_CLOCK_IMPLEMENTATION_SUMMARY.md; sourceTree = "<group>"; };
		291044A722547DB8618B8094 /* config.rs */ = {isa = PBXFileReference; path = config.rs; sourceTree = "<group>"; };
		29968E93927D2FCB68B2DDBE /* TOKEN_MANAGER_UPDATE_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TOKEN_MANAGER_UPDATE_SUMMARY.md; sourceTree = "<group>"; };
		29E2A7FD78FEA3FEB5FFC79F /* sync_status.rs */ = {isa = PBXFileReference; path = sync_status.rs; sourceTree = "<group>"; };
		2CB85F461F9DC0311791ABD3 /* icons.rs */ = {isa = PBXFileReference; path = icons.rs; sourceTree = "<group>"; };
		2D912F9421B5EFB5B56B4A83 /* errors.rs */ = {isa = PBXFileReference; path = errors.rs; sourceTree = "<group>"; };
		2F561CCAD224395E43A05C94 /* vector_clock.rs */ = {isa = PBXFileReference; path = vector_clock.rs; sourceTree = "<group>"; };
		300631369481EBAD020E9372 /* TRAY_MENU_FIX.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TRAY_MENU_FIX.md; sourceTree = "<group>"; };
		31A6B7475626AE6892833CE6 /* PASSWORD_MANAGER_SYNC_ROADMAP.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = PASSWORD_MANAGER_SYNC_ROADMAP.md; sourceTree = "<group>"; };
		34805F3D530C1EDC18CA4AA8 /* utils.rs */ = {isa = PBXFileReference; path = utils.rs; sourceTree = "<group>"; };
		3483154FA38D1D57A1358895 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		3660147ECC4DE14D6C625961 /* key_derivation.rs */ = {isa = PBXFileReference; path = key_derivation.rs; sourceTree = "<group>"; };
		36F1C4B42861D4EB6C13FD1C /* examples.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = examples.md; sourceTree = "<group>"; };
		374C64A51EB16F246CA0EC89 /* vector_clock_demo.rs */ = {isa = PBXFileReference; path = vector_clock_demo.rs; sourceTree = "<group>"; };
		37681E2A7832DB0FB33D2084 /* USER_PERSISTENCE_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = USER_PERSISTENCE_IMPLEMENTATION.md; sourceTree = "<group>"; };
		399BAE0ED864540C8BCC7303 /* manager.rs */ = {isa = PBXFileReference; path = manager.rs; sourceTree = "<group>"; };
		3AA189DD5AEA76F70DEA7655 /* utils.rs */ = {isa = PBXFileReference; path = utils.rs; sourceTree = "<group>"; };
		3B23B55A29BC39FF4830B112 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		3B2712CFFB51107C688A1655 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		3E420858EB8E97DB8831B3E9 /* README_crypto.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_crypto.md; sourceTree = "<group>"; };
		40053780482F4AC77CC06B8E /* conflict_resolver.rs */ = {isa = PBXFileReference; path = conflict_resolver.rs; sourceTree = "<group>"; };
		428FAFD1C9130A1E6B16DCF4 /* tray_integration.rs */ = {isa = PBXFileReference; path = tray_integration.rs; sourceTree = "<group>"; };
		447FD04986D2B49D8B0187D3 /* main.rs */ = {isa = PBXFileReference; path = main.rs; sourceTree = "<group>"; };
		44D5D264FD6DD8C8BF2B30FC /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		4A1DB273E56E2B687550BE05 /* IMPLEMENTATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = IMPLEMENTATION_SUMMARY.md; sourceTree = "<group>"; };
		4BABE19FBBE0D9A2C6166751 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		4BFED44A5062682146B02B7A /* keychain_master_key_sync.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = keychain_master_key_sync.md; sourceTree = "<group>"; };
		4E88BC963937BBD8D02C1631 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		5120D456B898E87B1F470E23 /* sync_storage.rs */ = {isa = PBXFileReference; path = sync_storage.rs; sourceTree = "<group>"; };
		512E9990906C017180600DE3 /* HYBRID_STORAGE_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = HYBRID_STORAGE_SUMMARY.md; sourceTree = "<group>"; };
		5220D27502BA240AA69986EC /* m20241202_000002_add_archive_fields.rs */ = {isa = PBXFileReference; path = m20241202_000002_add_archive_fields.rs; sourceTree = "<group>"; };
		53447B133BF54615B248A129 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		5745A94725341DE6F072B210 /* handler.rs */ = {isa = PBXFileReference; path = handler.rs; sourceTree = "<group>"; };
		58C8880F2B2896237944446F /* REMOTE_AUTH_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REMOTE_AUTH_IMPLEMENTATION.md; sourceTree = "<group>"; };
		5A7207055B731A0C5F87BC1C /* pure_vector_clock_sync.rs */ = {isa = PBXFileReference; path = pure_vector_clock_sync.rs; sourceTree = "<group>"; };
		5CAF54168AF9F54D69F1F61B /* key_pair.rs */ = {isa = PBXFileReference; path = key_pair.rs; sourceTree = "<group>"; };
		5CFABD9EC04B9F1953110891 /* integration_test.rs */ = {isa = PBXFileReference; path = integration_test.rs; sourceTree = "<group>"; };
		5D6B660EE32DC98CB7FBE7AE /* device_info.rs */ = {isa = PBXFileReference; path = device_info.rs; sourceTree = "<group>"; };
		5D7A890A99526E27FD40EFE0 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = SOURCE_ROOT; };
		5E85CE061C932FB45B941431 /* events.rs */ = {isa = PBXFileReference; path = events.rs; sourceTree = "<group>"; };
		60B7BF3A0DE840664F0AAED9 /* ASYNC_HANDLER_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = ASYNC_HANDLER_SUMMARY.md; sourceTree = "<group>"; };
		61DDA1EF492523A571732027 /* TOKEN_MANAGER_USAGE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TOKEN_MANAGER_USAGE.md; sourceTree = "<group>"; };
		61FBF1A12B46FD77BF772384 /* m20241202_000001_add_soft_delete.rs */ = {isa = PBXFileReference; path = m20241202_000001_add_soft_delete.rs; sourceTree = "<group>"; };
		624BAF37E7A8F4F3F5709172 /* DOMAIN_MATCHING_OPTIMIZATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = DOMAIN_MATCHING_OPTIMIZATION.md; sourceTree = "<group>"; };
		6582AE07B98D8A0334739E18 /* vector_clock_example.rs */ = {isa = PBXFileReference; path = vector_clock_example.rs; sourceTree = "<group>"; };
		65957BAC6106F2476043EBD1 /* item.rs */ = {isa = PBXFileReference; path = item.rs; sourceTree = "<group>"; };
		662C9473E27DCE225B3D60C3 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		66386C263434690C12BE23ED /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		6875A0D1C3CE5B06FAA4598D /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		6B0829E87D57C3695F86467E /* manager.rs */ = {isa = PBXFileReference; path = manager.rs; sourceTree = "<group>"; };
		6B9FA6AA3EB5D3C888F6AE59 /* SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = SUMMARY.md; sourceTree = "<group>"; };
		6C889A273AD267D44372A9BA /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		6D35B493D6A4E4EDABE540EB /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		6ECC648B7C142BC5D8A952AB /* CRYPTO_INTEGRATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = CRYPTO_INTEGRATION.md; sourceTree = "<group>"; };
		6F424289F22D5022C93482C4 /* UPDATE_CREDENTIAL_ENCRYPTION_FIX.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = UPDATE_CREDENTIAL_ENCRYPTION_FIX.md; sourceTree = "<group>"; };
		7250E98ABB16A0C2E296833A /* vault.rs */ = {isa = PBXFileReference; path = vault.rs; sourceTree = "<group>"; };
		73283360B5410B1908D47BAD /* state.rs */ = {isa = PBXFileReference; path = state.rs; sourceTree = "<group>"; };
		74E3B8F70FB202CBA65C994C /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		74F9C26BAE269B8EA0BE8201 /* REFACTOR_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REFACTOR_SUMMARY.md; sourceTree = "<group>"; };
		7541448DB67DD6333089082C /* main.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		760596F5B56AE50E6A10FC20 /* integration_tests.rs */ = {isa = PBXFileReference; path = integration_tests.rs; sourceTree = "<group>"; };
		76DE3AA5C2733F15186C97EA /* three_way_merge_demo.rs */ = {isa = PBXFileReference; path = three_way_merge_demo.rs; sourceTree = "<group>"; };
		7A562D0DDA8B36C14B8DE930 /* tests.rs */ = {isa = PBXFileReference; path = tests.rs; sourceTree = "<group>"; };
		7B27D37CC5E283D4D3859006 /* DEVICE_INFO_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = DEVICE_INFO_IMPLEMENTATION.md; sourceTree = "<group>"; };
		7B474F96E7CC24C8469689B2 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		7B6F3DD41CF1FFD3A0984D1D /* DEPRECATED_CODE_CLEANUP.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = DEPRECATED_CODE_CLEANUP.md; sourceTree = "<group>"; };
		7D7B64A5C1E966A55486A7B5 /* OPTIMIZATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = OPTIMIZATION_SUMMARY.md; sourceTree = "<group>"; };
		8041A5AD38221959B9050D15 /* services.rs */ = {isa = PBXFileReference; path = services.rs; sourceTree = "<group>"; };
		804EB57A0C6A5B45C878314A /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		8073F9011E62C88EA7C0C897 /* examples.rs */ = {isa = PBXFileReference; path = examples.rs; sourceTree = "<group>"; };
		80E8B7EE2B49953BEF7B28EF /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		81425A62E6FA7B8E0916FC58 /* config.rs */ = {isa = PBXFileReference; path = config.rs; sourceTree = "<group>"; };
		82D7AEE4FB5BFD117D5F43E0 /* secure-password_iOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "secure-password_iOS.entitlements"; sourceTree = "<group>"; };
		8645F5D31175CEABAE9329D0 /* INTEGRATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = INTEGRATION_SUMMARY.md; sourceTree = "<group>"; };
		87321D2C005D0E55E413CFAA /* validation.rs */ = {isa = PBXFileReference; path = validation.rs; sourceTree = "<group>"; };
		8737D79801EF910AC6A50B2F /* three_way_merge_summary.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = three_way_merge_summary.md; sourceTree = "<group>"; };
		881EDF56C6D9FD23D67A8526 /* types.rs */ = {isa = PBXFileReference; path = types.rs; sourceTree = "<group>"; };
		89AFC72110F0B8C490AA0F7D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		89DB091F23FDE62DBB133574 /* REFACTORING_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REFACTORING_SUMMARY.md; sourceTree = "<group>"; };
		8BA8CA71F4DCDEE8DF353147 /* m20241201_000001_create_tables.rs */ = {isa = PBXFileReference; path = m20241201_000001_create_tables.rs; sourceTree = "<group>"; };
		8CECCB89A2C2B8FDF7560E44 /* menu.rs */ = {isa = PBXFileReference; path = menu.rs; sourceTree = "<group>"; };
		8EAFFE744583A4670BF2231E /* lib.rs */ = {isa = PBXFileReference; path = lib.rs; sourceTree = "<group>"; };
		8ED4404173429E4748F124B3 /* example.rs */ = {isa = PBXFileReference; path = example.rs; sourceTree = "<group>"; };
		9473526BFCCAC60835B52A8B /* ntp_client.rs */ = {isa = PBXFileReference; path = ntp_client.rs; sourceTree = "<group>"; };
		94D2F416A34304B27D564707 /* commands.rs */ = {isa = PBXFileReference; path = commands.rs; sourceTree = "<group>"; };
		957C9476E159614DDEB33A5B /* secure-password_iOS.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = "secure-password_iOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		970DC3015BA1CA511CA18A53 /* README_AUTH_MODULE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_AUTH_MODULE.md; sourceTree = "<group>"; };
		97A7FD97B8B206E4BFD8ED0C /* platform.rs */ = {isa = PBXFileReference; path = platform.rs; sourceTree = "<group>"; };
		97E3489F006FC9ADA76FB0E6 /* events.rs */ = {isa = PBXFileReference; path = events.rs; sourceTree = "<group>"; };
		98726426F54CB5C167434FEA /* ntp.rs */ = {isa = PBXFileReference; path = ntp.rs; sourceTree = "<group>"; };
		98E548569FC8C49C98E1B331 /* README_async_handler.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_async_handler.md; sourceTree = "<group>"; };
		9A146268830F6CE7D17C0134 /* log_tracker.rs */ = {isa = PBXFileReference; path = log_tracker.rs; sourceTree = "<group>"; };
		9B1B3B73055528C283DDC96B /* interceptor.rs */ = {isa = PBXFileReference; path = interceptor.rs; sourceTree = "<group>"; };
		9C4251B6499B2083526E6617 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		9D0FCBD93D8B8AF27FE654B1 /* CRYPTO_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = CRYPTO_SUMMARY.md; sourceTree = "<group>"; };
		9D4336BFC990D619284F98BB /* integration_test.rs */ = {isa = PBXFileReference; path = integration_test.rs; sourceTree = "<group>"; };
		9F67217226DF46D22CA9F071 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		A12FD46D97BDDF0E68DA2F13 /* server.rs */ = {isa = PBXFileReference; path = server.rs; sourceTree = "<group>"; };
		A1ABCC5DD8CFC31DF84DC597 /* keychain_adapter.rs */ = {isa = PBXFileReference; path = keychain_adapter.rs; sourceTree = "<group>"; };
		A38BF32A1410450B073299F3 /* menu.rs */ = {isa = PBXFileReference; path = menu.rs; sourceTree = "<group>"; };
		A65D900EE36336ECD4E75895 /* orm_database.rs */ = {isa = PBXFileReference; path = orm_database.rs; sourceTree = "<group>"; };
		A76EFAB7D27DAD5356BF0AB8 /* token_manager.rs */ = {isa = PBXFileReference; path = token_manager.rs; sourceTree = "<group>"; };
		A9DA30E04A82411E263A27CE /* login.rs */ = {isa = PBXFileReference; path = login.rs; sourceTree = "<group>"; };
		AA6B8590393B52E2345AC689 /* services.rs */ = {isa = PBXFileReference; path = services.rs; sourceTree = "<group>"; };
		AA9DB9C8B88C0CDAE3F87139 /* compat.rs */ = {isa = PBXFileReference; path = compat.rs; sourceTree = "<group>"; };
		ADD830D180CC36E11B576459 /* HYBRID_STORAGE_REFACTOR_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = HYBRID_STORAGE_REFACTOR_SUMMARY.md; sourceTree = "<group>"; };
		ADE8D5234F08CE66DA23C8A0 /* TASK_COMPLETION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TASK_COMPLETION_SUMMARY.md; sourceTree = "<group>"; };
		AEE8C57343CD5B8527BD12F2 /* mobile.rs */ = {isa = PBXFileReference; path = mobile.rs; sourceTree = "<group>"; };
		AF51A41A309D96364D0D7A96 /* integration_example.rs */ = {isa = PBXFileReference; path = integration_example.rs; sourceTree = "<group>"; };
		B05210483A7DCCA7514FA7F9 /* orm_services.rs */ = {isa = PBXFileReference; path = orm_services.rs; sourceTree = "<group>"; };
		B10B0E3277C3BA71AE056B2C /* repositories.rs */ = {isa = PBXFileReference; path = repositories.rs; sourceTree = "<group>"; };
		B1BDE8746FB43A1291F0C2E7 /* NATIVE_MESSAGING_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = NATIVE_MESSAGING_SUMMARY.md; sourceTree = "<group>"; };
		B381336564D08850EE304A4D /* simple_api.rs */ = {isa = PBXFileReference; path = simple_api.rs; sourceTree = "<group>"; };
		B7CA017E727EF141E342A7BD /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		B7F1E10AC76E6552DB538A75 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		BC9203D2570ED22CD512EBF0 /* commands.rs */ = {isa = PBXFileReference; path = commands.rs; sourceTree = "<group>"; };
		BCE32BAC28F76921B3A42FB9 /* README_THREE_WAY_MERGE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_THREE_WAY_MERGE.md; sourceTree = "<group>"; };
		BD35FF4B9A780CB8257B43B1 /* symmetric.rs */ = {isa = PBXFileReference; path = symmetric.rs; sourceTree = "<group>"; };
		BF1DEC87D7B824BF6EFC4C48 /* merge.rs */ = {isa = PBXFileReference; path = merge.rs; sourceTree = "<group>"; };
		C00CBB94B4740968626719DB /* REMOTE_API_INTEGRATION_UPDATE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REMOTE_API_INTEGRATION_UPDATE.md; sourceTree = "<group>"; };
		C19C39BF884D04B94D73E9C0 /* HTTP_AUTH_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = HTTP_AUTH_SUMMARY.md; sourceTree = "<group>"; };
		C1E803BFFD9F42C79F15BAB1 /* dependencies.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = dependencies.md; sourceTree = "<group>"; };
		C64E9DC31B050C1F0B943F5F /* SIMPLIFICATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = SIMPLIFICATION_SUMMARY.md; sourceTree = "<group>"; };
		C721EA740F42CDFF59F4B0BA /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		C89622A8D5D0FA669D4C89AD /* ARCHIVE_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = ARCHIVE_IMPLEMENTATION.md; sourceTree = "<group>"; };
		C949D1B7DB6979DBAB53A278 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		CB42161E9EF60D7619D1FE2E /* libapp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libapp.a; sourceTree = "<group>"; };
		CD55FC0D6DC7F3B7607B3438 /* SMART_REGISTRATION_SUMMARY.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = SMART_REGISTRATION_SUMMARY.md; sourceTree = "<group>"; };
		CF284B8015BBBE58DBD0A13B /* api.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = api.md; sourceTree = "<group>"; };
		D0EE0DC5581054DF0C5A09E9 /* sync_manager.rs */ = {isa = PBXFileReference; path = sync_manager.rs; sourceTree = "<group>"; };
		D1D497C53A5A0EA253CB9A10 /* errors.rs */ = {isa = PBXFileReference; path = errors.rs; sourceTree = "<group>"; };
		D4B27B519034588A4A3C972B /* storage.rs */ = {isa = PBXFileReference; path = storage.rs; sourceTree = "<group>"; };
		D525E6E56999CD44FF3B3D02 /* bindings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bindings.h; sourceTree = "<group>"; };
		D5D63505222F1F8D003F9501 /* models.rs */ = {isa = PBXFileReference; path = models.rs; sourceTree = "<group>"; };
		D8F79D831FCA197FB72A21DE /* TRAY_LOG_OPTIMIZATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TRAY_LOG_OPTIMIZATION.md; sourceTree = "<group>"; };
		DAB960F6F22AF17C31A114CE /* types.rs */ = {isa = PBXFileReference; path = types.rs; sourceTree = "<group>"; };
		DADAA6A729BFE0B4AD579D44 /* TECHNICAL_SPECIFICATIONS.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TECHNICAL_SPECIFICATIONS.md; sourceTree = "<group>"; };
		DD78EC44314BDDB1FD400E60 /* PURE_VECTOR_CLOCK_SYNC.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = PURE_VECTOR_CLOCK_SYNC.md; sourceTree = "<group>"; };
		E1EB3454FAD3DD97A3AF0259 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		E25DF9AF76B442E5151288FD /* README_hybrid_storage.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_hybrid_storage.md; sourceTree = "<group>"; };
		E37112F197F83D0BF043B6AF /* vector_clock.rs */ = {isa = PBXFileReference; path = vector_clock.rs; sourceTree = "<group>"; };
		E5FE05A7FA9D0355C6B9290A /* manager.rs */ = {isa = PBXFileReference; path = manager.rs; sourceTree = "<group>"; };
		E6ADA317E2744162FB861BD0 /* protocol.rs */ = {isa = PBXFileReference; path = protocol.rs; sourceTree = "<group>"; };
		E76ABCF814237B1F40423CEB /* config.rs */ = {isa = PBXFileReference; path = config.rs; sourceTree = "<group>"; };
		E8DF2F1E202AE6A9146E00FE /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		E8E7766CDFE5AD2CBCA988BA /* client.rs */ = {isa = PBXFileReference; path = client.rs; sourceTree = "<group>"; };
		EB934A6ECD8F3D5D7EF902C4 /* sync_commands.rs */ = {isa = PBXFileReference; path = sync_commands.rs; sourceTree = "<group>"; };
		EBB149D48C9C113E8A8590E3 /* REGISTRATION_FORM_UPDATES.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = REGISTRATION_FORM_UPDATES.md; sourceTree = "<group>"; };
		ED120B66D0BBED69809394F2 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		EE50792F3C9BBCFF11E39E0A /* keychain.rs */ = {isa = PBXFileReference; path = keychain.rs; sourceTree = "<group>"; };
		EF2003650067F3C465E576A0 /* pure_vector_clock_demo.rs */ = {isa = PBXFileReference; path = pure_vector_clock_demo.rs; sourceTree = "<group>"; };
		EF67EE1CF0A1FD223210C0BD /* master_key.rs */ = {isa = PBXFileReference; path = master_key.rs; sourceTree = "<group>"; };
		F2676C8CC1CC79C6F81CF6F4 /* hybrid_commands.rs */ = {isa = PBXFileReference; path = hybrid_commands.rs; sourceTree = "<group>"; };
		F354C45212E340748DA46B83 /* KEYCHAIN_ENCRYPTION_IMPLEMENTATION.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = KEYCHAIN_ENCRYPTION_IMPLEMENTATION.md; sourceTree = "<group>"; };
		F7F766048A7DC26751D7D616 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		FB291D01C69FE824ABAA72E6 /* FINAL_REPORT.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = FINAL_REPORT.md; sourceTree = "<group>"; };
		FB59461C69B12296FE120C79 /* handlers.rs */ = {isa = PBXFileReference; path = handlers.rs; sourceTree = "<group>"; };
		FBD5991D09211DF86869BF24 /* secure_memory.rs */ = {isa = PBXFileReference; path = secure_memory.rs; sourceTree = "<group>"; };
		FDDEE74A44CE82F282BF4157 /* README_native_messaging.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README_native_messaging.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		FE4F1E62D0ECDF13214978B7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D23D317F6A6611FD0056E2E /* libapp.a in Frameworks */,
				0EB360487ADF430644DF7DC6 /* CoreGraphics.framework in Frameworks */,
				05837A89037011117E93B973 /* Metal.framework in Frameworks */,
				8C3336A5E731FF6ABBD6010D /* MetalKit.framework in Frameworks */,
				E6AFAC2903E8762F208A55EF /* QuartzCore.framework in Frameworks */,
				C1711BCEBAC860EBFC7481C3 /* Security.framework in Frameworks */,
				352BD46C166BC84EAB78B0E9 /* UIKit.framework in Frameworks */,
				A5E905114014BFA94A561BFE /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00FEED76C3F1E2131983BE67 /* sync */ = {
			isa = PBXGroup;
			children = (
				2D912F9421B5EFB5B56B4A83 /* errors.rs */,
				8073F9011E62C88EA7C0C897 /* examples.rs */,
				399BAE0ED864540C8BCC7303 /* manager.rs */,
				BF1DEC87D7B824BF6EFC4C48 /* merge.rs */,
				F7F766048A7DC26751D7D616 /* mod.rs */,
				98726426F54CB5C167434FEA /* ntp.rs */,
				0D40BB5C168B0045EFD13353 /* README.md */,
				D4B27B519034588A4A3C972B /* storage.rs */,
				881EDF56C6D9FD23D67A8526 /* types.rs */,
				2F561CCAD224395E43A05C94 /* vector_clock.rs */,
			);
			path = sync;
			sourceTree = "<group>";
		};
		01E3670E9FDDBB7B50BF56BE /* migrations */ = {
			isa = PBXGroup;
			children = (
				8BA8CA71F4DCDEE8DF353147 /* m20241201_000001_create_tables.rs */,
				61FBF1A12B46FD77BF772384 /* m20241202_000001_add_soft_delete.rs */,
				5220D27502BA240AA69986EC /* m20241202_000002_add_archive_fields.rs */,
				6875A0D1C3CE5B06FAA4598D /* mod.rs */,
			);
			path = migrations;
			sourceTree = "<group>";
		};
		047C2DB83010469E6693E630 /* examples */ = {
			isa = PBXGroup;
			children = (
				EF2003650067F3C465E576A0 /* pure_vector_clock_demo.rs */,
				76DE3AA5C2733F15186C97EA /* three_way_merge_demo.rs */,
				374C64A51EB16F246CA0EC89 /* vector_clock_demo.rs */,
			);
			path = examples;
			sourceTree = "<group>";
		};
		350A4F8EF435F2E1DF7DE5FD /* docs */ = {
			isa = PBXGroup;
			children = (
				7B27D37CC5E283D4D3859006 /* DEVICE_INFO_IMPLEMENTATION.md */,
				3483154FA38D1D57A1358895 /* README.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		5D4A33C95AB2F62C044EDDD4 /* secure-password */ = {
			isa = PBXGroup;
			children = (
				7541448DB67DD6333089082C /* main.mm */,
				A1BE0521E20265117E6C4E52 /* bindings */,
			);
			path = "secure-password";
			sourceTree = "<group>";
		};
		5E3D5CC29A1E0C34C7B9B904 /* Sources */ = {
			isa = PBXGroup;
			children = (
				5D4A33C95AB2F62C044EDDD4 /* secure-password */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		66D3EB3F8D4E6F5B4153FE11 /* Products */ = {
			isa = PBXGroup;
			children = (
				957C9476E159614DDEB33A5B /* secure-password_iOS.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		780FBE5761AB5FD030252863 /* custom_tray */ = {
			isa = PBXGroup;
			children = (
				94D2F416A34304B27D564707 /* commands.rs */,
				291044A722547DB8618B8094 /* config.rs */,
				01BF9DA97481E76AA5E46A75 /* errors.rs */,
				5E85CE061C932FB45B941431 /* events.rs */,
				1B9FB4D7C4730A6D2FC9C4A1 /* examples.rs */,
				2CB85F461F9DC0311791ABD3 /* icons.rs */,
				0A24F80609D277E3477BDF63 /* integration_tests.rs */,
				6B0829E87D57C3695F86467E /* manager.rs */,
				A38BF32A1410450B073299F3 /* menu.rs */,
				0C618521CB18B84FC80EBD04 /* mod.rs */,
				97A7FD97B8B206E4BFD8ED0C /* platform.rs */,
				73283360B5410B1908D47BAD /* state.rs */,
				7A562D0DDA8B36C14B8DE930 /* tests.rs */,
				91A24C34ABFBC11A05E701EC /* docs */,
			);
			path = custom_tray;
			sourceTree = "<group>";
		};
		9057A2C5A8EF1D931ECD48CD /* src */ = {
			isa = PBXGroup;
			children = (
				E76ABCF814237B1F40423CEB /* config.rs */,
				D1D497C53A5A0EA253CB9A10 /* errors.rs */,
				8EAFFE744583A4670BF2231E /* lib.rs */,
				447FD04986D2B49D8B0187D3 /* main.rs */,
				AEE8C57343CD5B8527BD12F2 /* mobile.rs */,
				06C3E43673F1864E5A083C2F /* state.rs */,
				428FAFD1C9130A1E6B16DCF4 /* tray_integration.rs */,
				FADE6AE5FF6931F8367B91AC /* async_handler */,
				E9D5FA16276C55C39A2F6C65 /* auth */,
				D60B2770417E3257C568FFBE /* crypto */,
				780FBE5761AB5FD030252863 /* custom_tray */,
				A9F01C6CDC60446B1CD65758 /* http */,
				9C978EEB844EB22D28203DBD /* hybrid_storage */,
				FA81BC0F00060A7328F96A44 /* native_messaging */,
				00FEED76C3F1E2131983BE67 /* sync */,
				D5DE961D2BEFD5B005A4666F /* sync_backup */,
				B851E723119D6A170884F94F /* tray */,
			);
			name = src;
			path = ../../src;
			sourceTree = "<group>";
		};
		91A24C34ABFBC11A05E701EC /* docs */ = {
			isa = PBXGroup;
			children = (
				CF284B8015BBBE58DBD0A13B /* api.md */,
				C1E803BFFD9F42C79F15BAB1 /* dependencies.md */,
				36F1C4B42861D4EB6C13FD1C /* examples.md */,
				4A1DB273E56E2B687550BE05 /* IMPLEMENTATION_SUMMARY.md */,
				C949D1B7DB6979DBAB53A278 /* README.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		9C978EEB844EB22D28203DBD /* hybrid_storage */ = {
			isa = PBXGroup;
			children = (
				0DDC48AA8A009C44DF41E554 /* database.rs */,
				E8DF2F1E202AE6A9146E00FE /* mod.rs */,
				16AD4FA8A344FC11E5180B3E /* models.rs */,
				A65D900EE36336ECD4E75895 /* orm_database.rs */,
				B05210483A7DCCA7514FA7F9 /* orm_services.rs */,
				B10B0E3277C3BA71AE056B2C /* repositories.rs */,
				8041A5AD38221959B9050D15 /* services.rs */,
				E964F624059E2314D7AB59A7 /* commands */,
				DBD091C24B7CF4014B918A64 /* docs */,
				A343D53B4C1B79DC62B70D37 /* entities */,
				01E3670E9FDDBB7B50BF56BE /* migrations */,
			);
			path = hybrid_storage;
			sourceTree = "<group>";
		};
		A1BE0521E20265117E6C4E52 /* bindings */ = {
			isa = PBXGroup;
			children = (
				D525E6E56999CD44FF3B3D02 /* bindings.h */,
			);
			path = bindings;
			sourceTree = "<group>";
		};
		A343D53B4C1B79DC62B70D37 /* entities */ = {
			isa = PBXGroup;
			children = (
				65957BAC6106F2476043EBD1 /* item.rs */,
				A9DA30E04A82411E263A27CE /* login.rs */,
				3B23B55A29BC39FF4830B112 /* mod.rs */,
				29E2A7FD78FEA3FEB5FFC79F /* sync_status.rs */,
				7250E98ABB16A0C2E296833A /* vault.rs */,
			);
			path = entities;
			sourceTree = "<group>";
		};
		A9F01C6CDC60446B1CD65758 /* http */ = {
			isa = PBXGroup;
			children = (
				E8E7766CDFE5AD2CBCA988BA /* client.rs */,
				5D6B660EE32DC98CB7FBE7AE /* device_info.rs */,
				9D4336BFC990D619284F98BB /* integration_test.rs */,
				9B1B3B73055528C283DDC96B /* interceptor.rs */,
				B7F1E10AC76E6552DB538A75 /* mod.rs */,
				DAB960F6F22AF17C31A114CE /* types.rs */,
				350A4F8EF435F2E1DF7DE5FD /* docs */,
			);
			path = http;
			sourceTree = "<group>";
		};
		ADBAF6A2A874E56030DD1B39 /* secure-password_iOS */ = {
			isa = PBXGroup;
			children = (
				89AFC72110F0B8C490AA0F7D /* Info.plist */,
				82D7AEE4FB5BFD117D5F43E0 /* secure-password_iOS.entitlements */,
			);
			path = "secure-password_iOS";
			sourceTree = "<group>";
		};
		B5F1275F7E2884F5BCF43E09 /* docs */ = {
			isa = PBXGroup;
			children = (
				0F169AC8186496EC78CCD1AC /* CONTACT_BASED_SALT_IMPLEMENTATION.md */,
				6ECC648B7C142BC5D8A952AB /* CRYPTO_INTEGRATION.md */,
				7B6F3DD41CF1FFD3A0984D1D /* DEPRECATED_CODE_CLEANUP.md */,
				16CEF088506E3699A747AF71 /* HTTP_AUTH_IMPLEMENTATION.md */,
				C19C39BF884D04B94D73E9C0 /* HTTP_AUTH_SUMMARY.md */,
				8645F5D31175CEABAE9329D0 /* INTEGRATION_SUMMARY.md */,
				4BFED44A5062682146B02B7A /* keychain_master_key_sync.md */,
				970DC3015BA1CA511CA18A53 /* README_AUTH_MODULE.md */,
				ED120B66D0BBED69809394F2 /* README.md */,
				033729ADC95B53BE9AAE0BD0 /* REGISTRATION_FLOW_IMPLEMENTATION.md */,
				EBB149D48C9C113E8A8590E3 /* REGISTRATION_FORM_UPDATES.md */,
				C00CBB94B4740968626719DB /* REMOTE_API_INTEGRATION_UPDATE.md */,
				16B6D481F9A95ACACCB546B4 /* REMOTE_AUTH_GUIDE.md */,
				58C8880F2B2896237944446F /* REMOTE_AUTH_IMPLEMENTATION.md */,
				1A3D3758F64F26D279AEC572 /* REMOTE_KEYS_SYNC_USAGE.md */,
				CD55FC0D6DC7F3B7607B3438 /* SMART_REGISTRATION_SUMMARY.md */,
				ADE8D5234F08CE66DA23C8A0 /* TASK_COMPLETION_SUMMARY.md */,
				29968E93927D2FCB68B2DDBE /* TOKEN_MANAGER_UPDATE_SUMMARY.md */,
				61DDA1EF492523A571732027 /* TOKEN_MANAGER_USAGE.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		B851E723119D6A170884F94F /* tray */ = {
			isa = PBXGroup;
			children = (
				81425A62E6FA7B8E0916FC58 /* config.rs */,
				213A88065EA7FC3F1E46EA13 /* errors.rs */,
				97E3489F006FC9ADA76FB0E6 /* events.rs */,
				FB59461C69B12296FE120C79 /* handlers.rs */,
				E5FE05A7FA9D0355C6B9290A /* manager.rs */,
				8CECCB89A2C2B8FDF7560E44 /* menu.rs */,
				74E3B8F70FB202CBA65C994C /* mod.rs */,
				34805F3D530C1EDC18CA4AA8 /* utils.rs */,
				ED5B47549F8601024609F648 /* docs */,
			);
			path = tray;
			sourceTree = "<group>";
		};
		CA011D8C2D583E2E45C3AE23 /* docs */ = {
			isa = PBXGroup;
			children = (
				FB291D01C69FE824ABAA72E6 /* FINAL_REPORT.md */,
				164E0AA0ECFEC4ACE78BE057 /* IMPLEMENTATION_GUIDE.md */,
				31A6B7475626AE6892833CE6 /* PASSWORD_MANAGER_SYNC_ROADMAP.md */,
				DD78EC44314BDDB1FD400E60 /* PURE_VECTOR_CLOCK_SYNC.md */,
				24B8A43D85663F264D2C07BF /* README_PURE_VECTOR_CLOCK.md */,
				BCE32BAC28F76921B3A42FB9 /* README_THREE_WAY_MERGE.md */,
				6D35B493D6A4E4EDABE540EB /* README.md */,
				DADAA6A729BFE0B4AD579D44 /* TECHNICAL_SPECIFICATIONS.md */,
				8737D79801EF910AC6A50B2F /* three_way_merge_summary.md */,
				24C2D354D26CC7A96018243C /* VECTOR_CLOCK_IMPLEMENTATION_SUMMARY.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		D0DD4BB2413F446E3C29BA6B /* Externals */ = {
			isa = PBXGroup;
			children = (
			);
			path = Externals;
			sourceTree = "<group>";
		};
		D5DE961D2BEFD5B005A4666F /* sync_backup */ = {
			isa = PBXGroup;
			children = (
				40053780482F4AC77CC06B8E /* conflict_resolver.rs */,
				8ED4404173429E4748F124B3 /* example.rs */,
				9A146268830F6CE7D17C0134 /* log_tracker.rs */,
				4BABE19FBBE0D9A2C6166751 /* mod.rs */,
				9473526BFCCAC60835B52A8B /* ntp_client.rs */,
				5A7207055B731A0C5F87BC1C /* pure_vector_clock_sync.rs */,
				3B2712CFFB51107C688A1655 /* README.md */,
				6B9FA6AA3EB5D3C888F6AE59 /* SUMMARY.md */,
				EB934A6ECD8F3D5D7EF902C4 /* sync_commands.rs */,
				D0EE0DC5581054DF0C5A09E9 /* sync_manager.rs */,
				5120D456B898E87B1F470E23 /* sync_storage.rs */,
				2106769CD93AC64699F5BDB4 /* three_way_merge_tests.rs */,
				21FB0CEE305E14CEAD1A5FD8 /* three_way_merge.rs */,
				6582AE07B98D8A0334739E18 /* vector_clock_example.rs */,
				E37112F197F83D0BF043B6AF /* vector_clock.rs */,
				CA011D8C2D583E2E45C3AE23 /* docs */,
				047C2DB83010469E6693E630 /* examples */,
			);
			path = sync_backup;
			sourceTree = "<group>";
		};
		D60B2770417E3257C568FFBE /* crypto */ = {
			isa = PBXGroup;
			children = (
				AA9DB9C8B88C0CDAE3F87139 /* compat.rs */,
				5CFABD9EC04B9F1953110891 /* integration_test.rs */,
				3660147ECC4DE14D6C625961 /* key_derivation.rs */,
				5CAF54168AF9F54D69F1F61B /* key_pair.rs */,
				A1ABCC5DD8CFC31DF84DC597 /* keychain_adapter.rs */,
				EE50792F3C9BBCFF11E39E0A /* keychain.rs */,
				EF67EE1CF0A1FD223210C0BD /* master_key.rs */,
				4E88BC963937BBD8D02C1631 /* mod.rs */,
				FBD5991D09211DF86869BF24 /* secure_memory.rs */,
				BD35FF4B9A780CB8257B43B1 /* symmetric.rs */,
				3AA189DD5AEA76F70DEA7655 /* utils.rs */,
				0661CD3E22CD487F440AB5A9 /* vault_crypto.rs */,
				E01348C8104E950B8E69AB8F /* docs */,
			);
			path = crypto;
			sourceTree = "<group>";
		};
		DBD091C24B7CF4014B918A64 /* docs */ = {
			isa = PBXGroup;
			children = (
				C89622A8D5D0FA669D4C89AD /* ARCHIVE_IMPLEMENTATION.md */,
				624BAF37E7A8F4F3F5709172 /* DOMAIN_MATCHING_OPTIMIZATION.md */,
				ADD830D180CC36E11B576459 /* HYBRID_STORAGE_REFACTOR_SUMMARY.md */,
				512E9990906C017180600DE3 /* HYBRID_STORAGE_SUMMARY.md */,
				F354C45212E340748DA46B83 /* KEYCHAIN_ENCRYPTION_IMPLEMENTATION.md */,
				E25DF9AF76B442E5151288FD /* README_hybrid_storage.md */,
				15B2985EEF1CB83592B591FA /* SOFT_DELETE_IMPLEMENTATION.md */,
				6F424289F22D5022C93482C4 /* UPDATE_CREDENTIAL_ENCRYPTION_FIX.md */,
				37681E2A7832DB0FB33D2084 /* USER_PERSISTENCE_IMPLEMENTATION.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		E01348C8104E950B8E69AB8F /* docs */ = {
			isa = PBXGroup;
			children = (
				9D0FCBD93D8B8AF27FE654B1 /* CRYPTO_SUMMARY.md */,
				3E420858EB8E97DB8831B3E9 /* README_crypto.md */,
				74F9C26BAE269B8EA0BE8201 /* REFACTOR_SUMMARY.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		E964F624059E2314D7AB59A7 /* commands */ = {
			isa = PBXGroup;
			children = (
				F2676C8CC1CC79C6F81CF6F4 /* hybrid_commands.rs */,
				9F67217226DF46D22CA9F071 /* mod.rs */,
				1D65C4A5C1BD4E6DB5AFF43F /* orm_commands.rs */,
			);
			path = commands;
			sourceTree = "<group>";
		};
		E9D5FA16276C55C39A2F6C65 /* auth */ = {
			isa = PBXGroup;
			children = (
				BC9203D2570ED22CD512EBF0 /* commands.rs */,
				760596F5B56AE50E6A10FC20 /* integration_tests.rs */,
				44D5D264FD6DD8C8BF2B30FC /* mod.rs */,
				D5D63505222F1F8D003F9501 /* models.rs */,
				AA6B8590393B52E2345AC689 /* services.rs */,
				02D981C872F2489808CE0C38 /* test_remote_auth.rs */,
				A76EFAB7D27DAD5356BF0AB8 /* token_manager.rs */,
				87321D2C005D0E55E413CFAA /* validation.rs */,
				B5F1275F7E2884F5BCF43E09 /* docs */,
			);
			path = auth;
			sourceTree = "<group>";
		};
		ED5B47549F8601024609F648 /* docs */ = {
			isa = PBXGroup;
			children = (
				7D7B64A5C1E966A55486A7B5 /* OPTIMIZATION_SUMMARY.md */,
				7B474F96E7CC24C8469689B2 /* README.md */,
				89DB091F23FDE62DBB133574 /* REFACTORING_SUMMARY.md */,
				C64E9DC31B050C1F0B943F5F /* SIMPLIFICATION_SUMMARY.md */,
				D8F79D831FCA197FB72A21DE /* TRAY_LOG_OPTIMIZATION.md */,
				300631369481EBAD020E9372 /* TRAY_MENU_FIX.md */,
			);
			path = docs;
			sourceTree = "<group>";
		};
		F0DF8317C082E3D1C9723696 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E1EB3454FAD3DD97A3AF0259 /* CoreGraphics.framework */,
				CB42161E9EF60D7619D1FE2E /* libapp.a */,
				80E8B7EE2B49953BEF7B28EF /* Metal.framework */,
				9C4251B6499B2083526E6617 /* MetalKit.framework */,
				C721EA740F42CDFF59F4B0BA /* QuartzCore.framework */,
				66386C263434690C12BE23ED /* Security.framework */,
				B7CA017E727EF141E342A7BD /* UIKit.framework */,
				6C889A273AD267D44372A9BA /* WebKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FA81BC0F00060A7328F96A44 /* native_messaging */ = {
			isa = PBXGroup;
			children = (
				5745A94725341DE6F072B210 /* handler.rs */,
				2118AEC5CA795E528E08D84D /* mod.rs */,
				B1BDE8746FB43A1291F0C2E7 /* NATIVE_MESSAGING_SUMMARY.md */,
				E6ADA317E2744162FB861BD0 /* protocol.rs */,
				FDDEE74A44CE82F282BF4157 /* README_native_messaging.md */,
				A12FD46D97BDDF0E68DA2F13 /* server.rs */,
			);
			path = native_messaging;
			sourceTree = "<group>";
		};
		FADE6AE5FF6931F8367B91AC /* async_handler */ = {
			isa = PBXGroup;
			children = (
				60B7BF3A0DE840664F0AAED9 /* ASYNC_HANDLER_SUMMARY.md */,
				182FAB9E0D33BBB36372A032 /* examples.rs */,
				AF51A41A309D96364D0D7A96 /* integration_example.rs */,
				662C9473E27DCE225B3D60C3 /* mod.rs */,
				98E548569FC8C49C98E1B331 /* README_async_handler.md */,
				B381336564D08850EE304A4D /* simple_api.rs */,
			);
			path = async_handler;
			sourceTree = "<group>";
		};
		FCE79AF0BB35B1E43F602875 = {
			isa = PBXGroup;
			children = (
				5D7A890A99526E27FD40EFE0 /* assets */,
				53447B133BF54615B248A129 /* Assets.xcassets */,
				804EB57A0C6A5B45C878314A /* LaunchScreen.storyboard */,
				D0DD4BB2413F446E3C29BA6B /* Externals */,
				ADBAF6A2A874E56030DD1B39 /* secure-password_iOS */,
				5E3D5CC29A1E0C34C7B9B904 /* Sources */,
				9057A2C5A8EF1D931ECD48CD /* src */,
				F0DF8317C082E3D1C9723696 /* Frameworks */,
				66D3EB3F8D4E6F5B4153FE11 /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		37DD68EEEA2F95449B53E069 /* secure-password_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6EDC9440A700EB204EC86CFB /* Build configuration list for PBXNativeTarget "secure-password_iOS" */;
			buildPhases = (
				AE6912A76E321F19F09764E8 /* Build Rust Code */,
				07EE895A1336C525BA01931F /* Sources */,
				BCA07901266826638DE3664C /* Resources */,
				FE4F1E62D0ECDF13214978B7 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "secure-password_iOS";
			packageProductDependencies = (
			);
			productName = "secure-password_iOS";
			productReference = 957C9476E159614DDEB33A5B /* secure-password_iOS.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6F02883A23A3A7790C21AD96 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
				};
			};
			buildConfigurationList = 6CBE4B6803EE9B830A699886 /* Build configuration list for PBXProject "secure-password" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = FCE79AF0BB35B1E43F602875;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 54;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				37DD68EEEA2F95449B53E069 /* secure-password_iOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		BCA07901266826638DE3664C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				67FBC0814BA0197F675B049D /* Assets.xcassets in Resources */,
				39AD7EB755EC6F76A594DED4 /* LaunchScreen.storyboard in Resources */,
				524C0080385C3F3B413C0D96 /* assets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		AE6912A76E321F19F09764E8 /* Build Rust Code */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Build Rust Code";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(SRCROOT)/Externals/x86_64/${CONFIGURATION}/libapp.a",
				"$(SRCROOT)/Externals/arm64/${CONFIGURATION}/libapp.a",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "npm run -- tauri ios xcode-script -v --platform ${PLATFORM_DISPLAY_NAME:?} --sdk-root ${SDKROOT:?} --framework-search-paths \"${FRAMEWORK_SEARCH_PATHS:?}\" --header-search-paths \"${HEADER_SEARCH_PATHS:?}\" --gcc-preprocessor-definitions \"${GCC_PREPROCESSOR_DEFINITIONS:-}\" --configuration ${CONFIGURATION:?} ${FORCE_COLOR} ${ARCHS:?}";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		07EE895A1336C525BA01931F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AA5DB6AA53461BD2088EB5B4 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7429EE3FAE87078F6348B185 /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = debug;
		};
		95948589FCA7D55C7F29439B /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = release;
		};
		BA291742FCC88965B0B44F8B /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "secure-password_iOS/secure-password_iOS.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = "secure-password_iOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = com.secure-password.app;
				PRODUCT_NAME = "secure-password";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = debug;
		};
		BEF5BEF9F0475448F0114611 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "secure-password_iOS/secure-password_iOS.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = "secure-password_iOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = com.secure-password.app;
				PRODUCT_NAME = "secure-password";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6CBE4B6803EE9B830A699886 /* Build configuration list for PBXProject "secure-password" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7429EE3FAE87078F6348B185 /* debug */,
				95948589FCA7D55C7F29439B /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
		6EDC9440A700EB204EC86CFB /* Build configuration list for PBXNativeTarget "secure-password_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BA291742FCC88965B0B44F8B /* debug */,
				BEF5BEF9F0475448F0114611 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6F02883A23A3A7790C21AD96 /* Project object */;
}
