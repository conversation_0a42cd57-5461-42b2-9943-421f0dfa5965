#!/bin/bash

# Set the environment variable for Rust backtraces
export RUST_BACKTRACE=1

# Get the directory where this script is located
# This is important for finding the actual executable relative to the script
# within the .app bundle after installation.
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Construct the path to the actual executable.
# Assuming the script is placed in Contents/Resources and the executable is in Contents/MacOS
# Adjust this path if the script or executable location differs in your build.
EXECUTABLE_PATH="$SCRIPT_DIR/target/debug/secure-password"

# Check if the executable exists
if [ ! -f "$EXECUTABLE_PATH" ]; then
  # Fallback or error handling: Maybe the script is run directly from src-tauri during dev?
  # This part might need adjustment based on how you run/debug.
  # Trying a path relative to src-tauri for development scenarios.
  DEV_EXECUTABLE_PATH="$SCRIPT_DIR/target/debug/secure-password" # Or release path
  if [ -f "$DEV_EXECUTABLE_PATH" ]; then
    EXECUTABLE_PATH="$DEV_EXECUTABLE_PATH"
  else
    # Log an error if the executable cannot be found
    # Native Messaging might not show stderr easily, consider logging to a file for debugging
    echo "Error: secure-password executable not found at $EXECUTABLE_PATH or $DEV_EXECUTABLE_PATH" >&2
    exit 1
  fi
fi


# Define a log file path (e.g., in the user's home directory or a temporary location)
LOG_FILE="$HOME/native_host.log"

# Clear the log file on each start (optional)
> "$LOG_FILE"

# Execute the actual Tauri application binary, redirecting stdout and stderr to the log file
echo "Starting native host: $(date)" >> "$LOG_FILE"
"$EXECUTABLE_PATH" "$@" >> "$LOG_FILE" 2>&1
echo "Native host finished: $(date)" >> "$LOG_FILE"