# AsyncTaskManager 使用指南

## 概述

`AsyncTaskManager` 是一个基于 Tauri 官方 API 的独立异步处理器实现，提供了对 Tauri 异步运行时的封装，用于管理异步任务的生成和执行。这是一个完全独立的实现，可以在闭源项目中安全使用。

## 特性

- ✅ **基本异步任务管理** - 生成和管理异步任务
- ✅ **阻塞任务支持** - 处理 CPU 密集型和阻塞 I/O 操作
- ✅ **超时控制** - 为任务设置超时限制
- ✅ **并发任务执行** - 同时执行多个任务
- ✅ **重试机制** - 自动重试失败的任务
- ✅ **完整的测试覆盖** - 确保代码质量和可靠性
- ✅ **与 Tauri 深度集成** - 专为 Tauri 应用设计

## 基本用法

### 1. 导入模块

```rust
use crate::async_handler::AsyncTaskManager;
use std::time::Duration;
```

### 2. 生成异步任务

```rust
// 基本异步任务
let handle = AsyncTaskManager::spawn_task(async {
    println!("异步任务正在执行");
    42
});

let result = handle.await.unwrap();
println!("任务结果: {}", result);
```

### 3. 生成阻塞任务

适用于 CPU 密集型操作或阻塞 I/O：

```rust
let handle = AsyncTaskManager::spawn_blocking_task(|| {
    // 执行一些 CPU 密集型操作
    let mut sum = 0;
    for i in 0..1_000_000 {
        sum += i;
    }
    sum
});

let result = handle.await.unwrap();
```

### 4. 同步等待异步任务

```rust
let result = AsyncTaskManager::block_on_task(async {
    "同步等待的结果"
});
```

## 高级功能

### 带超时的任务

```rust
let handle = AsyncTaskManager::spawn_task_with_timeout(
    async {
        // 可能耗时的操作
        tokio::time::sleep(Duration::from_secs(2)).await;
        "操作完成"
    },
    Duration::from_secs(1), // 1 秒超时
);

match handle.await.unwrap() {
    Ok(result) => println!("任务成功: {}", result),
    Err(_) => println!("任务超时"),
}
```

### 并发执行多个任务

```rust
let tasks = vec![
    async { 1 },
    async { 2 },
    async { 3 },
];

let handle = AsyncTaskManager::join_all_tasks(tasks);
let results = handle.await.unwrap();
println!("所有任务完成，结果: {:?}", results);
```

### 带重试机制的任务

```rust
let handle = AsyncTaskManager::spawn_task_with_retry(
    || async {
        // 可能失败的操作
        if random_condition() {
            Ok("成功")
        } else {
            Err("失败")
        }
    },
    3, // 最多重试 3 次
    Duration::from_millis(500), // 重试间隔 500ms
);

match handle.await.unwrap() {
    Ok(result) => println!("任务成功: {}", result),
    Err(error) => println!("任务最终失败: {}", error),
}
```

## 在 Tauri 命令中使用

### 异步数据处理

```rust
#[tauri::command]
pub async fn process_data_async(data: String) -> Result<String, String> {
    let handle = AsyncTaskManager::spawn_task(async move {
        // 模拟异步数据处理
        tokio::time::sleep(Duration::from_millis(500)).await;
        format!("已处理: {}", data)
    });

    handle.await
        .map_err(|e| format!("任务执行失败: {:?}", e))
}
```

### 批量数据处理

```rust
#[tauri::command]
pub async fn batch_process_data(items: Vec<String>) -> Result<Vec<String>, String> {
    let tasks: Vec<_> = items.into_iter().map(|item| {
        async move {
            tokio::time::sleep(Duration::from_millis(100)).await;
            format!("处理完成: {}", item)
        }
    }).collect();

    let handle = AsyncTaskManager::join_all_tasks(tasks);
    handle.await
        .map_err(|e| format!("批量处理失败: {:?}", e))
}
```

### 文件操作

```rust
#[tauri::command]
pub async fn read_large_file(file_path: String) -> Result<String, String> {
    let handle = AsyncTaskManager::spawn_blocking_task(move || {
        std::fs::read_to_string(&file_path)
            .map_err(|e| format!("读取文件失败: {}", e))
    });

    match handle.await {
        Ok(Ok(content)) => Ok(content),
        Ok(Err(error)) => Err(error),
        Err(e) => Err(format!("任务执行失败: {:?}", e)),
    }
}
```

## 最佳实践

### 1. 选择合适的任务类型

- **`spawn_task`**: 用于异步 I/O 操作、网络请求等
- **`spawn_blocking_task`**: 用于 CPU 密集型计算、同步文件 I/O 等
- **`block_on_task`**: 仅在主线程或需要同步等待的场景中使用

### 2. 错误处理

```rust
// 良好的错误处理
let handle = AsyncTaskManager::spawn_task(async {
    // 可能失败的操作
});

match handle.await {
    Ok(result) => {
        // 处理成功结果
    }
    Err(join_error) => {
        // 处理任务执行错误
        log::error!("任务执行失败: {:?}", join_error);
    }
}
```

### 3. 资源管理

```rust
// 使用超时避免任务无限期执行
let handle = AsyncTaskManager::spawn_task_with_timeout(
    potentially_long_running_task(),
    Duration::from_secs(30),
);
```

### 4. 避免阻塞

```rust
// ❌ 不要在异步上下文中使用 block_on_task
async fn bad_example() {
    let result = AsyncTaskManager::block_on_task(async {
        // 这可能导致死锁
    });
}

// ✅ 正确的做法
async fn good_example() {
    let handle = AsyncTaskManager::spawn_task(async {
        // 异步操作
    });
    let result = handle.await.unwrap();
}
```

## 测试

项目包含了完整的测试套件，可以通过以下命令运行测试：

```bash
cd src-tauri
cargo test async_handler
```

## 许可证兼容性

这个实现完全基于 Tauri 官方 API 和 Rust 标准库，可以在闭源项目中安全使用。

## 性能考虑

- 异步任务使用 Tauri 的异步运行时，性能优异
- 阻塞任务在专用线程池中执行，不会阻塞主异步运行时
- 重试机制使用指数退避策略，避免资源浪费
- 超时控制确保任务不会无限期执行

## 故障排除

### 常见问题

1. **编译错误**: 确保 `futures` 依赖已添加到 `Cargo.toml`
2. **运行时错误**: 检查是否在正确的异步上下文中使用
3. **性能问题**: 确保 CPU 密集型操作使用 `spawn_blocking_task`

### 调试技巧

```rust
// 启用日志记录
log::info!("开始执行异步任务");
let handle = AsyncTaskManager::spawn_task(async {
    log::debug!("任务正在执行中");
    // 任务逻辑
});
``` 