use super::AsyncTaskManager;
use std::time::Duration;

/// 使用示例：基本异步任务
pub async fn example_basic_async_task() {
    // 生成一个简单的异步任务
    let handle = AsyncTaskManager::spawn_task(async {
        tokio::time::sleep(Duration::from_millis(100)).await;
        println!("异步任务完成");
        42
    });

    match handle.await {
        Ok(result) => println!("任务结果: {}", result),
        Err(e) => eprintln!("任务失败: {:?}", e),
    }
}

/// 使用示例：阻塞任务
pub async fn example_blocking_task() {
    // 生成一个阻塞任务（适用于 CPU 密集型操作）
    let handle = AsyncTaskManager::spawn_blocking_task(|| {
        // 模拟一些计算密集型操作
        let mut sum = 0;
        for i in 0..1_000_000 {
            sum += i;
        }
        sum
    });

    match handle.await {
        Ok(result) => println!("计算结果: {}", result),
        Err(e) => eprintln!("计算失败: {:?}", e),
    }
}

/// 使用示例：带超时的任务
pub async fn example_task_with_timeout() {
    // 创建一个可能超时的任务
    let handle = AsyncTaskManager::spawn_task_with_timeout(
        async {
            // 模拟一个可能耗时的操作
            tokio::time::sleep(Duration::from_secs(2)).await;
            "操作完成"
        },
        Duration::from_secs(1), // 1 秒超时
    );

    match handle.await {
        Ok(Ok(result)) => println!("任务成功: {}", result),
        Ok(Err(_)) => println!("任务超时"),
        Err(e) => eprintln!("任务错误: {:?}", e),
    }
}

/// 使用示例：并发执行多个任务
pub async fn example_concurrent_tasks() {
    // 修复：使用 Box::pin 来统一类型
    let tasks: Vec<std::pin::Pin<Box<dyn std::future::Future<Output = i32> + Send>>> = vec![
        Box::pin(async {
            tokio::time::sleep(Duration::from_millis(100)).await;
            1
        }),
        Box::pin(async {
            tokio::time::sleep(Duration::from_millis(200)).await;
            2
        }),
        Box::pin(async {
            tokio::time::sleep(Duration::from_millis(150)).await;
            3
        }),
    ];

    let handle = AsyncTaskManager::join_all_tasks(tasks);

    match handle.await {
        Ok(results) => {
            println!("所有任务完成，结果: {:?}", results);
        }
        Err(e) => eprintln!("并发任务失败: {:?}", e),
    }
}

/// 使用示例：带重试机制的任务
pub async fn example_task_with_retry() {
    let attempt_counter = std::sync::Arc::new(std::sync::atomic::AtomicUsize::new(0));
    let counter_clone = attempt_counter.clone();

    let handle = AsyncTaskManager::spawn_task_with_retry(
        move || {
            let counter = counter_clone.clone();
            async move {
                let attempt = counter.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
                println!("尝试 #{}", attempt + 1);

                // 前两次尝试失败，第三次成功
                if attempt < 2 {
                    Err(format!("第{}次尝试失败", attempt + 1))
                } else {
                    Ok("最终成功!")
                }
            }
        },
        3,                          // 最多重试 3 次
        Duration::from_millis(500), // 重试间隔 500ms
    );

    match handle.await {
        Ok(Ok(result)) => println!("重试任务成功: {}", result),
        Ok(Err(error)) => println!("重试任务最终失败: {}", error),
        Err(e) => eprintln!("重试任务执行错误: {:?}", e),
    }
}

/// 使用示例：在 Tauri 命令中使用异步处理器
#[tauri::command]
pub async fn process_data_async(data: String) -> Result<String, String> {
    let handle = AsyncTaskManager::spawn_task(async move {
        // 模拟一些异步数据处理
        tokio::time::sleep(Duration::from_millis(500)).await;

        // 处理数据
        let processed = format!("已处理: {}", data);
        processed
    });

    handle.await.map_err(|e| format!("任务执行失败: {:?}", e))
}

/// 使用示例：批量处理数据
#[tauri::command]
pub async fn batch_process_data(items: Vec<String>) -> Result<Vec<String>, String> {
    // 修复：使用 Box::pin 来统一类型
    let tasks: Vec<std::pin::Pin<Box<dyn std::future::Future<Output = String> + Send>>> = items
        .into_iter()
        .map(|item| {
            Box::pin(async move {
                tokio::time::sleep(Duration::from_millis(100)).await;
                format!("处理完成: {}", item)
            }) as std::pin::Pin<Box<dyn std::future::Future<Output = String> + Send>>
        })
        .collect();

    // 并发执行所有任务
    let handle = AsyncTaskManager::join_all_tasks(tasks);

    handle.await.map_err(|e| format!("批量处理失败: {:?}", e))
}

/// 使用示例：文件操作（阻塞 I/O）
#[tauri::command]
pub async fn read_large_file(file_path: String) -> Result<String, String> {
    let handle = AsyncTaskManager::spawn_blocking_task(move || {
        // 模拟读取大文件的阻塞操作
        std::fs::read_to_string(&file_path).map_err(|e| format!("读取文件失败: {}", e))
    });

    match handle.await {
        Ok(Ok(content)) => Ok(content),
        Ok(Err(error)) => Err(error),
        Err(e) => Err(format!("任务执行失败: {:?}", e)),
    }
}
