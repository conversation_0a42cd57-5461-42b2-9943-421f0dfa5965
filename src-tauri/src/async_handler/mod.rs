use std::future::Future;
use tauri::async_runtime::JoinHandle;

pub mod examples; // 示例模块
pub mod integration_example;
pub mod simple_api; // 简化 API 模块 // 集成示例模块

/// 异步任务处理器
///
/// 提供对 Tauri 异步运行时的封装，用于管理异步任务的生成和执行。
/// 这是一个独立实现，基于 Tauri 官方 API
pub struct AsyncTaskManager;

impl AsyncTaskManager {
    /// 生成一个新的异步任务
    ///
    /// # 参数
    /// * `future` - 要执行的 Future
    ///
    /// # 返回值
    /// 返回一个 JoinHandle，可用于等待任务完成或取消任务
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::AsyncTaskManager;
    ///
    /// let handle = AsyncTaskManager::spawn_task(async {
    ///     println!("异步任务正在执行");
    /// });
    /// ```
    pub fn spawn_task<F>(future: F) -> JoinHandle<F::Output>
    where
        F: Future + Send + 'static,
        F::Output: Send + 'static,
    {
        tauri::async_runtime::spawn(future)
    }

    /// 生成一个阻塞任务
    ///
    /// 适用于需要执行 CPU 密集型或阻塞 I/O 操作的任务。
    /// 这些任务会在专用的线程池中执行，不会阻塞异步运行时。
    ///
    /// # 参数
    /// * `f` - 要执行的阻塞函数
    ///
    /// # 返回值
    /// 返回一个 JoinHandle，包含函数的执行结果
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::AsyncTaskManager;
    ///
    /// let handle = AsyncTaskManager::spawn_blocking_task(|| {
    ///     // 执行一些 CPU 密集型操作
    ///     std::thread::sleep(std::time::Duration::from_secs(1));
    ///     "任务完成"
    /// });
    /// ```
    pub fn spawn_blocking_task<F, R>(f: F) -> JoinHandle<R>
    where
        F: FnOnce() -> R + Send + 'static,
        R: Send + 'static,
    {
        tauri::async_runtime::spawn_blocking(f)
    }

    /// 阻塞当前线程直到 Future 完成
    ///
    /// 注意：这个方法应该谨慎使用，通常只在主线程或者需要同步等待的场景中使用。
    /// 在异步上下文中使用可能会导致死锁。
    ///
    /// # 参数
    /// * `future` - 要等待完成的 Future
    ///
    /// # 返回值
    /// 返回 Future 的执行结果
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::AsyncTaskManager;
    ///
    /// let result = AsyncTaskManager::block_on_task(async {
    ///     "同步等待的结果"
    /// });
    /// ```
    pub fn block_on_task<F>(future: F) -> F::Output
    where
        F: Future,
    {
        tauri::async_runtime::block_on(future)
    }

    /// 创建一个带超时的异步任务
    ///
    /// # 参数
    /// * `future` - 要执行的 Future
    /// * `timeout` - 超时时间
    ///
    /// # 返回值
    /// 返回 Result，Ok 包含任务结果，Err 表示超时
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::AsyncTaskManager;
    /// use std::time::Duration;
    ///
    /// let handle = AsyncTaskManager::spawn_task_with_timeout(
    ///     async { "任务结果" },
    ///     Duration::from_secs(5)
    /// );
    /// ```
    pub fn spawn_task_with_timeout<F>(
        future: F,
        timeout: std::time::Duration,
    ) -> JoinHandle<Result<F::Output, tokio::time::error::Elapsed>>
    where
        F: Future + Send + 'static,
        F::Output: Send + 'static,
    {
        Self::spawn_task(async move { tokio::time::timeout(timeout, future).await })
    }

    /// 并发执行多个任务并等待所有任务完成
    ///
    /// # 参数
    /// * `futures` - 要并发执行的 Future 向量
    ///
    /// # 返回值
    /// 返回包含所有任务结果的向量
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::AsyncTaskManager;
    ///
    /// let tasks = vec![
    ///     async { 1 },
    ///     async { 2 },
    ///     async { 3 },
    /// ];
    ///
    /// let handle = AsyncTaskManager::join_all_tasks(tasks);
    /// ```
    pub fn join_all_tasks<F>(futures: Vec<F>) -> JoinHandle<Vec<F::Output>>
    where
        F: Future + Send + 'static,
        F::Output: Send + 'static,
    {
        Self::spawn_task(async move { futures::future::join_all(futures).await })
    }

    /// 执行任务并在失败时重试
    ///
    /// # 参数
    /// * `task_factory` - 生成任务的工厂函数
    /// * `max_retries` - 最大重试次数
    /// * `retry_delay` - 重试间隔
    ///
    /// # 返回值
    /// 返回任务的执行结果或最后一次失败的错误
    pub fn spawn_task_with_retry<F, Fut, T, E>(
        task_factory: F,
        max_retries: usize,
        retry_delay: std::time::Duration,
    ) -> JoinHandle<Result<T, E>>
    where
        F: Fn() -> Fut + Send + 'static,
        Fut: Future<Output = Result<T, E>> + Send + 'static,
        T: Send + 'static,
        E: Send + 'static,
    {
        Self::spawn_task(async move {
            let mut last_error = None;

            for attempt in 0..=max_retries {
                match task_factory().await {
                    Ok(result) => return Ok(result),
                    Err(error) => {
                        last_error = Some(error);
                        if attempt < max_retries {
                            tokio::time::sleep(retry_delay).await;
                        }
                    }
                }
            }

            Err(last_error.unwrap())
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_spawn_task() {
        let handle = AsyncTaskManager::spawn_task(async { 42 });

        let result = handle.await.unwrap();
        assert_eq!(result, 42);
    }

    #[tokio::test]
    async fn test_spawn_blocking_task() {
        let handle = AsyncTaskManager::spawn_blocking_task(|| {
            std::thread::sleep(Duration::from_millis(10));
            "blocking_result"
        });

        let result = handle.await.unwrap();
        assert_eq!(result, "blocking_result");
    }

    #[tokio::test]
    async fn test_spawn_task_with_timeout() {
        // 测试成功的情况
        let handle =
            AsyncTaskManager::spawn_task_with_timeout(async { "success" }, Duration::from_secs(1));

        let result = handle.await.unwrap();
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");

        // 测试超时的情况
        let handle = AsyncTaskManager::spawn_task_with_timeout(
            async {
                tokio::time::sleep(Duration::from_secs(2)).await;
                "never_reached"
            },
            Duration::from_millis(100),
        );

        let result = handle.await.unwrap();
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_join_all_tasks() {
        let tasks: Vec<std::pin::Pin<Box<dyn std::future::Future<Output = i32> + Send>>> = vec![
            Box::pin(async { 1 }),
            Box::pin(async { 2 }),
            Box::pin(async { 3 }),
        ];

        let handle = AsyncTaskManager::join_all_tasks(tasks);
        let results = handle.await.unwrap();

        assert_eq!(results, vec![1, 2, 3]);
    }

    #[tokio::test]
    async fn test_spawn_task_with_retry() {
        let attempt_count = std::sync::Arc::new(std::sync::atomic::AtomicUsize::new(0));
        let attempt_count_clone = attempt_count.clone();

        let handle = AsyncTaskManager::spawn_task_with_retry(
            move || {
                let count = attempt_count_clone.clone();
                async move {
                    let current = count.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
                    if current < 2 {
                        Err("retry_needed")
                    } else {
                        Ok("success")
                    }
                }
            },
            3,
            Duration::from_millis(10),
        );

        let result = handle.await.unwrap();
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
        assert_eq!(attempt_count.load(std::sync::atomic::Ordering::SeqCst), 3);
    }
}
