use std::future::Future;
use tauri::async_runtime::Jo<PERSON><PERSON><PERSON><PERSON>;

/// 简化的异步处理器
///
/// 这个实现更接近于原始参考代码的简洁性，但完全基于 Tauri 官方 API。
/// 适用于希望保持简洁 API 的用户。
pub struct AsyncHandler;

impl AsyncHandler {
    /// 生成异步任务（类似原始的 spawn 方法）
    ///
    /// # 参数
    /// * `f` - 生成 Future 的函数
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::simple_api::AsyncHandler;
    ///
    /// let handle = AsyncHandler::spawn(|| async {
    ///     println!("执行异步任务");
    ///     42
    /// });
    /// ```
    pub fn spawn<F, Fut>(f: F) -> JoinHandle<Fut::Output>
    where
        F: FnOnce() -> Fut + Send + 'static,
        Fut: Future + Send + 'static,
        Fut::Output: Send + 'static,
    {
        tauri::async_runtime::spawn(f())
    }

    /// 生成阻塞任务（类似原始的 spawn_blocking 方法）
    ///
    /// # 参数
    /// * `f` - 要执行的阻塞函数
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::simple_api::AsyncHandler;
    ///
    /// let handle = AsyncHandler::spawn_blocking(|| {
    ///     // CPU 密集型操作
    ///     42
    /// });
    /// ```
    pub fn spawn_blocking<F, R>(f: F) -> JoinHandle<R>
    where
        F: FnOnce() -> R + Send + 'static,
        R: Send + 'static,
    {
        tauri::async_runtime::spawn_blocking(f)
    }

    /// 阻塞等待 Future 完成（类似原始的 block_on 方法）
    ///
    /// # 参数
    /// * `f` - 生成 Future 的函数
    ///
    /// # 示例
    /// ```ignore
    /// use crate::async_handler::simple_api::AsyncHandler;
    ///
    /// let result = AsyncHandler::block_on(|| async {
    ///     "同步结果"
    /// });
    /// ```
    pub fn block_on<F, Fut>(f: F) -> Fut::Output
    where
        F: FnOnce() -> Fut,
        Fut: Future,
    {
        tauri::async_runtime::block_on(f())
    }
}

/// 工具函数：直接生成 Future（无需闭包包装）
impl AsyncHandler {
    /// 直接生成 Future
    ///
    /// 这是对 `spawn` 方法的简化版本，直接接受 Future
    pub fn spawn_future<Fut>(future: Fut) -> JoinHandle<Fut::Output>
    where
        Fut: Future + Send + 'static,
        Fut::Output: Send + 'static,
    {
        tauri::async_runtime::spawn(future)
    }

    /// 直接阻塞等待 Future 完成
    ///
    /// 这是对 `block_on` 方法的简化版本，直接接受 Future
    pub fn block_on_future<Fut>(future: Fut) -> Fut::Output
    where
        Fut: Future,
    {
        tauri::async_runtime::block_on(future)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_spawn() {
        let handle = AsyncHandler::spawn(|| async { 42 });

        let result = handle.await.unwrap();
        assert_eq!(result, 42);
    }

    #[tokio::test]
    async fn test_spawn_blocking() {
        let handle = AsyncHandler::spawn_blocking(|| {
            std::thread::sleep(Duration::from_millis(10));
            "blocking_result"
        });

        let result = handle.await.unwrap();
        assert_eq!(result, "blocking_result");
    }

    #[test]
    fn test_block_on() {
        let result = AsyncHandler::block_on(|| async { "sync_result" });

        assert_eq!(result, "sync_result");
    }

    #[tokio::test]
    async fn test_spawn_future() {
        let handle = AsyncHandler::spawn_future(async {
            tokio::time::sleep(Duration::from_millis(10)).await;
            "future_result"
        });

        let result = handle.await.unwrap();
        assert_eq!(result, "future_result");
    }

    #[test]
    fn test_block_on_future() {
        let result = AsyncHandler::block_on_future(async { "direct_future_result" });

        assert_eq!(result, "direct_future_result");
    }
}
