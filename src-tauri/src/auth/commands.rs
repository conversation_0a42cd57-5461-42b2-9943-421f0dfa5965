use serde_json;
use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};

use crate::auth::models::*;
use crate::auth::services::{AuthService, PasswordService, RemoteAuthService};
use crate::auth::token_manager::GLOBAL_TOKEN_MANAGER;
use crate::auth::validation::*;
use crate::crypto::key_derivation::{
    assess_password_strength, KeyDerivationParams, PasswordStrength,
};
use crate::crypto::{generate_keypair, KeychainManager, RegistrationKeychainManager};
use crate::state::AppState;

/// 发送验证码命令
///
/// # 参数
/// - `contact`: 联系方式（邮箱或手机号）
/// - `contact_type`: 联系方式类型
///
/// # 返回
/// - 成功时返回空的 Result
/// - 失败时返回错误信息
#[command]
pub async fn send_verification_code(
    app_handle: AppHandle,
    _state: State<'_, AppState>,
    contact: String,
    contact_type: RegistrationType,
) -> Result<(), String> {
    log::info!("发送验证码请求: {} - {:?}", contact, contact_type);

    let auth_service = AuthService::new(&app_handle);

    auth_service
        .send_verification_code(contact, contact_type)
        .await
        .map_err(|e| {
            log::error!("发送验证码失败: {}", e);
            e.to_string()
        })
}

/// 远程注册用户命令（新的注册流程）
///
/// 注册逻辑完全由远程服务端处理，客户端只负责数据验证和发送请求
///
/// # 参数
/// - `request`: 用户注册请求
///
/// # 返回
/// - 成功时返回远程注册响应（包含令牌信息）
/// - 失败时返回错误信息
#[command]
pub async fn register_user_remote_only(
    app_handle: AppHandle,
    _state: State<'_, AppState>,
    request: RegisterRequest,
) -> Result<RemoteRegisterResponse, String> {
    log::info!(
        "远程注册用户请求: {} - {}",
        request.nickname,
        request.contact
    );

    let auth_service = AuthService::new(&app_handle);

    let result = auth_service.register_user_remote_only(request).await;

    match result {
        Ok(response) => {
            if response.code == 0 {
                log::info!("远程注册成功: {}", response.message);
            } else {
                log::warn!(
                    "远程注册失败: {} (code: {})",
                    response.message,
                    response.code
                );
            }
            Ok(response)
        }
        Err(e) => {
            log::error!("远程注册失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 使用高安全性参数哈希密码命令
///
/// # 参数
/// - `password`: 原始密码
/// - `contact`: 联系方式（用作盐的一部分）
///
/// # 返回
/// - 成功时返回安全密码哈希信息
/// - 失败时返回错误信息
#[command]
pub async fn hash_password_secure(
    app_handle: AppHandle,
    password: String,
    contact: String,
) -> Result<SecurePasswordHashInfo, String> {
    log::info!("安全密码哈希请求: {}", contact);

    let auth_service = AuthService::new(&app_handle);
    let password_service = auth_service.password_service();

    password_service
        .hash_password_secure(password, contact.clone())
        .await
        .map(|secure_hash| SecurePasswordHashInfo {
            hash: secure_hash.hash,
            salt: secure_hash.salt,
            kdf_iterations: secure_hash.kdf_params.iterations,
            kdf_memory: secure_hash.kdf_params.memory_cost,
            kdf_parallelism: secure_hash.kdf_params.parallelism,
            keychain_enabled: secure_hash.keychain_service.is_some(),
            created_at: secure_hash.created_at.timestamp(),
        })
        .map_err(|e| {
            log::error!("安全密码哈希失败: {}", e);
            e.to_string()
        })
}

/// 验证密码强度命令（增强版本）
///
/// # 参数
/// - `password`: 待验证的密码
///
/// # 返回
/// - 增强的密码强度信息，包含详细的安全评估
#[command]
pub async fn validate_password_strength_enhanced(
    app_handle: AppHandle,
    password: String,
) -> Result<EnhancedPasswordStrengthResult, String> {
    let strength = PasswordService::calculate_strength(&password);

    // 使用 crypto 模块的密码强度评估
    let assessment = assess_password_strength(&password);

    let password_service = PasswordService::new(&app_handle);
    let validation_result = password_service.validate_password(&password);

    let result = EnhancedPasswordStrengthResult {
        strength,
        is_valid: validation_result.is_ok(),
        is_acceptable: assessment.is_acceptable(),
        security_level: match strength {
            90..=100 => "极高".to_string(),
            75..=89 => "很高".to_string(),
            60..=74 => "高".to_string(),
            45..=59 => "中等".to_string(),
            30..=44 => "较低".to_string(),
            _ => "很低".to_string(),
        },
        feedback: match validation_result {
            Ok(_) => {
                if assessment.is_acceptable() {
                    if strength >= 80 {
                        "密码强度优秀，安全性很高".to_string()
                    } else if strength >= 60 {
                        "密码强度良好，但建议使用更复杂的密码".to_string()
                    } else {
                        "密码强度一般，建议增加复杂性".to_string()
                    }
                } else {
                    "密码强度不足，建议使用更安全的密码".to_string()
                }
            }
            Err(e) => e.to_string(),
        },
        recommendations: get_password_recommendations(&assessment),
        estimated_crack_time: get_estimated_crack_time(&assessment),
    };

    Ok(result)
}

/// 验证密码强度命令（兼容版本）
///
/// # 参数
/// - `password`: 待验证的密码
///
/// # 返回
/// - 密码强度信息，包含强度分数和是否符合要求
#[command]
pub async fn validate_password_strength(
    app_handle: AppHandle,
    password: String,
) -> Result<PasswordStrengthResult, String> {
    let strength = PasswordService::calculate_strength(&password);

    let password_service = PasswordService::new(&app_handle);
    let validation_result = password_service.validate_password(&password);

    let result = PasswordStrengthResult {
        strength,
        is_valid: validation_result.is_ok(),
        feedback: match validation_result {
            Ok(_) => {
                if strength >= 70 {
                    "密码强度很高".to_string()
                } else if strength >= 50 {
                    "密码强度中等".to_string()
                } else {
                    "密码强度较低，建议使用更复杂的密码".to_string()
                }
            }
            Err(e) => e.to_string(),
        },
    };

    Ok(result)
}

/// 验证用户名可用性命令
///
/// # 参数
/// - `username`: 待验证的用户名
///
/// # 返回
/// - 用户名可用性结果
#[command]
pub async fn check_username_availability(
    _app_handle: AppHandle,
    _state: State<'_, AppState>,
    username: String,
) -> Result<UsernameAvailabilityResult, String> {
    log::info!("检查用户名可用性: {}", username);

    // 验证用户名格式
    let format_validation = validate_username(&username);

    if let Err(e) = format_validation {
        return Ok(UsernameAvailabilityResult {
            is_available: false,
            is_valid_format: false,
            message: e.to_string(),
        });
    }

    // 注意：新的注册流程不再使用本地用户存储，用户名重复检查由远程服务端处理
    // 这里只进行格式验证，实际的重复检查在远程注册时进行
    let result = UsernameAvailabilityResult {
        is_available: true, // 本地不再检查重复性
        is_valid_format: true,
        message: "用户名格式有效，重复性检查将在注册时由服务端处理".to_string(),
    };

    Ok(result)
}

/// 验证联系方式可用性命令
///
/// # 参数
/// - `contact`: 联系方式（邮箱或手机号）
/// - `contact_type`: 联系方式类型
///
/// # 返回
/// - 联系方式可用性结果
#[command]
pub async fn check_contact_availability(
    _app_handle: AppHandle,
    _state: State<'_, AppState>,
    contact: String,
    contact_type: RegistrationType,
) -> Result<ContactAvailabilityResult, String> {
    log::info!("检查联系方式可用性: {} ({:?})", contact, contact_type);

    // 验证联系方式格式
    let format_validation = validate_contact(&contact, &contact_type);

    if let Err(e) = format_validation {
        return Ok(ContactAvailabilityResult {
            is_available: false,
            is_valid_format: false,
            message: e.to_string(),
        });
    }

    // 注意：新的注册流程不再使用本地用户存储，联系方式重复检查由远程服务端处理
    // 这里只进行格式验证，实际的重复检查在远程注册时进行
    let contact_type_str = match contact_type {
        RegistrationType::Email => "邮箱",
        RegistrationType::Phone => "手机号",
    };

    let result = ContactAvailabilityResult {
        is_available: true, // 本地不再检查重复性
        is_valid_format: true,
        message: format!(
            "{}格式有效，重复性检查将在注册时由服务端处理",
            contact_type_str
        ),
    };

    Ok(result)
}

/// 验证密码提示词命令
///
/// # 参数
/// - `hint`: 密码提示词
/// - `password`: 原始密码
///
/// # 返回
/// - 验证结果
#[command]
pub fn validate_password_hint(
    hint: Option<String>,
    password: String,
) -> Result<PasswordHintValidationResult, String> {
    let validation_result = crate::auth::validation::validate_password_hint(&hint, &password);

    let result = PasswordHintValidationResult {
        is_valid: validation_result.is_ok(),
        message: match validation_result {
            Ok(_) => "密码提示词有效".to_string(),
            Err(e) => e.to_string(),
        },
    };

    Ok(result)
}

/// 创建用户保险库命令
///
/// # 参数
/// - `contact`: 用户联系方式
/// - `password`: 用户密码
///
/// # 返回
/// - 成功时返回保险库创建信息
/// - 失败时返回错误信息
#[command]
pub async fn create_user_vault(
    app_handle: AppHandle,
    contact: String,
    password: String,
) -> Result<VaultCreationResult, String> {
    log::info!("创建用户保险库: {}", contact);

    let auth_service = AuthService::new(&app_handle);
    let password_service = auth_service.password_service();

    match password_service
        .create_user_vault_crypto(&contact, &password)
        .await
    {
        Ok(_vault_crypto) => {
            let result = VaultCreationResult {
                success: true,
                vault_id: format!("vault_{}", contact),
                keychain_enabled: true,
                auto_lock_timeout: 600,
                message: "用户保险库创建成功".to_string(),
            };

            log::info!("用户保险库创建成功: {}", contact);
            Ok(result)
        }
        Err(e) => {
            log::error!("用户保险库创建失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 检查用户密钥链状态命令
///
/// # 参数
/// - `contact`: 用户联系方式
///
/// # 返回
/// - 密钥链状态信息
#[command]
pub async fn check_keychain_status(
    app_handle: AppHandle,
    contact: String,
) -> Result<KeychainStatusResult, String> {
    log::info!("检查用户密钥链状态: {}", contact);

    let auth_service = AuthService::new(&app_handle);
    let password_service = auth_service.password_service();

    match password_service.get_user_master_key(&contact).await {
        Ok(key_option) => Ok(KeychainStatusResult {
            keychain_available: true,
            key_exists: key_option.is_some(),
            service_name: format!("secure-password.user.{}", contact),
            message: if key_option.is_some() {
                "密钥链中存在用户密钥".to_string()
            } else {
                "密钥链中不存在用户密钥".to_string()
            },
        }),
        Err(e) => Ok(KeychainStatusResult {
            keychain_available: false,
            key_exists: false,
            service_name: format!("secure-password.user.{}", contact),
            message: format!("密钥链访问失败: {}", e),
        }),
    }
}

/// 获取 KDF 参数建议命令
///
/// # 返回
/// - KDF 参数建议信息
#[command]
pub async fn get_kdf_recommendations() -> Result<KdfRecommendationsResult, String> {
    let fast_params = KeyDerivationParams::fast();
    let balanced_params = KeyDerivationParams::balanced();
    let high_security_params = KeyDerivationParams::high_security();

    Ok(KdfRecommendationsResult {
        fast: KdfParamsInfo {
            iterations: fast_params.iterations,
            memory_cost: fast_params.memory_cost,
            parallelism: fast_params.parallelism,
            description: "快速模式（仅用于开发/测试）".to_string(),
            estimated_time_ms: 100,
        },
        balanced: KdfParamsInfo {
            iterations: balanced_params.iterations,
            memory_cost: balanced_params.memory_cost,
            parallelism: balanced_params.parallelism,
            description: "平衡模式（推荐用于生产环境）".to_string(),
            estimated_time_ms: 500,
        },
        high_security: KdfParamsInfo {
            iterations: high_security_params.iterations,
            memory_cost: high_security_params.memory_cost,
            parallelism: high_security_params.parallelism,
            description: "高安全模式（计算密集，最高安全性）".to_string(),
            estimated_time_ms: 1000,
        },
        recommended: "balanced".to_string(),
    })
}

// ============================================================================
// 响应数据结构
// ============================================================================

/// 安全密码哈希信息（前端响应）
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct SecurePasswordHashInfo {
    /// Base64编码的密钥哈希
    pub hash: String,
    /// 盐值
    pub salt: String,
    /// KDF 迭代次数
    pub kdf_iterations: u32,
    /// KDF 内存使用量 (KB)
    pub kdf_memory: u32,
    /// KDF 并行度
    pub kdf_parallelism: u32,
    /// 是否启用密钥链
    pub keychain_enabled: bool,
    /// 创建时间戳
    pub created_at: i64,
}

/// 增强密码强度结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct EnhancedPasswordStrengthResult {
    /// 密码强度分数 (0-100)
    pub strength: u8,
    /// 是否符合密码要求
    pub is_valid: bool,
    /// 是否可接受的密码
    pub is_acceptable: bool,
    /// 安全等级描述
    pub security_level: String,
    /// 反馈信息
    pub feedback: String,
    /// 改进建议
    pub recommendations: Vec<String>,
    /// 预估破解时间
    pub estimated_crack_time: String,
}

/// 密码强度结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct PasswordStrengthResult {
    /// 密码强度分数 (0-100)
    pub strength: u8,
    /// 是否符合密码要求
    pub is_valid: bool,
    /// 反馈信息
    pub feedback: String,
}

/// 用户名可用性结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct UsernameAvailabilityResult {
    /// 用户名是否可用
    pub is_available: bool,
    /// 用户名格式是否有效
    pub is_valid_format: bool,
    /// 反馈信息
    pub message: String,
}

/// 联系方式可用性结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ContactAvailabilityResult {
    /// 联系方式是否可用
    pub is_available: bool,
    /// 联系方式格式是否有效
    pub is_valid_format: bool,
    /// 反馈信息
    pub message: String,
}

/// 密码提示词验证结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct PasswordHintValidationResult {
    /// 提示词是否有效
    pub is_valid: bool,
    /// 反馈信息
    pub message: String,
}

/// 保险库创建结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct VaultCreationResult {
    /// 是否创建成功
    pub success: bool,
    /// 保险库ID
    pub vault_id: String,
    /// 是否启用密钥链
    pub keychain_enabled: bool,
    /// 自动锁定超时时间（秒）
    pub auto_lock_timeout: u64,
    /// 结果信息
    pub message: String,
}

/// 密钥链状态结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct KeychainStatusResult {
    /// 密钥链是否可用
    pub keychain_available: bool,
    /// 密钥是否存在
    pub key_exists: bool,
    /// 服务名称
    pub service_name: String,
    /// 状态信息
    pub message: String,
}

/// KDF 参数信息
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct KdfParamsInfo {
    /// 迭代次数
    pub iterations: u32,
    /// 内存使用量 (KB)
    pub memory_cost: u32,
    /// 并行度
    pub parallelism: u32,
    /// 描述
    pub description: String,
    /// 预估时间 (毫秒)
    pub estimated_time_ms: u32,
}

/// KDF 参数建议结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct KdfRecommendationsResult {
    /// 快速参数
    pub fast: KdfParamsInfo,
    /// 平衡参数
    pub balanced: KdfParamsInfo,
    /// 高安全性参数
    pub high_security: KdfParamsInfo,
    /// 推荐使用的参数类型
    pub recommended: String,
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 根据密码强度获取改进建议
pub fn get_password_recommendations(strength: &PasswordStrength) -> Vec<String> {
    match strength {
        PasswordStrength::VeryWeak => vec![
            "增加密码长度至少8个字符".to_string(),
            "使用大小写字母混合".to_string(),
            "添加数字".to_string(),
            "添加特殊字符(!@#$%^&*等)".to_string(),
        ],
        PasswordStrength::Weak => vec![
            "增加密码长度至少12个字符".to_string(),
            "使用大小写字母、数字和特殊字符的组合".to_string(),
            "避免使用常见词汇".to_string(),
        ],
        PasswordStrength::Medium => vec![
            "继续增加密码长度".to_string(),
            "使用更多类型的字符".to_string(),
            "考虑使用密码短语".to_string(),
        ],
        PasswordStrength::Strong => vec![
            "密码强度已经很好".to_string(),
            "可以考虑使用更长的密码以进一步提高安全性".to_string(),
        ],
        PasswordStrength::VeryStrong => {
            vec!["密码强度优秀".to_string(), "继续保持这种复杂度".to_string()]
        }
    }
}

/// 根据密码强度估算破解时间
pub fn get_estimated_crack_time(strength: &PasswordStrength) -> String {
    match strength {
        PasswordStrength::VeryWeak => "几秒钟".to_string(),
        PasswordStrength::Weak => "几分钟到几小时".to_string(),
        PasswordStrength::Medium => "几天到几周".to_string(),
        PasswordStrength::Strong => "几个月到几年".to_string(),
        PasswordStrength::VeryStrong => "几个世纪或更久".to_string(),
    }
}

// ============================================================================
// 远程认证命令
// ============================================================================

/// 远程注册用户命令
///
/// # 参数
/// - `request`: 用户注册请求（包含加密后的密码和验证码）
///
/// # 返回
/// - 成功时返回远程注册响应
/// - 失败时返回错误信息
#[command]
pub async fn register_user_remote(
    request: RegisterRequest,
) -> Result<RemoteRegisterResponse, String> {
    log::info!(
        "远程注册用户请求: {} - {}",
        request.nickname,
        request.contact
    );

    let remote_auth_service = RemoteAuthService::default();

    let result = remote_auth_service.register_user_remote(request).await;

    match result {
        Ok(response) => {
            if response.code == 0 {
                log::info!("远程注册成功: {}", response.message);
            } else {
                log::warn!(
                    "远程注册失败: {} (code: {})",
                    response.message,
                    response.code
                );
            }
            Ok(response)
        }
        Err(e) => {
            log::error!("远程注册请求失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 远程登录用户命令
///
/// # 参数
/// - `contact`: 用户邮箱或手机号
/// - `password_hash`: 加密后的主密码哈希
/// - `verification_code`: 验证码
///
/// # 返回
/// - 成功时返回远程登录响应
/// - 失败时返回错误信息
#[command]
pub async fn login_user_remote(
    contact: String,
    password_hash: String,
    verification_code: String,
) -> Result<RemoteLoginResponse, String> {
    log::info!("远程登录用户请求: {}", contact);

    let remote_auth_service = RemoteAuthService::default();

    let result = remote_auth_service
        .login_user_remote(contact, password_hash, verification_code)
        .await;

    match result {
        Ok(response) => {
            if response.code == 0 && response.data.is_some() {
                log::info!("远程登录成功: {}", response.message);
            } else {
                log::warn!(
                    "远程登录失败: {} (code: {})",
                    response.message,
                    response.code
                );
            }
            Ok(response)
        }
        Err(e) => {
            log::error!("远程登录请求失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 测试远程服务端连接命令
///
/// # 返回
/// - 连接成功时返回 true
/// - 连接失败时返回错误信息
#[command]
pub async fn test_remote_connection() -> Result<RemoteConnectionTestResult, String> {
    log::info!("测试远程服务端连接");

    let remote_auth_service = RemoteAuthService::default();

    match remote_auth_service.test_connection().await {
        Ok(is_connected) => {
            let result = RemoteConnectionTestResult {
                connected: is_connected,
                message: if is_connected {
                    "连接成功".to_string()
                } else {
                    "连接失败".to_string()
                },
                server_url: "http://39.107.78.133".to_string(),
                response_time_ms: None, // 可以后续添加响应时间测量
            };
            Ok(result)
        }
        Err(e) => {
            log::error!("连接测试失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取远程服务端状态命令
///
/// # 返回
/// - 成功时返回服务端状态信息
/// - 失败时返回错误信息
#[command]
pub async fn get_remote_server_status() -> Result<serde_json::Value, String> {
    log::info!("获取远程服务端状态");

    let remote_auth_service = RemoteAuthService::default();

    remote_auth_service.get_server_status().await.map_err(|e| {
        log::error!("获取服务端状态失败: {}", e);
        e.to_string()
    })
}

/// 完整注册流程（使用通用密码派生方案）
///
/// 此函数使用新的通用密码派生方案，完全基于密码派生，不依赖本地存储
/// 支持跨设备一致性，并自动将密钥存储到keychain
#[tauri::command]
pub async fn register_complete_flow(
    app_handle: AppHandle,
    username: String,
    contact: String,
    registration_type: RegistrationType,
    password: String,
    verification_code: String,
    password_hint: Option<String>,
) -> Result<CompleteRegistrationResult, String> {
    log::info!("开始完整注册流程（通用版本）: {} - {}", username, contact);

    // 1. 验证输入
    validate_contact(&contact, &registration_type)
        .map_err(|e| format!("联系方式验证失败: {}", e))?;

    validate_password_strength(app_handle.clone(), password.clone())
        .await
        .map_err(|e| format!("密码强度验证失败: {}", e))?;

    // 验证密码提示长度
    if let Some(ref hint) = password_hint {
        if hint.len() > 20 {
            return Err("密码提示信息不能超过20个字符".to_string());
        }
    }

    // 2. 使用通用注册方法
    let auth_service = AuthService::new(&app_handle);

    let (remote_response, _registration_data) = auth_service
        .register_user_universal(
            contact.clone(),
            password.clone(),
            username.clone(),
            verification_code,
            registration_type.clone(),
            password_hint.clone(),
        )
        .await
        .map_err(|e| format!("通用注册失败: {}", e))?;

    // 3. 如果注册成功，创建本地保险库
    let vault_created = if remote_response.code == 0 {
        match auth_service
            .password_service()
            .create_user_vault_crypto_universal(&contact, &password)
            .await
        {
            Ok(_) => {
                log::info!("本地保险库创建成功: {}", contact);
                true
            }
            Err(e) => {
                log::warn!("本地保险库创建失败，但远程注册成功: {}", e);
                false
            }
        }
    } else {
        false
    };

    // 4. 检查keychain状态
    let keychain_manager = RegistrationKeychainManager::new(&contact);
    let keychain_enabled = if remote_response.code == 0 {
        keychain_manager.all_keys_exist()
    } else {
        false
    };

    // 5. 构建结果
    let result = CompleteRegistrationResult {
        remote_success: remote_response.code == 0,
        remote_message: remote_response.message,
        remote_data: remote_response.data,
        vault_created,
        security_info: RegistrationSecurityInfo {
            kdf_iterations: 3,  // 高安全性参数
            kdf_memory: 65536,  // 64 MB
            kdf_parallelism: 1, // 单线程
            keychain_enabled,
        },
    };

    if result.remote_success {
        log::info!("完整注册流程成功完成: {}", username);
        log::info!("使用通用密码派生方案，支持跨设备一致性");
        log::info!("密钥已自动存储到keychain: {}", keychain_enabled);
        log::info!("本地保险库创建状态: {}", vault_created);
    } else {
        log::warn!("注册流程失败: {}", result.remote_message);
        // 如果注册失败，清理可能已保存的密钥
        let _ = keychain_manager.delete_all_keys();
    }

    Ok(result)
}

/// 通用登录流程（使用通用密码派生方案）
///
/// 此函数使用新的通用密码派生方案，完全基于密码派生，不依赖本地存储
/// 支持跨设备一致性，自动从密码重新生成所有必要的密钥
#[tauri::command]
pub async fn login_user_universal(
    app_handle: AppHandle,
    contact: String,
    password: String,
    verification_code: String,
) -> Result<RemoteLoginResponse, String> {
    log::info!("开始通用登录流程: {}", contact);

    // 1. 验证输入
    validate_contact(
        &contact,
        &if contact.contains('@') {
            RegistrationType::Email
        } else {
            RegistrationType::Phone
        },
    )
    .map_err(|e| format!("联系方式验证失败: {}", e))?;

    // 2. 使用通用登录方法
    let auth_service = AuthService::new(&app_handle);

    let (login_response, _login_data) = auth_service
        .login_user_universal(contact.clone(), password, verification_code)
        .await
        .map_err(|e| format!("通用登录失败: {}", e))?;

    if login_response.code == 0 {
        log::info!("通用登录流程成功完成: {}", contact);
        log::info!("使用通用密码派生方案，支持跨设备一致性");

        // 3. 登录成功后，设置当前用户信息到应用状态
        let app_state = app_handle.state::<crate::state::AppState>();
        let current_user = crate::state::CurrentUser {
            contact: contact.clone(),
            nickname: None, // 可以从 token 或其他地方获取昵称
        };
        app_state.set_current_user(current_user).await;
        log::info!("当前用户信息已设置到应用状态: {}", contact);
    } else {
        log::warn!("登录流程失败: {}", login_response.message);
    }

    Ok(login_response)
}

// ============================================================================
// 远程认证响应数据结构
// ============================================================================

/// 远程连接测试结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RemoteConnectionTestResult {
    /// 是否连接成功
    pub connected: bool,
    /// 连接状态消息
    pub message: String,
    /// 服务器地址
    pub server_url: String,
    /// 响应时间（毫秒）
    pub response_time_ms: Option<u32>,
}

/// 完整注册结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct CompleteRegistrationResult {
    /// 远程注册是否成功
    pub remote_success: bool,
    /// 远程注册消息
    pub remote_message: String,
    /// 远程注册数据（包含令牌信息）
    pub remote_data: Option<RemoteRegisterData>,
    /// 本地保险库是否创建成功
    pub vault_created: bool,
    /// 安全信息
    pub security_info: RegistrationSecurityInfo,
}

/// 注册安全信息
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RegistrationSecurityInfo {
    /// KDF 迭代次数
    pub kdf_iterations: u32,
    /// KDF 内存使用量 (KB)
    pub kdf_memory: u32,
    /// KDF 并行度
    pub kdf_parallelism: u32,
    /// 是否启用密钥链
    pub keychain_enabled: bool,
}

// ============================================================================
// Token 管理命令
// ============================================================================

/// 获取当前访问令牌命令
///
/// # 返回
/// - 当前有效的访问令牌，如果没有或已过期返回 None
#[command]
pub async fn get_current_token() -> Result<Option<String>, String> {
    let manager = GLOBAL_TOKEN_MANAGER.read().await;
    let token = manager.get_token().await;
    log::info!(
        "获取当前令牌: {}",
        if token.is_some() {
            "已找到"
        } else {
            "未找到"
        }
    );
    Ok(token)
}

/// 获取令牌信息命令
///
/// # 返回
/// - 当前令牌的详细信息，用于调试
#[command]
pub async fn get_token_info() -> Result<Option<TokenInfoResponse>, String> {
    let manager = GLOBAL_TOKEN_MANAGER.read().await;
    let token_info = manager.get_token_info().await;

    match token_info {
        Some(info) => {
            let response = TokenInfoResponse {
                access_token: info.access_token,
                refresh_token: info.refresh_token,
                token_type: info.token_type,
                expires_at: info.expires_at.timestamp(),
                created_at: info.created_at.timestamp(),
                is_expired: chrono::Utc::now() >= info.expires_at,
                expires_in_seconds: (info.expires_at - chrono::Utc::now()).num_seconds(),
            };
            log::info!("令牌信息: 过期时间 {}", info.expires_at);
            Ok(Some(response))
        }
        None => {
            log::info!("未找到令牌信息");
            Ok(None)
        }
    }
}

/// 清除访问令牌命令
///
/// # 返回
/// - 总是成功
#[command]
pub async fn clear_token() -> Result<(), String> {
    let manager = GLOBAL_TOKEN_MANAGER.read().await;
    manager.clear_token().await.map_err(|e| e.to_string())?;
    log::info!("访问令牌已清除");
    Ok(())
}

/// 检查令牌是否即将过期命令
///
/// # 返回
/// - 如果令牌即将过期（5分钟内）或不存在返回 true
#[command]
pub async fn is_token_expiring_soon() -> Result<bool, String> {
    let manager = GLOBAL_TOKEN_MANAGER.read().await;
    let is_expiring = manager.is_token_expiring_soon().await;
    log::info!(
        "令牌过期检查: {}",
        if is_expiring {
            "即将过期"
        } else {
            "有效"
        }
    );
    Ok(is_expiring)
}

/// 刷新访问令牌命令
///
/// 使用刷新令牌获取新的访问令牌
///
/// # 返回
/// - 刷新成功返回新的令牌信息
/// - 失败返回错误信息
#[command]
pub async fn refresh_access_token() -> Result<RemoteLoginResponse, String> {
    log::info!("开始刷新访问令牌");

    // 获取刷新令牌
    let manager = GLOBAL_TOKEN_MANAGER.read().await;
    let refresh_token = manager.get_refresh_token().await;

    if refresh_token.is_none() {
        return Err("未找到刷新令牌".to_string());
    }

    let remote_auth_service = RemoteAuthService::default();

    match remote_auth_service
        .refresh_token(refresh_token.unwrap())
        .await
    {
        Ok(login_response) => Ok(login_response),
        Err(e) => Err(e.to_string()),
    }
}

// ============================================================================
// Token 响应数据结构
// ============================================================================

/// Token 信息响应
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct TokenInfoResponse {
    /// 访问令牌
    pub access_token: String,
    /// 刷新令牌
    pub refresh_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 过期时间戳
    pub expires_at: i64,
    /// 创建时间戳
    pub created_at: i64,
    /// 是否已过期
    pub is_expired: bool,
    /// 剩余秒数（可能为负数）
    pub expires_in_seconds: i64,
}

/// 检查用户密钥状态命令
///
/// # 参数
/// - `contact`: 联系方式
///
/// # 返回
/// - 密钥状态信息
#[command]
pub async fn check_user_keys_status(contact: String) -> Result<UserKeysStatusResult, String> {
    log::info!("检查用户密钥状态: {}", contact);

    let keychain_manager = RegistrationKeychainManager::new(&contact);

    let master_key_exists = KeychainManager::new("secure-password", &format!("{}-master", contact))
        .map(|m| m.key_exists())
        .unwrap_or(false);

    let symmetric_key_exists =
        KeychainManager::new("secure-password", &format!("{}-symmetric", contact))
            .map(|m| m.key_exists())
            .unwrap_or(false);

    let private_key_exists =
        KeychainManager::new("secure-password", &format!("{}-private", contact))
            .map(|m| m.key_exists())
            .unwrap_or(false);

    let public_key_exists = KeychainManager::new("secure-password", &format!("{}-public", contact))
        .map(|m| m.key_exists())
        .unwrap_or(false);

    let all_keys_exist = keychain_manager.all_keys_exist();

    Ok(UserKeysStatusResult {
        contact,
        master_key_exists,
        symmetric_key_exists,
        private_key_exists,
        public_key_exists,
        all_keys_exist,
        keychain_available: true, // 假设钥匙串可用
    })
}

/// 清理用户密钥命令
///
/// # 参数
/// - `contact`: 联系方式
///
/// # 返回
/// - 清理结果
#[command]
pub async fn cleanup_user_keys(contact: String) -> Result<(), String> {
    log::info!("清理用户密钥: {}", contact);

    let keychain_manager = RegistrationKeychainManager::new(&contact);

    keychain_manager
        .delete_all_keys()
        .map_err(|e| format!("清理密钥失败: {}", e))?;

    log::info!("用户密钥清理完成: {}", contact);
    Ok(())
}

/// 生成测试密钥对命令（仅用于测试）
///
/// # 返回
/// - 生成的密钥对
#[command]
pub async fn generate_test_keypair() -> Result<crate::crypto::KeyPair, String> {
    log::info!("生成测试密钥对");

    generate_keypair().map_err(|e| format!("密钥对生成失败: {}", e))
}

/// 用户密钥状态结果
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct UserKeysStatusResult {
    /// 联系方式
    pub contact: String,
    /// 主密钥是否存在
    pub master_key_exists: bool,
    /// 对称密钥是否存在
    pub symmetric_key_exists: bool,
    /// 私钥是否存在
    pub private_key_exists: bool,
    /// 公钥是否存在
    pub public_key_exists: bool,
    /// 所有密钥是否都存在
    pub all_keys_exist: bool,
    /// 钥匙串是否可用
    pub keychain_available: bool,
}

/// 同步远程密钥到本地命令
///
/// # 参数
/// - `contact`: 用户联系方式
/// - `password`: 用户密码（用于生成本地主密钥）
///
/// # 返回
/// - 成功时返回密钥同步结果
/// - 失败时返回错误信息
#[command]
pub async fn sync_remote_keys(
    contact: String,
    password: String,
    app_handle: AppHandle,
) -> Result<KeysSyncResult, String> {
    log::info!("开始同步远程密钥: {}", contact);

    // 创建认证服务
    let auth_service = AuthService::new(&app_handle);

    // 生成本地主密钥
    let login_data = auth_service
        .password_service()
        .generate_login_data(password, contact.clone())
        .await
        .map_err(|e| format!("生成本地主密钥失败: {}", e))?;

    // 调用密钥同步
    let sync_result = auth_service
        .remote_service()
        .sync_keys_with_remote(&contact, &login_data.local_master_key)
        .await
        .map_err(|e| format!("同步远程密钥失败: {}", e))?;

    log::info!("远程密钥同步完成: {}", sync_result.message);
    Ok(sync_result)
}

/// 获取远程密钥信息命令
///
/// # 返回
/// - 成功时返回远程密钥信息
/// - 失败时返回错误信息
#[command]
pub async fn get_remote_keys_info(
    _app_handle: AppHandle,
) -> Result<RemoteKeysInfoResponse, String> {
    log::info!("获取远程密钥信息");

    // 创建远程认证服务
    let remote_service = RemoteAuthService::default();

    // 获取远程密钥信息
    let keys_info = remote_service
        .get_remote_keys_info()
        .await
        .map_err(|e| format!("获取远程密钥信息失败: {}", e))?;

    log::info!("远程密钥信息获取成功");
    Ok(keys_info)
}

/// 获取当前登录用户信息命令
///
/// # 返回
/// - 当前登录的用户信息，如果没有登录返回 None
#[command]
pub async fn get_current_user_info(
    app_handle: AppHandle,
) -> Result<Option<crate::state::CurrentUser>, String> {
    let app_state = app_handle.state::<crate::state::AppState>();
    Ok(app_state.get_current_user().await)
}

/// 检查用户是否已登录命令
///
/// # 返回
/// - 如果有用户已登录返回 true，否则返回 false
#[command]
pub async fn is_user_logged_in(app_handle: AppHandle) -> Result<bool, String> {
    let app_state = app_handle.state::<crate::state::AppState>();
    Ok(app_state.has_logged_in_user().await)
}

/// 登出当前用户命令
///
/// # 返回
/// - 成功时返回空的 Result
#[command]
pub async fn logout_current_user(app_handle: AppHandle) -> Result<(), String> {
    log::info!("用户登出请求");

    let app_state = app_handle.state::<crate::state::AppState>();

    // 获取当前用户信息用于日志
    if let Some(user) = app_state.get_current_user().await {
        log::info!("用户 {} 正在登出", user.contact);
    }

    // 清除用户信息
    app_state.clear_current_user().await;

    // 清除访问令牌
    let manager = crate::auth::token_manager::GLOBAL_TOKEN_MANAGER
        .read()
        .await;
    if let Err(e) = manager.clear_token().await {
        log::warn!("清除访问令牌失败: {}", e);
    }

    log::info!("用户登出完成");
    Ok(())
}

/// 重新加载用户信息命令（从持久化存储）
///
/// # 返回
/// - 重新加载后的用户信息
#[command]
pub async fn reload_user_info(
    app_handle: AppHandle,
) -> Result<Option<crate::state::CurrentUser>, String> {
    log::info!("重新加载用户信息请求");

    let app_state = app_handle.state::<crate::state::AppState>();

    // 从持久化存储重新加载用户信息
    if let Err(e) = app_state.reload_user_from_store().await {
        log::error!("重新加载用户信息失败: {}", e);
        return Err(format!("Failed to reload user info: {}", e));
    }

    let user = app_state.get_current_user().await;
    if let Some(ref user) = user {
        log::info!("用户信息重新加载成功: {}", user.contact);
    } else {
        log::info!("没有找到已保存的用户信息");
    }

    Ok(user)
}
