# 基于联系方式的安全盐值生成实现

## 概述

本项目实现了基于用户联系方式（邮箱或手机号）的安全盐值生成功能，确保相同密码在不同用户间具有不同的盐值，提高密码安全性。

## 技术实现

### 核心函数：`generate_contact_based_salt()`

位置：`src-tauri/src/auth/services.rs`

```rust
fn generate_contact_based_salt(contact: &str) -> Result<SaltString, AppError>
```

### 生成算法

1. **随机数生成**：使用加密安全的随机数生成器(`OsRng`)生成32字节随机数据
2. **哈希组合**：使用SHA-256哈希算法组合以下数据：
   - 用户联系方式（邮箱或手机号）
   - 32字节随机数据
   - 固定域分离符（`"secure-password-salt-v1"`）
   - 当前时间戳（纳秒级别）
3. **盐值提取**：从哈希结果中提取前16字节作为盐值
4. **格式编码**：使用无填充Base64编码(`STANDARD_NO_PAD`)
5. **SaltString创建**：使用`SaltString::from_b64()`创建有效的argon2盐值

### 安全特性

- **唯一性**：每次生成的盐值都不相同（由于随机数和时间戳）
- **用户相关性**：包含用户联系方式，确保相同密码在不同用户间有不同盐值
- **加密安全**：使用`OsRng`提供密码学级别的随机数
- **时序攻击防护**：通过时间戳增加随机性
- **域分离**：防止不同用途的盐值产生冲突

### 格式兼容性

- 生成的盐值格式完全兼容argon2库要求
- 使用无填充Base64编码，避免'='字符导致的解析错误
- 16字节盐值长度符合argon2最佳实践

## 集成点

该功能已集成到以下密码处理方法中：

1. `hash_password_secure()` - 高安全性密码哈希
3. `create_user_vault_crypto()` - 用户保险库加密

## 测试验证

包含完整的单元测试，验证：
- 盐值格式正确性
- 不同联系方式生成不同盐值
- 相同联系方式多次生成的盐值不同
- Base64格式有效性

## 依赖要求

- `sha2 = "0.10"` - SHA-256哈希算法
- `argon2 = "0.5"` - 密码哈希和盐值处理
- `base64 = "0.22"` - Base64编码
- `rand = "0.8"` - 加密安全随机数生成

## 安全注意事项

1. 该实现确保了盐值的唯一性和不可预测性
2. 结合联系方式提供了用户级别的盐值差异化
3. 时间戳和随机数确保了相同输入产生不同输出
4. 使用标准加密算法和最佳实践

## 性能考虑

- SHA-256哈希计算成本很低
- 盐值生成时间复杂度为O(1)
- 内存使用最小化
- 适合高频次调用

---

*实现日期：2025年5月*
*作者：AI Assistant*