# Auth 模块与 Crypto 模块集成

本文档介绍 Auth 模块与 Crypto 模块的集成，提供了高安全性的用户认证功能。

## 🚀 新功能概览

### 高安全性密码处理
- **Argon2id 高安全性参数**: 使用 OWASP 推荐的高安全性配置
- **系统密钥链集成**: 将用户主密钥安全存储在系统密钥链中
- **基于联系方式的盐生成**: 结合用户联系方式生成唯一盐值，确保相同密码在不同用户间产生不同哈希
- **内存安全**: 使用 Zeroize 自动清理敏感数据
- **常时间比较**: 防止时序攻击

### 增强的密码强度评估
- **详细强度分析**: 提供 0-100 分的密码强度评分
- **安全等级描述**: 从"很低"到"极高"的安全等级
- **改进建议**: 针对性的密码改进建议
- **破解时间估算**: 预估密码被破解所需时间

### 用户保险库管理
- **VaultCrypto 集成**: 为每个用户创建独立的加密保险库
- **自动锁定机制**: 可配置的超时自动锁定
- **跨平台密钥链**: 支持 macOS、Windows、Linux 系统密钥链

## 📚 API 使用指南

### 1. 高安全性用户注册

```typescript
import { invoke } from '@tauri-apps/api/tauri';

// 使用高安全性参数注册用户
const userResponse = await invoke('register_user_remote_only', {
  request: {
    username: 'testuser',
    contact: '<EMAIL>',
    registration_type: 'Email',
    password_hash: 'user_password_here', // 前端应该先哈希
    verification_code: '123456',
    password_hint: 'My birthday',
    kdf_type: 1,
    kdf_iterations: 100000
  }
});

console.log('用户注册成功:', userResponse);
```

### 2. 高安全性密码哈希

```typescript
// 使用高安全性参数哈希密码
const hashInfo = await invoke('hash_password_secure', {
  password: 'UserPassword123!',
  contact: '<EMAIL>'
});

console.log('密码哈希信息:', {
  hash: hashInfo.hash,
  kdf_iterations: hashInfo.kdf_iterations,
  kdf_memory: hashInfo.kdf_memory,
  keychain_enabled: hashInfo.keychain_enabled
});
```

### 安全性对比示例

```typescript
// 对比：相同密码，不同用户的哈希结果
const password = 'SecurePassword123!';

const user1Hash = await invoke('hash_password_secure', {
  password: password,
  contact: '<EMAIL>'
});

const user2Hash = await invoke('hash_password_secure', {
  password: password,  // 相同密码
  contact: '<EMAIL>'
});

// 即使密码相同，哈希值也完全不同
console.log('Alice的哈希:', user1Hash.hash);
console.log('Bob的哈希:', user2Hash.hash);
console.log('哈希是否相同:', user1Hash.hash === user2Hash.hash); // 输出: false
```

### 3. 增强密码强度验证

```typescript
// 获取详细的密码强度分析
const strengthResult = await invoke('validate_password_strength_enhanced', {
  password: 'UserPassword123!'
});

console.log('密码强度分析:', {
  strength: strengthResult.strength,           // 0-100 分数
  security_level: strengthResult.security_level, // 安全等级描述
  is_acceptable: strengthResult.is_acceptable,   // 是否可接受
  feedback: strengthResult.feedback,             // 反馈信息
  recommendations: strengthResult.recommendations, // 改进建议
  estimated_crack_time: strengthResult.estimated_crack_time // 破解时间估算
});
```

### 4. 创建用户保险库

```typescript
// 为用户创建加密保险库
const vaultResult = await invoke('create_user_vault', {
  contact: '<EMAIL>',
  password: 'UserPassword123!'
});

console.log('保险库创建结果:', {
  success: vaultResult.success,
  vault_id: vaultResult.vault_id,
  keychain_enabled: vaultResult.keychain_enabled,
  auto_lock_timeout: vaultResult.auto_lock_timeout
});
```

### 5. 检查密钥链状态

```typescript
// 检查用户密钥链状态
const keychainStatus = await invoke('check_keychain_status', {
  contact: '<EMAIL>'
});

console.log('密钥链状态:', {
  keychain_available: keychainStatus.keychain_available,
  key_exists: keychainStatus.key_exists,
  service_name: keychainStatus.service_name,
  message: keychainStatus.message
});
```

### 6. 获取 KDF 参数建议

```typescript
// 获取密钥派生函数参数建议
const kdfRecommendations = await invoke('get_kdf_recommendations');

console.log('KDF 参数建议:', {
  recommended: kdfRecommendations.recommended, // 推荐的参数类型
  fast: kdfRecommendations.fast,               // 快速参数（测试用）
  balanced: kdfRecommendations.balanced,       // 平衡参数（生产推荐）
  high_security: kdfRecommendations.high_security // 高安全性参数
});
```

## 🔒 安全特性详解

### 基于联系方式的盐生成

为了确保每个用户的密码哈希都是唯一的，即使使用相同的密码，我们采用了基于联系方式的盐生成策略：

```rust
// 生成结合联系方式的安全盐（使用加密安全的随机数生成器）
let base_salt = generate_salt()?; // 使用 OsRng 生成加密安全的随机盐
let contact_salt = format!("{}:{}", contact, base_salt);
let salt = base64::encode(contact_salt.as_bytes());
```

**安全优势:**
- **加密安全随机性**: 使用 `OsRng` (操作系统随机数生成器) 确保最高质量的随机性
- **唯一性保证**: 每个用户的盐都包含其联系方式，确保即使密码相同，哈希值也不同
- **彩虹表抵抗**: 攻击者无法使用通用彩虹表进行攻击
- **用户隔离**: 一个用户的密码泄露不会影响其他用户的安全性
- **标准格式**: 使用 `SaltString` 标准格式，确保与密码学标准兼容
- **一致性**: 与 crypto 模块的其他功能保持一致

#### 盐值结构示例

```
基础盐值: dYHPNE7PL01ZhNznnH7RBg (由 OsRng 生成的加密安全随机字符串)
联系方式: <EMAIL>

组合盐值: <EMAIL>:dYHPNE7PL01ZhNznnH7RBg
最终盐值: ******************************************************** (Base64编码)
```

这种结构确保了：
1. **双重随机性**: 既有联系方式的唯一性，又有加密安全的随机组件
2. **防重放攻击**: 即使攻击者知道盐值结构，也无法预测下次生成的盐值
3. **审计友好**: 盐值结构清晰，便于安全审计和调试

### Argon2id 高安全性参数

```rust
// 高安全性配置
KeyDerivationParams {
    algorithm: Argon2id,
    version: V0x13,
    iterations: 5,        // 高迭代次数
    memory_cost: 131072,  // 128 MB 内存使用
    parallelism: 8,       // 8 线程并行
    output_length: 32,    // 256位输出
}
```

### 系统密钥链集成

- **macOS**: 使用 Keychain Services
- **Windows**: 使用 Windows Credential Manager  
- **Linux**: 使用 Secret Service API

密钥以服务名称 `secure-password.user.{contact}` 存储，每个用户拥有独立的密钥空间。

### 内存安全机制

```rust
// 自动内存清理
impl Drop for SecureDerivedKey {
    fn drop(&mut self) {
        self.zeroize(); // 清零敏感内存
    }
}

// 常时间比较（防时序攻击）
pub fn verify(&self, other: &[u8]) -> bool {
    use subtle::ConstantTimeEq;
    self.key.ct_eq(other).into()
}
```

## 🎛️ 配置选项

### CryptoConfig 配置

```rust
CryptoConfig {
    argon2_iterations: 5,       // 高安全性迭代次数
    argon2_memory: 131072,      // 128 MB 内存使用
    argon2_parallelism: 8,      // 8 线程并行
    enable_keychain: true,      // 启用密钥链
    auto_lock_timeout: Some(600), // 10分钟自动锁定
}
```

### 安全等级配置

| 等级 | 分数范围 | 描述 | 推荐场景 |
|-----|---------|------|----------|
| 很低 | 0-29 | 极易破解 | 不推荐使用 |
| 较低 | 30-44 | 容易破解 | 不推荐使用 |
| 中等 | 45-59 | 一般安全 | 普通用户可接受 |
| 高 | 60-74 | 较高安全 | 推荐使用 |
| 很高 | 75-89 | 高安全性 | 企业级推荐 |
| 极高 | 90-100 | 最高安全 | 关键应用推荐 |

## 🔄 兼容性

### 向后兼容

项目保持了与原有 auth 模块的完全兼容：

- `register_user`: 原有的注册功能

- `validate_password_strength`: 原有的密码强度验证

### 新功能扩展

- `register_user_remote_only`: 远程注册
- `hash_password_secure`: 高安全性密码哈希
- `validate_password_strength_enhanced`: 增强密码强度验证
- `create_user_vault`: 用户保险库管理
- `check_keychain_status`: 密钥链状态检查
- `get_kdf_recommendations`: KDF 参数建议

## 🛠️ 开发指南

### 错误处理

所有新的命令都返回 `Result<T, String>` 类型，确保错误信息清晰：

```typescript
try {
  const result = await invoke('register_user_remote_only', { request });
  console.log('注册成功:', result);
} catch (error) {
  console.error('注册失败:', error);
  // error 包含详细的错误信息
}
```

### 日志记录

集成了完整的日志记录，便于调试：

```rust
log::info!("用户安全注册成功: {}", user.username);
log::warn!("密钥链访问失败: {}", e);
log::error!("用户安全注册失败: {}", e);
```

### 性能考虑

高安全性参数会增加计算时间：

- **快速模式**: ~100ms（仅测试用）
- **平衡模式**: ~500ms（生产推荐）
- **高安全模式**: ~1000ms（最高安全性）

可以根据应用需求选择合适的参数。

## 📋 最佳实践

### 1. 密码策略
- 最小长度：12个字符
- 必须包含：大小写字母、数字、特殊字符
- 避免：常见词汇、个人信息、键盘模式

### 2. 密钥管理
- 优先使用系统密钥链
- 定期检查密钥链状态
- 提供密钥链失败的降级方案

### 3. 用户体验
- 实时密码强度反馈
- 清晰的错误信息
- 合理的超时设置

### 4. 安全考虑
- 前端也应进行密码预哈希
- 使用 HTTPS 传输
- 实施速率限制
- 定期清理过期验证码

## 🔧 故障排除

### 常见问题

**Q: 密钥链访问失败**
A: 检查系统权限，或在配置中禁用密钥链

**Q: 密码哈希时间过长**
A: 考虑使用平衡模式参数，或在后台线程处理

**Q: 验证码过期**
A: 检查系统时间，确保验证码在5分钟有效期内使用

**Q: 用户注册失败**
A: 检查用户名、邮箱/手机号格式，确保未被注册

### 调试技巧

1. 启用详细日志：`RUST_LOG=debug`
2. 检查密钥链状态：使用 `check_keychain_status` 命令
3. 验证密码强度：使用 `validate_password_strength_enhanced` 命令
4. 测试 KDF 参数：使用 `get_kdf_recommendations` 获取建议