# HTTP 认证实现文档

## 概述

本文档描述了应用中 HTTP 请求的认证机制实现。系统使用两种不同的 HTTP 客户端来处理不同类型的请求：
- **普通客户端**：用于不需要认证的请求（注册、登录等）
- **认证客户端**：用于需要 Authorization header 的请求

## 架构设计

### 双客户端架构

```rust
pub struct RemoteAuthService {
    /// 普通 HTTP 客户端（用于注册、登录等不需要认证的请求）
    http_client: HttpClient,
    /// 带认证的 HTTP 客户端（用于需要 Authorization header 的请求）
    auth_http_client: HttpClient,
    /// 认证拦截器（用于管理 token）
    auth_interceptor: std::sync::Arc<crate::http::interceptor::AuthInterceptor>,
    base_url: String,
}
```

### 认证拦截器

`AuthInterceptor` 负责自动为需要认证的请求添加 Authorization header：

```rust
#[derive(Clone)]
pub struct AuthInterceptor {
    token_store: Arc<tokio::sync::RwLock<Option<String>>>,
}
```

## 接口分类

### 不需要认证的接口（使用 http_client）

这些接口不需要在 header 中携带 token：

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新访问令牌
- `POST /api/auth/send-verification-code` - 发送验证码
- `GET /api/health` - 健康检查
- `GET /api/status` - 获取服务端状态

### 需要认证的接口（使用 auth_http_client）

这些接口需要在 header 中携带有效的 Authorization token：

- `POST /api/account/keys/info` - 获取用户密钥信息
- 其他需要用户身份验证的接口

## Token 管理流程

### 1. Token 获取

当用户成功登录或注册时，系统会：

```rust
// 存储到全局 Token 管理器
let manager = GLOBAL_TOKEN_MANAGER.read().await;
manager.store_token(data.token.clone()).await?;

// 同时更新认证拦截器中的 token
self.update_token(Some(data.token.access_token.clone())).await;
```

### 2. Token 自动添加

认证拦截器会自动为使用 `auth_http_client` 的请求添加 Authorization header：

```rust
impl RequestInterceptor for AuthInterceptor {
    async fn on_request(&self, mut request: HttpRequest) -> Result<HttpRequest, HttpError> {
        if let Some(token) = self.get_token().await {
            request.config.headers.insert(
                "Authorization".to_string(), 
                format!("Bearer {}", token)
            );
        }
        Ok(request)
    }
}
```

### 3. Token 刷新

当 token 过期时，可以使用 refresh token 获取新的访问令牌：

```rust
let new_response = remote_service.refresh_token(refresh_token).await?;
// 新的 token 会自动更新到认证拦截器中
```

## 使用示例

### 创建服务实例

```rust
let remote_service = RemoteAuthService::default();
```

### 不需要认证的请求

```rust
// 登录请求（不需要认证）
let login_response = remote_service
    .login_user_remote(contact, password_hash, verification_code)
    .await?;
```

### 需要认证的请求

```rust
// 获取密钥信息（需要认证）
let keys_info = remote_service
    .get_remote_keys_info()
    .await?;
```

### 手动管理 Token

```rust
// 设置 token
remote_service.update_token(Some("your_access_token".to_string())).await;

// 获取当前 token
let current_token = remote_service.get_current_token().await;

// 清除 token
remote_service.update_token(None).await;
```

## 设备信息头部

所有 HTTP 请求（无论是否需要认证）都会自动添加设备信息头部：

- `X-Device-ID`: 设备唯一标识符
- `X-Device-Type`: 设备类型（desktop/mobile）
- `X-App-Version`: 应用版本号

这些头部由 `DeviceInfoInterceptor` 自动添加。

## 错误处理

### Token 过期

当 token 过期时，服务端会返回 401 状态码。客户端应该：

1. 使用 refresh token 获取新的访问令牌
2. 如果 refresh token 也过期，引导用户重新登录

### 网络错误

所有网络请求都包含适当的错误处理和重试机制。

## 安全考虑

1. **Token 存储**：访问令牌存储在内存中，应用关闭时自动清除
2. **HTTPS**：生产环境必须使用 HTTPS 传输
3. **Token 过期**：访问令牌有较短的有效期，需要定期刷新
4. **设备绑定**：每个 token 与特定设备绑定

## 测试

运行认证相关测试：

```bash
# 测试认证拦截器功能
cargo test test_auth_interceptor_token_management -- --nocapture

# 测试跨设备一致性
cargo test test_cross_device_consistency -- --nocapture
```

## 最佳实践

1. **自动 Token 管理**：登录/注册成功后自动设置 token，无需手动管理
2. **错误隔离**：认证失败不应影响应用的其他功能
3. **日志记录**：所有认证相关操作都有详细的日志记录
4. **测试覆盖**：确保所有认证流程都有对应的测试用例

## 故障排除

### 常见问题

1. **401 Unauthorized**：检查 token 是否有效，是否需要刷新
2. **403 Forbidden**：检查用户权限，可能需要重新登录
3. **网络超时**：检查网络连接和服务端状态

### 调试技巧

1. 启用详细日志：`RUST_LOG=debug`
2. 检查 token 状态：使用 `get_current_token()` 方法
3. 验证请求头部：查看日志中的请求详情 