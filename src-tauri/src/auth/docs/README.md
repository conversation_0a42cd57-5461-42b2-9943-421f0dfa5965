# Auth 模块

这是一个完整的用户认证模块，实现了安全的用户注册和登录功能。

## 功能特性

### 用户注册
- 支持邮箱和手机号两种注册方式
- 6位数字验证码验证
- 强密码要求和密码强度检测
- 密码提示词（不能与主密码相同）
- 使用 Argon2id 算法进行密码哈希

### 数据验证
- 用户名验证（3-20个字符，字母数字下划线，不能以数字开头）
- 邮箱格式验证
- 中国手机号验证
- 验证码格式验证（6位数字）
- 密码强度计算和验证

### 安全特性
- 使用 Argon2id 进行密码哈希
- 内存安全清理（Zeroize）
- 账户锁定机制（5次失败后锁定30分钟）
- 验证码过期机制（5分钟有效期）

## 模块结构

```
auth/
├── mod.rs          # 模块入口和测试
├── models.rs       # 数据模型定义
├── services.rs     # 业务逻辑服务
├── validation.rs   # 数据验证工具
├── commands.rs     # Tauri 命令接口
└── README.md       # 本文档
```

## 主要组件

### 数据模型 (models.rs)
- `RegisterRequest`: 用户注册请求
- `LoginRequest`: 用户登录请求
- `RegistrationType`: 注册类型枚举（邮箱/手机号）


### 服务层 (services.rs)
- `PasswordService`: 密码处理服务
- `AuthService`: 综合认证服务

### 验证工具 (validation.rs)
- 用户名、邮箱、手机号格式验证
- 密码强度计算和验证
- 验证码格式验证
- 密码提示词验证

### Tauri 命令 (commands.rs)
- `send_verification_code`: 发送验证码

- `register_user`: 用户注册

- `validate_password_strength`: 密码强度验证
- `check_username_availability`: 检查用户名可用性
- `check_contact_availability`: 检查联系方式可用性
- `validate_password_hint`: 验证密码提示词

## 使用示例

### 前端调用示例

```typescript
import { invoke } from '@tauri-apps/api/tauri';

// 发送验证码
await invoke('send_verification_code', {
  contact: '<EMAIL>',
  contactType: 'Email'
});

// 注册用户
const userResponse = await invoke('register_user', {
  request: {
    username: 'testuser',
    contact: '<EMAIL>',
    registration_type: 'Email',
    password_hash: 'hashed_password',
    verification_code: '123456',
    password_hint: 'My birthday',
    kdf_type: 1,
    kdf_iterations: 100000
  }
});

// 检查用户名可用性
const availability = await invoke('check_username_availability', {
  username: 'testuser'
});

// 验证密码强度
const strength = await invoke('validate_password_strength', {
  password: 'MyPassword123!'
});
```

## 数据存储

用户数据以 JSON 文件形式存储在应用数据目录中：
- 用户文件：`auth/users/{user_id}.json`
- 邮箱索引：`auth/users/email_index.json`
- 手机号索引：`auth/users/phone_index.json`
- 用户名索引：`auth/users/username_index.json`
- 验证码：`auth/verification_codes/{code_id}.json`

## 安全考虑

1. **密码安全**：使用 Argon2id 算法进行密码哈希，结合用户联系方式作为盐的一部分
2. **内存安全**：敏感数据使用 Zeroize 进行内存清理
3. **账户保护**：实现登录失败锁定机制
4. **验证码安全**：验证码有时效性，使用后立即失效
5. **输入验证**：所有用户输入都经过严格验证

## 测试

运行测试：
```bash
cargo test auth
```

测试覆盖：
- 数据验证功能
- 密码强度计算
- 用户和验证码创建
- 各种边界条件

## 待实现功能

- [ ] 邮件发送服务集成
- [ ] 短信发送服务集成
- [ ] 双因素认证
- [ ] 密码重置功能
- [ ] 用户登录功能
- [ ] 会话管理
- [ ] 数据库存储支持