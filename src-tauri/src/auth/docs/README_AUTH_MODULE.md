# 模块化认证系统

这是一个基于 React + TypeScript + Ant Design + TailwindCSS 的模块化认证系统，集成了 Tauri 后端的 `register_complete_flow` 完整注册流程。

## 🏗️ 架构设计

### 目录结构
```
src/
├── types/                  # TypeScript 类型定义
│   ├── auth.ts            # 认证相关类型
│   └── index.ts           # 类型统一导出
├── api/                   # API 调用层
│   ├── auth.ts            # 认证相关 API
│   └── index.ts           # API 统一导出
├── contexts/              # React Context 状态管理
│   ├── AuthContext.tsx    # 认证状态管理
│   └── index.ts           # Context 统一导出
├── components/            # 可复用组件
│   ├── auth/              # 认证相关组件
│   │   ├── AuthTabs.tsx   # 认证标签页
│   │   ├── RegisterForm.tsx # 注册表单
│   │   ├── LoginForm.tsx  # 登录表单
│   │   └── index.ts       # 认证组件导出
│   ├── layout/            # 布局组件
│   │   └── AppLayout.tsx  # 应用主布局
│   └── index.ts           # 组件统一导出
├── pages/                 # 页面组件
│   ├── AuthPage.tsx       # 认证页面
│   ├── DashboardPage.tsx  # 仪表板页面
│   └── index.ts           # 页面统一导出
└── App.tsx                # 应用入口
```

## 🚀 核心功能

### 1. 完整注册流程
- ✅ 用户名验证和可用性检查
- ✅ 邮箱/手机号验证和格式检查
- ✅ 密码强度实时检测
- ✅ 验证码发送和验证
- ✅ 服务器连接测试
- ✅ 调用 `register_complete_flow` 完成注册

### 2. 用户登录
- ✅ 邮箱/手机号登录
- ✅ 密码加密处理
- ✅ 可选验证码验证
- ✅ 记住登录状态

### 3. 状态管理
- ✅ 全局认证状态管理
- ✅ 用户信息本地存储
- ✅ Token 自动管理
- ✅ 登录状态持久化

### 4. UI/UX 设计
- ✅ 响应式设计
- ✅ 步骤指示器
- ✅ 实时表单验证
- ✅ 密码强度可视化
- ✅ 加载状态和错误处理

## 🛠️ 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design** - UI 组件库
- **TailwindCSS** - 样式框架
- **React Hooks** - 状态管理
- **Tauri** - 桌面应用框架

## 📦 使用方法

### 1. 基本使用

```tsx
import React from 'react';
import { AuthProvider } from './contexts';
import { AppLayout } from './components';
import { AuthPage, DashboardPage } from './pages';

function App() {
  return (
    <AuthProvider>
      <AppLayout>
        {/* 根据认证状态自动切换页面 */}
        <AuthPage />
        {/* 或 */}
        <DashboardPage />
      </AppLayout>
    </AuthProvider>
  );
}
```

### 2. 使用认证 Hooks

```tsx
import { useAuthState, useAuthActions } from './contexts';

function MyComponent() {
  const { isAuthenticated, user, loading } = useAuthState();
  const { login, logout } = useAuthActions();

  if (loading) return <div>加载中...</div>;
  
  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>欢迎，{user?.username}！</p>
          <button onClick={logout}>登出</button>
        </div>
      ) : (
        <div>请登录</div>
      )}
    </div>
  );
}
```

### 3. 自定义注册表单

```tsx
import { RegisterForm } from './components';

function CustomAuthPage() {
  const handleSuccess = () => {
    console.log('注册成功！');
    // 自定义成功后的逻辑
  };

  return (
    <RegisterForm onSuccess={handleSuccess} />
  );
}
```

## 🔧 API 集成

### 注册流程 API
```typescript
// 完整注册流程
const result = await registerCompleteFlow({
  username: 'testuser',
  contact: '<EMAIL>',
  registrationType: 'Email',
  password: 'securepassword',
  verificationCode: '123456',
  passwordHint: '我的生日'
});
```

### 其他 API
```typescript
// 发送验证码
await sendVerificationCode('<EMAIL>', 'Email', 'Registration');

// 检查用户名可用性
await checkUsernameAvailability('testuser');

// 验证密码强度
await validatePasswordStrength('mypassword');

// 测试服务器连接
await testRemoteConnection();
```

## 🎨 样式定制

### TailwindCSS 配置
```javascript
// tailwind.config.js
export default {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          500: '#3b82f6',
          600: '#2563eb',
        },
      },
    },
  },
  corePlugins: {
    preflight: false, // 避免与 Ant Design 冲突
  },
}
```

### 自定义样式
```css
/* 自定义认证标签样式 */
.auth-tabs .ant-tabs-tab-active {
  @apply text-blue-600;
}

/* 自定义表单样式 */
.ant-input-affix-wrapper {
  @apply border-gray-300 focus:border-blue-500;
}
```

## 🔒 安全特性

- ✅ 密码客户端加密
- ✅ 表单数据验证
- ✅ XSS 防护
- ✅ 安全的状态管理
- ✅ Token 自动管理

## 📱 响应式设计

- ✅ 移动端适配
- ✅ 平板端适配
- ✅ 桌面端优化
- ✅ 触摸友好的交互

## 🚀 部署准备

1. 确保 Tauri 后端已实现所有必需的命令
2. 配置正确的服务器地址
3. 测试所有认证流程
4. 验证响应式设计
5. 进行安全测试

## 📝 开发指南

### 添加新的认证方式
1. 在 `types/auth.ts` 中添加新类型
2. 在 `api/auth.ts` 中添加 API 调用
3. 在组件中添加 UI 支持
4. 更新状态管理逻辑

### 扩展用户信息
1. 更新 `RemoteUserData` 类型
2. 修改存储逻辑
3. 更新 UI 显示

这个模块化的认证系统为后续的功能开发提供了坚实的基础，所有组件都是可复用和可扩展的。 