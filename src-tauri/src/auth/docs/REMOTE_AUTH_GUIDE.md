# 远程认证功能使用指南

本指南介绍如何使用 Auth 模块的远程认证功能，与远程服务端进行用户注册和登录。

## 🌐 功能概览

Auth 模块现在支持以下远程认证功能：

1. **远程用户注册** - 将加密后的用户数据发送到远程服务端注册
2. **远程用户登录** - 通过远程服务端验证用户凭据
3. **完整注册流程** - 包含本地加密和远程验证的一体化注册
4. **连接测试** - 测试与远程服务端的连接状态
5. **服务端状态** - 获取远程服务端的状态信息
6. **Token 管理** - 自动管理访问令牌和刷新令牌

## 📡 远程服务端配置

默认的远程服务端地址：`http://*************`

### API 端点

- **注册端点**: `POST /api/auth/register`
- **登录端点**: `POST /api/auth/login`
- **刷新令牌**: `POST /api/auth/refresh`
- **健康检查**: `GET /api/health`
- **状态查询**: `GET /api/status`

### 认证机制

系统使用 Bearer Token 认证：
- 登录成功后，服务端返回 `access_token` 和 `refresh_token`
- 所有需要认证的请求都会在 Header 中自动添加 `Authorization: Bearer {access_token}`
- Token 过期前会自动刷新

## 🔧 前端调用示例

### 1. 完整的远程注册流程（推荐）

这是最简单的方式，包含本地加密和远程注册的完整流程：

```typescript
import { invoke } from '@tauri-apps/api/tauri';

// 完整的远程注册流程
async function registerUser() {
  try {
    const result = await invoke('register_complete_flow', {
      username: 'testuser',
      contact: '<EMAIL>',
      registrationType: 'Email',
      password: 'MySecurePassword123!', // 原始密码，系统会自动加密
      verificationCode: '123456',
      passwordHint: '我的生日'
    });

    if (result.remote_success) {
      console.log('注册成功！', result);
      console.log('用户数据:', result.remote_data);
      console.log('本地保险库:', result.vault_created ? '已创建' : '创建失败');
      console.log('安全信息:', result.security_info);
    } else {
      console.error('注册失败:', result.remote_message);
    }
  } catch (error) {
    console.error('注册过程出错:', error);
  }
}
```

### 2. 分步骤注册流程

如果需要更细粒度的控制：

```typescript
// 步骤1: 发送验证码
async function sendVerificationCode() {
  try {
    await invoke('send_verification_code', {
      request: {
        contact: '<EMAIL>',
        contact_type: 'Email',
        purpose: 'Registration'
      }
    });
    console.log('验证码已发送');
  } catch (error) {
    console.error('发送验证码失败:', error);
  }
}

// 步骤2: 远程注册
async function remoteRegister() {
  try {
    const response = await invoke('register_user_remote', {
      request: {
        username: 'testuser',
        contact: '<EMAIL>',
        registration_type: 'Email',
        password_hash: 'encrypted_password_hash', // 需要先加密
        verification_code: '123456',
        password_hint: '我的生日',
        kdf_type: 1,
        kdf_iterations: 100000
      }
    });

    if (response.code === 0) {
      console.log('远程注册成功:', response);
    } else {
      console.error('远程注册失败:', response.message);
    }
  } catch (error) {
    console.error('远程注册出错:', error);
  }
}
```

### 3. 远程登录

```typescript
async function loginUser() {
  try {
    const response = await invoke('login_user_remote', {
      contact: '<EMAIL>',
      masterPasswordHash: 'encrypted_password_hash', // 需要先加密
      verificationCode: '123456'
    });

    if (response.code === 0 && response.data) {
      console.log('登录成功！');
      console.log('Token 信息:', response.data.token);
      
      // Token 会自动存储，后续请求会自动添加认证头
    } else {
      console.error('登录失败:', response.message);
    }
  } catch (error) {
    console.error('登录出错:', error);
  }
}
```

### 4. Token 管理

```typescript
// 获取当前 Token
async function getCurrentToken() {
  try {
    const token = await invoke('get_current_token');
    console.log('当前 Token:', token);
  } catch (error) {
    console.error('获取 Token 失败:', error);
  }
}

// 获取 Token 详细信息
async function getTokenInfo() {
  try {
    const tokenInfo = await invoke('get_token_info');
    if (tokenInfo) {
      console.log('Token 信息:', {
        类型: tokenInfo.token_type,
        过期时间: new Date(tokenInfo.expires_at * 1000),
        是否过期: tokenInfo.is_expired,
        剩余时间: tokenInfo.expires_in_seconds + '秒'
      });
    } else {
      console.log('没有 Token 信息');
    }
  } catch (error) {
    console.error('获取 Token 信息失败:', error);
  }
}

// 检查 Token 是否即将过期
async function checkTokenExpiry() {
  try {
    const isExpiring = await invoke('is_token_expiring_soon');
    if (isExpiring) {
      console.log('Token 即将过期，建议刷新');
      await refreshToken();
    }
  } catch (error) {
    console.error('检查 Token 过期状态失败:', error);
  }
}

// 刷新 Token
async function refreshToken() {
  try {
    const response = await invoke('refresh_access_token');
    if (response.code === 0) {
      console.log('Token 刷新成功');
    } else {
      console.error('Token 刷新失败:', response.message);
    }
  } catch (error) {
    console.error('Token 刷新出错:', error);
  }
}

// 清除 Token（登出）
async function logout() {
  try {
    await invoke('clear_token');
    console.log('已登出，Token 已清除');
  } catch (error) {
    console.error('登出失败:', error);
  }
}
```

### 5. 连接测试

```typescript
// 测试服务端连接
async function testConnection() {
  try {
    const result = await invoke('test_remote_connection');
    console.log('连接测试结果:', {
      连接状态: result.connected ? '成功' : '失败',
      消息: result.message,
      服务器地址: result.server_url
    });
  } catch (error) {
    console.error('连接测试失败:', error);
  }
}

// 获取服务端状态
async function getServerStatus() {
  try {
    const status = await invoke('get_remote_server_status');
    console.log('服务端状态:', status);
  } catch (error) {
    console.error('获取服务端状态失败:', error);
  }
}
```

## 🔐 安全特性

### 1. 密码加密
- 使用 Argon2id 算法进行密码哈希
- 高安全性参数：5次迭代，128MB内存，8线程并行
- 结合用户联系方式生成唯一盐值
- 支持系统密钥链存储

### 2. Token 管理
- 自动存储和管理访问令牌
- 支持令牌自动刷新
- 所有认证请求自动添加 Authorization header
- 令牌过期检测和提醒

### 3. 网络安全
- 使用 HTTPS 加密传输
- 请求和响应拦截器支持
- 详细的日志记录（开发模式）

## 📝 响应格式

### 注册响应
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "id": "user_id",
    "username": "testuser",
    "email": "<EMAIL>",
    "status": "active",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### 登录响应
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": {
      "token_type": "Bearer",
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "2d8075d55abb3eef12c761d2ba084683...",
      "expires_in": 3600
    }
  }
}
```

## 🚨 错误处理

### 常见错误码
- `0`: 成功
- `1001`: 参数错误
- `1002`: 用户已存在
- `1003`: 验证码错误
- `1004`: 密码错误
- `1005`: Token 无效
- `1006`: Token 过期

### 错误处理示例
```typescript
async function handleApiCall() {
  try {
    const response = await invoke('register_user_remote', { /* ... */ });
    
    switch (response.code) {
      case 0:
        console.log('操作成功');
        break;
      case 1002:
        console.error('用户已存在，请使用其他邮箱');
        break;
      case 1003:
        console.error('验证码错误，请重新输入');
        break;
      default:
        console.error('操作失败:', response.message);
    }
  } catch (error) {
    console.error('网络错误或系统错误:', error);
  }
}
```

## 🔧 开发调试

### 启用详细日志
在开发环境中，系统会自动记录详细的 HTTP 请求和响应日志：

```
[INFO] HTTP Request: POST http://*************/api/auth/register
[DEBUG] Request headers: {"Content-Type": "application/json", "Authorization": "Bearer ..."}
[INFO] HTTP Response: Status 200
[DEBUG] Response headers: {"content-type": "application/json"}
```

### Token 调试
```typescript
// 在开发中查看 Token 状态
async function debugToken() {
  const tokenInfo = await invoke('get_token_info');
  console.table(tokenInfo);
}
```

## 📚 最佳实践

1. **使用完整注册流程**: 推荐使用 `register_complete_flow` 命令，它包含了所有必要的安全措施
2. **定期检查 Token**: 在应用启动和关键操作前检查 Token 状态
3. **错误处理**: 始终处理网络错误和业务逻辑错误
4. **安全存储**: 不要在前端存储敏感信息，依赖后端的 Token 管理
5. **用户体验**: 在 Token 即将过期时提前刷新，避免用户操作中断

## 🔄 更新日志

### v1.0.0
- 实现基础的远程注册和登录功能
- 添加 Token 自动管理
- 支持 HTTP 拦截器和认证头自动添加
- 集成高安全性密码加密
- 完整的错误处理和日志记录