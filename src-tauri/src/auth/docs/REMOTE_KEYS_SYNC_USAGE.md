# 远程密钥同步功能使用指南

## 概述

远程密钥同步功能允许在用户登录成功后，自动从远程服务端获取加密的密钥信息，并与本地 keychain 中的密钥进行比对和同步。这确保了用户在不同设备间的密钥一致性。

## 主要特性

- **自动同步**: 登录成功后自动触发密钥同步
- **冲突解决**: 以远程密钥为准，自动更新本地不一致的密钥
- **安全解密**: 使用本地主密钥解密远程加密的密钥
- **完整性检查**: 比对对称密钥、公钥、私钥的一致性

## 工作流程

### 1. 登录时自动同步

```rust
// 在 login_user_universal 方法中，登录成功后自动执行
let (login_response, login_data) = auth_service
    .login_user_universal(contact, password, verification_code)
    .await?;

if login_response.code == 0 {
    // 自动同步远程密钥到本地
    // 这个过程在后台自动完成，不需要用户干预
}
```

### 2. 手动触发同步

前端可以调用命令手动触发密钥同步：

```typescript
import { invoke } from '@tauri-apps/api/tauri';

// 手动同步远程密钥
const syncResult = await invoke<KeysSyncResult>('sync_remote_keys', {
    contact: '<EMAIL>',
    password: 'userPassword123!'
});

console.log('同步结果:', syncResult);
```

### 3. 获取远程密钥信息

```typescript
// 获取远程密钥信息（加密状态）
const keysInfo = await invoke<RemoteKeysInfoResponse>('get_remote_keys_info');

console.log('远程密钥信息:', keysInfo);
```

## 数据结构

### RemoteKeysInfoResponse

```rust
pub struct RemoteKeysInfoResponse {
    /// 状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 密钥数据
    pub data: Option<RemoteKeysData>,
}
```

### RemoteKeysData

```rust
pub struct RemoteKeysData {
    /// 加密的对称密钥（Base64编码）
    pub sym_key: String,
    /// 公钥（Base64编码，明文）
    pub pub_key: String,
    /// 加密的私钥（Base64编码）
    pub prv_key: String,
}
```

### KeysSyncResult

```rust
pub struct KeysSyncResult {
    /// 是否同步成功
    pub success: bool,
    /// 同步消息
    pub message: String,
    /// 更新的密钥类型
    pub updated_keys: Vec<String>,
    /// 是否有密钥不一致
    pub has_conflicts: bool,
}
```

## API 接口

### 远程接口

#### GET /api/account/keys/info

获取用户的加密密钥信息。

**响应示例:**
```json
{
    "code": 0,
    "message": "",
    "data": {
        "sym_key": "ZlJVYjJYcHFJM0lobks1aFRUQXhFSWtYdGpjNDhlMFFx",
        "pub_key": "TUlJQkNnS0NBUUVBNEQzZTUrdmJTK3B0Vkl1YjhsR2JLN3JUdEUvMkJOSHp3dHU3TklnSlVibk8vRTR5cEZ4aSAzRE5DbEI1Ymx1QnRLVm1nd0NuZmh6VGRyNVNLVzVrYVQxM0w1UTNnVmx4QTRIZHJVMXFFa0ZUZ1h2NHJla0hVIGYyUFYvZ1JDOGI0ZXpOQjE5WkNsVjE1YVJnb0d2cStPQ3Fyc2pRTVpwODEwNzlGblF0UnJnZFpYMHlaWklDanEgMlozVU81OVMvcDJvUVFmNytFK3BlM0RKcytsVUUybW01dE9adUszdy90T3JDMXVXWlI1Rml1QTJnTWxxZno3USA0N25jV2FnOXNlakY5bFBPUUgwNWFXK1ptNDhQUVFxalZ3elBpL1VSS2pLVDc1d2lidUFJaU1LdmFsYTYxVEp6IElPOWRRYkpjcFVHNFJ1Wm5KaFFHaVo5NXpNbElxUkpobndJ",
        "prv_key": "ZGZoYWZlZmVoZmlvd2VqYjIzeTlmOWg4M2ZxZmVpZm5xZmg5cThmNDg="
    }
}
```

**说明:**
- `sym_key`: 使用本地主密钥加密后的对称密钥
- `pub_key`: 明文公钥（Base64编码）
- `prv_key`: 使用本地主密钥加密后的私钥

## 同步逻辑

### 1. 密钥解密

```rust
// 使用本地主密钥解密远程密钥
let decrypted_sym_key = decrypt_symmetric_key(&remote_keys.sym_key, local_master_key)?;
let decrypted_prv_key = decrypt_private_key(&remote_keys.prv_key, local_master_key)?;
```

### 2. 密钥比对

对每种密钥类型进行比对：

- **对称密钥**: 比对解密后的字节数组
- **公钥**: 直接比对字符串（明文）
- **私钥**: 比对解密后的字符串

### 3. 冲突解决

当检测到本地密钥与远程密钥不一致时：

1. 记录警告日志
2. 以远程密钥为准
3. 更新本地 keychain 中的密钥
4. 在同步结果中标记冲突

## 错误处理

### 常见错误

1. **网络错误**: 无法连接到远程服务端
2. **认证错误**: Token 无效或过期
3. **解密错误**: 本地主密钥不正确
4. **Keychain 错误**: 本地密钥存储失败

### 错误恢复

- 网络错误：重试机制
- 认证错误：提示用户重新登录
- 解密错误：提示用户检查密码
- Keychain 错误：记录警告，不影响主流程

## 安全考虑

### 1. 密钥保护

- 远程存储的密钥使用本地主密钥加密
- 本地主密钥从用户密码派生，不存储在服务端
- 公钥明文存储，私钥和对称密钥加密存储

### 2. 传输安全

- 所有通信使用 HTTPS
- 使用 JWT Token 进行身份验证
- 密钥数据在传输前已加密

### 3. 本地安全

- 使用系统 keychain 存储敏感密钥
- 内存中的密钥使用 zeroize 清理
- 支持自动锁定机制

## 使用示例

### 完整的登录和同步流程

```typescript
import { invoke } from '@tauri-apps/api/tauri';

async function loginAndSync() {
    try {
        // 1. 执行登录
        const loginResult = await invoke('login_user_universal', {
            contact: '<EMAIL>',
            password: 'userPassword123!',
            verificationCode: '123456'
        });

        if (loginResult.code === 0) {
            console.log('登录成功，密钥已自动同步');
            
            // 2. 可选：检查同步状态
            const keysStatus = await invoke('check_user_keys_status', {
                contact: '<EMAIL>'
            });
            
            console.log('密钥状态:', keysStatus);
        }
    } catch (error) {
        console.error('登录或同步失败:', error);
    }
}
```

### 手动同步密钥

```typescript
async function manualSync() {
    try {
        const syncResult = await invoke<KeysSyncResult>('sync_remote_keys', {
            contact: '<EMAIL>',
            password: 'userPassword123!'
        });

        if (syncResult.success) {
            console.log('同步成功:', syncResult.message);
            
            if (syncResult.has_conflicts) {
                console.warn('检测到密钥冲突，已更新:', syncResult.updated_keys);
            }
        }
    } catch (error) {
        console.error('手动同步失败:', error);
    }
}
```

## 日志和调试

### 日志级别

- **INFO**: 正常的同步操作
- **WARN**: 密钥冲突和非关键错误
- **ERROR**: 严重错误，影响功能

### 调试信息

```rust
log::info!("开始同步远程密钥到本地: {}", contact);
log::warn!("本地对称密钥与远程不一致，更新为远程版本");
log::error!("解密远程对称密钥失败: {}", e);
```

## 最佳实践

1. **定期同步**: 建议在每次登录时自动同步
2. **错误处理**: 同步失败不应影响主要功能
3. **用户提示**: 在有密钥冲突时适当提示用户
4. **备份策略**: 重要密钥应有备份机制
5. **版本控制**: 考虑密钥的版本管理

## 故障排除

### 常见问题

1. **Q: 同步失败，提示解密错误**
   A: 检查用户密码是否正确，本地主密钥是否匹配

2. **Q: 网络请求超时**
   A: 检查网络连接和服务端状态

3. **Q: Keychain 存储失败**
   A: 检查系统权限和 keychain 可用性

4. **Q: 密钥冲突频繁出现**
   A: 可能是多设备并发使用，建议统一密钥管理策略 