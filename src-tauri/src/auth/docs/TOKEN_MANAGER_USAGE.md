# Token 管理器使用指南

## 概述

Token 管理器现在使用 Tauri Store 插件实现持久化存储，确保 Token 在应用重启后仍然可用。

## 主要特性

- **持久化存储**: 使用 Tauri Store 插件将 Token 存储到本地文件
- **自动加载**: 应用启动时自动从存储中加载 Token
- **线程安全**: 使用 Arc<RwLock<TokenManager>> 确保多线程安全
- **错误处理**: 完善的错误处理机制

## 初始化

Token 管理器在应用启动时自动初始化：

```rust
// 在 lib.rs 的 setup 函数中
async_handler::AsyncTaskManager::spawn_task(async move {
    // 初始化 Token 管理器
    if let Err(e) = auth::token_manager::initialize_global_token_manager(handle.clone()).await {
        log::error!("Failed to initialize token manager: {}", e);
    } else {
        log::info!("Token manager initialized successfully.");
    }
});
```

## 使用方法

### 1. 存储 Token

```rust
use crate::auth::token_manager::GLOBAL_TOKEN_MANAGER;

// 存储 Token
let manager = GLOBAL_TOKEN_MANAGER.read().await;
if let Err(e) = manager.store_token(token_info).await {
    log::error!("存储 Token 失败: {}", e);
} else {
    log::info!("Token 已成功存储");
}
```

### 2. 获取当前 Token

```rust
// 获取当前有效的 Token
let manager = GLOBAL_TOKEN_MANAGER.read().await;
if let Some(token) = manager.get_token().await {
    // 使用 Token
    println!("当前 Token: {}", token);
} else {
    println!("没有有效的 Token");
}
```

### 3. 检查 Token 是否即将过期

```rust
// 检查 Token 是否即将过期（5分钟内）
let manager = GLOBAL_TOKEN_MANAGER.read().await;
if manager.is_token_expiring_soon().await {
    println!("Token 即将过期，需要刷新");
}
```

### 4. 清除 Token

```rust
// 清除 Token
let manager = GLOBAL_TOKEN_MANAGER.read().await;
if let Err(e) = manager.clear_token().await {
    log::error!("清除 Token 失败: {}", e);
} else {
    log::info!("Token 已清除");
}
```

### 5. 获取刷新令牌

```rust
// 获取刷新令牌
let manager = GLOBAL_TOKEN_MANAGER.read().await;
if let Some(refresh_token) = manager.get_refresh_token().await {
    // 使用刷新令牌
    println!("刷新令牌: {}", refresh_token);
}
```

## 存储位置

Token 数据存储在应用数据目录下的 `tokens.json` 文件中：

- **macOS**: `~/Library/Application Support/com.secure-password.app/tokens.json`
- **Windows**: `%APPDATA%/com.secure-password.app/tokens.json`
- **Linux**: `~/.config/com.secure-password.app/tokens.json`

## 数据结构

存储的 Token 信息包含以下字段：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredTokenInfo {
    /// 访问令牌
    pub access_token: String,
    /// 刷新令牌
    pub refresh_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}
```

## 安全考虑

1. **加密存储**: Tauri Store 插件提供基本的文件存储，建议在敏感环境中考虑额外的加密
2. **权限控制**: 存储文件的访问权限由操作系统管理
3. **自动清理**: Token 过期后会自动失效，但不会自动删除

## 错误处理

所有 Token 管理操作都返回 `Result<T, anyhow::Error>`，应该适当处理错误：

```rust
match manager.store_token(token_info).await {
    Ok(()) => log::info!("Token 存储成功"),
    Err(e) => log::error!("Token 存储失败: {}", e),
}
```

## 最佳实践

1. **定期检查**: 在需要使用 Token 前检查是否过期
2. **自动刷新**: 实现自动刷新机制，在 Token 即将过期时自动刷新
3. **错误恢复**: 在 Token 操作失败时提供适当的错误恢复机制
4. **日志记录**: 记录 Token 操作的关键事件，便于调试

## 命令行接口

以下 Tauri 命令可用于前端调用：

- `get_current_token()`: 获取当前 Token
- `get_token_info()`: 获取 Token 详细信息
- `clear_token()`: 清除 Token
- `is_token_expiring_soon()`: 检查是否即将过期
- `refresh_access_token()`: 刷新 Token 