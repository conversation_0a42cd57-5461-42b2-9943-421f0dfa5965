# Keychain 主密钥同步功能

## 概述

在用户登录成功后，系统会自动检查并同步本地 keychain 中存储的主密钥，确保 keychain 中的主密钥与当前登录使用的主密钥保持一致。

## 功能特性

### 自动检查和更新

登录成功后，系统会自动执行以下检查：

1. **检查 keychain 中是否存在主密钥**
   - 如果不存在，将当前登录的主密钥存储到 keychain
   - 如果存在，进行一致性检查

2. **主密钥一致性检查**
   - 比较 keychain 中的主密钥与当前登录的主密钥
   - 如果一致，无需操作
   - 如果不一致，更新 keychain 中的主密钥

3. **错误处理**
   - keychain 操作失败不会影响登录流程
   - 所有操作都有详细的日志记录

## 实现细节

### 核心方法

```rust
async fn check_and_update_keychain_master_key(
    &self,
    contact: &str,
    current_master_key: &[u8; KEY_SIZE],
) -> Result<(), AppError>
```

### 调用时机

在 `login_user_universal` 方法中，远程登录成功后立即调用：

```rust
if login_response.code == 0 {
    log::info!("通用登录成功: {}", login_response.message);
    
    // 检查并更新keychain中的主密钥
    self.check_and_update_keychain_master_key(&contact, &login_data.local_master_key)
        .await?;
    
    // 继续其他登录后操作...
}
```

## 使用场景

### 场景1：新设备首次登录
- keychain 中没有主密钥
- 系统自动存储当前登录的主密钥
- 后续登录可以利用 keychain 中的主密钥

### 场景2：密码更改后登录
- keychain 中的主密钥与新密码派生的主密钥不同
- 系统自动更新 keychain 中的主密钥
- 确保 keychain 数据与当前密码保持同步

### 场景3：正常登录
- keychain 中的主密钥与当前登录主密钥一致
- 无需任何操作，直接继续登录流程

## 安全考虑

### 密钥保护
- 主密钥在内存中使用 `zeroize` 进行安全清理
- keychain 操作使用系统级别的安全存储
- 所有密钥比较操作都是常时间比较

### 错误隔离
- keychain 操作失败不会影响登录成功
- 详细的错误日志便于问题诊断
- 优雅降级，确保用户体验

## 日志记录

系统会记录以下关键操作：

```
INFO: 检查并更新keychain中的主密钥: <EMAIL>
INFO: keychain中的主密钥与当前登录主密钥一致，无需更新: <EMAIL>
WARN: keychain中的主密钥与当前登录主密钥不一致，更新keychain: <EMAIL>
INFO: keychain主密钥已更新为当前登录主密钥: <EMAIL>
INFO: keychain中没有主密钥或读取失败, 存储当前登录主密钥: <EMAIL>
```

## 测试覆盖

### 测试用例

1. **keychain 中没有主密钥**
   - 验证主密钥正确存储
   - 验证后续读取一致性

2. **keychain 中有相同主密钥**
   - 验证无需更新操作
   - 验证日志记录正确

3. **keychain 中有不同主密钥**
   - 验证主密钥正确更新
   - 验证更新后的一致性

### 运行测试

```bash
cargo test test_keychain_master_key_check_and_update -- --nocapture
```

## 兼容性

- **macOS**: 使用系统 Keychain
- **Windows**: 使用 Windows Credential Manager
- **Linux**: 使用 Secret Service API
- **其他平台**: 优雅降级，记录警告日志

## 性能影响

- keychain 操作是异步的，不会阻塞登录流程
- 密钥比较操作是 O(1) 时间复杂度
- 整个检查和更新过程通常在几毫秒内完成

## 故障排除

### 常见问题

1. **keychain 不可用**
   - 检查系统权限设置
   - 查看应用程序是否有 keychain 访问权限

2. **主密钥不一致**
   - 通常发生在密码更改后
   - 系统会自动更新，无需手动干预

3. **存储失败**
   - 检查磁盘空间
   - 检查 keychain 服务状态

### 调试方法

启用详细日志记录：
```bash
RUST_LOG=debug cargo run
```

查看 keychain 相关日志：
```bash
grep "keychain" application.log
``` 