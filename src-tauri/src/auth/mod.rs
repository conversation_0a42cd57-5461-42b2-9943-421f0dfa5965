pub mod commands;
pub mod models;
pub mod services;
pub mod token_manager;
pub mod validation;

#[cfg(test)]
mod integration_tests;

pub use commands::*;
pub use models::*;
pub use services::*;

#[cfg(test)]
mod tests {
    use crate::auth::validation;

    #[test]
    fn test_username_validation() {
        // 有效用户名
        assert!(validation::validate_username("user123").is_ok());
        assert!(validation::validate_username("test_user").is_ok());
        assert!(validation::validate_username("a_1").is_ok());

        // 无效用户名
        assert!(validation::validate_username("").is_err());
        assert!(validation::validate_username("ab").is_err());
        assert!(validation::validate_username("123user").is_err());
        assert!(validation::validate_username("user-name").is_err());
        assert!(validation::validate_username("user@name").is_err());
    }

    #[test]
    fn test_email_validation() {
        // 有效邮箱
        assert!(validation::validate_email("<EMAIL>").is_ok());
        assert!(validation::validate_email("<EMAIL>").is_ok());

        // 无效邮箱
        assert!(validation::validate_email("").is_err());
        assert!(validation::validate_email("invalid").is_err());
        assert!(validation::validate_email("@example.com").is_err());
        assert!(validation::validate_email("test@").is_err());
    }

    #[test]
    fn test_phone_validation() {
        // 有效手机号
        assert!(validation::validate_phone("13812345678").is_ok());
        assert!(validation::validate_phone("15987654321").is_ok());

        // 无效手机号
        assert!(validation::validate_phone("").is_err());
        assert!(validation::validate_phone("12345678901").is_err());
        assert!(validation::validate_phone("1381234567").is_err());
        assert!(validation::validate_phone("23812345678").is_err());
    }

    #[test]
    fn test_verification_code_validation() {
        // 有效验证码
        assert!(validation::validate_verification_code("123456").is_ok());
        assert!(validation::validate_verification_code("000000").is_ok());

        // 无效验证码
        assert!(validation::validate_verification_code("").is_err());
        assert!(validation::validate_verification_code("12345").is_err());
        assert!(validation::validate_verification_code("1234567").is_err());
        assert!(validation::validate_verification_code("12345a").is_err());
    }

    #[test]
    fn test_password_strength() {
        // 弱密码
        assert!(validation::calculate_password_strength("123456") < 40);
        assert!(validation::calculate_password_strength("password") < 40);

        // 中等强度密码 - 调整期望值
        let strength = validation::calculate_password_strength("Password123");
        println!("Password123 strength: {}", strength);
        assert!(strength >= 30); // 降低期望值

        // 强密码
        assert!(validation::calculate_password_strength("P@ssw0rd123!") >= 60);
    }

    #[test]
    fn test_password_hint_validation() {
        // 有效提示词
        assert!(validation::validate_password_hint(
            &Some("my birthday".to_string()),
            "StrongPassword123!"
        )
        .is_ok());
        assert!(validation::validate_password_hint(&None, "StrongPassword123!").is_ok());

        // 无效提示词（与密码相同）
        assert!(validation::validate_password_hint(
            &Some("StrongPassword123!".to_string()),
            "StrongPassword123!"
        )
        .is_err());
        assert!(validation::validate_password_hint(
            &Some("strongpassword123!".to_string()),
            "StrongPassword123!"
        )
        .is_err());
    }
}
