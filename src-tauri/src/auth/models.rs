use serde::{Deserialize, Serialize};

/// 用户注册类型
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub enum RegistrationType {
    Email,
    Phone,
}

/// 通过宏定义生成结构体
macro_rules! register_request_fields {
    ($(#[$doc:meta])* $struct_name:ident) => {
        $(#[$doc])*
        #[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
        pub struct $struct_name {
            /// 用户昵称
            pub nickname: String,
            /// 邮箱或手机号
            pub contact: String,
            /// 注册类型（邮箱或手机号）
            pub registration_type: RegistrationType,
            /// 用户主密码加盐哈希，base64编码
            pub password_hash: String,
            /// 主密码提示信息，最长20字符
            pub password_hint: Option<String>,
            /// 使用主密钥加密后的对称密钥，base64编码
            pub symmetric_key: String,
            /// 用于分享的公钥，不需要加密，base64编码
            pub public_key: String,
            /// 用于分享的使用主密钥加密后的用户私钥，base64编码
            pub private_key: String,
            /// 验证码
            pub verification_code: String,
        }
    };
}

// 用户注册请求
register_request_fields!(RegisterRequest);

/// 用户登录请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    /// 邮箱或手机号
    pub contact: String,
    /// 主密码（客户端哈希后的）
    pub password_hash: String,
    /// 双因素认证令牌（可选）
    pub two_factor_token: Option<String>,
    /// 双因素认证提供商类型
    pub two_factor_provider: Option<TwoFactorProviderType>,
    /// 验证码令牌（防机器人）
    pub captcha_token: Option<String>,
    /// 是否记住登录状态
    pub remember_me: bool,
}

/// 双因素认证提供商类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TwoFactorProviderType {
    Authenticator = 0,
    Email = 1,
    Sms = 2,
}

/// 认证令牌
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthToken {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub token_type: String,
    pub expires_in: i64,
    pub issued_at: i64,
}

/// 密码重置请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetRequest {
    pub contact: String,
    pub verification_code: String,
}

/// 密码重置确认请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetConfirmRequest {
    pub contact: String,
    pub verification_code: String,
    pub new_password_hash: String,
    pub password_hint: Option<String>,
}

// 远程注册请求（发送到服务端）, 和 RegisterRequest 结构相同
register_request_fields!(RemoteRegisterRequest);

/// 远程注册响应（从服务端接收）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteRegisterResponse {
    /// 状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 注册成功后的数据（成功时返回）
    pub data: Option<RemoteRegisterData>,
}

/// 远程注册成功后的数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteRegisterData {
    /// 认证令牌信息
    pub token: RemoteTokenInfo,
}

/// 远程登录请求（发送到服务端）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteLoginRequest {
    /// 邮箱
    pub email: String,
    /// 加密后的主密码哈希
    pub password_hash: String,
    /// 验证码
    pub verification_code: String,
}

/// 远程登录响应（从服务端接收）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteLoginResponse {
    /// 状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 响应数据（成功时返回）
    pub data: Option<RemoteLoginData>,
}

/// 远程登录数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteLoginData {
    /// 认证令牌信息
    pub token: RemoteTokenInfo,
}

/// 远程令牌信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteTokenInfo {
    /// 令牌类型
    pub token_type: String,
    /// 访问令牌
    pub access_token: String,
    /// 刷新令牌
    pub refresh_token: String,
    /// 过期时间（秒）
    pub expires_in: i64,
}

/// 远程密钥信息响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteKeysInfoResponse {
    /// 状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 密钥数据
    pub data: Option<RemoteKeysData>,
}

/// 远程密钥数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoteKeysData {
    /// 加密的对称密钥（Base64编码）
    pub sym_key: String,
    /// 公钥（Base64编码，明文）
    pub pub_key: String,
    /// 加密的私钥（Base64编码）
    pub prv_key: String,
}

/// 密钥同步结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeysSyncResult {
    /// 是否同步成功
    pub success: bool,
    /// 同步消息
    pub message: String,
    /// 更新的密钥类型
    pub updated_keys: Vec<String>,
    /// 是否有密钥不一致
    pub has_conflicts: bool,
}
