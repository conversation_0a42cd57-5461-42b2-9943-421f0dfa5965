#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::services::RemoteAuthService;
    use crate::auth::models::{RemoteRegisterRequest, RegistrationType};

    #[tokio::test]
    async fn test_remote_auth_service_creation() {
        let service = RemoteAuthService::new("http://39.107.78.133");
        // 测试服务创建是否成功
        assert!(true); // 如果能创建就说明基本结构正确
    }

    #[tokio::test]
    async fn test_remote_register_request_serialization() {
        let request = RemoteRegisterRequest {
            nickname: "test_user".to_string(),
            contact: "<EMAIL>".to_string(),
            registration_type: RegistrationType::Email,
            password_hash: "test_hash".to_string(),
            password_hint: Some("test hint".to_string()),
            symmetric_key: "test_symmetric_key".to_string(),
            public_key: "test_public_key".to_string(),
            private_key: "test_private_key".to_string(),
            verification_code: "123456".to_string(),
        };

        // 测试序列化
        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("nickname"));
        assert!(json.contains("contact"));
        assert!(json.contains("password_hash"));
        assert!(json.contains("verification_code"));
    }

    #[tokio::test]
    async fn test_token_manager() {
        use crate::auth::token_manager::TokenManager;
        use crate::auth::models::RemoteTokenInfo;

        let manager = TokenManager::new();
        
        // 测试存储 token
        let token_info = RemoteTokenInfo {
            token_type: "Bearer".to_string(),
            access_token: "test_access_token".to_string(),
            refresh_token: "test_refresh_token".to_string(),
            expires_in: 3600,
        };

        manager.store_token(token_info).await;

        // 测试获取 token
        let token = manager.get_token().await;
        assert!(token.is_some());
        assert_eq!(token.unwrap(), "test_access_token");

        // 测试获取刷新 token
        let refresh_token = manager.get_refresh_token().await;
        assert!(refresh_token.is_some());
        assert_eq!(refresh_token.unwrap(), "test_refresh_token");

        // 测试清除 token
        manager.clear_token().await;
        let token_after_clear = manager.get_token().await;
        assert!(token_after_clear.is_none());
    }

    #[test]
    fn test_http_method_display() {
        use crate::http::types::HttpMethod;
        
        assert_eq!(format!("{}", HttpMethod::Get), "GET");
        assert_eq!(format!("{}", HttpMethod::Post), "POST");
        assert_eq!(format!("{}", HttpMethod::Put), "PUT");
        assert_eq!(format!("{}", HttpMethod::Delete), "DELETE");
    }

    #[tokio::test]
    async fn test_auth_interceptor() {
        use crate::http::interceptor::AuthInterceptor;
        
        let interceptor = AuthInterceptor::new();
        
        // 测试设置和获取 token
        interceptor.set_token(Some("test_token".to_string())).await;
        let token = interceptor.get_token().await;
        assert_eq!(token, Some("test_token".to_string()));
        
        // 测试清除 token
        interceptor.set_token(None).await;
        let token_after_clear = interceptor.get_token().await;
        assert!(token_after_clear.is_none());
    }
}