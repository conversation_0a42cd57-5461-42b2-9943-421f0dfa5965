// 向后兼容模块
// 提供旧API的别名函数，确保现有代码不会中断

use crate::errors::VaultResult;

use super::{
    key_derivation::derive_key_from_password,
    symmetric::{decrypt_data, encrypt_data, EncryptedData},
    KEY_SIZE,
};

/// 为了向后兼容，提供旧的密钥派生函数别名
pub fn derive_key(password: &str, salt: &str) -> VaultResult<[u8; KEY_SIZE]> {
    derive_key_from_password(password, salt)
}

/// 为了向后兼容，提供旧的加密函数别名
pub fn encrypt(data: &[u8], key: &[u8; KEY_SIZE]) -> VaultResult<(Vec<u8>, Vec<u8>)> {
    let encrypted = encrypt_data(data, key)?;
    let ciphertext = encrypted.ciphertext_bytes()?;
    let nonce = encrypted.nonce_bytes()?;
    Ok((ciphertext, nonce))
}

/// 为了向后兼容，提供旧的解密函数别名
pub fn decrypt(ciphertext: &[u8], key: &[u8; KEY_SIZE], nonce: &[u8]) -> VaultResult<Vec<u8>> {
    let encrypted = EncryptedData::from_bytes(ciphertext, nonce);
    decrypt_data(&encrypted, key)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::crypto::key_derivation::generate_salt;

    #[test]
    fn test_compat_derive_key() {
        let salt = generate_salt().unwrap();
        let password = "test_password_123";

        let key1 = derive_key(password, &salt).unwrap();
        let key2 = derive_key_from_password(password, &salt).unwrap();

        assert_eq!(key1, key2);
    }

    #[test]
    fn test_compat_encrypt_decrypt() {
        let key = [0x42; KEY_SIZE];
        let data = b"Hello, World!";

        let (ciphertext, nonce) = encrypt(data, &key).unwrap();
        let decrypted = decrypt(&ciphertext, &key, &nonce).unwrap();

        assert_eq!(data.to_vec(), decrypted);
    }
}
