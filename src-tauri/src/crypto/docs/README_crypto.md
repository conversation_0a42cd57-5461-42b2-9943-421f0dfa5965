# 模块化加密系统文档

## 概述

本项目采用了全新的模块化加密系统，基于 Tauri 2.5.0 密码管理应用的最佳实践设计。该系统提供了强大的安全保障、优秀的可维护性和灵活的扩展能力。

## 系统架构

```
src-tauri/src/crypto/
├── mod.rs              # 主模块接口和兼容性层
├── key_derivation.rs   # 密钥派生（Argon2id）
├── symmetric.rs        # 对称加密（AES-256-GCM）
├── keychain.rs         # 系统密钥链集成
├── secure_memory.rs    # 安全内存管理
└── vault_crypto.rs     # 高级加密操作API
```

## 核心模块详解

### 1. `crypto/mod.rs` - 主接口模块

**核心常量：**
```rust
pub const KEY_SIZE: usize = 32;           // AES-256密钥长度
pub const NONCE_SIZE: usize = 12;         // AES-GCM随机数长度
pub const SALT_SIZE: usize = 32;          // 盐值长度
pub const MIN_PASSWORD_LENGTH: usize = 8; // 最小密码长度
```

**主要结构体：**
- `MasterKey`: 主密钥管理
- `CryptoConfig`: 加密配置参数
- `CryptoContext`: 加密上下文（兼容性）

**兼容性API：**
```rust
// 为旧代码提供的兼容函数
pub fn derive_key(password: &str, salt: &str) -> VaultResult<[u8; KEY_SIZE]>
pub fn encrypt(data: &[u8], key: &[u8; KEY_SIZE]) -> VaultResult<(Vec<u8>, Vec<u8>)>
pub fn decrypt(ciphertext: &[u8], key: &[u8; KEY_SIZE], nonce: &[u8]) -> VaultResult<Vec<u8>>
pub fn encode_base64(data: &[u8]) -> String
pub fn decode_base64(data: &str) -> VaultResult<Vec<u8>>
```

### 2. `key_derivation.rs` - 密钥派生模块

**核心功能：**
- **Argon2id算法**：业界标准的密码哈希算法
- **安全参数**：基于OWASP推荐的安全配置
- **可配置强度**：支持快速、平衡、安全三种预设

**主要结构体：**
```rust
pub struct KeyDerivationParams {
    pub algorithm: Algorithm,
    pub version: Version,
    pub iterations: u32,
    pub memory_cost: u32,
    pub parallelism: u32,
}

pub struct SecureDerivedKey {
    key: [u8; KEY_SIZE],
    params: KeyDerivationParams,
}
```

**使用示例：**
```rust
// 生成随机盐值
let salt = generate_salt()?;

// 派生密钥
let key = derive_key_from_password("user_password", &salt)?;

// 使用自定义参数
let params = KeyDerivationParams::secure(); // 高安全级别
let custom_key = derive_key_from_password_with_params("password", &salt, &params)?;
```

**密码强度评估：**
```rust
let strength = assess_password_strength("MyPassword123!");
if strength.is_acceptable() {
    // 密码强度足够
}
```

### 3. `symmetric.rs` - 对称加密模块

**加密算法：**
- **AES-256-GCM**：提供加密和认证
- **随机Nonce**：每次加密使用新的随机数
- **时间戳**：记录加密时间用于审计

**核心结构体：**
```rust
pub struct EncryptedData {
    ciphertext: SecureBytes,
    nonce: [u8; NONCE_SIZE],
    algorithm: String,
    timestamp: DateTime<Utc>,
}

pub struct SymmetricCipher {
    key: [u8; KEY_SIZE],
}
```

**使用示例：**
```rust
// 创建加密器
let cipher = SymmetricCipher::new(&master_key);

// 加密文本
let encrypted = cipher.encrypt_text("Hello, World!")?;

// 解密
let decrypted = cipher.decrypt_text(&encrypted)?;

// 序列化存储
let base64_string = encrypted.to_base64();
let restored = EncryptedData::from_base64(&base64_string)?;
```

**批量操作：**
```rust
let batch_cipher = BatchCipher::new(&key);
let plaintexts = vec!["text1", "text2", "text3"];
let encrypted_batch = batch_cipher.encrypt_batch(&plaintexts)?;
```

### 4. `keychain.rs` - 系统密钥链集成

**跨平台支持：**
- **macOS**: Keychain Services
- **Windows**: Windows Credential Manager
- **Linux**: Secret Service API

**主要功能：**
```rust
pub struct KeychainManager {
    service: String,
    account: String,
}

// 密钥类型
pub enum KeyType {
    MasterKey,
    DerivedKey,
    SessionKey,
    BackupKey,
}
```

**使用示例：**
```rust
// 创建密钥链管理器
let keychain = KeychainManager::new("MyApp", "master-key")?;

// 存储密钥
keychain.store_key_with_type(&key_bytes, KeyType::MasterKey)?;

// 获取密钥
let stored_key = keychain.get_key()?;

// 检查是否存在
if keychain.key_exists() {
    // 密钥已存在
}
```

### 5. `secure_memory.rs` - 安全内存管理

**核心特性：**
- **自动清零**：离开作用域时自动清除内存
- **常时间比较**：防止时序攻击
- **内存锁定**：防止内存被交换到磁盘（可选）

**主要结构体：**
```rust
pub struct SecureBytes {
    data: Vec<u8>,
    locked: bool,
}

pub struct SecureString {
    inner: SecureBytes,
}
```

**使用示例：**
```rust
// 安全字节容器
let secure_data = SecureBytes::from_slice(b"sensitive data");

// 安全字符串
let secure_password = SecureString::from_str("password123");

// 常时间比较
let is_equal = secure_data.constant_time_eq(b"sensitive data");

// 临时访问
let exposed = secure_password.expose_secret()?;
println!("Password: {}", exposed);
```

**工具函数：**
```rust
// 生成随机安全字节
let random_bytes = utils::random_secure_bytes(32);

// 生成随机密码
let random_password = utils::random_secure_password(16);
```

### 6. `vault_crypto.rs` - 高级加密API

**核心状态管理：**
```rust
pub enum CryptoState {
    Uninitialized,  // 未初始化
    Locked,         // 已锁定
    Unlocked,       // 已解锁
    Error(String),  // 错误状态
}

pub struct VaultCrypto {
    state: Arc<RwLock<CryptoState>>,
    master_key: Arc<RwLock<Option<MasterKey>>>,
    config: CryptoConfig,
    keychain: Option<KeychainManager>,
    auto_lock_timer: Arc<RwLock<Option<Instant>>>,
    session_keys: Arc<RwLock<HashMap<String, SecureSessionKey>>>,
}
```

**主要功能：**

#### 保险库生命周期管理
```rust
// 初始化保险库
vault.initialize("strong_password", &salt).await?;

// 解锁保险库
vault.unlock("strong_password", &salt).await?;

// 锁定保险库
vault.lock().await;

// 检查状态
if vault.is_unlocked().await {
    // 可以进行加密操作
}
```

#### 数据加密解密
```rust
// 单个数据加密
let encrypted = vault.encrypt("sensitive data").await?;
let decrypted = vault.decrypt(&encrypted).await?;

// 批量操作
let batch_data = vec!["data1", "data2", "data3"];
let encrypted_batch = vault.encrypt_batch(&batch_data).await?;
let decrypted_batch = vault.decrypt_batch(&encrypted_batch.iter().collect()).await?;
```

#### 会话密钥管理
```rust
// 生成会话密钥
vault.generate_session_key("session_001").await?;

// 使用会话密钥加密
let session_encrypted = vault.encrypt_with_session("session_001", "data").await?;
let session_decrypted = vault.decrypt_with_session("session_001", &session_encrypted).await?;

// 撤销会话
vault.revoke_session("session_001").await?;
```

#### 自动锁定功能
```rust
// 启动自动锁定任务
let vault_arc = Arc::new(vault);
vault_arc.start_auto_lock_task();

// 配置自动锁定时间（在CryptoConfig中）
let mut config = CryptoConfig::default();
config.auto_lock_timeout = Some(300); // 5分钟
```

#### 数据导入导出
```rust
// 导出保险库数据
let exported_data = vault.export_vault("export_password").await?;

// 导入保险库配置
vault.import_vault_config(&exported_data, "export_password").await?;
```

## 配置参数

### CryptoConfig 结构体
```rust
pub struct CryptoConfig {
    pub argon2_iterations: u32,      // Argon2迭代次数（默认：3）
    pub argon2_memory: u32,          // 内存使用量KB（默认：65536 = 64MB）
    pub argon2_parallelism: u32,     // 并行度（默认：4）
    pub enable_keychain: bool,       // 是否启用系统密钥链（默认：true）
    pub auto_lock_timeout: Option<u64>, // 自动锁定时间秒（默认：300 = 5分钟）
}
```

### 预设配置
```rust
// 快速配置（开发环境）
let fast_config = CryptoConfig {
    argon2_iterations: 2,
    argon2_memory: 19456,  // 19MB
    argon2_parallelism: 2,
    enable_keychain: false,
    auto_lock_timeout: Some(600), // 10分钟
};

// 平衡配置（默认）
let balanced_config = CryptoConfig::default();

// 高安全配置（生产环境）
let secure_config = CryptoConfig {
    argon2_iterations: 5,
    argon2_memory: 131072, // 128MB
    argon2_parallelism: 8,
    enable_keychain: true,
    auto_lock_timeout: Some(180), // 3分钟
};
```

## 集成示例

### 在AppState中使用
```rust
use crate::crypto::{CryptoConfig, VaultCrypto};

impl AppState {
    pub fn new() -> Self {
        let config = CryptoConfig::default();
        let crypto = VaultCrypto::new(config).expect("Failed to initialize crypto");
        
        Self {
            db: Arc::new(Mutex::new(None)),
            crypto: Arc::new(crypto),
            db_path: Arc::new(Mutex::new(None)),
        }
    }
    
    pub fn crypto(&self) -> &VaultCrypto {
        &self.crypto
    }
}
```

### 在Service中使用
```rust
pub struct PasswordService {
    vault_repo: VaultRepository,
    login_repo: LoginRepository,
    crypto: Arc<VaultCrypto>,
}

impl PasswordService {
    pub async fn add_login_credential(&self, password: String) -> VaultResult<String> {
        // 加密密码
        let encrypted_data = self.crypto.encrypt(&password).await?;
        let encrypted_password = encrypted_data.to_base64();
        
        // 存储到数据库
        // ...
        
        Ok(item_id)
    }
    
    pub async fn get_decrypted_password(&self, item_id: &str) -> VaultResult<String> {
        // 从数据库获取加密数据
        let encrypted_password = self.login_repo.get_password(item_id).await?;
        
        // 解密密码
        let encrypted_data = EncryptedData::from_base64(&encrypted_password)?;
        let decrypted_password = self.crypto.decrypt(&encrypted_data).await?;
        
        Ok(decrypted_password)
    }
}
```

### Tauri命令中使用
```rust
#[tauri::command]
pub async fn unlock_vault(
    state: State<'_, AppState>,
    master_password: String,
    salt: String,
) -> Result<bool, String> {
    let crypto = state.crypto();
    
    crypto.unlock(&master_password, &salt).await
        .map_err(|e| e.to_string())?;
    
    // 启动自动锁定任务
    state.start_auto_lock_task();
    
    Ok(true)
}

#[tauri::command]
pub async fn encrypt_data(
    state: State<'_, AppState>,
    data: String,
) -> Result<String, String> {
    let crypto = state.crypto();
    
    let encrypted = crypto.encrypt(&data).await
        .map_err(|e| e.to_string())?;
    
    Ok(encrypted.to_base64())
}
```

## 迁移指南

### 从旧系统迁移

#### 1. 更新导入
```rust
// 旧方式
use crate::crypto::{encrypt_text, decrypt_text, derive_key};

// 新方式
use crate::crypto::{VaultCrypto, EncryptedData};
use std::sync::Arc;
```

#### 2. 更新服务构造函数
```rust
// 旧方式
impl PasswordService {
    pub fn new(vault_repo: VaultRepository, login_repo: LoginRepository) -> Self {
        Self { vault_repo, login_repo }
    }
}

// 新方式
impl PasswordService {
    pub fn new(vault_repo: VaultRepository, login_repo: LoginRepository, crypto: Arc<VaultCrypto>) -> Self {
        Self { vault_repo, login_repo, crypto }
    }
}
```

#### 3. 更新加密调用
```rust
// 旧方式
let encrypted_password = encrypt_text(&password)?;
let decrypted_password = decrypt_text(&encrypted_password)?;

// 新方式
let encrypted_data = self.crypto.encrypt(&password).await?;
let encrypted_password = encrypted_data.to_base64();

let encrypted_data = EncryptedData::from_base64(&encrypted_password)?;
let decrypted_password = self.crypto.decrypt(&encrypted_data).await?;
```

### 数据兼容性

新系统完全兼容现有数据格式：
- 数据库结构无需更改
- Base64编码格式保持一致
- 可以无缝读取旧数据

## 安全特性

### 1. 密码学安全
- **Argon2id**: 抗ASIC和GPU攻击的密码哈希
- **AES-256-GCM**: NSA认证的对称加密算法
- **随机Nonce**: 每次加密使用新的随机数
- **认证加密**: 提供机密性和完整性保护

### 2. 内存安全
- **自动清零**: 敏感数据自动从内存中清除
- **常时间比较**: 防止时序攻击
- **内存锁定**: 防止敏感数据被交换到磁盘

### 3. 密钥管理
- **系统密钥链**: 安全存储主密钥
- **会话密钥**: 临时加密密钥管理
- **密钥派生**: 安全的密码到密钥转换

### 4. 状态管理
- **自动锁定**: 超时自动锁定保险库
- **状态追踪**: 实时追踪加密系统状态
- **错误处理**: 完善的错误处理机制

## 性能优化

### 1. 异步操作
所有加密操作都是异步的，不会阻塞UI：
```rust
// 并发加密多个项目
let futures: Vec<_> = passwords.iter()
    .map(|p| crypto.encrypt(p))
    .collect();
let results = futures::future::join_all(futures).await;
```

### 2. 批量操作
支持批量加密解密，减少系统调用：
```rust
let encrypted_batch = crypto.encrypt_batch(&data_list).await?;
```

### 3. 会话密钥缓存
使用会话密钥减少密钥派生开销：
```rust
crypto.generate_session_key("session").await?;
let encrypted = crypto.encrypt_with_session("session", data).await?;
```

### 4. 内存优化
- 使用零拷贝操作
- 及时释放敏感内存
- 优化数据结构大小

## 错误处理

### VaultError 类型
```rust
pub enum VaultError {
    PasswordVerificationFailed,
    VaultLocked,
    VaultNotInitialized,
    Encryption(String),
    Decryption(String),
    KeyDerivation(String),
    KeychainError(KeychainError),
    InvalidInput(String),
    InternalError(String),
    Base64Decode(base64::DecodeError),
    SerializationError(String),
    AuthenticationError(String),
    CryptoOperationFailed(String),
}
```

### 错误处理最佳实践
```rust
match crypto.unlock(&password, &salt).await {
    Ok(_) => {
        log::info!("保险库解锁成功");
        // 继续操作
    }
    Err(VaultError::PasswordVerificationFailed) => {
        // 密码错误，提示用户
    }
    Err(VaultError::VaultNotInitialized) => {
        // 保险库未初始化，引导用户设置
    }
    Err(e) => {
        log::error!("解锁失败: {}", e);
        // 处理其他错误
    }
}
```

## 最佳实践

### 1. 密码强度
```rust
let strength = assess_password_strength(&password);
if !strength.is_acceptable() {
    return Err(VaultError::InvalidInput(
        format!("密码强度不足: {}", strength.description())
    ));
}
```

### 2. 安全日志
```rust
log::info!("保险库初始化完成");
log::warn!("密码强度较低");
log::error!("加密操作失败: {}", error);
// 注意：永远不要记录敏感信息
```

### 3. 异常处理
```rust
let encrypted = match crypto.encrypt(&data).await {
    Ok(encrypted) => encrypted,
    Err(VaultError::VaultLocked) => {
        // 提示用户解锁
        return Err("请先解锁保险库".into());
    }
    Err(e) => {
        log::error!("加密失败: {}", e);
        return Err("加密操作失败".into());
    }
};
```

### 4. 资源管理
```rust
{
    let sensitive_data = SecureString::from_str("password");
    // 使用sensitive_data
} // 自动清零内存
```

## 测试

### 单元测试示例
```rust
#[tokio::test]
async fn test_encrypt_decrypt_cycle() {
    let config = CryptoConfig::default();
    let vault = VaultCrypto::new(config).unwrap();
    let salt = generate_salt().unwrap();
    
    vault.initialize("test_password", &salt).await.unwrap();
    
    let plaintext = "Hello, World!";
    let encrypted = vault.encrypt(plaintext).await.unwrap();
    let decrypted = vault.decrypt(&encrypted).await.unwrap();
    
    assert_eq!(plaintext, decrypted);
}
```

### 性能测试
```rust
#[tokio::test]
async fn test_batch_performance() {
    let crypto = setup_crypto().await;
    let data: Vec<String> = (0..1000).map(|i| format!("data_{}", i)).collect();
    let data_refs: Vec<&str> = data.iter().map(|s| s.as_str()).collect();
    
    let start = Instant::now();
    let encrypted = crypto.encrypt_batch(&data_refs).await.unwrap();
    let duration = start.elapsed();
    
    assert!(duration < Duration::from_millis(1000)); // 1秒内完成
    assert_eq!(encrypted.len(), 1000);
}
```

## 故障排除

### 常见问题

**1. 编译错误：找不到加密函数**
```
error[E0432]: unresolved import `crate::crypto::encrypt_text`
```
解决方案：更新导入为新的API
```rust
use crate::crypto::{VaultCrypto, EncryptedData};
```

**2. 运行时错误：保险库未解锁**
```
VaultError::VaultLocked
```
解决方案：在加密操作前确保保险库已解锁
```rust
if !crypto.is_unlocked().await {
    crypto.unlock(&password, &salt).await?;
}
```

**3. 反序列化错误**
```
Failed to parse encrypted data: Invalid base64
```
解决方案：检查数据格式和版本兼容性

### 调试工具

启用详细日志：
```bash
RUST_LOG=debug cargo run
```

检查保险库状态：
```rust
let stats = crypto.get_vault_stats().await;
println!("当前状态: {:?}", stats.state);
println!("会话数量: {}", stats.session_count);
```

## 更新日志

### v2.0.0 (当前版本)
- ✅ 完全重写为模块化架构
- ✅ 新增VaultCrypto高级API
- ✅ 支持会话密钥管理
- ✅ 自动锁定功能
- ✅ 跨平台密钥链集成
- ✅ 安全内存管理
- ✅ 向后兼容支持

### v1.0.0 (旧版本)
- 基础加密解密功能
- 简单密钥派生
- 无状态管理

## 贡献指南

1. **安全第一**: 所有加密相关更改必须经过安全审查
2. **测试覆盖**: 新功能必须包含完整的单元测试
3. **文档更新**: API更改必须同步更新文档
4. **性能基准**: 性能关键路径需要基准测试

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 支持

如有问题请提交 Issue 或参考：
- [Tauri文档](https://tauri.app/)
- [Rust密码学库文档](https://docs.rs/)
- [OWASP密码存储指南](https://cheatsheetseries.owasp.org/cheatsheets/Password_Storage_Cheat_Sheet.html) 