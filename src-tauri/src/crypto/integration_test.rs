// 集成测试模块
// 验证重构后的所有模块是否能正常协同工作

#[cfg(test)]
mod tests {
    use crate::crypto::*;

    #[test]
    fn test_complete_registration_flow_components() {
        // 测试完整注册流程的各个组件
        let password = "TestPassword123!";
        let salt = generate_salt().unwrap();

        // 1. 派生主密钥
        let master_key = derive_key_from_password(password, &salt).unwrap();

        // 2. 生成对称密钥
        let symmetric_key = generate_symmetric_key().unwrap();

        // 3. 生成密钥对
        let keypair = generate_keypair().unwrap();

        // 4. 加密对称密钥
        let encrypted_symmetric_key = encrypt_symmetric_key(&symmetric_key, &master_key).unwrap();

        // 5. 加密私钥
        let encrypted_private_key = encrypt_private_key(&keypair.private_key, &master_key).unwrap();

        // 验证所有组件都正常工作
        assert_eq!(master_key.len(), KEY_SIZE);
        assert_eq!(symmetric_key.len(), KEY_SIZE);
        assert!(!keypair.public_key.is_empty());
        assert!(!keypair.private_key.is_empty());
        assert!(!encrypted_symmetric_key.is_empty());
        assert!(!encrypted_private_key.is_empty());

        // 验证加密后的数据与原始数据不同
        assert_ne!(encrypted_private_key, keypair.private_key);

        println!("✅ 完整注册流程组件测试通过");
        println!("- 主密钥长度: {} 字节", master_key.len());
        println!("- 对称密钥长度: {} 字节", symmetric_key.len());
        println!("- 公钥长度: {} 字符", keypair.public_key.len());
        println!("- 私钥长度: {} 字符", keypair.private_key.len());
        println!(
            "- 加密后对称密钥长度: {} 字符",
            encrypted_symmetric_key.len()
        );
        println!("- 加密后私钥长度: {} 字符", encrypted_private_key.len());
    }

    #[test]
    fn test_crypto_context_integration() {
        // 测试加密上下文的完整功能
        let salt = generate_salt().unwrap();
        let config = CryptoConfig::default();
        let mut context = CryptoContext::new(salt, config).unwrap();

        assert!(!context.is_unlocked());

        // 解锁
        context.unlock("test_password_123").unwrap();
        assert!(context.is_unlocked());

        // 加密解密
        let test_data = "Hello, World! 这是一个测试数据。";
        let encrypted = context.encrypt(test_data).unwrap();
        let decrypted = context.decrypt(&encrypted).unwrap();
        assert_eq!(decrypted, test_data);

        // 锁定
        context.lock();
        assert!(!context.is_unlocked());

        println!("✅ 加密上下文集成测试通过");
    }

    #[test]
    fn test_backward_compatibility() {
        // 测试向后兼容性
        let salt = generate_salt().unwrap();
        let password = "test_password_123";

        // 使用新API
        let key_new = derive_key_from_password(password, &salt).unwrap();

        // 使用兼容API
        let key_compat = derive_key(password, &salt).unwrap();

        assert_eq!(key_new, key_compat);

        // 测试加密解密兼容性
        let data = b"Test data for compatibility";
        let (ciphertext, nonce) = encrypt(data, &key_new).unwrap();
        let decrypted = decrypt(&ciphertext, &key_new, &nonce).unwrap();

        assert_eq!(data.to_vec(), decrypted);

        println!("✅ 向后兼容性测试通过");
    }

    #[test]
    fn test_utils_integration() {
        // 测试工具函数
        let password = generate_random_password(16, true);
        assert_eq!(password.len(), 16);

        let data = b"Test data for base64";
        let encoded = encode_base64(data);
        let decoded = decode_base64(&encoded).unwrap();
        assert_eq!(data.to_vec(), decoded);

        println!("✅ 工具函数集成测试通过");
    }

    #[test]
    fn test_all_modules_working_together() {
        // 综合测试：模拟一个完整的用户注册和数据加密流程

        // 1. 用户注册阶段
        let user_password = "UserPassword123!";
        let salt = generate_salt().unwrap();

        // 2. 创建加密上下文
        let config = CryptoConfig::default();
        let mut crypto_context = CryptoContext::new(salt.clone(), config).unwrap();

        // 3. 解锁保险库
        crypto_context.unlock(user_password).unwrap();

        // 4. 生成密钥对用于分享
        let keypair = generate_keypair().unwrap();

        // 5. 生成对称密钥用于数据加密
        let data_key = generate_symmetric_key().unwrap();

        // 6. 使用主密钥加密对称密钥和私钥
        let master_key = derive_key_from_password(user_password, &salt).unwrap();
        let encrypted_data_key = encrypt_symmetric_key(&data_key, &master_key).unwrap();
        let encrypted_private_key = encrypt_private_key(&keypair.private_key, &master_key).unwrap();

        // 7. 使用对称密钥加密用户数据
        let user_data = "这是用户的敏感数据，包含密码、笔记等信息。";
        let encrypted_user_data = encrypt_data(user_data.as_bytes(), &data_key).unwrap();

        // 8. 验证整个流程
        assert!(!encrypted_data_key.is_empty());
        assert!(!encrypted_private_key.is_empty());
        assert!(!encrypted_user_data.to_base64().is_empty());

        // 9. 解密验证
        let decrypted_user_data = decrypt_data(&encrypted_user_data, &data_key).unwrap();
        let decrypted_text = String::from_utf8(decrypted_user_data).unwrap();
        assert_eq!(decrypted_text, user_data);

        // 10. 使用加密上下文加密数据
        let context_encrypted = crypto_context.encrypt("Context encrypted data").unwrap();
        let context_decrypted = crypto_context.decrypt(&context_encrypted).unwrap();
        assert_eq!(context_decrypted, "Context encrypted data");

        println!("✅ 所有模块协同工作测试通过");
        println!("- 用户密码: {}", user_password);
        println!("- 盐值长度: {} 字符", salt.len());
        println!("- 公钥长度: {} 字符", keypair.public_key.len());
        println!("- 加密后数据密钥长度: {} 字符", encrypted_data_key.len());
        println!("- 加密后私钥长度: {} 字符", encrypted_private_key.len());
        println!(
            "- 加密后用户数据长度: {} 字符",
            encrypted_user_data.to_base64().len()
        );
    }
}
