// 密钥派生模块
// 实现基于 Argon2id 的安全密钥派生功能

use crate::errors::{VaultError, VaultResult};
use argon2::{
    password_hash::{rand_core::OsRng, SaltString},
    Algorithm, Argon2, Params, Version,
};
use serde::{Deserialize, Serialize};
use zeroize::Zeroize;

use super::KEY_SIZE;

/// 密钥派生参数配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyDerivationParams {
    /// 算法类型 (序列化为字符串)
    #[serde(
        serialize_with = "serialize_algorithm",
        deserialize_with = "deserialize_algorithm"
    )]
    pub algorithm: Algorithm,
    /// 版本 (序列化为字符串)
    #[serde(
        serialize_with = "serialize_version",
        deserialize_with = "deserialize_version"
    )]
    pub version: Version,
    /// 迭代次数
    pub iterations: u32,
    /// 内存使用量 (KB)
    pub memory_cost: u32,
    /// 并行度
    pub parallelism: u32,
    /// 输出长度
    pub output_length: usize,
}

// 序列化算法类型为字符串
fn serialize_algorithm<S>(algorithm: &Algorithm, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    let s = match algorithm {
        Algorithm::Argon2d => "Argon2d",
        Algorithm::Argon2i => "Argon2i",
        Algorithm::Argon2id => "Argon2id",
    };
    serializer.serialize_str(s)
}

// 从字符串反序列化算法类型
fn deserialize_algorithm<'de, D>(deserializer: D) -> Result<Algorithm, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    match s.as_str() {
        "Argon2d" => Ok(Algorithm::Argon2d),
        "Argon2i" => Ok(Algorithm::Argon2i),
        "Argon2id" => Ok(Algorithm::Argon2id),
        _ => Err(serde::de::Error::custom(format!(
            "Unknown algorithm: {}",
            s
        ))),
    }
}

// 序列化版本为字符串
fn serialize_version<S>(version: &Version, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    let s = match version {
        Version::V0x10 => "0x10",
        Version::V0x13 => "0x13",
    };
    serializer.serialize_str(s)
}

// 从字符串反序列化版本
fn deserialize_version<'de, D>(deserializer: D) -> Result<Version, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    match s.as_str() {
        "0x10" => Ok(Version::V0x10),
        "0x13" => Ok(Version::V0x13),
        _ => Err(serde::de::Error::custom(format!("Unknown version: {}", s))),
    }
}

impl Default for KeyDerivationParams {
    fn default() -> Self {
        Self {
            algorithm: Algorithm::Argon2id,
            version: Version::V0x13,
            // OWASP 推荐的参数
            iterations: 3,
            memory_cost: 65536, // 64 MB
            parallelism: 4,
            output_length: KEY_SIZE,
        }
    }
}

impl KeyDerivationParams {
    /// 创建高安全性参数（计算密集）
    pub fn high_security() -> Self {
        Self {
            algorithm: Algorithm::Argon2id,
            version: Version::V0x13,
            iterations: 5,
            memory_cost: 131072, // 128 MB
            parallelism: 8,
            output_length: KEY_SIZE,
        }
    }

    /// 创建平衡参数（推荐用于生产环境）
    pub fn balanced() -> Self {
        Self::default()
    }

    /// 创建快速参数（仅用于开发/测试）
    pub fn fast() -> Self {
        Self {
            algorithm: Algorithm::Argon2id,
            version: Version::V0x13,
            iterations: 2,
            memory_cost: 19456, // 19 MB
            parallelism: 1,
            output_length: KEY_SIZE,
        }
    }

    /// 验证参数是否有效
    pub fn validate(&self) -> VaultResult<()> {
        if self.iterations < 1 {
            return Err(VaultError::InvalidInput("迭代次数必须大于0".to_string()));
        }
        if self.memory_cost < 8 {
            return Err(VaultError::InvalidInput(
                "内存使用量必须至少8KB".to_string(),
            ));
        }
        if self.parallelism < 1 {
            return Err(VaultError::InvalidInput("并行度必须大于0".to_string()));
        }
        if self.output_length < 16 || self.output_length > 64 {
            return Err(VaultError::InvalidInput(
                "输出长度必须在16-64字节之间".to_string(),
            ));
        }
        Ok(())
    }

    /// 转换为 Argon2 参数
    fn to_argon2_params(&self) -> VaultResult<Params> {
        self.validate()?;

        Params::new(
            self.memory_cost,
            self.iterations,
            self.parallelism,
            Some(self.output_length),
        )
        .map_err(|e| VaultError::ConfigError(format!("无效的Argon2参数: {}", e)))
    }
}

/// 生成加密安全的盐值
pub fn generate_salt() -> VaultResult<String> {
    let salt = SaltString::generate(&mut OsRng);
    Ok(salt.to_string())
}

/// 从密码派生密钥
pub fn derive_key_from_password(password: &str, salt_str: &str) -> VaultResult<[u8; KEY_SIZE]> {
    derive_key_from_password_with_params(password, salt_str, &KeyDerivationParams::default())
}

/// 使用自定义参数从密码派生密钥
pub fn derive_key_from_password_with_params(
    password: &str,
    salt_str: &str,
    params: &KeyDerivationParams,
) -> VaultResult<[u8; KEY_SIZE]> {
    // 验证输入
    if password.is_empty() {
        return Err(VaultError::InvalidInput("密码不能为空".to_string()));
    }
    if salt_str.is_empty() {
        return Err(VaultError::InvalidInput("盐值不能为空".to_string()));
    }

    // 解析盐值
    let salt = SaltString::from_b64(salt_str)
        .map_err(|e| VaultError::ConfigError(format!("无效的盐值格式: {}", e)))?;

    // 创建 Argon2 实例
    let argon2_params = params.to_argon2_params()?;
    let argon2 = Argon2::new(params.algorithm, params.version, argon2_params);

    // 派生密钥
    let mut key = [0u8; KEY_SIZE];
    argon2
        .hash_password_into(password.as_bytes(), salt.as_ref().as_bytes(), &mut key)
        .map_err(|e| {
            log::error!("Argon2 密钥派生失败: {}", e);
            VaultError::Hashing(format!("密钥派生失败: {}", e))
        })?;

    Ok(key)
}

/// 安全的密钥派生器，自动管理内存清理
pub struct KeyDeriver {
    params: KeyDerivationParams,
}

impl KeyDeriver {
    /// 创建新的密钥派生器
    pub fn new(params: KeyDerivationParams) -> VaultResult<Self> {
        params.validate()?;
        Ok(Self { params })
    }

    /// 使用默认参数创建
    pub fn default() -> VaultResult<Self> {
        Self::new(KeyDerivationParams::default())
    }

    /// 派生密钥
    pub fn derive(&self, password: &str, salt: &str) -> VaultResult<SecureDerivedKey> {
        let key = derive_key_from_password_with_params(password, salt, &self.params)?;
        Ok(SecureDerivedKey::new(key))
    }

    /// 派生密钥并返回原始字节
    pub fn derive_raw(&self, password: &str, salt: &str) -> VaultResult<[u8; KEY_SIZE]> {
        derive_key_from_password_with_params(password, salt, &self.params)
    }

    /// 获取当前参数
    pub fn params(&self) -> &KeyDerivationParams {
        &self.params
    }

    /// 更新参数
    pub fn update_params(&mut self, params: KeyDerivationParams) -> VaultResult<()> {
        params.validate()?;
        self.params = params;
        Ok(())
    }
}

/// 安全的派生密钥容器，自动清理内存
#[derive(Zeroize)]
pub struct SecureDerivedKey {
    key: [u8; KEY_SIZE],
}

impl SecureDerivedKey {
    /// 创建新的安全密钥容器
    pub fn new(key: [u8; KEY_SIZE]) -> Self {
        Self { key }
    }

    /// 获取密钥字节（只读引用）
    pub fn as_bytes(&self) -> &[u8; KEY_SIZE] {
        &self.key
    }

    /// 获取密钥的副本（调用者负责清理）
    pub fn clone_key(&self) -> [u8; KEY_SIZE] {
        self.key
    }

    /// 验证另一个密钥是否相同
    pub fn verify(&self, other: &[u8]) -> bool {
        if other.len() != KEY_SIZE {
            return false;
        }

        // 使用常时间比较防止时序攻击
        use subtle::ConstantTimeEq;
        self.key.ct_eq(other).into()
    }
}

impl Drop for SecureDerivedKey {
    fn drop(&mut self) {
        self.zeroize();
    }
}

/// 密钥强度评估
pub fn assess_password_strength(password: &str) -> PasswordStrength {
    let length = password.len();
    let has_lower = password.chars().any(|c| c.is_lowercase());
    let has_upper = password.chars().any(|c| c.is_uppercase());
    let has_digit = password.chars().any(|c| c.is_ascii_digit());
    let has_special = password
        .chars()
        .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

    let charset_bonus = [has_lower, has_upper, has_digit, has_special]
        .iter()
        .map(|&b| if b { 1 } else { 0 })
        .sum::<usize>();

    let score = length + charset_bonus * 2;

    match score {
        0..=6 => PasswordStrength::VeryWeak,
        7..=10 => PasswordStrength::Weak,
        11..=15 => PasswordStrength::Medium,
        16..=20 => PasswordStrength::Strong,
        _ => PasswordStrength::VeryStrong,
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum PasswordStrength {
    VeryWeak,
    Weak,
    Medium,
    Strong,
    VeryStrong,
}

impl PasswordStrength {
    pub fn description(&self) -> &'static str {
        match self {
            Self::VeryWeak => "非常弱",
            Self::Weak => "弱",
            Self::Medium => "中等",
            Self::Strong => "强",
            Self::VeryStrong => "非常强",
        }
    }

    pub fn is_acceptable(&self) -> bool {
        matches!(self, Self::Medium | Self::Strong | Self::VeryStrong)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_salt_generation() {
        let salt1 = generate_salt().unwrap();
        let salt2 = generate_salt().unwrap();

        assert_ne!(salt1, salt2);
        assert!(!salt1.is_empty());
        assert!(!salt2.is_empty());
    }

    #[test]
    fn test_key_derivation() {
        let salt = generate_salt().unwrap();
        let password = "test_password_123";

        let key1 = derive_key_from_password(password, &salt).unwrap();
        let key2 = derive_key_from_password(password, &salt).unwrap();

        assert_eq!(key1, key2);

        // 不同的盐应该产生不同的密钥
        let salt2 = generate_salt().unwrap();
        let key3 = derive_key_from_password(password, &salt2).unwrap();
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_key_deriver() {
        let params = KeyDerivationParams::fast(); // 用于测试的快速参数
        let deriver = KeyDeriver::new(params).unwrap();

        let salt = generate_salt().unwrap();
        let password = "test_password_123";

        let secure_key = deriver.derive(password, &salt).unwrap();
        let raw_key = deriver.derive_raw(password, &salt).unwrap();

        assert_eq!(secure_key.as_bytes(), &raw_key);
        assert!(secure_key.verify(&raw_key));
    }

    #[test]
    fn test_password_strength() {
        assert_eq!(assess_password_strength("123"), PasswordStrength::VeryWeak);
        assert_eq!(assess_password_strength("password"), PasswordStrength::Weak);
        assert_eq!(
            assess_password_strength("Password123"),
            PasswordStrength::Strong
        );

        // 调试 Password123! 的计算
        let password = "Password123!";
        let length = password.len();
        let has_lower = password.chars().any(|c| c.is_lowercase());
        let has_upper = password.chars().any(|c| c.is_uppercase());
        let has_digit = password.chars().any(|c| c.is_ascii_digit());
        let has_special = password
            .chars()
            .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));
        let charset_bonus = [has_lower, has_upper, has_digit, has_special]
            .iter()
            .map(|&b| if b { 1 } else { 0 })
            .sum::<usize>();
        let score = length + charset_bonus * 2;

        println!("Password: {}", password);
        println!("Length: {}", length);
        println!("Has lower: {}", has_lower);
        println!("Has upper: {}", has_upper);
        println!("Has digit: {}", has_digit);
        println!("Has special: {}", has_special);
        println!("Charset bonus: {}", charset_bonus);
        println!("Score: {}", score);

        let strength = assess_password_strength(password);
        println!("Strength: {:?}", strength);

        // Password123! 长度12 + 4种字符类型*2 = 20，在16-20范围内，确实是Strong
        assert_eq!(strength, PasswordStrength::Strong);

        assert_eq!(
            assess_password_strength("MyVerySecurePassword123!@#"),
            PasswordStrength::VeryStrong
        );
    }

    #[test]
    fn test_params_validation() {
        let mut params = KeyDerivationParams::default();
        assert!(params.validate().is_ok());

        params.iterations = 0;
        assert!(params.validate().is_err());

        params.iterations = 1;
        params.memory_cost = 7;
        assert!(params.validate().is_err());

        params.memory_cost = 8;
        params.parallelism = 0;
        assert!(params.validate().is_err());
    }

    #[test]
    fn test_serialization() {
        let params = KeyDerivationParams::default();
        let serialized = serde_json::to_string(&params).unwrap();
        let deserialized: KeyDerivationParams = serde_json::from_str(&serialized).unwrap();

        assert_eq!(params.iterations, deserialized.iterations);
        assert_eq!(params.memory_cost, deserialized.memory_cost);
        assert_eq!(params.parallelism, deserialized.parallelism);
        assert_eq!(params.output_length, deserialized.output_length);
    }
}
