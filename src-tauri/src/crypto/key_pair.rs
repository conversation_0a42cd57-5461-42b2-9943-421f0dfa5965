// 密钥对模块
// 实现 Ed25519 密钥对生成和管理功能

use crate::errors::VaultResult;
use serde::{Deserialize, Serialize};

use super::{symmetric::encrypt_data, utils::encode_base64, KEY_SIZE};

/// 密钥对结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct KeyPair {
    /// 公钥（Base64编码）
    pub public_key: String,
    /// 私钥（Base64编码）
    pub private_key: String,
}

/// 生成Ed25519密钥对用于分享功能
///
/// # 返回
/// - 成功时返回包含公钥和私钥的KeyPair结构
/// - 失败时返回VaultError
pub fn generate_keypair() -> VaultResult<KeyPair> {
    use ed25519_dalek::{SigningKey, VerifyingKey};
    use rand::rngs::OsRng;

    // 生成随机的签名密钥（私钥）
    let signing_key = SigningKey::generate(&mut OsRng);

    // 从签名密钥派生验证密钥（公钥）
    let verifying_key: VerifyingKey = signing_key.verifying_key();

    // 将密钥编码为Base64
    let private_key = encode_base64(&signing_key.to_bytes());
    let public_key = encode_base64(&verifying_key.to_bytes());

    Ok(KeyPair {
        public_key,
        private_key,
    })
}

/// 使用主密钥加密私钥
///
/// # 参数
/// - `private_key`: 要加密的私钥（Base64编码）
/// - `master_key`: 用于加密的主密钥
///
/// # 返回
/// - 成功时返回加密后的私钥（Base64编码）
/// - 失败时返回VaultError
pub fn encrypt_private_key(private_key: &str, master_key: &[u8; KEY_SIZE]) -> VaultResult<String> {
    let encrypted = encrypt_data(private_key.as_bytes(), master_key)?;
    Ok(encrypted.to_base64())
}

/// 使用主密钥解密私钥
///
/// # 参数
/// - `encrypted_private_key`: 加密的私钥（Base64编码）
/// - `master_key`: 用于解密的主密钥
///
/// # 返回
/// - 成功时返回解密后的私钥（Base64编码）
/// - 失败时返回VaultError
pub fn decrypt_private_key(
    encrypted_private_key: &str,
    master_key: &[u8; KEY_SIZE],
) -> VaultResult<String> {
    use super::symmetric::{decrypt_data, EncryptedData};

    let encrypted_data = EncryptedData::from_base64(encrypted_private_key)?;
    let decrypted_bytes = decrypt_data(&encrypted_data, master_key)?;

    String::from_utf8(decrypted_bytes)
        .map_err(|e| crate::errors::VaultError::InvalidData(format!("Invalid UTF-8: {}", e)))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::crypto::symmetric::generate_symmetric_key;
    use base64::Engine;

    #[test]
    fn test_keypair_generation() {
        let keypair = generate_keypair().unwrap();

        // 验证密钥对不为空
        assert!(!keypair.public_key.is_empty());
        assert!(!keypair.private_key.is_empty());

        // 验证是有效的Base64编码
        assert!(base64::engine::general_purpose::STANDARD
            .decode(&keypair.public_key)
            .is_ok());
        assert!(base64::engine::general_purpose::STANDARD
            .decode(&keypair.private_key)
            .is_ok());

        // 验证每次生成的密钥对都不同
        let keypair2 = generate_keypair().unwrap();
        assert_ne!(keypair.public_key, keypair2.public_key);
        assert_ne!(keypair.private_key, keypair2.private_key);
    }

    #[test]
    fn test_private_key_encryption() {
        let keypair = generate_keypair().unwrap();
        let master_key = generate_symmetric_key().unwrap();

        let encrypted = encrypt_private_key(&keypair.private_key, &master_key).unwrap();

        // 验证加密结果是有效的Base64
        assert!(base64::engine::general_purpose::STANDARD
            .decode(&encrypted)
            .is_ok());

        // 验证加密结果不为空
        assert!(!encrypted.is_empty());

        // 验证加密结果与原始私钥不同
        assert_ne!(encrypted, keypair.private_key);
    }
}
