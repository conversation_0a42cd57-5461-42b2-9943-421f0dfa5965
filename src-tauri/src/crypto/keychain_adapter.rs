/// 跨平台密钥链适配器
/// 
/// 提供统一的密钥存储接口，在桌面端使用系统密钥链，在移动端使用平台特定的安全存储

use crate::crypto::{VaultResult, VaultError, KEY_SIZE};
use log::{info, warn, error};

/// 跨平台密钥链管理器
pub struct CrossPlatformKeychain {
    service_name: String,
    account_name: String,
}

impl CrossPlatformKeychain {
    /// 创建新的跨平台密钥链管理器
    pub fn new(service_name: &str, account_name: &str) -> Self {
        Self {
            service_name: service_name.to_string(),
            account_name: account_name.to_string(),
        }
    }
    
    /// 存储密钥
    pub async fn store_key(&self, key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let key_base64 = base64::encode(key);
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            self.store_key_desktop(&key_base64).await
        }
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            self.store_key_mobile(&key_base64).await
        }
    }
    
    /// 获取密钥
    pub async fn get_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let key_base64 = self.get_key_desktop().await?;
            self.decode_key(&key_base64)
        }
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            let key_base64 = self.get_key_mobile().await?;
            self.decode_key(&key_base64)
        }
    }
    
    /// 删除密钥
    pub async fn delete_key(&self) -> VaultResult<()> {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            self.delete_key_desktop().await
        }
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            self.delete_key_mobile().await
        }
    }
    
    /// 检查密钥是否存在
    pub async fn key_exists(&self) -> bool {
        match self.get_key().await {
            Ok(_) => true,
            Err(_) => false,
        }
    }
    
    /// 桌面端密钥存储实现
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    async fn store_key_desktop(&self, key_base64: &str) -> VaultResult<()> {
        use crate::crypto::keychain::KeychainManager;
        
        info!("在桌面端系统密钥链中存储密钥");
        
        let keychain = KeychainManager::new(&self.service_name, &self.account_name)?;
        
        // 解码 base64 密钥
        let key_bytes = base64::decode(key_base64)
            .map_err(|e| VaultError::Crypto(format!("Failed to decode key: {}", e)))?;
        
        if key_bytes.len() != KEY_SIZE {
            return Err(VaultError::Crypto("Invalid key size".to_string()));
        }
        
        let mut key_array = [0u8; KEY_SIZE];
        key_array.copy_from_slice(&key_bytes);
        
        keychain.store_key(&key_array)?;
        
        Ok(())
    }
    
    /// 桌面端密钥获取实现
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    async fn get_key_desktop(&self) -> VaultResult<String> {
        use crate::crypto::keychain::KeychainManager;
        
        info!("从桌面端系统密钥链获取密钥");
        
        let keychain = KeychainManager::new(&self.service_name, &self.account_name)?;
        let key = keychain.get_key()?;
        
        Ok(base64::encode(&key))
    }
    
    /// 桌面端密钥删除实现
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    async fn delete_key_desktop(&self) -> VaultResult<()> {
        use crate::crypto::keychain::KeychainManager;
        
        info!("从桌面端系统密钥链删除密钥");
        
        let keychain = KeychainManager::new(&self.service_name, &self.account_name)?;
        keychain.delete_key()?;
        
        Ok(())
    }
    
    /// 移动端密钥存储实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn store_key_mobile(&self, key_base64: &str) -> VaultResult<()> {
        use crate::mobile::MobileSecureStorage;
        
        info!("在移动端安全存储中存储密钥");
        
        let storage = MobileSecureStorage::new();
        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        
        storage.store(&storage_key, key_base64).await
            .map_err(|e| VaultError::Crypto(format!("Failed to store key in mobile storage: {}", e)))?;
        
        Ok(())
    }
    
    /// 移动端密钥获取实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn get_key_mobile(&self) -> VaultResult<String> {
        use crate::mobile::MobileSecureStorage;
        
        info!("从移动端安全存储获取密钥");
        
        let storage = MobileSecureStorage::new();
        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        
        let key_base64 = storage.get(&storage_key).await
            .map_err(|e| VaultError::Crypto(format!("Failed to get key from mobile storage: {}", e)))?
            .ok_or_else(|| VaultError::Crypto("Key not found in mobile storage".to_string()))?;
        
        Ok(key_base64)
    }
    
    /// 移动端密钥删除实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn delete_key_mobile(&self) -> VaultResult<()> {
        use crate::mobile::MobileSecureStorage;
        
        info!("从移动端安全存储删除密钥");
        
        let storage = MobileSecureStorage::new();
        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        
        storage.delete(&storage_key).await
            .map_err(|e| VaultError::Crypto(format!("Failed to delete key from mobile storage: {}", e)))?;
        
        Ok(())
    }
    
    /// 解码 base64 密钥
    fn decode_key(&self, key_base64: &str) -> VaultResult<[u8; KEY_SIZE]> {
        let key_bytes = base64::decode(key_base64)
            .map_err(|e| VaultError::Crypto(format!("Failed to decode key: {}", e)))?;
        
        if key_bytes.len() != KEY_SIZE {
            return Err(VaultError::Crypto("Invalid key size".to_string()));
        }
        
        let mut key_array = [0u8; KEY_SIZE];
        key_array.copy_from_slice(&key_bytes);
        
        Ok(key_array)
    }
}

/// 跨平台密钥链工厂
pub struct CrossPlatformKeychainFactory;

impl CrossPlatformKeychainFactory {
    /// 创建用户主密钥的密钥链管理器
    pub fn create_user_master_key_keychain(contact: &str) -> CrossPlatformKeychain {
        let service_name = "secure-vault-master-key";
        CrossPlatformKeychain::new(service_name, contact)
    }
    
    /// 创建对称密钥的密钥链管理器
    pub fn create_symmetric_key_keychain(contact: &str) -> CrossPlatformKeychain {
        let service_name = "secure-vault-symmetric-key";
        CrossPlatformKeychain::new(service_name, contact)
    }
    
    /// 创建私钥的密钥链管理器
    pub fn create_private_key_keychain(contact: &str) -> CrossPlatformKeychain {
        let service_name = "secure-vault-private-key";
        CrossPlatformKeychain::new(service_name, contact)
    }
    
    /// 检查当前平台是否支持密钥链
    pub fn is_keychain_supported() -> bool {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 桌面端检查系统密钥链支持
            true // 假设桌面端都支持
        }
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            // 移动端检查安全存储支持
            true // 移动端都有安全存储
        }
    }
    
    /// 获取平台特定的密钥链信息
    pub fn get_platform_info() -> PlatformKeychainInfo {
        #[cfg(target_os = "windows")]
        {
            PlatformKeychainInfo {
                platform: "Windows".to_string(),
                keychain_type: "Windows Credential Manager".to_string(),
                supports_biometric: false,
                supports_hardware_security: false,
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            PlatformKeychainInfo {
                platform: "macOS".to_string(),
                keychain_type: "macOS Keychain".to_string(),
                supports_biometric: true, // Touch ID / Face ID
                supports_hardware_security: true, // Secure Enclave
            }
        }
        
        #[cfg(target_os = "linux")]
        {
            PlatformKeychainInfo {
                platform: "Linux".to_string(),
                keychain_type: "Secret Service / GNOME Keyring".to_string(),
                supports_biometric: false,
                supports_hardware_security: false,
            }
        }
        
        #[cfg(target_os = "ios")]
        {
            PlatformKeychainInfo {
                platform: "iOS".to_string(),
                keychain_type: "iOS Keychain".to_string(),
                supports_biometric: true, // Touch ID / Face ID
                supports_hardware_security: true, // Secure Enclave
            }
        }
        
        #[cfg(target_os = "android")]
        {
            PlatformKeychainInfo {
                platform: "Android".to_string(),
                keychain_type: "Android Keystore".to_string(),
                supports_biometric: true, // Fingerprint / Face unlock
                supports_hardware_security: true, // Hardware Security Module
            }
        }
        
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux", target_os = "ios", target_os = "android")))]
        {
            PlatformKeychainInfo {
                platform: "Unknown".to_string(),
                keychain_type: "None".to_string(),
                supports_biometric: false,
                supports_hardware_security: false,
            }
        }
    }
}

/// 平台密钥链信息
#[derive(Debug, Clone)]
pub struct PlatformKeychainInfo {
    pub platform: String,
    pub keychain_type: String,
    pub supports_biometric: bool,
    pub supports_hardware_security: bool,
}

/// 批量密钥管理器
pub struct BatchKeychainManager {
    base_service: String,
}

impl BatchKeychainManager {
    /// 创建新的批量密钥管理器
    pub fn new(base_service: &str) -> Self {
        Self {
            base_service: base_service.to_string(),
        }
    }
    
    /// 批量存储密钥
    pub async fn store_keys(&self, keys: &[(&str, &[u8; KEY_SIZE])]) -> VaultResult<()> {
        for (account, key) in keys {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            keychain.store_key(key).await?;
        }
        Ok(())
    }
    
    /// 批量获取密钥
    pub async fn get_keys(&self, accounts: &[&str]) -> VaultResult<Vec<(String, [u8; KEY_SIZE])>> {
        let mut results = Vec::new();
        
        for account in accounts {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            match keychain.get_key().await {
                Ok(key) => results.push((account.to_string(), key)),
                Err(e) => {
                    warn!("Failed to get key for account {}: {}", account, e);
                    // 继续处理其他密钥，不中断整个批量操作
                }
            }
        }
        
        Ok(results)
    }
    
    /// 批量删除密钥
    pub async fn delete_keys(&self, accounts: &[&str]) -> VaultResult<()> {
        for account in accounts {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            if let Err(e) = keychain.delete_key().await {
                warn!("Failed to delete key for account {}: {}", account, e);
                // 继续删除其他密钥，不中断整个批量操作
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_cross_platform_keychain() {
        let keychain = CrossPlatformKeychain::new("test-service", "test-account");
        
        // 测试密钥存储和获取
        let test_key = [42u8; KEY_SIZE];
        
        // 在测试环境中，这些操作的行为取决于平台
        // 在 CI 环境中可能会失败，所以我们只测试接口
        assert!(!keychain.key_exists().await);
    }
    
    #[test]
    fn test_platform_info() {
        let info = CrossPlatformKeychainFactory::get_platform_info();
        assert!(!info.platform.is_empty());
        assert!(!info.keychain_type.is_empty());
    }
    
    #[test]
    fn test_keychain_support() {
        // 应该在所有支持的平台上返回 true
        assert!(CrossPlatformKeychainFactory::is_keychain_supported());
    }
    
    #[tokio::test]
    async fn test_batch_keychain_manager() {
        let batch_manager = BatchKeychainManager::new("test-batch-service");
        
        let test_keys = vec![
            ("account1", &[1u8; KEY_SIZE]),
            ("account2", &[2u8; KEY_SIZE]),
        ];
        
        // 测试批量操作接口
        // 在测试环境中可能会失败，所以我们只测试接口存在
        let accounts = vec!["account1", "account2"];
        let _ = batch_manager.get_keys(&accounts).await;
    }
} 