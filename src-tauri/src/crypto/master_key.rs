// 主密钥和加密上下文模块
// 实现主密钥管理和加密上下文功能

use crate::errors::{VaultError, VaultResult};
use serde::{Deserialize, Serialize};
use zeroize::Zeroize;

use super::{
    key_derivation::{derive_key_from_password, generate_salt},
    keychain::KeychainManager,
    symmetric::{decrypt_data, encrypt_data, EncryptedData},
    KEY_SIZE, MIN_PASSWORD_LENGTH,
};

/// 主密钥结构
#[derive(Clone, Zeroize)]
pub struct MasterKey {
    key: [u8; KEY_SIZE],
}

impl MasterKey {
    /// 从密码派生主密钥
    pub fn from_password(password: &str, salt: &str) -> VaultResult<Self> {
        if password.len() < MIN_PASSWORD_LENGTH {
            return Err(VaultError::InvalidInput(format!(
                "密码长度至少需要 {} 个字符",
                MIN_PASSWORD_LENGTH
            )));
        }

        let key = derive_key_from_password(password, salt)?;
        Ok(Self { key })
    }

    /// 从已有的密钥字节创建
    pub fn from_bytes(key_bytes: [u8; KEY_SIZE]) -> Self {
        Self { key: key_bytes }
    }

    /// 获取密钥字节（不拷贝）
    pub fn as_bytes(&self) -> &[u8; KEY_SIZE] {
        &self.key
    }

    /// 验证密码是否正确
    pub fn verify_password(&self, password: &str, salt: &str) -> VaultResult<bool> {
        let test_key = Self::from_password(password, salt)?;
        Ok(test_key.key == self.key)
    }
}

/// 加密配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptoConfig {
    /// Argon2 迭代次数
    pub argon2_iterations: u32,
    /// Argon2 内存使用量 (KB)
    pub argon2_memory: u32,
    /// Argon2 并行度
    pub argon2_parallelism: u32,
    /// 是否启用系统密钥链
    pub enable_keychain: bool,
    /// 自动锁定超时时间（秒）
    pub auto_lock_timeout: Option<u64>,
}

impl Default for CryptoConfig {
    fn default() -> Self {
        Self {
            // 推荐的 Argon2id 参数
            argon2_iterations: 3,
            argon2_memory: 65536, // 64 MB
            argon2_parallelism: 4,
            enable_keychain: true,
            auto_lock_timeout: Some(300), // 5分钟
        }
    }
}

/// 加密上下文
pub struct CryptoContext {
    master_key: Option<MasterKey>,
    salt: String,
    config: CryptoConfig,
    keychain: Option<KeychainManager>,
}

impl CryptoContext {
    /// 创建新的加密上下文
    pub fn new(salt: String, config: CryptoConfig) -> VaultResult<Self> {
        let keychain = if config.enable_keychain {
            Some(KeychainManager::new("secure-vault", "master-key")?)
        } else {
            None
        };

        Ok(Self {
            master_key: None,
            salt,
            config,
            keychain,
        })
    }

    /// 解锁保险库
    pub fn unlock(&mut self, password: &str) -> VaultResult<()> {
        let master_key = MasterKey::from_password(password, &self.salt)?;

        // 如果启用了密钥链，尝试存储或验证密钥
        if let Some(ref keychain) = self.keychain {
            if let Ok(_stored_key) = keychain.get_key() {
                // 验证密码是否正确
                if !master_key.verify_password(password, &self.salt)? {
                    return Err(VaultError::PasswordVerificationFailed);
                }
            } else {
                // 首次使用，存储密钥到密钥链
                keychain.store_key(master_key.as_bytes())?;
            }
        }

        self.master_key = Some(master_key);
        Ok(())
    }

    /// 锁定保险库
    pub fn lock(&mut self) {
        if let Some(mut key) = self.master_key.take() {
            key.zeroize();
        }
    }

    /// 检查是否已解锁
    pub fn is_unlocked(&self) -> bool {
        self.master_key.is_some()
    }

    /// 加密数据
    pub fn encrypt(&self, data: &str) -> VaultResult<String> {
        let master_key = self.master_key.as_ref().ok_or(VaultError::VaultLocked)?;

        encrypt_data(data.as_bytes(), master_key.as_bytes()).map(|encrypted| encrypted.to_base64())
    }

    /// 解密数据
    pub fn decrypt(&self, encrypted_data: &str) -> VaultResult<String> {
        let master_key = self.master_key.as_ref().ok_or(VaultError::VaultLocked)?;

        let encrypted = EncryptedData::from_base64(encrypted_data)?;
        let decrypted_bytes = decrypt_data(&encrypted, master_key.as_bytes())?;

        String::from_utf8(decrypted_bytes)
            .map_err(|e| VaultError::Decryption(format!("无效的UTF-8: {}", e)))
    }

    /// 更改主密码
    pub fn change_password(
        &mut self,
        old_password: &str,
        new_password: &str,
    ) -> VaultResult<String> {
        // 验证旧密码
        if !self.is_unlocked() {
            return Err(VaultError::VaultLocked);
        }

        let current_key = self.master_key.as_ref().unwrap();
        if !current_key.verify_password(old_password, &self.salt)? {
            return Err(VaultError::PasswordVerificationFailed);
        }

        // 生成新的盐
        let new_salt = generate_salt()?;
        let new_master_key = MasterKey::from_password(new_password, &new_salt)?;

        // 更新密钥链
        if let Some(ref keychain) = self.keychain {
            keychain.store_key(new_master_key.as_bytes())?;
        }

        // 更新内部状态
        self.salt = new_salt.clone();
        self.master_key = Some(new_master_key);

        Ok(new_salt)
    }

    /// 获取当前盐值
    pub fn get_salt(&self) -> &str {
        &self.salt
    }

    /// 获取配置
    pub fn get_config(&self) -> &CryptoConfig {
        &self.config
    }
}

impl Drop for CryptoContext {
    fn drop(&mut self) {
        self.lock();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_master_key_creation() {
        let salt = generate_salt().unwrap();
        let password = "test_password_123";

        let key1 = MasterKey::from_password(password, &salt).unwrap();
        let key2 = MasterKey::from_password(password, &salt).unwrap();

        assert_eq!(key1.as_bytes(), key2.as_bytes());
    }

    #[test]
    fn test_crypto_context() {
        let salt = generate_salt().unwrap();
        let config = CryptoConfig::default();
        let mut context = CryptoContext::new(salt, config).unwrap();

        assert!(!context.is_unlocked());

        context.unlock("test_password_123").unwrap();
        assert!(context.is_unlocked());

        let encrypted = context.encrypt("Hello, World!").unwrap();
        let decrypted = context.decrypt(&encrypted).unwrap();
        assert_eq!(decrypted, "Hello, World!");

        context.lock();
        assert!(!context.is_unlocked());
    }

    #[test]
    fn test_master_key_password_verification() {
        let salt = generate_salt().unwrap();
        let password = "correct_password_123";
        let wrong_password = "wrong_password_123";

        let key = MasterKey::from_password(password, &salt).unwrap();

        assert!(key.verify_password(password, &salt).unwrap());
        assert!(!key.verify_password(wrong_password, &salt).unwrap());
    }

    #[test]
    fn test_crypto_config_default() {
        let config = CryptoConfig::default();

        assert_eq!(config.argon2_iterations, 3);
        assert_eq!(config.argon2_memory, 65536);
        assert_eq!(config.argon2_parallelism, 4);
        assert!(config.enable_keychain);
        assert_eq!(config.auto_lock_timeout, Some(300));
    }
}
