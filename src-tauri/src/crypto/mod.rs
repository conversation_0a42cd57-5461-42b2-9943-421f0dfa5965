// 模块化加密系统
// 根据 Tauri 2.5.0 密码管理应用数据存储方案分析文档的最佳实践实现

// 子模块声明
pub mod compat; // 向后兼容API
pub mod integration_test; // 集成测试
pub mod key_derivation; // 密钥派生
pub mod key_pair; // 密钥对管理
pub mod keychain; // 系统密钥链集成
pub mod master_key; // 主密钥和加密上下文
pub mod secure_memory; // 安全内存管理
pub mod symmetric; // 对称加密/解密
pub mod utils; // 实用工具函数
pub mod vault_crypto; // 高级加密操作

// 常量定义
pub const KEY_SIZE: usize = 32; // 256 bits for AES-256
pub const NONCE_SIZE: usize = 12; // 96 bits for AES-GCM
pub const SALT_SIZE: usize = 32; // 256 bits for salt
pub const MIN_PASSWORD_LENGTH: usize = 8; // 最小密码长度

// 重新导出常用类型和函数
pub use compat::{decrypt, derive_key, encrypt};
pub use key_derivation::{derive_key_from_password, generate_salt, KeyDerivationParams};
pub use key_pair::{decrypt_private_key, encrypt_private_key, generate_keypair, KeyPair};
pub use keychain::{KeychainError, KeychainManager, RegistrationKeychainManager};
pub use master_key::{CryptoConfig, CryptoContext, MasterKey};
pub use secure_memory::SecureBytes;
pub use symmetric::{
    decrypt_data, decrypt_symmetric_key, decrypt_text, encrypt_data, encrypt_symmetric_key,
    encrypt_text, generate_symmetric_key, EncryptedData,
};
pub use utils::{decode_base64, encode_base64, generate_random_password};
pub use vault_crypto::{CryptoState, VaultCrypto};
