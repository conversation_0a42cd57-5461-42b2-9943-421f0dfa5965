// 安全内存管理模块
// 实现内存保护和自动清理功能

use std::fmt;
use std::mem;
use std::ops::{Deref, DerefMut};
use zeroize::{Zeroize, ZeroizeOnDrop};

/// 安全字节容器，自动清理内存
#[derive(ZeroizeOnDrop)]
pub struct SecureBytes {
    data: Vec<u8>,
    #[allow(dead_code)]
    locked: bool, // 预留字段，用于将来的内存锁定功能
}

impl SecureBytes {
    /// 创建新的安全字节容器
    pub fn new(data: Vec<u8>) -> Self {
        Self {
            data,
            locked: false,
        }
    }

    /// 创建指定大小的零初始化安全字节容器
    pub fn zeros(size: usize) -> Self {
        Self::new(vec![0u8; size])
    }

    /// 从字节切片创建
    pub fn from_slice(slice: &[u8]) -> Self {
        Self::new(slice.to_vec())
    }

    /// 获取长度
    pub fn len(&self) -> usize {
        self.data.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// 获取不可变引用
    pub fn as_slice(&self) -> &[u8] {
        &self.data
    }

    /// 获取可变引用
    pub fn as_mut_slice(&mut self) -> &mut [u8] {
        &mut self.data
    }

    /// 安全克隆（创建新的受保护副本）
    pub fn secure_clone(&self) -> Self {
        Self::from_slice(&self.data)
    }

    /// 扩展容器大小
    pub fn extend(&mut self, other: &[u8]) {
        self.data.extend_from_slice(other);
    }

    /// 调整大小
    pub fn resize(&mut self, new_len: usize, value: u8) {
        self.data.resize(new_len, value);
    }

    /// 截断到指定长度
    pub fn truncate(&mut self, len: usize) {
        self.data.truncate(len);
    }

    /// 清空内容
    pub fn clear(&mut self) {
        self.data.zeroize();
        self.data.clear();
    }

    /// 获取内部向量的所有权（不推荐，会失去保护）
    pub fn into_vec(mut self) -> Vec<u8> {
        let data = mem::take(&mut self.data);
        // 显式zeroize以确保安全
        self.data.zeroize();
        data
    }

    /// 常时间比较，防止时序攻击
    pub fn constant_time_eq(&self, other: &[u8]) -> bool {
        use subtle::ConstantTimeEq;
        self.data.ct_eq(other).into()
    }

    /// 将数据与另一个SecureBytes进行常时间比较
    pub fn constant_time_eq_secure(&self, other: &SecureBytes) -> bool {
        self.constant_time_eq(&other.data)
    }

    // 预留的内存锁定功能（在支持的平台上可以实现）
    #[cfg(unix)]
    pub fn lock_memory(&mut self) -> Result<(), std::io::Error> {
        // 在Unix系统上可以使用mlock锁定内存页
        // 这里是一个占位符实现
        log::debug!("内存锁定功能暂未实现");
        self.locked = true;
        Ok(())
    }

    #[cfg(windows)]
    pub fn lock_memory(&mut self) -> Result<(), std::io::Error> {
        // 在Windows系统上可以使用VirtualLock锁定内存
        // 这里是一个占位符实现
        log::debug!("内存锁定功能暂未实现");
        self.locked = true;
        Ok(())
    }

    #[cfg(not(any(unix, windows)))]
    pub fn lock_memory(&mut self) -> Result<(), std::io::Error> {
        log::debug!("当前平台不支持内存锁定");
        Ok(())
    }
}

impl Deref for SecureBytes {
    type Target = [u8];

    fn deref(&self) -> &Self::Target {
        &self.data
    }
}

impl DerefMut for SecureBytes {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.data
    }
}

impl AsRef<[u8]> for SecureBytes {
    fn as_ref(&self) -> &[u8] {
        &self.data
    }
}

impl AsMut<[u8]> for SecureBytes {
    fn as_mut(&mut self) -> &mut [u8] {
        &mut self.data
    }
}

impl From<Vec<u8>> for SecureBytes {
    fn from(data: Vec<u8>) -> Self {
        Self::new(data)
    }
}

impl From<&[u8]> for SecureBytes {
    fn from(slice: &[u8]) -> Self {
        Self::from_slice(slice)
    }
}

impl From<String> for SecureBytes {
    fn from(s: String) -> Self {
        Self::new(s.into_bytes())
    }
}

impl From<&str> for SecureBytes {
    fn from(s: &str) -> Self {
        Self::from_slice(s.as_bytes())
    }
}

// 出于安全考虑，不实现Debug trait的默认行为
impl fmt::Debug for SecureBytes {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "SecureBytes {{ len: {}, locked: {} }}",
            self.data.len(),
            self.locked
        )
    }
}

// 不实现Display trait，防止意外泄露

/// 安全字符串容器，专门用于存储敏感文本数据
#[derive(ZeroizeOnDrop)]
pub struct SecureString {
    inner: SecureBytes,
}

impl SecureString {
    /// 创建新的安全字符串
    pub fn new(s: String) -> Self {
        Self {
            inner: SecureBytes::new(s.into_bytes()),
        }
    }

    /// 从字符串引用创建
    pub fn from_str(s: &str) -> Self {
        Self::new(s.to_string())
    }

    /// 创建空的安全字符串
    pub fn empty() -> Self {
        Self {
            inner: SecureBytes::new(Vec::new()),
        }
    }

    /// 获取长度（字符数，不是字节数）
    pub fn len(&self) -> usize {
        // 这是一个近似值，因为我们不能安全地解析为UTF-8
        // 在实际应用中应该记录字符数
        self.inner.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.inner.is_empty()
    }

    /// 获取字节长度
    pub fn byte_len(&self) -> usize {
        self.inner.len()
    }

    /// 安全地获取字符串引用（临时使用）
    pub fn expose_secret(&self) -> Result<&str, std::str::Utf8Error> {
        std::str::from_utf8(&self.inner.data)
    }

    /// 安全地克隆字符串
    pub fn secure_clone(&self) -> Self {
        Self {
            inner: self.inner.secure_clone(),
        }
    }

    /// 添加字符串到末尾
    pub fn push_str(&mut self, s: &str) {
        self.inner.extend(s.as_bytes());
    }

    /// 添加字符到末尾
    pub fn push(&mut self, ch: char) {
        let mut buf = [0; 4];
        let s = ch.encode_utf8(&mut buf);
        self.push_str(s);
    }

    /// 清空内容
    pub fn clear(&mut self) {
        self.inner.clear();
    }

    /// 常时间字符串比较
    pub fn constant_time_eq(&self, other: &str) -> bool {
        self.inner.constant_time_eq(other.as_bytes())
    }

    /// 与另一个SecureString进行常时间比较
    pub fn constant_time_eq_secure(&self, other: &SecureString) -> bool {
        self.inner.constant_time_eq_secure(&other.inner)
    }

    /// 获取底层的SecureBytes引用
    pub fn as_bytes(&self) -> &SecureBytes {
        &self.inner
    }

    /// 获取底层的SecureBytes可变引用
    pub fn as_bytes_mut(&mut self) -> &mut SecureBytes {
        &mut self.inner
    }

    /// 转换为SecureBytes
    pub fn into_bytes(self) -> SecureBytes {
        // 创建一个内部数据的副本
        let bytes_copy = self.inner.secure_clone();

        // 手动触发当前实例的清理（通过调用 drop）
        drop(self);

        bytes_copy
    }
}

impl From<String> for SecureString {
    fn from(s: String) -> Self {
        Self::new(s)
    }
}

impl From<&str> for SecureString {
    fn from(s: &str) -> Self {
        Self::from_str(s)
    }
}

impl fmt::Debug for SecureString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SecureString {{ byte_len: {} }}", self.byte_len())
    }
}

/// 临时暴露机制，用于安全地访问敏感数据
pub struct ExposeSecret<'a, T> {
    secret: &'a T,
}

impl<'a, T> ExposeSecret<'a, T> {
    /// 创建新的临时暴露
    pub fn new(secret: &'a T) -> Self {
        Self { secret }
    }

    /// 获取秘密数据的引用
    pub fn expose_secret(&self) -> &T {
        self.secret
    }
}

impl<'a> ExposeSecret<'a, SecureString> {
    /// 安全地获取字符串引用
    pub fn as_str(&self) -> Result<&str, std::str::Utf8Error> {
        self.secret.expose_secret()
    }
}

/// 安全的内存分配器特征（未来扩展）
pub trait SecureAllocator {
    /// 分配安全内存
    fn allocate(&self, size: usize) -> Result<SecureBytes, std::io::Error>;

    /// 释放安全内存
    fn deallocate(&self, memory: SecureBytes) -> Result<(), std::io::Error>;

    /// 检查是否支持内存锁定
    fn supports_memory_locking(&self) -> bool;
}

/// 默认安全分配器
pub struct DefaultSecureAllocator;

impl SecureAllocator for DefaultSecureAllocator {
    fn allocate(&self, size: usize) -> Result<SecureBytes, std::io::Error> {
        let mut memory = SecureBytes::zeros(size);
        let _ = memory.lock_memory(); // 尝试锁定内存，失败也继续
        Ok(memory)
    }

    fn deallocate(&self, mut memory: SecureBytes) -> Result<(), std::io::Error> {
        memory.clear(); // 确保清零
        Ok(())
    }

    fn supports_memory_locking(&self) -> bool {
        cfg!(any(unix, windows))
    }
}

/// 内存保护工具函数
pub mod utils {
    use super::*;

    /// 安全地比较两个字节切片
    pub fn secure_compare(a: &[u8], b: &[u8]) -> bool {
        use subtle::ConstantTimeEq;
        a.ct_eq(b).into()
    }

    /// 安全地比较两个字符串
    pub fn secure_str_compare(a: &str, b: &str) -> bool {
        secure_compare(a.as_bytes(), b.as_bytes())
    }

    /// 创建随机填充的安全字节
    pub fn random_secure_bytes(len: usize) -> SecureBytes {
        use rand::{rngs::OsRng, RngCore};

        let mut data = vec![0u8; len];
        OsRng.fill_bytes(&mut data);
        SecureBytes::new(data)
    }

    /// 创建随机密码字符串
    pub fn random_secure_password(len: usize) -> SecureString {
        use rand::{rngs::OsRng, Rng};

        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?";

        let mut rng = OsRng;
        let password: String = (0..len)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect();

        SecureString::new(password)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_secure_bytes_creation() {
        let data = vec![1, 2, 3, 4, 5];
        let secure = SecureBytes::new(data.clone());

        assert_eq!(secure.len(), 5);
        assert_eq!(secure.as_slice(), &data);
        assert!(!secure.is_empty());
    }

    #[test]
    fn test_secure_bytes_zeros() {
        let secure = SecureBytes::zeros(10);
        assert_eq!(secure.len(), 10);
        assert_eq!(secure.as_slice(), &[0u8; 10]);
    }

    #[test]
    fn test_secure_bytes_operations() {
        let mut secure = SecureBytes::from_slice(b"hello");
        secure.extend(b" world");

        assert_eq!(secure.as_slice(), b"hello world");

        secure.truncate(5);
        assert_eq!(secure.as_slice(), b"hello");

        secure.clear();
        assert!(secure.is_empty());
    }

    #[test]
    fn test_secure_string() {
        let secret = SecureString::from_str("my secret password");

        assert!(!secret.is_empty());
        assert_eq!(secret.expose_secret().unwrap(), "my secret password");
    }

    #[test]
    fn test_secure_string_operations() {
        let mut secret = SecureString::from_str("hello");
        secret.push_str(" world");
        secret.push('!');

        assert_eq!(secret.expose_secret().unwrap(), "hello world!");

        secret.clear();
        assert!(secret.is_empty());
    }

    #[test]
    fn test_constant_time_comparison() {
        let data1 = SecureBytes::from_slice(b"test data");
        let data2 = SecureBytes::from_slice(b"test data");
        let data3 = SecureBytes::from_slice(b"different");

        assert!(data1.constant_time_eq_secure(&data2));
        assert!(!data1.constant_time_eq_secure(&data3));

        assert!(data1.constant_time_eq(b"test data"));
        assert!(!data1.constant_time_eq(b"different"));
    }

    #[test]
    fn test_secure_string_comparison() {
        let str1 = SecureString::from_str("password123");
        let str2 = SecureString::from_str("password123");
        let str3 = SecureString::from_str("different");

        assert!(str1.constant_time_eq_secure(&str2));
        assert!(!str1.constant_time_eq_secure(&str3));

        assert!(str1.constant_time_eq("password123"));
        assert!(!str1.constant_time_eq("wrong"));
    }

    #[test]
    fn test_expose_secret() {
        let secret = SecureString::from_str("my secret");
        let exposed = ExposeSecret::new(&secret);

        assert_eq!(exposed.as_str().unwrap(), "my secret");
    }

    #[test]
    fn test_secure_allocator() {
        let allocator = DefaultSecureAllocator;

        let memory = allocator.allocate(64).unwrap();
        assert_eq!(memory.len(), 64);
        assert_eq!(memory.as_slice(), &[0u8; 64]);

        allocator.deallocate(memory).unwrap();
    }

    #[test]
    fn test_utils() {
        assert!(utils::secure_compare(b"test", b"test"));
        assert!(!utils::secure_compare(b"test", b"different"));

        assert!(utils::secure_str_compare("hello", "hello"));
        assert!(!utils::secure_str_compare("hello", "world"));

        let random_bytes = utils::random_secure_bytes(32);
        assert_eq!(random_bytes.len(), 32);

        let random_password = utils::random_secure_password(16);
        assert_eq!(random_password.byte_len(), 16);
    }

    #[test]
    fn test_conversions() {
        let data = vec![1, 2, 3];
        let secure_from_vec = SecureBytes::from(data);
        assert_eq!(secure_from_vec.len(), 3);

        let secure_from_slice = SecureBytes::from(&[4, 5, 6][..]);
        assert_eq!(secure_from_slice.len(), 3);

        let secure_from_string = SecureBytes::from("hello".to_string());
        assert_eq!(secure_from_string.as_slice(), b"hello");

        let secure_from_str = SecureBytes::from("world");
        assert_eq!(secure_from_str.as_slice(), b"world");
    }
}
