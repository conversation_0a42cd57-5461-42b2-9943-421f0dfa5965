// 高级加密操作模块
// 整合所有加密功能并提供高级API

use crate::errors::{VaultError, VaultResult};
use base64::Engine;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use zeroize::Zeroize;

use super::{
    key_derivation::{assess_password_strength, KeyDerivationParams},
    keychain::{KeyType, KeychainManager},
    symmetric::{BatchCipher, EncryptedData, SymmetricCipher},
    CryptoConfig, MasterKey, KEY_SIZE,
};

/// 加密状态
#[derive(Debug, Clone, PartialEq, Serialize)]
pub enum CryptoState {
    /// 未初始化
    Uninitialized,
    /// 已锁定
    Locked,
    /// 已解锁
    Unlocked,
    /// 错误状态
    Error(String),
}

/// 保险库加密操作中心
pub struct VaultCrypto {
    /// 当前状态
    state: Arc<RwLock<CryptoState>>,
    /// 主密钥（只有解锁时才有值）
    master_key: Arc<RwLock<Option<MasterKey>>>,
    /// 加密配置
    config: CryptoConfig,
    /// 密钥链管理器
    keychain: Option<KeychainManager>,
    /// 自动锁定定时器
    auto_lock_timer: Arc<RwLock<Option<Instant>>>,
    /// 密钥派生参数
    kdf_params: KeyDerivationParams,
    /// 会话密钥缓存
    session_keys: Arc<RwLock<HashMap<String, SecureSessionKey>>>,
}

impl VaultCrypto {
    /// 创建新的保险库加密实例
    pub fn new(config: CryptoConfig) -> VaultResult<Self> {
        let keychain = if config.enable_keychain {
            Some(KeychainManager::new("secure-vault", "master-key")?)
        } else {
            None
        };

        Ok(Self {
            state: Arc::new(RwLock::new(CryptoState::Uninitialized)),
            master_key: Arc::new(RwLock::new(None)),
            config,
            keychain,
            auto_lock_timer: Arc::new(RwLock::new(None)),
            kdf_params: KeyDerivationParams::balanced(),
            session_keys: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// 初始化保险库（设置主密码）
    pub async fn initialize(&self, password: &str, salt: &str) -> VaultResult<()> {
        // 验证密码强度
        let strength = assess_password_strength(password);
        if !strength.is_acceptable() {
            return Err(VaultError::InvalidInput(format!(
                "密码强度不足: {}",
                strength.description()
            )));
        }

        let master_key = MasterKey::from_password(password, salt)?;

        // 存储到密钥链（如果启用）
        if let Some(ref keychain) = self.keychain {
            keychain.store_key_with_type(master_key.as_bytes(), KeyType::MasterKey)?;
        }

        *self.master_key.write().await = Some(master_key);
        *self.state.write().await = CryptoState::Unlocked;

        self.reset_auto_lock_timer().await;

        log::info!("保险库初始化完成");
        Ok(())
    }

    /// 解锁保险库
    pub async fn unlock(&self, password: &str, salt: &str) -> VaultResult<()> {
        let current_state = self.state.read().await.clone();

        match current_state {
            CryptoState::Unlocked => {
                log::debug!("保险库已经解锁");
                return Ok(());
            }
            CryptoState::Uninitialized => {
                return Err(VaultError::VaultNotInitialized);
            }
            _ => {}
        }

        let master_key = MasterKey::from_password(password, salt)?;

        // 验证密码（如果有密钥链存储的密钥）
        if let Some(ref keychain) = self.keychain {
            if keychain.key_exists() {
                let stored_key = keychain.get_key()?;
                if stored_key != *master_key.as_bytes() {
                    return Err(VaultError::PasswordVerificationFailed);
                }
            }
        }

        *self.master_key.write().await = Some(master_key);
        *self.state.write().await = CryptoState::Unlocked;

        self.reset_auto_lock_timer().await;

        log::info!("保险库解锁成功");
        Ok(())
    }

    /// 锁定保险库
    pub async fn lock(&self) {
        {
            let mut master_key = self.master_key.write().await;
            if let Some(mut key) = master_key.take() {
                key.zeroize();
            }
        }

        // 清空会话密钥缓存
        self.session_keys.write().await.clear();

        *self.state.write().await = CryptoState::Locked;
        *self.auto_lock_timer.write().await = None;

        log::info!("保险库已锁定");
    }

    /// 获取当前状态
    pub async fn state(&self) -> CryptoState {
        self.state.read().await.clone()
    }

    /// 检查是否已解锁
    pub async fn is_unlocked(&self) -> bool {
        matches!(*self.state.read().await, CryptoState::Unlocked)
    }

    /// 加密数据
    pub async fn encrypt(&self, data: &str) -> VaultResult<EncryptedData> {
        self.ensure_unlocked().await?;
        self.update_auto_lock_timer().await;

        let master_key = self.master_key.read().await;
        let key = master_key.as_ref().unwrap();

        let cipher = SymmetricCipher::new(key.as_bytes());
        cipher.encrypt_text(data)
    }

    /// 解密数据
    pub async fn decrypt(&self, encrypted_data: &EncryptedData) -> VaultResult<String> {
        self.ensure_unlocked().await?;
        self.update_auto_lock_timer().await;

        let master_key = self.master_key.read().await;
        let key = master_key.as_ref().unwrap();

        let cipher = SymmetricCipher::new(key.as_bytes());
        cipher.decrypt_text(encrypted_data)
    }

    /// 批量加密
    pub async fn encrypt_batch(&self, data_items: &[&str]) -> VaultResult<Vec<EncryptedData>> {
        self.ensure_unlocked().await?;
        self.update_auto_lock_timer().await;

        let master_key = self.master_key.read().await;
        let key = master_key.as_ref().unwrap();

        let batch_cipher = BatchCipher::new(key.as_bytes());
        batch_cipher.encrypt_batch(data_items)
    }

    /// 批量解密
    pub async fn decrypt_batch(
        &self,
        encrypted_items: &[&EncryptedData],
    ) -> VaultResult<Vec<String>> {
        self.ensure_unlocked().await?;
        self.update_auto_lock_timer().await;

        let master_key = self.master_key.read().await;
        let key = master_key.as_ref().unwrap();

        let batch_cipher = BatchCipher::new(key.as_bytes());
        batch_cipher.decrypt_batch(encrypted_items)
    }

    /// 更改主密码
    pub async fn change_password(
        &self,
        old_password: &str,
        new_password: &str,
        old_salt: &str,
        new_salt: &str,
    ) -> VaultResult<()> {
        self.ensure_unlocked().await?;

        // 验证新密码强度
        let strength = assess_password_strength(new_password);
        if !strength.is_acceptable() {
            return Err(VaultError::InvalidInput(format!(
                "新密码强度不足: {}",
                strength.description()
            )));
        }

        // 验证旧密码
        let current_key = self.master_key.read().await;
        let current_key = current_key.as_ref().unwrap();

        if !current_key.verify_password(old_password, old_salt)? {
            return Err(VaultError::PasswordVerificationFailed);
        }

        // 创建新密钥
        let new_master_key = MasterKey::from_password(new_password, new_salt)?;

        // 更新密钥链
        if let Some(ref keychain) = self.keychain {
            keychain.store_key_with_type(new_master_key.as_bytes(), KeyType::MasterKey)?;
        }

        // 更新内部密钥
        let _ = current_key;
        *self.master_key.write().await = Some(new_master_key);

        log::info!("主密码更改成功");
        Ok(())
    }

    /// 生成会话密钥
    pub async fn generate_session_key(&self, session_id: &str) -> VaultResult<()> {
        self.ensure_unlocked().await?;

        let master_key = self.master_key.read().await;
        let key = master_key.as_ref().unwrap();

        // 使用主密钥和会话ID派生会话密钥
        let session_data = format!("{}::{}", session_id, chrono::Utc::now().timestamp());
        let derived_key = super::key_derivation::derive_key_from_password(
            &format!("{:?}", key.as_bytes()),
            &base64::engine::general_purpose::STANDARD.encode(session_data.as_bytes()),
        )?;

        let session_key = SecureSessionKey::new(derived_key, Duration::from_secs(3600)); // 1小时有效期

        self.session_keys
            .write()
            .await
            .insert(session_id.to_string(), session_key);

        log::debug!("为会话 {} 生成了新的会话密钥", session_id);
        Ok(())
    }

    /// 使用会话密钥加密
    pub async fn encrypt_with_session(
        &self,
        session_id: &str,
        data: &str,
    ) -> VaultResult<EncryptedData> {
        let session_keys = self.session_keys.read().await;
        let session_key = session_keys
            .get(session_id)
            .ok_or_else(|| VaultError::InvalidInput("会话密钥不存在".to_string()))?;

        if session_key.is_expired() {
            return Err(VaultError::InvalidInput("会话密钥已过期".to_string()));
        }

        let cipher = SymmetricCipher::new(session_key.key());
        cipher.encrypt_text(data)
    }

    /// 使用会话密钥解密
    pub async fn decrypt_with_session(
        &self,
        session_id: &str,
        encrypted_data: &EncryptedData,
    ) -> VaultResult<String> {
        let session_keys = self.session_keys.read().await;
        let session_key = session_keys
            .get(session_id)
            .ok_or_else(|| VaultError::InvalidInput("会话密钥不存在".to_string()))?;

        if session_key.is_expired() {
            return Err(VaultError::InvalidInput("会话密钥已过期".to_string()));
        }

        let cipher = SymmetricCipher::new(session_key.key());
        cipher.decrypt_text(encrypted_data)
    }

    /// 清理过期的会话密钥
    pub async fn cleanup_expired_sessions(&self) {
        let mut session_keys = self.session_keys.write().await;
        session_keys.retain(|_, key| !key.is_expired());
    }

    /// 撤销会话密钥
    pub async fn revoke_session(&self, session_id: &str) -> VaultResult<()> {
        let mut session_keys = self.session_keys.write().await;
        if session_keys.remove(session_id).is_some() {
            log::info!("会话 {} 的密钥已撤销", session_id);
        }
        Ok(())
    }

    /// 导出加密的保险库数据
    pub async fn export_vault(&self, password: &str) -> VaultResult<String> {
        self.ensure_unlocked().await?;

        let export_data = VaultExportData {
            version: "1.0".to_string(),
            config: self.config.clone(),
            kdf_params: self.kdf_params.clone(),
            timestamp: chrono::Utc::now().timestamp(),
        };

        let serialized = serde_json::to_string(&export_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        // 使用提供的密码加密导出数据
        let export_salt = super::key_derivation::generate_salt()?;
        let export_key = super::key_derivation::derive_key_from_password(password, &export_salt)?;

        let cipher = SymmetricCipher::new(&export_key);
        let encrypted = cipher.encrypt_text(&serialized)?;

        let export_container = VaultExportContainer {
            salt: export_salt,
            data: encrypted,
        };

        serde_json::to_string(&export_container)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))
    }

    /// 从加密的数据导入保险库配置
    pub async fn import_vault_config(
        &mut self,
        encrypted_data: &str,
        password: &str,
    ) -> VaultResult<()> {
        let export_container: VaultExportContainer = serde_json::from_str(encrypted_data)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        let import_key =
            super::key_derivation::derive_key_from_password(password, &export_container.salt)?;
        let cipher = SymmetricCipher::new(&import_key);

        let decrypted = cipher.decrypt_text(&export_container.data)?;

        let export_data: VaultExportData = serde_json::from_str(&decrypted)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        // 更新配置
        self.config = export_data.config;
        self.kdf_params = export_data.kdf_params;

        log::info!("保险库配置导入成功");
        Ok(())
    }

    /// 获取保险库统计信息
    pub async fn get_vault_stats(&self) -> VaultStats {
        let state = self.state.read().await.clone();
        let session_count = self.session_keys.read().await.len();
        let auto_lock_enabled = self.config.auto_lock_timeout.is_some();

        VaultStats {
            state,
            session_count,
            auto_lock_enabled,
            keychain_enabled: self.config.enable_keychain,
            kdf_strength: self.kdf_params.memory_cost,
        }
    }

    // 私有辅助方法

    /// 确保保险库已解锁
    async fn ensure_unlocked(&self) -> VaultResult<()> {
        if !self.is_unlocked().await {
            return Err(VaultError::VaultLocked);
        }
        Ok(())
    }

    /// 重置自动锁定定时器
    async fn reset_auto_lock_timer(&self) {
        if self.config.auto_lock_timeout.is_some() {
            *self.auto_lock_timer.write().await = Some(Instant::now());
        }
    }

    /// 更新自动锁定定时器
    async fn update_auto_lock_timer(&self) {
        if let Some(timeout) = self.config.auto_lock_timeout {
            let timer = self.auto_lock_timer.read().await;
            if let Some(last_activity) = *timer {
                if last_activity.elapsed() > Duration::from_secs(timeout) {
                    drop(timer);
                    self.lock().await;
                    return;
                }
            }
            drop(timer);
            *self.auto_lock_timer.write().await = Some(Instant::now());
        }
    }

    /// 启动自动锁定检查任务
    pub fn start_auto_lock_task(self: Arc<Self>) {
        if let Some(timeout) = self.config.auto_lock_timeout {
            let self_clone = Arc::clone(&self);
            tokio::spawn(async move {
                let mut interval = tokio::time::interval(Duration::from_secs(30)); // 每30秒检查一次

                loop {
                    interval.tick().await;

                    if !self_clone.is_unlocked().await {
                        break;
                    }

                    let timer = self_clone.auto_lock_timer.read().await;
                    if let Some(last_activity) = *timer {
                        if last_activity.elapsed() > Duration::from_secs(timeout) {
                            drop(timer);
                            log::info!("自动锁定保险库");
                            self_clone.lock().await;
                            break;
                        }
                    }
                }
            });
        }
    }
}

/// 会话密钥
struct SecureSessionKey {
    key: [u8; KEY_SIZE],
    expires_at: Instant,
}

impl SecureSessionKey {
    fn new(key: [u8; KEY_SIZE], duration: Duration) -> Self {
        Self {
            key,
            expires_at: Instant::now() + duration,
        }
    }

    fn key(&self) -> &[u8; KEY_SIZE] {
        &self.key
    }

    fn is_expired(&self) -> bool {
        Instant::now() > self.expires_at
    }
}

impl Zeroize for SecureSessionKey {
    fn zeroize(&mut self) {
        self.key.zeroize();
        // Instant 不需要清理，因为它不包含敏感信息
    }
}

/// 保险库导出数据
#[derive(Serialize, Deserialize)]
struct VaultExportData {
    version: String,
    config: CryptoConfig,
    kdf_params: KeyDerivationParams,
    timestamp: i64,
}

/// 保险库导出容器
#[derive(Serialize, Deserialize)]
struct VaultExportContainer {
    salt: String,
    data: EncryptedData,
}

/// 保险库统计信息
#[derive(Debug, Serialize)]
pub struct VaultStats {
    pub state: CryptoState,
    pub session_count: usize,
    pub auto_lock_enabled: bool,
    pub keychain_enabled: bool,
    pub kdf_strength: u32,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_vault_crypto_initialization() {
        let config = CryptoConfig::default();
        let vault = VaultCrypto::new(config).unwrap();

        assert_eq!(vault.state().await, CryptoState::Uninitialized);
        assert!(!vault.is_unlocked().await);
    }

    #[test]
    async fn test_vault_lifecycle() {
        let mut config = CryptoConfig::default();
        config.enable_keychain = false; // 禁用密钥链以便测试

        let vault = VaultCrypto::new(config).unwrap();
        let salt = super::super::key_derivation::generate_salt().unwrap();

        // 初始化
        vault.initialize("StrongPassword123!", &salt).await.unwrap();
        assert!(vault.is_unlocked().await);

        // 锁定
        vault.lock().await;
        assert!(!vault.is_unlocked().await);

        // 解锁
        vault.unlock("StrongPassword123!", &salt).await.unwrap();
        assert!(vault.is_unlocked().await);
    }

    #[test]
    async fn test_encryption_decryption() {
        let mut config = CryptoConfig::default();
        config.enable_keychain = false;

        let vault = VaultCrypto::new(config).unwrap();
        let salt = super::super::key_derivation::generate_salt().unwrap();

        vault.initialize("StrongPassword123!", &salt).await.unwrap();

        let plaintext = "Hello, secure world!";
        let encrypted = vault.encrypt(plaintext).await.unwrap();
        let decrypted = vault.decrypt(&encrypted).await.unwrap();

        assert_eq!(plaintext, decrypted);
    }

    #[test]
    async fn test_batch_operations() {
        let mut config = CryptoConfig::default();
        config.enable_keychain = false;

        let vault = VaultCrypto::new(config).unwrap();
        let salt = super::super::key_derivation::generate_salt().unwrap();

        vault.initialize("StrongPassword123!", &salt).await.unwrap();

        let plaintexts = vec!["Text 1", "Text 2", "Text 3"];
        let encrypted_batch = vault.encrypt_batch(&plaintexts).await.unwrap();
        let decrypted_batch = vault
            .decrypt_batch(&encrypted_batch.iter().collect::<Vec<_>>())
            .await
            .unwrap();

        assert_eq!(plaintexts, decrypted_batch);
    }

    #[test]
    async fn test_session_keys() {
        let mut config = CryptoConfig::default();
        config.enable_keychain = false;

        let vault = VaultCrypto::new(config).unwrap();
        let salt = super::super::key_derivation::generate_salt().unwrap();

        vault.initialize("StrongPassword123!", &salt).await.unwrap();

        let session_id = "test_session";
        vault.generate_session_key(session_id).await.unwrap();

        let plaintext = "Session encrypted data";
        let encrypted = vault
            .encrypt_with_session(session_id, plaintext)
            .await
            .unwrap();
        let decrypted = vault
            .decrypt_with_session(session_id, &encrypted)
            .await
            .unwrap();

        assert_eq!(plaintext, decrypted);

        vault.revoke_session(session_id).await.unwrap();
        assert!(vault
            .encrypt_with_session(session_id, plaintext)
            .await
            .is_err());
    }

    #[test]
    async fn test_export_import() {
        let mut config = CryptoConfig::default();
        config.enable_keychain = false;

        let vault = VaultCrypto::new(config).unwrap();
        let salt = super::super::key_derivation::generate_salt().unwrap();

        vault.initialize("StrongPassword123!", &salt).await.unwrap();

        let export_password = "ExportPassword123!";
        let exported = vault.export_vault(export_password).await.unwrap();

        let mut new_vault = VaultCrypto::new(CryptoConfig::default()).unwrap();
        new_vault
            .import_vault_config(&exported, export_password)
            .await
            .unwrap();

        // 验证配置是否正确导入
        let stats1 = vault.get_vault_stats().await;
        let stats2 = new_vault.get_vault_stats().await;

        assert_eq!(stats1.keychain_enabled, stats2.keychain_enabled);
        assert_eq!(stats1.kdf_strength, stats2.kdf_strength);
    }
}
