/// Tauri 托盘命令
/// 
/// 提供前端可以调用的托盘相关功能

use crate::tray::{Tray<PERSON>anager, TrayError};
use std::sync::Arc;
use tokio::sync::Mutex;
use tauri::State;
use serde::{Deserialize, Serialize};

/// 托盘状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayStatusInfo {
    /// 是否可见
    pub visible: bool,
    /// 当前图标路径
    pub icon_path: Option<String>,
    /// 当前工具提示
    pub tooltip: Option<String>,
    /// 菜单项数量
    pub menu_items_count: usize,
    /// 状态描述
    pub status: String,
}

/// 托盘通知参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayNotificationParams {
    /// 通知标题
    pub title: String,
    /// 通知消息
    pub message: String,
    /// 通知持续时间（毫秒）
    pub duration: Option<u64>,
}

/// 托盘图标更新参数
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TrayIconUpdateParams {
    /// 图标路径
    pub icon_path: String,
    /// 工具提示（可选）
    pub tooltip: Option<String>,
}

/// 获取托盘状态信息
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// 
/// # 返回值
/// 
/// 返回托盘状态信息
#[tauri::command]
pub async fn get_tray_status(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
) -> Result<TrayStatusInfo, String> {
    let manager = tray_manager.lock().await;
    
    let status_info = TrayStatusInfo {
        visible: manager.is_running(),
        icon_path: manager.get_current_icon_id(),
        tooltip: None, // TODO: 需要在 TrayManager 中添加获取当前 tooltip 的方法
        menu_items_count: manager.get_current_menu().map(|m| m.items.len()).unwrap_or(0),
        status: format!("{:?}", manager.get_current_status()),
    };
    
    Ok(status_info)
}

/// 显示托盘
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
#[tauri::command]
pub async fn show_tray(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    manager.show().await.map_err(|e| e.to_string())?;
    Ok(())
}

/// 隐藏托盘
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
#[tauri::command]
pub async fn hide_tray(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    manager.hide().await.map_err(|e| e.to_string())?;
    Ok(())
}

/// 更新托盘图标
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// * `params` - 图标更新参数
#[tauri::command]
pub async fn update_tray_icon(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
    params: TrayIconUpdateParams,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    
    // 更新图标（通过路径）
    manager.set_icon_by_id(&params.icon_path).await.map_err(|e| e.to_string())?;
    
    // 如果提供了工具提示，也更新工具提示
    if let Some(tooltip) = params.tooltip {
        manager.set_tooltip(&tooltip).await.map_err(|e| e.to_string())?;
    }
    
    Ok(())
}

/// 更新托盘工具提示
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// * `tooltip` - 新的工具提示文本
#[tauri::command]
pub async fn update_tray_tooltip(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
    tooltip: String,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    manager.set_tooltip(&tooltip).await.map_err(|e| e.to_string())?;
    Ok(())
}

/// 显示托盘通知
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// * `params` - 通知参数
#[tauri::command]
pub async fn show_tray_notification(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
    params: TrayNotificationParams,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    // TODO: 需要在 TrayManager 中实现通知功能
    // manager.show_notification(&params.title, &params.message).await.map_err(|e| e.to_string())?;
    Ok(())
}

/// 设置托盘闪烁状态
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// * `enable` - 是否启用闪烁
#[tauri::command]
pub async fn set_tray_blinking(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
    enable: bool,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    
    // TODO: 需要在 TrayManager 中实现闪烁功能
    // if enable {
    //     manager.start_blinking().await.map_err(|e| e.to_string())?;
    // } else {
    //     manager.stop_blinking().await.map_err(|e| e.to_string())?;
    // }
    
    Ok(())
}

/// 获取托盘统计信息
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
/// 
/// # 返回值
/// 
/// 返回托盘统计信息的 JSON 字符串
#[tauri::command]
pub async fn get_tray_statistics(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
) -> Result<String, String> {
    let manager = tray_manager.lock().await;
    let stats = manager.get_stats();
    serde_json::to_string(&stats).map_err(|e| e.to_string())
}

/// 重置托盘统计信息
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器状态
#[tauri::command]
pub async fn reset_tray_statistics(
    tray_manager: State<'_, Arc<Mutex<TrayManager>>>,
) -> Result<(), String> {
    let manager = tray_manager.lock().await;
    // TODO: 需要在 TrayManager 中实现重置统计功能
    // manager.reset_statistics().await;
    Ok(())
}

/// 检查托盘是否支持
/// 
/// # 返回值
/// 
/// 返回当前平台是否支持系统托盘
#[tauri::command]
pub async fn is_tray_supported() -> Result<bool, String> {
    Ok(crate::tray::platform::is_tray_supported())
}

/// 获取平台特定的托盘信息
/// 
/// # 返回值
/// 
/// 返回平台特定信息的 JSON 字符串
#[tauri::command]
pub async fn get_platform_tray_info() -> Result<String, String> {
    let platform_info = crate::tray::platform::get_platform_info();
    serde_json::to_string(&platform_info).map_err(|e| e.to_string())
}

/// 托盘命令错误处理
/// 
/// 将 TrayError 转换为前端可以理解的错误消息
pub fn handle_tray_error(error: TrayError) -> String {
    match error {
        TrayError::InitializationFailed { reason } => format!("托盘初始化失败: {}", reason),
        TrayError::IconLoadFailed { path, reason } => format!("图标加载失败 {}: {}", path, reason),
        TrayError::MenuCreationFailed { reason } => format!("菜单创建失败: {}", reason),
        TrayError::EventHandlingFailed { event_type, reason } => format!("事件处理失败 {}: {}", event_type, reason),
        TrayError::ConfigurationError { field, reason } => format!("配置错误 {}: {}", field, reason),
        TrayError::InvalidState { expected, actual } => format!("无效状态: 期望 {}, 实际 {}", expected, actual),
        TrayError::MenuItemNotFound { id } => format!("菜单项未找到: {}", id),
        TrayError::UnsupportedPlatform { platform, operation } => format!("平台 {} 不支持操作: {}", platform, operation),
        TrayError::MenuItemLimitExceeded { count, max_allowed } => format!("菜单项数量超出限制: {} > {}", count, max_allowed),
        TrayError::DuplicateMenuItemId { id } => format!("菜单项ID重复: {}", id),
        TrayError::UnsupportedIconFormat { format, supported_formats } => format!("图标格式不支持: {}，支持的格式: {:?}", format, supported_formats),
        TrayError::InvalidIconSize { width, height, expected_width, expected_height } => format!("图标尺寸无效: {}x{}，期望: {}x{}", width, height, expected_width, expected_height),
        TrayError::SystemTrayUnavailable { reason } => format!("系统托盘不可用: {}", reason),
        TrayError::InsufficientPermissions { operation } => format!("权限不足，无法执行操作: {}", operation),
        TrayError::ResourceBusy { resource } => format!("资源已被占用: {}", resource),
        TrayError::OperationTimeout { operation, timeout_ms } => format!("操作超时: {}，超时时间: {}ms", operation, timeout_ms),
        TrayError::Io(msg) => format!("IO错误: {}", msg),
        TrayError::Serialization(msg) => format!("序列化错误: {}", msg),
        TrayError::Internal { message } => format!("内部错误: {}", message),
    }
}

/// 生成所有托盘相关的 Tauri 命令
/// 
/// 这个宏用于在 Tauri 应用中注册所有托盘命令
/// 
/// # 使用方法
/// 
/// ```rust
/// use crate::tray::commands::*;
/// 
/// tauri::Builder::default()
///     .invoke_handler(tauri::generate_handler![
///         get_tray_status,
///         show_tray,
///         hide_tray,
///         update_tray_icon,
///         update_tray_tooltip,
///         show_tray_notification,
///         set_tray_blinking,
///         get_tray_statistics,
///         reset_tray_statistics,
///         is_tray_supported,
///         get_platform_tray_info
///     ])
/// ```
pub fn get_tray_command_list() -> &'static [&'static str] {
    &[
        "get_tray_status",
        "show_tray",
        "hide_tray",
        "update_tray_icon",
        "update_tray_tooltip",
        "show_tray_notification",
        "set_tray_blinking",
        "get_tray_statistics",
        "reset_tray_statistics",
        "is_tray_supported",
        "get_platform_tray_info",
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_tray_notification_params_serialization() {
        let params = TrayNotificationParams {
            title: "测试标题".to_string(),
            message: "测试消息".to_string(),
            duration: Some(5000),
        };

        let json = serde_json::to_string(&params).unwrap();
        let deserialized: TrayNotificationParams = serde_json::from_str(&json).unwrap();

        assert_eq!(params.title, deserialized.title);
        assert_eq!(params.message, deserialized.message);
        assert_eq!(params.duration, deserialized.duration);
    }

    #[tokio::test]
    async fn test_tray_icon_update_params_serialization() {
        let params = TrayIconUpdateParams {
            icon_path: "icons/test.png".to_string(),
            tooltip: Some("测试工具提示".to_string()),
        };

        let json = serde_json::to_string(&params).unwrap();
        let deserialized: TrayIconUpdateParams = serde_json::from_str(&json).unwrap();

        assert_eq!(params.icon_path, deserialized.icon_path);
        assert_eq!(params.tooltip, deserialized.tooltip);
    }

    #[test]
    fn test_handle_tray_error() {
        let error = TrayError::initialization_failed("测试错误");
        let error_msg = handle_tray_error(error);
        assert!(error_msg.contains("托盘初始化失败"));
        assert!(error_msg.contains("测试错误"));
    }

    #[test]
    fn test_tray_status_info_creation() {
        let status_info = TrayStatusInfo {
            visible: true,
            icon_path: Some("icons/test.png".to_string()),
            tooltip: Some("测试工具提示".to_string()),
            menu_items_count: 5,
            status: "Active".to_string(),
        };

        assert!(status_info.visible);
        assert_eq!(status_info.menu_items_count, 5);
        assert!(status_info.icon_path.is_some());
    }
} 