# 跨平台可插拔托盘模块

这是一个为 Tauri 应用设计的跨平台系统托盘模块，提供了完整的托盘功能实现，包括图标管理、菜单系统、事件处理和状态管理。

## 特性

### 🚀 核心特性
- **跨平台支持**: Windows、macOS、Linux
- **可插拔架构**: 模块化设计，易于扩展和定制
- **异步支持**: 完全基于 async/await 的异步实现
- **类型安全**: 使用 Rust 的类型系统确保安全性
- **测试驱动**: 完整的单元测试和集成测试覆盖

### 🎯 功能模块
- **配置管理**: 灵活的配置系统，支持平台特定配置
- **图标管理**: 多格式图标支持，智能缓存机制
- **菜单系统**: 丰富的菜单功能，支持子菜单和各种菜单项类型
- **事件处理**: 可插拔的事件处理器系统
- **状态管理**: 完整的状态跟踪和持久化
- **错误处理**: 使用 thiserror 的精确错误类型定义

## 快速开始

### 基本使用

```rust
use crate::tray::{
    TrayManager, TrayConfigBuilder, TrayIcon, TrayIconType, TrayIconFormat,
    TrayMenu, TrayMenuItem, TrayMenuItemType, TrayMenuBuilder
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建配置
    let config = TrayConfigBuilder::new()
        .title("我的应用")
        .tooltip("这是一个示例托盘应用")
        .visible_on_startup(true)
        .build();

    // 创建托盘管理器
    let manager = TrayManager::new(config).await?;

    // 创建图标
    let icon_data = std::fs::read("assets/icon.png")?;
    let icon = TrayIcon::from_bytes(
        "main_icon",
        TrayIconType::Default,
        TrayIconFormat::Png,
        icon_data,
    )?;

    // 创建菜单
    let menu = TrayMenuBuilder::new()
        .add_item(TrayMenuItem::new(
            "show",
            "显示窗口",
            TrayMenuItemType::Normal,
        ))
        .add_separator()
        .add_item(TrayMenuItem::new(
            "quit",
            "退出",
            TrayMenuItemType::Normal,
        ))
        .build();

    // 设置托盘
    manager.set_icon(icon).await?;
    manager.set_menu(menu).await?;
    manager.set_tooltip("我的应用正在运行").await?;

    // 初始化并显示托盘
    manager.initialize().await?;
    manager.show().await?;

    // 保持运行
    tokio::signal::ctrl_c().await?;
    
    // 清理
    manager.destroy().await?;
    Ok(())
}
```

### 使用构建器模式

```rust
use crate::tray::{TrayManagerBuilder, SimpleEventHandler};

// 创建事件处理器
let handler = Box::new(SimpleEventHandler::new("my_handler"));

// 使用构建器创建完整的托盘系统
let manager = TrayManagerBuilder::new()
    .with_config(config)
    .with_icon(icon)
    .with_menu(menu)
    .add_event_handler(handler)
    .build()
    .await?;

// 一键启动
manager.initialize().await?;
manager.show().await?;
```

## 架构设计

### 模块结构

```
tray/
├── mod.rs              # 模块入口和重新导出
├── config.rs           # 配置管理
├── errors.rs           # 错误类型定义
├── events.rs           # 事件处理系统
├── icons.rs            # 图标管理
├── manager.rs          # 托盘管理器（核心协调器）
├── menu.rs             # 菜单系统
├── platform.rs         # 平台特定实现
├── state.rs            # 状态管理
├── tests.rs            # 集成测试
└── docs/               # 文档目录
    ├── README.md       # 主文档
    ├── api.md          # API 文档
    ├── examples.md     # 示例代码
    └── architecture.md # 架构说明
```

### 核心组件

#### 1. TrayManager (托盘管理器)
- 作为整个系统的核心协调器
- 管理所有子组件的生命周期
- 提供统一的 API 接口
- 处理组件间的通信和同步

#### 2. 配置系统
- 支持平台特定配置
- 运行时配置验证
- 配置热重载支持

#### 3. 事件系统
- 可插拔的事件处理器
- 事件优先级和过滤
- 异步事件处理

#### 4. 图标管理
- 多格式支持 (PNG, ICO, SVG, ICNS)
- 智能缓存和内存管理
- 动态图标切换

#### 5. 状态管理
- 完整的状态跟踪
- 状态持久化
- 状态变更通知

## 平台支持

### Windows
- 使用 Windows API 实现系统托盘
- 支持 ICO 和 PNG 格式图标
- 支持气球提示和系统主题

### macOS
- 使用 NSStatusBar 实现菜单栏
- 支持模板图标和深色模式
- 支持 ICNS 格式图标

### Linux
- 支持多种桌面环境 (KDE, XFCE, Unity)
- 使用 AppIndicator 或 StatusNotifierItem
- 支持 PNG 和 SVG 格式图标

## 配置选项

### 基本配置

```rust
let config = TrayConfigBuilder::new()
    .title("应用标题")
    .tooltip("工具提示文本")
    .visible_on_startup(true)
    .icon_directory("assets/icons")
    .build();
```

### 平台特定配置

```rust
use crate::tray::config::{PlatformConfig, WindowsConfig, MacOSConfig, LinuxConfig};

let platform_config = PlatformConfig {
    windows: Some(WindowsConfig {
        use_system_theme_icon: true,
        enable_balloon_tips: true,
        ..Default::default()
    }),
    macos: Some(MacOSConfig {
        use_template_icon: true,
        hide_dock_icon: false,
        ..Default::default()
    }),
    linux: Some(LinuxConfig {
        desktop_environment: None, // 自动检测
        use_app_indicator: true,
        ..Default::default()
    }),
};

let config = TrayConfigBuilder::new()
    .platform_config(platform_config)
    .build();
```

## 事件处理

### 自定义事件处理器

```rust
use crate::tray::events::{TrayEventHandler, TrayEvent, TrayEventType};
use async_trait::async_trait;

struct MyEventHandler {
    id: String,
}

#[async_trait]
impl TrayEventHandler for MyEventHandler {
    fn id(&self) -> &str {
        &self.id
    }

    async fn handle_event(&mut self, event: TrayEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match event.event_type {
            TrayEventType::LeftClick => {
                println!("托盘被左键点击");
                // 处理左键点击事件
            }
            TrayEventType::RightClick => {
                println!("托盘被右键点击");
                // 处理右键点击事件
            }
            TrayEventType::MenuItemSelected { item_id } => {
                println!("菜单项被选择: {}", item_id);
                // 处理菜单项选择事件
            }
            _ => {}
        }
        Ok(())
    }

    fn priority(&self) -> u8 {
        100 // 默认优先级
    }
}

// 使用自定义处理器
let handler = Box::new(MyEventHandler {
    id: "my_custom_handler".to_string(),
});
manager.add_event_handler(handler).await?;
```

### 状态变更监听

```rust
// 订阅状态变更事件
let mut state_receiver = manager.subscribe_state_changes();

tokio::spawn(async move {
    while let Ok(event) = state_receiver.recv().await {
        println!("状态变更: {:?} -> {:?}", 
                event.previous_state, 
                event.current_state);
    }
});
```

## 菜单系统

### 创建复杂菜单

```rust
use crate::tray::menu::{TrayMenuBuilder, TrayMenuItem, TrayMenuItemType};

// 创建子菜单
let settings_submenu = TrayMenuBuilder::new()
    .add_item(TrayMenuItem::new(
        "general",
        "常规设置",
        TrayMenuItemType::Normal,
    ))
    .add_item(TrayMenuItem::new(
        "advanced",
        "高级设置",
        TrayMenuItemType::Normal,
    ))
    .build();

// 创建主菜单
let main_menu = TrayMenuBuilder::new()
    .add_item(TrayMenuItem::new(
        "show",
        "显示窗口",
        TrayMenuItemType::Normal,
    ))
    .add_item(TrayMenuItem::new(
        "hide",
        "隐藏窗口",
        TrayMenuItemType::Normal,
    ))
    .add_separator()
    .add_item(TrayMenuItem::new(
        "auto_start",
        "开机自启",
        TrayMenuItemType::Checkbox,
    ).checked(true))
    .add_submenu("settings", "设置", settings_submenu)
    .add_separator()
    .add_item(TrayMenuItem::new(
        "about",
        "关于",
        TrayMenuItemType::Normal,
    ))
    .add_item(TrayMenuItem::new(
        "quit",
        "退出",
        TrayMenuItemType::Normal,
    ))
    .build();

manager.set_menu(main_menu).await?;
```

### 动态菜单更新

```rust
// 获取当前菜单
let mut menu = manager.get_current_menu().unwrap();

// 更新菜单项状态
if let Some(item) = menu.find_item_mut("auto_start") {
    item.set_checked(!item.checked);
}

// 添加新菜单项
menu.add_item(TrayMenuItem::new(
    "new_feature",
    "新功能",
    TrayMenuItemType::Normal,
));

// 重新设置菜单
manager.set_menu(menu).await?;
```

## 图标管理

### 多图标支持

```rust
use crate::tray::icons::{TrayIcon, TrayIconType, TrayIconFormat};

// 加载不同状态的图标
let default_icon = TrayIcon::from_file(
    "default",
    TrayIconType::Default,
    "assets/icon-default.png",
).await?;

let active_icon = TrayIcon::from_file(
    "active",
    TrayIconType::Active,
    "assets/icon-active.png",
).await?;

let error_icon = TrayIcon::from_file(
    "error",
    TrayIconType::Error,
    "assets/icon-error.png",
).await?;

// 添加到管理器
manager.set_icon(default_icon).await?;
manager.set_icon(active_icon).await?;
manager.set_icon(error_icon).await?;

// 根据应用状态切换图标
match app_status {
    AppStatus::Idle => manager.set_icon_by_id("default").await?,
    AppStatus::Working => manager.set_icon_by_id("active").await?,
    AppStatus::Error => manager.set_icon_by_id("error").await?,
}
```

### 图标缓存配置

```rust
use crate::tray::icons::IconCacheConfig;

let cache_config = IconCacheConfig {
    max_cache_size: 50 * 1024 * 1024, // 50MB
    max_cache_items: 200,
    cache_ttl: 7200, // 2小时
    enable_preload: true,
};

let icon_manager = TrayIconManager::new(cache_config);
```

## 错误处理

模块使用 `thiserror` 提供精确的错误类型：

```rust
use crate::tray::errors::{TrayError, TrayResult};

match manager.set_icon(invalid_icon).await {
    Ok(_) => println!("图标设置成功"),
    Err(TrayError::IconLoadFailed { path, reason }) => {
        eprintln!("图标加载失败: {} - {}", path, reason);
    }
    Err(TrayError::UnsupportedPlatform { platform, operation }) => {
        eprintln!("平台 {} 不支持操作: {}", platform, operation);
    }
    Err(e) => {
        eprintln!("其他错误: {}", e);
    }
}
```

## 测试

### 运行测试

```bash
# 运行所有测试
cargo test tray

# 运行特定模块测试
cargo test tray::config
cargo test tray::events
cargo test tray::icons

# 运行集成测试
cargo test tray::tests::integration_tests
```

### 测试覆盖

- 单元测试覆盖所有公共 API
- 集成测试覆盖完整的使用场景
- 错误处理测试确保健壮性
- 并发测试验证线程安全性

## 性能优化

### 内存管理
- 智能图标缓存，避免重复加载
- 及时清理不再使用的资源
- 使用 Arc 和 RwLock 优化并发访问

### 异步优化
- 所有 I/O 操作都是异步的
- 事件处理不阻塞主线程
- 批量操作减少系统调用

### 平台优化
- 针对不同平台使用最优的实现
- 减少跨平台抽象的性能开销
- 充分利用平台特定功能

## 故障排除

### 常见问题

1. **托盘不显示**
   - 检查平台是否支持系统托盘
   - 确认桌面环境配置正确
   - 验证图标文件格式和路径

2. **菜单不响应**
   - 检查事件处理器是否正确注册
   - 验证菜单项 ID 是否唯一
   - 确认事件循环正在运行

3. **图标显示异常**
   - 验证图标格式是否受支持
   - 检查图标尺寸是否符合平台要求
   - 确认图标文件没有损坏

### 调试技巧

```rust
// 启用详细日志
env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();

// 检查托盘状态
let status = manager.get_current_status();
println!("当前托盘状态: {:?}", status);

// 查看统计信息
let stats = manager.get_stats();
println!("托盘统计: {:#?}", stats);

// 监控状态变更
let mut receiver = manager.subscribe_state_changes();
tokio::spawn(async move {
    while let Ok(event) = receiver.recv().await {
        println!("状态变更: {:?}", event);
    }
});
```

## 贡献指南

### 开发环境设置

1. 克隆仓库
2. 安装 Rust 工具链
3. 运行测试确保环境正常

### 代码规范

- 遵循 Rust 官方代码风格
- 所有公共 API 必须有文档注释
- 新功能必须包含测试
- 使用 `cargo fmt` 格式化代码
- 使用 `cargo clippy` 检查代码质量

### 提交规范

- 使用清晰的提交信息
- 每个提交只包含一个逻辑变更
- 包含必要的测试和文档更新

## 许可证

本模块遵循项目的整体许可证。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 Windows、macOS、Linux
- 完整的托盘功能实现
- 可插拔架构设计
- 完整的测试覆盖

---

更多详细信息请参考：
- [API 文档](api.md)
- [示例代码](examples.md)
- [架构说明](architecture.md) 