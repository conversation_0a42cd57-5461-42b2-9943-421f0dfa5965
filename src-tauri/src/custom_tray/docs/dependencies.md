# Tray 模块依赖项

本文档列出了 tray 模块正常工作所需的 Rust 依赖项。请将这些依赖项添加到 `src-tauri/Cargo.toml` 文件中。

## 必需依赖项

```toml
[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步 trait 支持
async-trait = "0.1"

# 日志记录
log = "0.4"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 集合类型
indexmap = "2.0"

# UUID 生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 文件系统操作
dirs = "5.0"

# 图像处理（用于图标）
image = { version = "0.24", features = ["png", "ico"] }

# 平台特定依赖
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = [
    "winuser",
    "shellapi",
    "commctrl",
    "wingdi",
    "winbase",
    "processthreadsapi",
    "handleapi",
    "synchapi",
    "errhandlingapi"
] }

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.24"
objc = "0.2"
core-foundation = "0.9"
core-graphics = "0.22"

[target.'cfg(target_os = "linux")'.dependencies]
gtk = "0.18"
gdk = "0.18"
glib = "0.18"
cairo-rs = "0.18"
pango = "0.18"
gdk-pixbuf = "0.18"

# 开发依赖项（用于测试）
[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.0"
mockall = "0.11"
```

## 可选依赖项

以下依赖项可以根据需要添加：

```toml
# 配置文件处理
toml = "0.8"
config = "0.13"

# 更高级的图像处理
imageproc = "0.23"

# 系统通知
notify-rust = "4.0"

# 热键支持
global-hotkey = "0.4"

# 剪贴板操作
clipboard = "0.5"

# 网络请求（如果需要远程图标）
reqwest = { version = "0.11", features = ["json"] }

# 数据库支持（如果需要持久化）
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }
```

## Tauri 特定依赖项

确保在 `src-tauri/Cargo.toml` 中包含以下 Tauri 相关依赖：

```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
tauri-build = { version = "1.0", features = [] }

# 如果使用 Tauri 的系统托盘功能
tauri = { version = "1.0", features = ["system-tray"] }
```

## 功能特性

可以通过 Cargo 特性来控制模块的功能：

```toml
[features]
default = ["notifications", "blinking", "hotkeys"]

# 通知支持
notifications = ["notify-rust"]

# 图标闪烁支持
blinking = []

# 热键支持
hotkeys = ["global-hotkey"]

# 高级图标处理
advanced-icons = ["imageproc"]

# 网络图标支持
network-icons = ["reqwest"]

# 数据库持久化
persistence = ["sqlx"]

# 调试功能
debug = ["log/max_level_debug"]
```

## 编译配置

在 `src-tauri/Cargo.toml` 中添加以下编译配置：

```toml
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true

[profile.dev]
debug = true
opt-level = 0
```

## 平台特定说明

### Windows
- 需要 Windows 10 或更高版本
- 需要 Visual Studio Build Tools 或 Visual Studio

### macOS
- 需要 macOS 10.12 或更高版本
- 需要 Xcode Command Line Tools

### Linux
- 需要 GTK 3.0 或更高版本
- 在 Ubuntu/Debian 上：`sudo apt-get install libgtk-3-dev`
- 在 Fedora 上：`sudo dnf install gtk3-devel`
- 在 Arch Linux 上：`sudo pacman -S gtk3`

## 环境变量

可以设置以下环境变量来控制模块行为：

```bash
# 启用调试日志
export RUST_LOG=debug

# 设置图标缓存目录
export TRAY_ICON_CACHE_DIR=/path/to/cache

# 禁用某些功能
export TRAY_DISABLE_NOTIFICATIONS=1
export TRAY_DISABLE_BLINKING=1
```

## 构建脚本

如果需要，可以在 `src-tauri/build.rs` 中添加构建脚本：

```rust
fn main() {
    // 平台特定的构建配置
    #[cfg(target_os = "windows")]
    {
        println!("cargo:rustc-link-lib=user32");
        println!("cargo:rustc-link-lib=shell32");
    }
    
    #[cfg(target_os = "macos")]
    {
        println!("cargo:rustc-link-lib=framework=Cocoa");
        println!("cargo:rustc-link-lib=framework=AppKit");
    }
    
    #[cfg(target_os = "linux")]
    {
        pkg_config::probe_library("gtk+-3.0").unwrap();
    }
}
```

## 测试配置

在运行测试时，可能需要设置以下环境：

```bash
# 在 CI 环境中跳过需要 GUI 的测试
export CI=true

# 使用虚拟显示器（Linux）
export DISPLAY=:99

# 禁用实际的系统托盘创建
export TRAY_TEST_MODE=1
``` 