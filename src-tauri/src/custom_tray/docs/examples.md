# 托盘模块示例代码

本文档提供了托盘模块的各种使用示例，从基本用法到高级功能。

## 基础示例

### 1. 最简单的托盘应用

```rust
use secure_password_lib::tray::{Tray<PERSON>anager, TrayConfigBuilder};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建最基本的托盘配置
    let config = TrayConfigBuilder::new()
        .title("简单托盘")
        .tooltip("这是一个简单的托盘应用")
        .build();

    // 创建并启动托盘
    let manager = TrayManager::new(config).await?;
    manager.initialize().await?;
    manager.show().await?;

    println!("托盘已启动，按 Ctrl+C 退出");
    
    // 等待退出信号
    tokio::signal::ctrl_c().await?;
    
    // 清理资源
    manager.destroy().await?;
    println!("托盘已关闭");
    
    Ok(())
}
```

### 2. 带图标的托盘

```rust
use secure_password_lib::tray::{
    TrayManager, TrayConfigBuilder, TrayIcon, TrayIconType, TrayIconFormat
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = TrayConfigBuilder::new()
        .title("图标托盘")
        .tooltip("带图标的托盘应用")
        .build();

    let manager = TrayManager::new(config).await?;

    // 从文件加载图标
    let icon = TrayIcon::from_file(
        "app_icon",
        TrayIconType::Default,
        "assets/icon.png"
    ).await?;

    // 设置图标
    manager.set_icon(icon).await?;
    
    manager.initialize().await?;
    manager.show().await?;

    tokio::signal::ctrl_c().await?;
    manager.destroy().await?;
    
    Ok(())
}
```

### 3. 带菜单的托盘

```rust
use secure_password_lib::tray::{
    TrayManager, TrayConfigBuilder, TrayMenuBuilder, TrayMenuItem, TrayMenuItemType
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = TrayConfigBuilder::new()
        .title("菜单托盘")
        .build();

    let manager = TrayManager::new(config).await?;

    // 创建菜单
    let menu = TrayMenuBuilder::new()
        .add_item(TrayMenuItem::new(
            "show",
            "显示窗口",
            TrayMenuItemType::Normal,
        ))
        .add_item(TrayMenuItem::new(
            "hide",
            "隐藏窗口",
            TrayMenuItemType::Normal,
        ))
        .add_separator()
        .add_item(TrayMenuItem::new(
            "quit",
            "退出",
            TrayMenuItemType::Normal,
        ))
        .build();

    manager.set_menu(menu).await?;
    manager.initialize().await?;
    manager.show().await?;

    tokio::signal::ctrl_c().await?;
    manager.destroy().await?;
    
    Ok(())
}
```

## 中级示例

### 4. 事件处理示例

```rust
use secure_password_lib::tray::{
    TrayManager, TrayConfigBuilder, TrayMenuBuilder, TrayMenuItem, TrayMenuItemType,
    events::{TrayEventHandler, TrayEvent, TrayEventType}
};
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::Mutex;

// 自定义事件处理器
struct AppEventHandler {
    id: String,
    window_visible: Arc<Mutex<bool>>,
}

impl AppEventHandler {
    fn new() -> Self {
        Self {
            id: "app_handler".to_string(),
            window_visible: Arc::new(Mutex::new(true)),
        }
    }
}

#[async_trait]
impl TrayEventHandler for AppEventHandler {
    fn id(&self) -> &str {
        &self.id
    }

    async fn handle_event(&mut self, event: TrayEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match event.event_type {
            TrayEventType::LeftClick => {
                println!("托盘被左键点击");
                // 切换窗口显示状态
                let mut visible = self.window_visible.lock().await;
                *visible = !*visible;
                if *visible {
                    println!("显示窗口");
                } else {
                    println!("隐藏窗口");
                }
            }
            TrayEventType::RightClick => {
                println!("托盘被右键点击，显示菜单");
            }
            TrayEventType::MenuItemSelected { item_id } => {
                match item_id.as_str() {
                    "show" => {
                        println!("显示窗口");
                        *self.window_visible.lock().await = true;
                    }
                    "hide" => {
                        println!("隐藏窗口");
                        *self.window_visible.lock().await = false;
                    }
                    "quit" => {
                        println!("退出应用");
                        std::process::exit(0);
                    }
                    _ => {
                        println!("未知菜单项: {}", item_id);
                    }
                }
            }
            _ => {}
        }
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = TrayConfigBuilder::new()
        .title("事件处理托盘")
        .build();

    let manager = TrayManager::new(config).await?;

    // 创建菜单
    let menu = TrayMenuBuilder::new()
        .add_item(TrayMenuItem::new("show", "显示", TrayMenuItemType::Normal))
        .add_item(TrayMenuItem::new("hide", "隐藏", TrayMenuItemType::Normal))
        .add_separator()
        .add_item(TrayMenuItem::new("quit", "退出", TrayMenuItemType::Normal))
        .build();

    // 添加事件处理器
    let handler = Box::new(AppEventHandler::new());
    manager.add_event_handler(handler).await?;

    manager.set_menu(menu).await?;
    manager.initialize().await?;
    manager.show().await?;

    println!("托盘已启动，左键点击切换窗口状态，右键显示菜单");
    
    tokio::signal::ctrl_c().await?;
    manager.destroy().await?;
    
    Ok(())
}
```

这些示例展示了托盘模块的各种使用方式，从简单的基础用法到复杂的企业级应用集成。每个示例都包含了完整的代码和详细的注释，可以直接运行或作为参考。