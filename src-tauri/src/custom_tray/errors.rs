/// 托盘模块的错误类型定义
/// 
/// 使用 thiserror 提供精确的错误类型和错误信息

use thiserror::Error;

/// 托盘操作的结果类型
pub type TrayResult<T> = Result<T, TrayError>;

/// 托盘模块的错误类型
#[derive(Error, Debug)]
pub enum TrayError {
    /// 托盘初始化失败
    #[error("托盘初始化失败: {reason}")]
    InitializationFailed { reason: String },

    /// 托盘图标加载失败
    #[error("托盘图标加载失败: {path} - {reason}")]
    IconLoadFailed { path: String, reason: String },

    /// 托盘菜单创建失败
    #[error("托盘菜单创建失败: {reason}")]
    MenuCreationFailed { reason: String },

    /// 托盘事件处理失败
    #[error("托盘事件处理失败: {event_type} - {reason}")]
    EventHandlingFailed { event_type: String, reason: String },

    /// 托盘状态无效
    #[error("托盘状态无效: 期望 {expected}，实际 {actual}")]
    InvalidState { expected: String, actual: String },

    /// 平台不支持的操作
    #[error("当前平台 {platform} 不支持操作: {operation}")]
    UnsupportedPlatform { platform: String, operation: String },

    /// 配置错误
    #[error("托盘配置错误: {field} - {reason}")]
    ConfigurationError { field: String, reason: String },

    /// 菜单项数量超出限制
    #[error("菜单项数量超出限制: {count} > {max_allowed}")]
    MenuItemLimitExceeded { count: usize, max_allowed: usize },

    /// 菜单项ID重复
    #[error("菜单项ID重复: {id}")]
    DuplicateMenuItemId { id: String },

    /// 菜单项未找到
    #[error("菜单项未找到: {id}")]
    MenuItemNotFound { id: String },

    /// 图标格式不支持
    #[error("图标格式不支持: {format}，支持的格式: {supported_formats:?}")]
    UnsupportedIconFormat {
        format: String,
        supported_formats: Vec<String>,
    },

    /// 图标尺寸无效
    #[error("图标尺寸无效: {width}x{height}，期望: {expected_width}x{expected_height}")]
    InvalidIconSize {
        width: u32,
        height: u32,
        expected_width: u32,
        expected_height: u32,
    },

    /// 系统托盘不可用
    #[error("系统托盘在当前环境中不可用: {reason}")]
    SystemTrayUnavailable { reason: String },

    /// 权限不足
    #[error("权限不足，无法执行操作: {operation}")]
    InsufficientPermissions { operation: String },

    /// 资源已被占用
    #[error("资源已被占用: {resource}")]
    ResourceBusy { resource: String },

    /// 超时错误
    #[error("操作超时: {operation}，超时时间: {timeout_ms}ms")]
    OperationTimeout { operation: String, timeout_ms: u64 },

    /// IO 错误
    #[error("IO 错误: {0}")]
    Io(String),

    /// 序列化错误
    #[error("序列化错误: {0}")]
    Serialization(String),

    /// 内部错误
    #[error("内部错误: {message}")]
    Internal { message: String },
}

impl From<std::io::Error> for TrayError {
    fn from(err: std::io::Error) -> Self {
        TrayError::Io(err.to_string())
    }
}

impl From<serde_json::Error> for TrayError {
    fn from(err: serde_json::Error) -> Self {
        TrayError::Serialization(err.to_string())
    }
}

impl TrayError {
    /// 创建初始化失败错误
    pub fn initialization_failed(reason: impl Into<String>) -> Self {
        Self::InitializationFailed {
            reason: reason.into(),
        }
    }

    /// 创建图标加载失败错误
    pub fn icon_load_failed(path: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::IconLoadFailed {
            path: path.into(),
            reason: reason.into(),
        }
    }

    /// 创建菜单创建失败错误
    pub fn menu_creation_failed(reason: impl Into<String>) -> Self {
        Self::MenuCreationFailed {
            reason: reason.into(),
        }
    }

    /// 创建事件处理失败错误
    pub fn event_handling_failed(
        event_type: impl Into<String>,
        reason: impl Into<String>,
    ) -> Self {
        Self::EventHandlingFailed {
            event_type: event_type.into(),
            reason: reason.into(),
        }
    }

    /// 创建状态无效错误
    pub fn invalid_state(expected: impl Into<String>, actual: impl Into<String>) -> Self {
        Self::InvalidState {
            expected: expected.into(),
            actual: actual.into(),
        }
    }

    /// 创建平台不支持错误
    pub fn unsupported_platform(
        platform: impl Into<String>,
        operation: impl Into<String>,
    ) -> Self {
        Self::UnsupportedPlatform {
            platform: platform.into(),
            operation: operation.into(),
        }
    }

    /// 创建配置错误
    pub fn configuration_error(field: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ConfigurationError {
            field: field.into(),
            reason: reason.into(),
        }
    }

    /// 创建内部错误
    pub fn internal(message: impl Into<String>) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }

    /// 检查是否为可恢复的错误
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::InitializationFailed { .. } => false,
            Self::SystemTrayUnavailable { .. } => false,
            Self::UnsupportedPlatform { .. } => false,
            Self::InsufficientPermissions { .. } => false,
            Self::IconLoadFailed { .. } => true,
            Self::MenuCreationFailed { .. } => true,
            Self::EventHandlingFailed { .. } => true,
            Self::InvalidState { .. } => true,
            Self::ConfigurationError { .. } => true,
            Self::MenuItemLimitExceeded { .. } => true,
            Self::DuplicateMenuItemId { .. } => true,
            Self::MenuItemNotFound { .. } => true,
            Self::UnsupportedIconFormat { .. } => true,
            Self::InvalidIconSize { .. } => true,
            Self::ResourceBusy { .. } => true,
            Self::OperationTimeout { .. } => true,
            Self::Io(_) => true,
            Self::Serialization(_) => true,
            Self::Internal { .. } => false,
        }
    }

    /// 获取错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            Self::InitializationFailed { .. } => ErrorSeverity::Critical,
            Self::SystemTrayUnavailable { .. } => ErrorSeverity::Critical,
            Self::UnsupportedPlatform { .. } => ErrorSeverity::Critical,
            Self::InsufficientPermissions { .. } => ErrorSeverity::High,
            Self::IconLoadFailed { .. } => ErrorSeverity::Medium,
            Self::MenuCreationFailed { .. } => ErrorSeverity::Medium,
            Self::EventHandlingFailed { .. } => ErrorSeverity::Medium,
            Self::InvalidState { .. } => ErrorSeverity::Medium,
            Self::ConfigurationError { .. } => ErrorSeverity::Medium,
            Self::MenuItemLimitExceeded { .. } => ErrorSeverity::Low,
            Self::DuplicateMenuItemId { .. } => ErrorSeverity::Low,
            Self::MenuItemNotFound { .. } => ErrorSeverity::Low,
            Self::UnsupportedIconFormat { .. } => ErrorSeverity::Low,
            Self::InvalidIconSize { .. } => ErrorSeverity::Low,
            Self::ResourceBusy { .. } => ErrorSeverity::Medium,
            Self::OperationTimeout { .. } => ErrorSeverity::Medium,
            Self::Io(_) => ErrorSeverity::Medium,
            Self::Serialization(_) => ErrorSeverity::Low,
            Self::Internal { .. } => ErrorSeverity::Critical,
        }
    }
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    /// 低严重程度 - 不影响核心功能
    Low,
    /// 中等严重程度 - 影响部分功能
    Medium,
    /// 高严重程度 - 影响主要功能
    High,
    /// 严重 - 影响核心功能
    Critical,
}

impl std::fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Low => write!(f, "低"),
            Self::Medium => write!(f, "中"),
            Self::High => write!(f, "高"),
            Self::Critical => write!(f, "严重"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = TrayError::initialization_failed("测试原因");
        assert!(matches!(error, TrayError::InitializationFailed { .. }));
        assert!(!error.is_recoverable());
        assert_eq!(error.severity(), ErrorSeverity::Critical);
    }

    #[test]
    fn test_error_severity() {
        let critical_error = TrayError::initialization_failed("测试");
        let medium_error = TrayError::icon_load_failed("test.png", "文件不存在");
        let low_error = TrayError::DuplicateMenuItemId { id: "test".to_string() };

        assert_eq!(critical_error.severity(), ErrorSeverity::Critical);
        assert_eq!(medium_error.severity(), ErrorSeverity::Medium);
        assert_eq!(low_error.severity(), ErrorSeverity::Low);
    }

    #[test]
    fn test_error_recoverability() {
        let non_recoverable = TrayError::initialization_failed("测试");
        let recoverable = TrayError::icon_load_failed("test.png", "文件不存在");

        assert!(!non_recoverable.is_recoverable());
        assert!(recoverable.is_recoverable());
    }
} 