/// 托盘菜单模块
/// 
/// 提供托盘菜单的创建、管理和事件处理功能

use crate::tray::{TrayError, TrayResult, MAX_MENU_ITEMS};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 托盘菜单项类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrayMenuItemType {
    /// 普通菜单项
    Normal,
    /// 分隔符
    Separator,
    /// 子菜单
    Submenu,
    /// 复选框菜单项
    Checkbox,
    /// 单选按钮菜单项
    Radio,
}

/// 托盘菜单项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayMenuItem {
    /// 菜单项唯一标识符
    pub id: String,
    
    /// 菜单项显示文本
    pub text: String,
    
    /// 菜单项类型
    pub item_type: TrayMenuItemType,
    
    /// 是否启用
    pub enabled: bool,
    
    /// 是否可见
    pub visible: bool,
    
    /// 是否选中（用于复选框和单选按钮）
    pub checked: bool,
    
    /// 快捷键
    pub accelerator: Option<String>,
    
    /// 图标路径或数据
    pub icon: Option<String>,
    
    /// 工具提示
    pub tooltip: Option<String>,
    
    /// 子菜单项（仅当类型为 Submenu 时有效）
    pub submenu: Option<Vec<TrayMenuItem>>,
    
    /// 单选按钮组名称（仅当类型为 Radio 时有效）
    pub radio_group: Option<String>,
    
    /// 菜单项的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

impl TrayMenuItem {
    /// 创建普通菜单项
    pub fn new(id: impl Into<String>, text: impl Into<String>) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Normal,
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            icon: None,
            tooltip: None,
            submenu: None,
            radio_group: None,
            data: HashMap::new(),
        }
    }

    /// 创建分隔符
    pub fn separator() -> Self {
        Self {
            id: format!("separator_{}", uuid::Uuid::new_v4()),
            text: String::new(),
            item_type: TrayMenuItemType::Separator,
            enabled: false,
            visible: true,
            checked: false,
            accelerator: None,
            icon: None,
            tooltip: None,
            submenu: None,
            radio_group: None,
            data: HashMap::new(),
        }
    }

    /// 创建子菜单
    pub fn submenu(
        id: impl Into<String>,
        text: impl Into<String>,
        items: Vec<TrayMenuItem>,
    ) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Submenu,
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            icon: None,
            tooltip: None,
            submenu: Some(items),
            radio_group: None,
            data: HashMap::new(),
        }
    }

    /// 创建复选框菜单项
    pub fn checkbox(
        id: impl Into<String>,
        text: impl Into<String>,
        checked: bool,
    ) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Checkbox,
            enabled: true,
            visible: true,
            checked,
            accelerator: None,
            icon: None,
            tooltip: None,
            submenu: None,
            radio_group: None,
            data: HashMap::new(),
        }
    }

    /// 创建单选按钮菜单项
    pub fn radio(
        id: impl Into<String>,
        text: impl Into<String>,
        radio_group: impl Into<String>,
        checked: bool,
    ) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Radio,
            enabled: true,
            visible: true,
            checked,
            accelerator: None,
            icon: None,
            tooltip: None,
            submenu: None,
            radio_group: Some(radio_group.into()),
            data: HashMap::new(),
        }
    }

    /// 设置是否启用
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置是否可见
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }

    /// 设置快捷键
    pub fn accelerator(mut self, accelerator: impl Into<String>) -> Self {
        self.accelerator = Some(accelerator.into());
        self
    }

    /// 设置图标
    pub fn icon(mut self, icon: impl Into<String>) -> Self {
        self.icon = Some(icon.into());
        self
    }

    /// 设置工具提示
    pub fn tooltip(mut self, tooltip: impl Into<String>) -> Self {
        self.tooltip = Some(tooltip.into());
        self
    }

    /// 添加附加数据
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }

    /// 获取附加数据
    pub fn get_data(&self, key: &str) -> Option<&serde_json::Value> {
        self.data.get(key)
    }

    /// 检查是否为分隔符
    pub fn is_separator(&self) -> bool {
        self.item_type == TrayMenuItemType::Separator
    }

    /// 检查是否为子菜单
    pub fn is_submenu(&self) -> bool {
        self.item_type == TrayMenuItemType::Submenu
    }

    /// 检查是否为可选择的菜单项（复选框或单选按钮）
    pub fn is_selectable(&self) -> bool {
        matches!(
            self.item_type,
            TrayMenuItemType::Checkbox | TrayMenuItemType::Radio
        )
    }

    /// 验证菜单项的有效性
    pub fn validate(&self) -> TrayResult<()> {
        // 验证ID不为空
        if self.id.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "menu_item_id",
                "菜单项ID不能为空",
            ));
        }

        // 验证非分隔符菜单项的文本不为空
        if !self.is_separator() && self.text.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "menu_item_text",
                "菜单项文本不能为空",
            ));
        }

        // 验证子菜单
        if let Some(ref submenu_items) = self.submenu {
            if self.item_type != TrayMenuItemType::Submenu {
                return Err(TrayError::configuration_error(
                    "submenu",
                    "只有子菜单类型的菜单项才能包含子菜单",
                ));
            }

            for item in submenu_items {
                item.validate()?;
            }
        }

        // 验证单选按钮组
        if self.item_type == TrayMenuItemType::Radio && self.radio_group.is_none() {
            return Err(TrayError::configuration_error(
                "radio_group",
                "单选按钮菜单项必须指定单选按钮组",
            ));
        }

        Ok(())
    }

    /// 递归计算菜单项总数（包括子菜单）
    pub fn count_total_items(&self) -> usize {
        let mut count = 1;
        if let Some(ref submenu_items) = self.submenu {
            for item in submenu_items {
                count += item.count_total_items();
            }
        }
        count
    }

    /// 查找指定ID的菜单项
    pub fn find_item_by_id(&self, id: &str) -> Option<&TrayMenuItem> {
        if self.id == id {
            return Some(self);
        }

        if let Some(ref submenu_items) = self.submenu {
            for item in submenu_items {
                if let Some(found) = item.find_item_by_id(id) {
                    return Some(found);
                }
            }
        }

        None
    }

    /// 查找指定ID的菜单项（可变引用）
    pub fn find_item_by_id_mut(&mut self, id: &str) -> Option<&mut TrayMenuItem> {
        if self.id == id {
            return Some(self);
        }

        if let Some(ref mut submenu_items) = self.submenu {
            for item in submenu_items {
                if let Some(found) = item.find_item_by_id_mut(id) {
                    return Some(found);
                }
            }
        }

        None
    }
}

/// 托盘菜单
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayMenu {
    /// 菜单项列表
    pub items: Vec<TrayMenuItem>,
    
    /// 菜单的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

impl TrayMenu {
    /// 创建新的托盘菜单
    pub fn new() -> Self {
        Self {
            items: Vec::new(),
            data: HashMap::new(),
        }
    }

    /// 从菜单项列表创建托盘菜单
    pub fn from_items(items: Vec<TrayMenuItem>) -> Self {
        Self {
            items,
            data: HashMap::new(),
        }
    }

    /// 添加菜单项
    pub fn add_item(&mut self, item: TrayMenuItem) -> TrayResult<()> {
        // 检查菜单项数量限制
        let total_items = self.count_total_items() + item.count_total_items();
        if total_items > MAX_MENU_ITEMS {
            return Err(TrayError::MenuItemLimitExceeded {
                count: total_items,
                max_allowed: MAX_MENU_ITEMS,
            });
        }

        // 检查ID是否重复
        if self.find_item_by_id(&item.id).is_some() {
            return Err(TrayError::DuplicateMenuItemId { id: item.id.clone() });
        }

        // 验证菜单项
        item.validate()?;

        self.items.push(item);
        Ok(())
    }

    /// 移除指定ID的菜单项
    pub fn remove_item(&mut self, id: &str) -> TrayResult<TrayMenuItem> {
        let position = self
            .items
            .iter()
            .position(|item| item.id == id)
            .ok_or_else(|| TrayError::MenuItemNotFound { id: id.to_string() })?;

        Ok(self.items.remove(position))
    }

    /// 查找指定ID的菜单项
    pub fn find_item_by_id(&self, id: &str) -> Option<&TrayMenuItem> {
        for item in &self.items {
            if let Some(found) = item.find_item_by_id(id) {
                return Some(found);
            }
        }
        None
    }

    /// 查找指定ID的菜单项（可变引用）
    pub fn find_item_by_id_mut(&mut self, id: &str) -> Option<&mut TrayMenuItem> {
        for item in &mut self.items {
            if let Some(found) = item.find_item_by_id_mut(id) {
                return Some(found);
            }
        }
        None
    }

    /// 更新菜单项
    pub fn update_item(&mut self, id: &str, updater: impl FnOnce(&mut TrayMenuItem)) -> TrayResult<()> {
        let item = self
            .find_item_by_id_mut(id)
            .ok_or_else(|| TrayError::MenuItemNotFound { id: id.to_string() })?;

        updater(item);
        item.validate()?;

        Ok(())
    }

    /// 设置菜单项的启用状态
    pub fn set_item_enabled(&mut self, id: &str, enabled: bool) -> TrayResult<()> {
        self.update_item(id, |item| {
            item.enabled = enabled;
        })
    }

    /// 设置菜单项的可见状态
    pub fn set_item_visible(&mut self, id: &str, visible: bool) -> TrayResult<()> {
        self.update_item(id, |item| {
            item.visible = visible;
        })
    }

    /// 设置菜单项的选中状态
    pub fn set_item_checked(&mut self, id: &str, checked: bool) -> TrayResult<()> {
        // 先获取单选按钮组信息
        let radio_group = if checked {
            self.find_item_by_id(id)
                .and_then(|item| {
                    if item.item_type == TrayMenuItemType::Radio {
                        item.radio_group.clone()
                    } else {
                        None
                    }
                })
        } else {
            None
        };

        // 更新菜单项状态
        self.update_item(id, |item| {
            if item.is_selectable() {
                item.checked = checked;
            }
        })?;

        // 如果是单选按钮且被选中，需要取消同组其他项的选中状态
        if let Some(radio_group) = radio_group {
            self.uncheck_radio_group_except(&radio_group, id);
        }

        Ok(())
    }

    /// 取消指定单选按钮组中除指定项外的所有项的选中状态
    fn uncheck_radio_group_except(&mut self, radio_group: &str, except_id: &str) {
        Self::uncheck_radio_group_in_items(&mut self.items, radio_group, except_id);
    }

    /// 在菜单项列表中取消单选按钮组的选中状态
    fn uncheck_radio_group_in_items(
        items: &mut [TrayMenuItem],
        radio_group: &str,
        except_id: &str,
    ) {
        for item in items {
            Self::uncheck_radio_group_recursive(item, radio_group, except_id);
        }
    }

    /// 递归取消单选按钮组的选中状态
    fn uncheck_radio_group_recursive(
        item: &mut TrayMenuItem,
        radio_group: &str,
        except_id: &str,
    ) {
        if item.item_type == TrayMenuItemType::Radio
            && item.id != except_id
            && item.radio_group.as_ref() == Some(&radio_group.to_string())
        {
            item.checked = false;
        }

        if let Some(ref mut submenu_items) = item.submenu {
            for submenu_item in submenu_items {
                Self::uncheck_radio_group_recursive(submenu_item, radio_group, except_id);
            }
        }
    }

    /// 获取所有菜单项的ID列表
    pub fn get_all_item_ids(&self) -> Vec<String> {
        let mut ids = Vec::new();
        for item in &self.items {
            self.collect_item_ids(item, &mut ids);
        }
        ids
    }

    /// 递归收集菜单项ID
    fn collect_item_ids(&self, item: &TrayMenuItem, ids: &mut Vec<String>) {
        ids.push(item.id.clone());
        if let Some(ref submenu_items) = item.submenu {
            for submenu_item in submenu_items {
                self.collect_item_ids(submenu_item, ids);
            }
        }
    }

    /// 计算菜单项总数（包括子菜单）
    pub fn count_total_items(&self) -> usize {
        self.items.iter().map(|item| item.count_total_items()).sum()
    }

    /// 验证菜单的有效性
    pub fn validate(&self) -> TrayResult<()> {
        // 检查菜单项数量限制
        let total_items = self.count_total_items();
        if total_items > MAX_MENU_ITEMS {
            return Err(TrayError::MenuItemLimitExceeded {
                count: total_items,
                max_allowed: MAX_MENU_ITEMS,
            });
        }

        // 验证每个菜单项
        for item in &self.items {
            item.validate()?;
        }

        // 检查ID重复
        let all_ids = self.get_all_item_ids();
        let mut unique_ids = std::collections::HashSet::new();
        for id in all_ids {
            if !unique_ids.insert(id.clone()) {
                return Err(TrayError::DuplicateMenuItemId { id });
            }
        }

        Ok(())
    }

    /// 清空菜单
    pub fn clear(&mut self) {
        self.items.clear();
    }

    /// 检查菜单是否为空
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// 添加附加数据
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }

    /// 获取附加数据
    pub fn get_data(&self, key: &str) -> Option<&serde_json::Value> {
        self.data.get(key)
    }

    /// 从 JSON 字符串加载菜单
    pub fn from_json(json: &str) -> TrayResult<Self> {
        serde_json::from_str(json).map_err(TrayError::from)
    }

    /// 将菜单序列化为 JSON 字符串
    pub fn to_json(&self) -> TrayResult<String> {
        serde_json::to_string_pretty(self).map_err(TrayError::from)
    }
}

impl Default for TrayMenu {
    fn default() -> Self {
        Self::new()
    }
}

/// 托盘菜单建造者
/// 
/// 使用建造者模式来构建托盘菜单，提供流畅的API
#[derive(Debug, Default)]
pub struct TrayMenuBuilder {
    menu: TrayMenu,
}

impl TrayMenuBuilder {
    /// 创建新的菜单建造者
    pub fn new() -> Self {
        Self {
            menu: TrayMenu::new(),
        }
    }

    /// 添加菜单项
    pub fn add_item(mut self, item: TrayMenuItem) -> TrayResult<Self> {
        self.menu.add_item(item)?;
        Ok(self)
    }

    /// 添加普通菜单项
    pub fn add_normal_item(self, id: impl Into<String>, text: impl Into<String>) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::new(id, text))
    }

    /// 添加分隔符
    pub fn add_separator(self) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::separator())
    }

    /// 添加子菜单
    pub fn add_submenu(
        self,
        id: impl Into<String>,
        text: impl Into<String>,
        items: Vec<TrayMenuItem>,
    ) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::submenu(id, text, items))
    }

    /// 添加复选框菜单项
    pub fn add_checkbox(
        self,
        id: impl Into<String>,
        text: impl Into<String>,
        checked: bool,
    ) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::checkbox(id, text, checked))
    }

    /// 添加单选按钮菜单项
    pub fn add_radio(
        self,
        id: impl Into<String>,
        text: impl Into<String>,
        radio_group: impl Into<String>,
        checked: bool,
    ) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::radio(id, text, radio_group, checked))
    }

    /// 添加附加数据
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.menu.data.insert(key, value);
        self
    }

    /// 构建菜单
    pub fn build(self) -> TrayResult<TrayMenu> {
        self.menu.validate()?;
        Ok(self.menu)
    }
}

impl TrayMenu {
    /// 创建菜单建造者
    pub fn builder() -> TrayMenuBuilder {
        TrayMenuBuilder::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_menu_item_creation() {
        let item = TrayMenuItem::new("test_id", "测试菜单项");
        assert_eq!(item.id, "test_id");
        assert_eq!(item.text, "测试菜单项");
        assert_eq!(item.item_type, TrayMenuItemType::Normal);
        assert!(item.enabled);
        assert!(item.visible);
        assert!(!item.checked);
    }

    #[test]
    fn test_separator_creation() {
        let separator = TrayMenuItem::separator();
        assert_eq!(separator.item_type, TrayMenuItemType::Separator);
        assert!(!separator.enabled);
        assert!(separator.is_separator());
    }

    #[test]
    fn test_submenu_creation() {
        let submenu_items = vec![
            TrayMenuItem::new("sub1", "子菜单项1"),
            TrayMenuItem::new("sub2", "子菜单项2"),
        ];
        let submenu = TrayMenuItem::submenu("submenu", "子菜单", submenu_items);
        
        assert_eq!(submenu.item_type, TrayMenuItemType::Submenu);
        assert!(submenu.is_submenu());
        assert_eq!(submenu.submenu.as_ref().unwrap().len(), 2);
    }

    #[test]
    fn test_checkbox_creation() {
        let checkbox = TrayMenuItem::checkbox("check1", "复选框", true);
        assert_eq!(checkbox.item_type, TrayMenuItemType::Checkbox);
        assert!(checkbox.checked);
        assert!(checkbox.is_selectable());
    }

    #[test]
    fn test_radio_creation() {
        let radio = TrayMenuItem::radio("radio1", "单选按钮", "group1", false);
        assert_eq!(radio.item_type, TrayMenuItemType::Radio);
        assert!(!radio.checked);
        assert_eq!(radio.radio_group.as_ref().unwrap(), "group1");
        assert!(radio.is_selectable());
    }

    #[test]
    fn test_menu_creation() {
        let mut menu = TrayMenu::new();
        assert!(menu.is_empty());

        let item = TrayMenuItem::new("test", "测试");
        menu.add_item(item).unwrap();
        assert!(!menu.is_empty());
        assert_eq!(menu.count_total_items(), 1);
    }

    #[test]
    fn test_menu_builder() {
        let menu = TrayMenu::builder()
            .add_normal_item("item1", "菜单项1").unwrap()
            .add_separator().unwrap()
            .add_checkbox("check1", "复选框", false).unwrap()
            .build()
            .unwrap();

        assert_eq!(menu.items.len(), 3);
        assert_eq!(menu.items[0].id, "item1");
        assert!(menu.items[1].is_separator());
        assert_eq!(menu.items[2].item_type, TrayMenuItemType::Checkbox);
    }

    #[test]
    fn test_find_item_by_id() {
        let mut menu = TrayMenu::new();
        let item = TrayMenuItem::new("test_id", "测试");
        menu.add_item(item).unwrap();

        let found = menu.find_item_by_id("test_id");
        assert!(found.is_some());
        assert_eq!(found.unwrap().id, "test_id");

        let not_found = menu.find_item_by_id("nonexistent");
        assert!(not_found.is_none());
    }

    #[test]
    fn test_duplicate_id_error() {
        let mut menu = TrayMenu::new();
        let item1 = TrayMenuItem::new("duplicate", "项目1");
        let item2 = TrayMenuItem::new("duplicate", "项目2");

        menu.add_item(item1).unwrap();
        let result = menu.add_item(item2);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), TrayError::DuplicateMenuItemId { .. }));
    }

    #[test]
    fn test_radio_group_behavior() {
        let mut menu = TrayMenu::new();
        let radio1 = TrayMenuItem::radio("radio1", "选项1", "group1", true);
        let radio2 = TrayMenuItem::radio("radio2", "选项2", "group1", false);

        menu.add_item(radio1).unwrap();
        menu.add_item(radio2).unwrap();

        // 选中第二个单选按钮，第一个应该被取消选中
        menu.set_item_checked("radio2", true).unwrap();

        let item1 = menu.find_item_by_id("radio1").unwrap();
        let item2 = menu.find_item_by_id("radio2").unwrap();

        assert!(!item1.checked);
        assert!(item2.checked);
    }

    #[test]
    fn test_menu_validation() {
        let mut menu = TrayMenu::new();
        
        // 添加有效的菜单项
        let valid_item = TrayMenuItem::new("valid", "有效菜单项");
        menu.add_item(valid_item).unwrap();

        // 验证应该成功
        assert!(menu.validate().is_ok());

        // 添加无效的菜单项（空ID）
        let invalid_item = TrayMenuItem::new("", "无效菜单项");
        let result = menu.add_item(invalid_item);
        assert!(result.is_err());
    }

    #[test]
    fn test_menu_serialization() {
        let menu = TrayMenu::builder()
            .add_normal_item("item1", "菜单项1").unwrap()
            .add_checkbox("check1", "复选框", true).unwrap()
            .build()
            .unwrap();

        let json = menu.to_json().unwrap();
        let deserialized_menu = TrayMenu::from_json(&json).unwrap();

        assert_eq!(menu.items.len(), deserialized_menu.items.len());
        assert_eq!(menu.items[0].id, deserialized_menu.items[0].id);
        assert_eq!(menu.items[1].checked, deserialized_menu.items[1].checked);
    }
} 