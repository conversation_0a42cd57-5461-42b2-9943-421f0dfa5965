/// 跨平台系统托盘模块
/// 
/// 该模块提供了一个可插拔的系统托盘实现，支持：
/// - 跨平台兼容性（Windows、macOS、Linux）
/// - 可插拔的菜单系统
/// - 事件处理机制
/// - 图标管理
/// - 状态管理
/// 
/// # 示例
/// 
/// ```rust
/// use crate::tray::{TrayManager, TrayConfig, TrayMenuItem};
/// 
/// let config = TrayConfig::builder()
///     .title("Secure Password")
///     .tooltip("密码管理器")
///     .build();
/// 
/// let mut tray_manager = TrayManager::new(config)?;
/// tray_manager.show()?;
/// ```

pub mod commands;
pub mod config;
pub mod errors;
pub mod events;
pub mod examples;
pub mod icons;
pub mod manager;
pub mod menu;
pub mod platform;
pub mod state;

#[cfg(test)]
mod tests;

// 重新导出主要类型
pub use config::{TrayConfig, TrayConfigBuilder};
pub use errors::{TrayError, TrayResult};
pub use events::{TrayEvent, TrayEventHandler, TrayEventType};
pub use icons::{TrayIcon, TrayIconManager, TrayIconType};
pub use manager::{TrayManager, TrayManagerBuilder};
pub use menu::{TrayMenu, TrayMenuItem, TrayMenuBuilder};
pub use state::{TrayState, TrayStatus};

/// 托盘模块的版本信息
pub const TRAY_MODULE_VERSION: &str = "1.0.0";

/// 默认托盘图标大小（像素）
pub const DEFAULT_TRAY_ICON_SIZE: u32 = 16;

/// 最大菜单项数量限制
pub const MAX_MENU_ITEMS: usize = 50; 