/// 平台特定的托盘实现模块
/// 
/// 提供针对不同操作系统的托盘实现，包括：
/// - Windows 系统托盘实现
/// - macOS 菜单栏实现
/// - Linux 系统托盘实现
/// - 平台检测和适配

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use crate::tray::errors::{TrayError, TrayResult};
use crate::tray::config::TrayConfig;
use crate::tray::icons::TrayIcon;
use crate::tray::menu::TrayMenu;
use crate::tray::events::TrayEvent;

/// 支持的平台类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PlatformType {
    /// Windows 平台
    Windows,
    /// macOS 平台
    MacOS,
    /// Linux 平台
    Linux,
    /// 未知平台
    Unknown,
}

impl PlatformType {
    /// 检测当前平台
    pub fn detect() -> Self {
        #[cfg(target_os = "windows")]
        return Self::Windows;
        
        #[cfg(target_os = "macos")]
        return Self::MacOS;
        
        #[cfg(target_os = "linux")]
        return Self::Linux;
        
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        return Self::Unknown;
    }

    /// 获取平台名称
    pub fn name(&self) -> &'static str {
        match self {
            Self::Windows => "Windows",
            Self::MacOS => "macOS",
            Self::Linux => "Linux",
            Self::Unknown => "Unknown",
        }
    }

    /// 检查平台是否支持系统托盘
    pub fn supports_system_tray(&self) -> bool {
        matches!(self, Self::Windows | Self::MacOS | Self::Linux)
    }

    /// 获取推荐的图标格式
    pub fn preferred_icon_format(&self) -> &'static str {
        match self {
            Self::Windows => "ico",
            Self::MacOS => "icns",
            Self::Linux => "png",
            Self::Unknown => "png",
        }
    }

    /// 获取推荐的图标尺寸
    pub fn preferred_icon_size(&self) -> (u32, u32) {
        match self {
            Self::Windows => (16, 16),
            Self::MacOS => (22, 22), // macOS 菜单栏图标通常是 22x22
            Self::Linux => (24, 24),  // Linux 系统托盘图标通常是 24x24
            Self::Unknown => (16, 16),
        }
    }
}

/// 平台特定的托盘实现 trait
#[async_trait::async_trait]
pub trait PlatformTray: Send + Sync {
    /// 初始化托盘
    async fn initialize(&mut self, config: &TrayConfig) -> TrayResult<()>;

    /// 显示托盘
    async fn show(&mut self) -> TrayResult<()>;

    /// 隐藏托盘
    async fn hide(&mut self) -> TrayResult<()>;

    /// 设置图标
    async fn set_icon(&mut self, icon: &TrayIcon) -> TrayResult<()>;

    /// 设置工具提示
    async fn set_tooltip(&mut self, tooltip: &str) -> TrayResult<()>;

    /// 设置菜单
    async fn set_menu(&mut self, menu: &TrayMenu) -> TrayResult<()>;

    /// 处理事件
    async fn handle_event(&mut self, event: TrayEvent) -> TrayResult<()>;

    /// 销毁托盘
    async fn destroy(&mut self) -> TrayResult<()>;

    /// 检查托盘是否可用
    fn is_available(&self) -> bool;

    /// 获取平台类型
    fn platform_type(&self) -> PlatformType;
}

/// 平台托盘工厂
pub struct PlatformTrayFactory;

impl PlatformTrayFactory {
    /// 创建平台特定的托盘实现
    pub fn create_tray() -> TrayResult<Box<dyn PlatformTray>> {
        let platform = PlatformType::detect();
        
        match platform {
            #[cfg(target_os = "windows")]
            PlatformType::Windows => Ok(Box::new(WindowsTray::new()?)),
            #[cfg(not(target_os = "windows"))]
            PlatformType::Windows => Err(TrayError::unsupported_platform(
                platform.name(),
                "系统托盘",
            )),
            
            #[cfg(target_os = "macos")]
            PlatformType::MacOS => Ok(Box::new(MacOSTray::new()?)),
            #[cfg(not(target_os = "macos"))]
            PlatformType::MacOS => Err(TrayError::unsupported_platform(
                platform.name(),
                "系统托盘",
            )),
            
            #[cfg(target_os = "linux")]
            PlatformType::Linux => Ok(Box::new(LinuxTray::new()?)),
            #[cfg(not(target_os = "linux"))]
            PlatformType::Linux => Err(TrayError::unsupported_platform(
                platform.name(),
                "系统托盘",
            )),
            
            PlatformType::Unknown => Err(TrayError::unsupported_platform(
                platform.name(),
                "系统托盘",
            )),
        }
    }

    /// 检查当前平台是否支持系统托盘
    pub fn is_supported() -> bool {
        PlatformType::detect().supports_system_tray()
    }
}

// Windows 平台实现
#[cfg(target_os = "windows")]
mod windows {
    use super::*;

    /// Windows 系统托盘实现
    pub struct WindowsTray {
        /// 托盘句柄
        handle: Option<WindowsTrayHandle>,
        /// 是否已初始化
        initialized: bool,
        /// 是否可见
        visible: bool,
        /// 配置
        config: Option<TrayConfig>,
    }

    /// Windows 托盘句柄（模拟）
    struct WindowsTrayHandle {
        _id: u32,
    }

    impl WindowsTray {
        /// 创建新的 Windows 托盘实例
        pub fn new() -> TrayResult<Self> {
            // 检查系统托盘是否可用
            if !Self::is_system_tray_available() {
                return Err(TrayError::SystemTrayUnavailable {
                    reason: "Windows 系统托盘服务不可用".to_string(),
                });
            }

            Ok(Self {
                handle: None,
                initialized: false,
                visible: false,
                config: None,
            })
        }

        /// 检查系统托盘是否可用
        fn is_system_tray_available() -> bool {
            // 在实际实现中，这里会检查 Windows 系统托盘服务
            // 目前返回 true 作为示例
            true
        }

        /// 创建 Windows 托盘
        fn create_tray(&mut self) -> TrayResult<WindowsTrayHandle> {
            // 在实际实现中，这里会调用 Windows API 创建系统托盘图标
            Ok(WindowsTrayHandle {
                _id: std::process::id(),
            })
        }
    }

    #[async_trait::async_trait]
    impl PlatformTray for WindowsTray {
        async fn initialize(&mut self, config: &TrayConfig) -> TrayResult<()> {
            if self.initialized {
                return Ok(());
            }

            self.config = Some(config.clone());
            self.handle = Some(self.create_tray()?);
            self.initialized = true;

            Ok(())
        }

        async fn show(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会调用 Windows API 显示托盘图标
            self.visible = true;
            Ok(())
        }

        async fn hide(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会调用 Windows API 隐藏托盘图标
            self.visible = false;
            Ok(())
        }

        async fn set_icon(&mut self, icon: &TrayIcon) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会设置 Windows 托盘图标
            // 验证图标格式
            if !matches!(icon.format, crate::tray::icons::TrayIconFormat::Ico | crate::tray::icons::TrayIconFormat::Png) {
                return Err(TrayError::UnsupportedIconFormat {
                    format: format!("{:?}", icon.format),
                    supported_formats: vec!["ico".to_string(), "png".to_string()],
                });
            }

            Ok(())
        }

        async fn set_tooltip(&mut self, tooltip: &str) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // Windows 托盘工具提示有长度限制
            if tooltip.len() > 127 {
                return Err(TrayError::configuration_error(
                    "tooltip",
                    "Windows 托盘工具提示长度不能超过 127 个字符",
                ));
            }

            // 在实际实现中，这里会设置 Windows 托盘工具提示
            Ok(())
        }

        async fn set_menu(&mut self, menu: &TrayMenu) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会创建 Windows 上下文菜单
            Ok(())
        }

        async fn handle_event(&mut self, event: TrayEvent) -> TrayResult<()> {
            // 在实际实现中，这里会处理 Windows 托盘事件
            Ok(())
        }

        async fn destroy(&mut self) -> TrayResult<()> {
            if let Some(_handle) = self.handle.take() {
                // 在实际实现中，这里会销毁 Windows 托盘图标
            }

            self.initialized = false;
            self.visible = false;
            self.config = None;

            Ok(())
        }

        fn is_available(&self) -> bool {
            Self::is_system_tray_available()
        }

        fn platform_type(&self) -> PlatformType {
            PlatformType::Windows
        }
    }
}

#[cfg(target_os = "windows")]
pub use windows::WindowsTray;

#[cfg(not(target_os = "windows"))]
pub struct WindowsTray;

#[cfg(not(target_os = "windows"))]
impl WindowsTray {
    pub fn new() -> TrayResult<Self> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
}

#[cfg(not(target_os = "windows"))]
#[async_trait::async_trait]
impl PlatformTray for WindowsTray {
    async fn initialize(&mut self, _config: &TrayConfig) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn show(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn hide(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn set_icon(&mut self, _icon: &TrayIcon) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn set_tooltip(&mut self, _tooltip: &str) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn set_menu(&mut self, _menu: &TrayMenu) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn handle_event(&mut self, _event: TrayEvent) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    async fn destroy(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Windows", "系统托盘"))
    }
    fn is_available(&self) -> bool {
        false
    }
    fn platform_type(&self) -> PlatformType {
        PlatformType::Windows
    }
}

// macOS 平台实现
#[cfg(target_os = "macos")]
mod macos {
    use super::*;

    /// macOS 菜单栏实现
    pub struct MacOSTray {
        /// 状态栏项句柄
        handle: Option<MacOSTrayHandle>,
        /// 是否已初始化
        initialized: bool,
        /// 是否可见
        visible: bool,
        /// 配置
        config: Option<TrayConfig>,
    }

    /// macOS 托盘句柄（模拟）
    struct MacOSTrayHandle {
        _id: u32,
    }

    impl MacOSTray {
        /// 创建新的 macOS 托盘实例
        pub fn new() -> TrayResult<Self> {
            Ok(Self {
                handle: None,
                initialized: false,
                visible: false,
                config: None,
            })
        }

        /// 创建 macOS 状态栏项
        fn create_status_item(&mut self) -> TrayResult<MacOSTrayHandle> {
            // 在实际实现中，这里会调用 macOS API 创建状态栏项
            Ok(MacOSTrayHandle {
                _id: std::process::id(),
            })
        }
    }

    #[async_trait::async_trait]
    impl PlatformTray for MacOSTray {
        async fn initialize(&mut self, config: &TrayConfig) -> TrayResult<()> {
            if self.initialized {
                return Ok(());
            }

            self.config = Some(config.clone());
            self.handle = Some(self.create_status_item()?);
            self.initialized = true;

            Ok(())
        }

        async fn show(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会显示 macOS 状态栏项
            self.visible = true;
            Ok(())
        }

        async fn hide(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会隐藏 macOS 状态栏项
            self.visible = false;
            Ok(())
        }

        async fn set_icon(&mut self, icon: &TrayIcon) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // macOS 推荐使用模板图标
            if let Some(_config) = &self.config {
                // 验证图标是否适合作为模板图标
                if icon.size.0 > 22 || icon.size.1 > 22 {
                    return Err(TrayError::InvalidIconSize {
                        width: icon.size.0,
                        height: icon.size.1,
                        expected_width: 22,
                        expected_height: 22,
                    });
                }
            }

            Ok(())
        }

        async fn set_tooltip(&mut self, tooltip: &str) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会设置 macOS 状态栏项工具提示
            Ok(())
        }

        async fn set_menu(&mut self, menu: &TrayMenu) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会创建 macOS 菜单
            Ok(())
        }

        async fn handle_event(&mut self, event: TrayEvent) -> TrayResult<()> {
            // 在实际实现中，这里会处理 macOS 状态栏事件
            Ok(())
        }

        async fn destroy(&mut self) -> TrayResult<()> {
            if let Some(_handle) = self.handle.take() {
                // 在实际实现中，这里会销毁 macOS 状态栏项
            }

            self.initialized = false;
            self.visible = false;
            self.config = None;

            Ok(())
        }

        fn is_available(&self) -> bool {
            // macOS 总是支持状态栏
            true
        }

        fn platform_type(&self) -> PlatformType {
            PlatformType::MacOS
        }
    }
}

#[cfg(target_os = "macos")]
pub use macos::MacOSTray;

#[cfg(not(target_os = "macos"))]
pub struct MacOSTray;

#[cfg(not(target_os = "macos"))]
impl MacOSTray {
    pub fn new() -> TrayResult<Self> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
}

#[cfg(not(target_os = "macos"))]
#[async_trait::async_trait]
impl PlatformTray for MacOSTray {
    async fn initialize(&mut self, _config: &TrayConfig) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn show(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn hide(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn set_icon(&mut self, _icon: &TrayIcon) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn set_tooltip(&mut self, _tooltip: &str) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn set_menu(&mut self, _menu: &TrayMenu) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn handle_event(&mut self, _event: TrayEvent) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    async fn destroy(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("macOS", "系统托盘"))
    }
    fn is_available(&self) -> bool {
        false
    }
    fn platform_type(&self) -> PlatformType {
        PlatformType::MacOS
    }
}

// Linux 平台实现
#[cfg(target_os = "linux")]
mod linux {
    use super::*;

    /// Linux 系统托盘实现
    pub struct LinuxTray {
        /// 托盘句柄
        handle: Option<LinuxTrayHandle>,
        /// 是否已初始化
        initialized: bool,
        /// 是否可见
        visible: bool,
        /// 配置
        config: Option<TrayConfig>,
        /// 桌面环境类型
        desktop_environment: DesktopEnvironment,
    }

    /// Linux 托盘句柄（模拟）
    struct LinuxTrayHandle {
        _id: u32,
        _de_type: DesktopEnvironment,
    }

    /// 桌面环境类型
    #[derive(Debug, Clone, PartialEq)]
    enum DesktopEnvironment {
        Gnome,
        Kde,
        Xfce,
        Unity,
        Other,
    }

    impl LinuxTray {
        /// 创建新的 Linux 托盘实例
        pub fn new() -> TrayResult<Self> {
            let desktop_environment = Self::detect_desktop_environment();
            
            // 检查系统托盘是否可用
            if !Self::is_system_tray_available(&desktop_environment) {
                return Err(TrayError::SystemTrayUnavailable {
                    reason: format!("桌面环境 {:?} 不支持系统托盘", desktop_environment),
                });
            }

            Ok(Self {
                handle: None,
                initialized: false,
                visible: false,
                config: None,
                desktop_environment,
            })
        }

        /// 检测桌面环境
        fn detect_desktop_environment() -> DesktopEnvironment {
            // 在实际实现中，这里会检查环境变量来确定桌面环境
            if let Ok(desktop) = std::env::var("XDG_CURRENT_DESKTOP") {
                match desktop.to_lowercase().as_str() {
                    "gnome" => DesktopEnvironment::Gnome,
                    "kde" => DesktopEnvironment::Kde,
                    "xfce" => DesktopEnvironment::Xfce,
                    "unity" => DesktopEnvironment::Unity,
                    _ => DesktopEnvironment::Other,
                }
            } else {
                DesktopEnvironment::Other
            }
        }

        /// 检查系统托盘是否可用
        fn is_system_tray_available(de: &DesktopEnvironment) -> bool {
            // 在实际实现中，这里会检查系统托盘服务是否运行
            match de {
                DesktopEnvironment::Gnome => {
                    // GNOME 3.26+ 默认不显示系统托盘，需要扩展
                    false // 简化处理
                }
                DesktopEnvironment::Kde | DesktopEnvironment::Xfce | DesktopEnvironment::Unity => true,
                DesktopEnvironment::Other => true, // 假设其他环境支持
            }
        }

        /// 创建 Linux 托盘
        fn create_tray(&mut self) -> TrayResult<LinuxTrayHandle> {
            // 在实际实现中，这里会使用 libappindicator 或 StatusNotifierItem
            Ok(LinuxTrayHandle {
                _id: std::process::id(),
                _de_type: self.desktop_environment.clone(),
            })
        }
    }

    #[async_trait::async_trait]
    impl PlatformTray for LinuxTray {
        async fn initialize(&mut self, config: &TrayConfig) -> TrayResult<()> {
            if self.initialized {
                return Ok(());
            }

            self.config = Some(config.clone());
            self.handle = Some(self.create_tray()?);
            self.initialized = true;

            Ok(())
        }

        async fn show(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会显示 Linux 系统托盘图标
            self.visible = true;
            Ok(())
        }

        async fn hide(&mut self) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会隐藏 Linux 系统托盘图标
            self.visible = false;
            Ok(())
        }

        async fn set_icon(&mut self, icon: &TrayIcon) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // Linux 系统托盘通常使用 PNG 图标
            if !matches!(icon.format, crate::tray::icons::TrayIconFormat::Png | crate::tray::icons::TrayIconFormat::Svg) {
                return Err(TrayError::UnsupportedIconFormat {
                    format: format!("{:?}", icon.format),
                    supported_formats: vec!["png".to_string(), "svg".to_string()],
                });
            }

            Ok(())
        }

        async fn set_tooltip(&mut self, tooltip: &str) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会设置 Linux 系统托盘工具提示
            Ok(())
        }

        async fn set_menu(&mut self, menu: &TrayMenu) -> TrayResult<()> {
            if !self.initialized {
                return Err(TrayError::invalid_state("initialized", "uninitialized"));
            }

            // 在实际实现中，这里会创建 Linux 上下文菜单
            Ok(())
        }

        async fn handle_event(&mut self, event: TrayEvent) -> TrayResult<()> {
            // 在实际实现中，这里会处理 Linux 系统托盘事件
            Ok(())
        }

        async fn destroy(&mut self) -> TrayResult<()> {
            if let Some(_handle) = self.handle.take() {
                // 在实际实现中，这里会销毁 Linux 系统托盘图标
            }

            self.initialized = false;
            self.visible = false;
            self.config = None;

            Ok(())
        }

        fn is_available(&self) -> bool {
            Self::is_system_tray_available(&self.desktop_environment)
        }

        fn platform_type(&self) -> PlatformType {
            PlatformType::Linux
        }
    }
}

#[cfg(target_os = "linux")]
pub use linux::LinuxTray;

#[cfg(not(target_os = "linux"))]
pub struct LinuxTray;

#[cfg(not(target_os = "linux"))]
impl LinuxTray {
    pub fn new() -> TrayResult<Self> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
}

#[cfg(not(target_os = "linux"))]
#[async_trait::async_trait]
impl PlatformTray for LinuxTray {
    async fn initialize(&mut self, _config: &TrayConfig) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn show(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn hide(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn set_icon(&mut self, _icon: &TrayIcon) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn set_tooltip(&mut self, _tooltip: &str) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn set_menu(&mut self, _menu: &TrayMenu) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn handle_event(&mut self, _event: TrayEvent) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    async fn destroy(&mut self) -> TrayResult<()> {
        Err(TrayError::unsupported_platform("Linux", "系统托盘"))
    }
    fn is_available(&self) -> bool {
        false
    }
    fn platform_type(&self) -> PlatformType {
        PlatformType::Linux
    }
}

// 为不支持的平台提供空实现
#[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
mod unsupported {
    use super::*;

    /// 不支持的平台实现
    pub struct UnsupportedTray;

    impl UnsupportedTray {
        pub fn new() -> TrayResult<Self> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "系统托盘",
            ))
        }
    }

    #[async_trait::async_trait]
    impl PlatformTray for UnsupportedTray {
        async fn initialize(&mut self, _config: &TrayConfig) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "初始化",
            ))
        }

        async fn show(&mut self) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "显示",
            ))
        }

        async fn hide(&mut self) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "隐藏",
            ))
        }

        async fn set_icon(&mut self, _icon: &TrayIcon) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "设置图标",
            ))
        }

        async fn set_tooltip(&mut self, _tooltip: &str) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "设置工具提示",
            ))
        }

        async fn set_menu(&mut self, _menu: &TrayMenu) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "设置菜单",
            ))
        }

        async fn handle_event(&mut self, _event: TrayEvent) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "处理事件",
            ))
        }

        async fn destroy(&mut self) -> TrayResult<()> {
            Err(TrayError::unsupported_platform(
                PlatformType::Unknown.name(),
                "销毁",
            ))
        }

        fn is_available(&self) -> bool {
            false
        }

        fn platform_type(&self) -> PlatformType {
            PlatformType::Unknown
        }
    }
}

#[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
pub use unsupported::UnsupportedTray;

/// 获取当前平台类型
pub fn get_current_platform() -> PlatformType {
    PlatformType::detect()
}

/// 检查当前平台是否支持系统托盘
pub fn is_tray_supported() -> bool {
    match get_current_platform() {
        PlatformType::Windows | PlatformType::MacOS | PlatformType::Linux => true,
        PlatformType::Unknown => false,
    }
}

/// 获取平台特定信息
pub fn get_platform_info() -> HashMap<String, String> {
    let mut info = HashMap::new();
    
    let platform = get_current_platform();
    info.insert("platform".to_string(), format!("{:?}", platform));
    info.insert("supported".to_string(), is_tray_supported().to_string());
    
    match platform {
        PlatformType::Windows => {
            info.insert("version".to_string(), "Windows".to_string());
            info.insert("icon_formats".to_string(), "ICO,PNG".to_string());
            info.insert("recommended_size".to_string(), "16x16".to_string());
        }
        PlatformType::MacOS => {
            info.insert("version".to_string(), "macOS".to_string());
            info.insert("icon_formats".to_string(), "PNG,ICNS".to_string());
            info.insert("recommended_size".to_string(), "22x22".to_string());
        }
        PlatformType::Linux => {
            info.insert("version".to_string(), "Linux".to_string());
            info.insert("icon_formats".to_string(), "PNG,SVG".to_string());
            info.insert("recommended_size".to_string(), "24x24".to_string());
            
            // 检测桌面环境
            if let Ok(desktop) = std::env::var("XDG_CURRENT_DESKTOP") {
                info.insert("desktop_environment".to_string(), desktop);
            }
        }
        PlatformType::Unknown => {
            info.insert("version".to_string(), "Unknown".to_string());
            info.insert("icon_formats".to_string(), "".to_string());
            info.insert("recommended_size".to_string(), "".to_string());
        }
    }
    
    info
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_platform_detection() {
        let platform = PlatformType::detect();
        assert!(matches!(
            platform,
            PlatformType::Windows | PlatformType::MacOS | PlatformType::Linux | PlatformType::Unknown
        ));
    }

    #[test]
    fn test_platform_properties() {
        let platform = PlatformType::Windows;
        assert_eq!(platform.name(), "Windows");
        assert_eq!(platform.preferred_icon_format(), "ico");
        assert_eq!(platform.preferred_icon_size(), (16, 16));
        assert!(platform.supports_system_tray());

        let platform = PlatformType::MacOS;
        assert_eq!(platform.name(), "macOS");
        assert_eq!(platform.preferred_icon_format(), "icns");
        assert_eq!(platform.preferred_icon_size(), (22, 22));

        let platform = PlatformType::Linux;
        assert_eq!(platform.name(), "Linux");
        assert_eq!(platform.preferred_icon_format(), "png");
        assert_eq!(platform.preferred_icon_size(), (24, 24));
    }

    #[test]
    fn test_factory_support_check() {
        let is_supported = PlatformTrayFactory::is_supported();
        let platform = PlatformType::detect();
        assert_eq!(is_supported, platform.supports_system_tray());
    }

    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    #[tokio::test]
    async fn test_platform_tray_creation() {
        let result = PlatformTrayFactory::create_tray();
        
        // 在支持的平台上应该能够创建托盘实例
        if PlatformTrayFactory::is_supported() {
            assert!(result.is_ok());
            let tray = result.unwrap();
            assert!(tray.is_available());
        }
    }
}