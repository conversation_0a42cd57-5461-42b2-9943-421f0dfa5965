/// 托盘状态管理模块
/// 
/// 提供托盘的状态管理和生命周期控制，包括：
/// - 托盘状态跟踪
/// - 状态变更通知
/// - 状态持久化
/// - 状态恢复机制

use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use serde::{Deserialize, Serialize};
use tokio::sync::broadcast;
use crate::tray::errors::{TrayError, TrayResult};

/// 托盘状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrayStatus {
    /// 未初始化
    Uninitialized,
    /// 初始化中
    Initializing,
    /// 已初始化但未显示
    Initialized,
    /// 显示中
    Visible,
    /// 隐藏中
    Hidden,
    /// 错误状态
    Error(String),
    /// 正在销毁
    Destroying,
    /// 已销毁
    Destroyed,
}

impl TrayStatus {
    /// 检查状态是否为活动状态
    pub fn is_active(&self) -> bool {
        matches!(self, Self::Visible | Self::Hidden)
    }

    /// 检查状态是否为错误状态
    pub fn is_error(&self) -> bool {
        matches!(self, Self::Error(_))
    }

    /// 检查状态是否可以进行操作
    pub fn is_operational(&self) -> bool {
        matches!(
            self,
            Self::Initialized | Self::Visible | Self::Hidden
        )
    }

    /// 获取状态的字符串表示
    pub fn as_str(&self) -> &str {
        match self {
            Self::Uninitialized => "uninitialized",
            Self::Initializing => "initializing",
            Self::Initialized => "initialized",
            Self::Visible => "visible",
            Self::Hidden => "hidden",
            Self::Error(_) => "error",
            Self::Destroying => "destroying",
            Self::Destroyed => "destroyed",
        }
    }
}

impl std::fmt::Display for TrayStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Error(msg) => write!(f, "error: {}", msg),
            _ => write!(f, "{}", self.as_str()),
        }
    }
}

/// 状态变更事件
#[derive(Debug, Clone)]
pub struct StateChangeEvent {
    /// 事件ID
    pub id: String,
    /// 之前的状态
    pub previous_state: TrayStatus,
    /// 当前状态
    pub current_state: TrayStatus,
    /// 变更时间戳
    pub timestamp: SystemTime,
    /// 变更原因
    pub reason: Option<String>,
}

impl StateChangeEvent {
    /// 创建新的状态变更事件
    pub fn new(
        previous_state: TrayStatus,
        current_state: TrayStatus,
        reason: Option<String>,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            previous_state,
            current_state,
            timestamp: SystemTime::now(),
            reason,
        }
    }
}

/// 托盘状态配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayStateConfig {
    /// 是否启用状态持久化
    pub enable_persistence: bool,
    /// 状态文件路径
    pub state_file_path: Option<String>,
    /// 状态历史记录最大数量
    pub max_history_size: usize,
    /// 自动保存间隔（秒）
    pub auto_save_interval: u64,
    /// 是否启用状态变更通知
    pub enable_change_notifications: bool,
}

impl Default for TrayStateConfig {
    fn default() -> Self {
        Self {
            enable_persistence: true,
            state_file_path: None,
            max_history_size: 100,
            auto_save_interval: 30,
            enable_change_notifications: true,
        }
    }
}

/// 状态历史记录项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateHistoryItem {
    /// 状态
    state: TrayStatus,
    /// 时间戳
    timestamp: SystemTime,
    /// 持续时间（如果已结束）
    duration: Option<Duration>,
    /// 变更原因
    reason: Option<String>,
}

/// 托盘状态管理器
#[derive(Debug)]
pub struct TrayState {
    /// 当前状态
    current_state: Arc<RwLock<TrayStatus>>,
    /// 状态历史记录
    state_history: Arc<RwLock<Vec<StateHistoryItem>>>,
    /// 状态变更事件发送器
    state_change_sender: broadcast::Sender<StateChangeEvent>,
    /// 配置
    config: TrayStateConfig,
    /// 最后保存时间
    last_save_time: Arc<RwLock<SystemTime>>,
    /// 状态统计
    state_stats: Arc<RwLock<StateStatistics>>,
}

/// 状态统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateStatistics {
    /// 总状态变更次数
    pub total_changes: u64,
    /// 错误状态次数
    pub error_count: u64,
    /// 可见状态总时长
    pub visible_duration: Duration,
    /// 隐藏状态总时长
    pub hidden_duration: Duration,
    /// 平均状态持续时间
    pub average_state_duration: Duration,
    /// 最后更新时间
    pub last_updated: SystemTime,
}

impl Default for StateStatistics {
    fn default() -> Self {
        Self {
            total_changes: 0,
            error_count: 0,
            visible_duration: Duration::ZERO,
            hidden_duration: Duration::ZERO,
            average_state_duration: Duration::ZERO,
            last_updated: SystemTime::now(),
        }
    }
}

impl TrayState {
    /// 创建新的状态管理器
    pub fn new(config: TrayStateConfig) -> Self {
        let (sender, _) = broadcast::channel(100);
        
        Self {
            current_state: Arc::new(RwLock::new(TrayStatus::Uninitialized)),
            state_history: Arc::new(RwLock::new(Vec::new())),
            state_change_sender: sender,
            config,
            last_save_time: Arc::new(RwLock::new(SystemTime::now())),
            state_stats: Arc::new(RwLock::new(StateStatistics::default())),
        }
    }

    /// 使用默认配置创建状态管理器
    pub fn with_default_config() -> Self {
        Self::new(TrayStateConfig::default())
    }

    /// 获取当前状态
    pub fn get_current_state(&self) -> TrayStatus {
        self.current_state.read().unwrap().clone()
    }

    /// 设置状态
    pub async fn set_state(&self, new_state: TrayStatus, reason: Option<String>) -> TrayResult<()> {
        let previous_state = {
            let mut current = self.current_state.write().unwrap();
            let previous = current.clone();
            *current = new_state.clone();
            previous
        };

        // 更新历史记录
        self.update_history(&previous_state, &new_state, reason.clone()).await?;

        // 更新统计信息
        self.update_statistics(&previous_state, &new_state).await;

        // 发送状态变更事件
        if self.config.enable_change_notifications {
            let event = StateChangeEvent::new(previous_state, new_state, reason);
            let _ = self.state_change_sender.send(event);
        }

        // 自动保存
        if self.config.enable_persistence {
            self.auto_save().await?;
        }

        Ok(())
    }

    /// 订阅状态变更事件
    pub fn subscribe_state_changes(&self) -> broadcast::Receiver<StateChangeEvent> {
        self.state_change_sender.subscribe()
    }

    /// 检查状态转换是否有效
    pub fn is_valid_transition(&self, from: &TrayStatus, to: &TrayStatus) -> bool {
        match (from, to) {
            // 从未初始化只能到初始化中
            (TrayStatus::Uninitialized, TrayStatus::Initializing) => true,
            
            // 从初始化中可以到已初始化或错误
            (TrayStatus::Initializing, TrayStatus::Initialized) => true,
            (TrayStatus::Initializing, TrayStatus::Error(_)) => true,
            
            // 从已初始化可以到显示或错误
            (TrayStatus::Initialized, TrayStatus::Visible) => true,
            (TrayStatus::Initialized, TrayStatus::Error(_)) => true,
            
            // 显示和隐藏之间可以互相切换
            (TrayStatus::Visible, TrayStatus::Hidden) => true,
            (TrayStatus::Hidden, TrayStatus::Visible) => true,
            
            // 从任何操作状态都可以到错误状态
            (_, TrayStatus::Error(_)) => true,
            
            // 从任何状态都可以到销毁中
            (_, TrayStatus::Destroying) => true,
            
            // 从销毁中只能到已销毁
            (TrayStatus::Destroying, TrayStatus::Destroyed) => true,
            
            // 其他转换无效
            _ => false,
        }
    }

    /// 强制设置状态（跳过验证）
    pub async fn force_set_state(&self, new_state: TrayStatus, reason: Option<String>) -> TrayResult<()> {
        self.set_state(new_state, reason).await
    }

    /// 安全设置状态（验证转换）
    pub async fn safe_set_state(&self, new_state: TrayStatus, reason: Option<String>) -> TrayResult<()> {
        let current_state = self.get_current_state();
        
        if !self.is_valid_transition(&current_state, &new_state) {
            return Err(TrayError::invalid_state(
                format!("{}", new_state),
                format!("{}", current_state),
            ));
        }
        
        self.set_state(new_state, reason).await
    }

    /// 获取状态历史记录
    pub fn get_state_history(&self) -> Vec<StateHistoryItem> {
        self.state_history.read().unwrap().clone()
    }

    /// 获取最近的状态历史记录
    pub fn get_recent_history(&self, count: usize) -> Vec<StateHistoryItem> {
        let history = self.state_history.read().unwrap();
        let start = history.len().saturating_sub(count);
        history[start..].to_vec()
    }

    /// 清空状态历史记录
    pub fn clear_history(&self) {
        self.state_history.write().unwrap().clear();
    }

    /// 获取状态统计信息
    pub fn get_statistics(&self) -> StateStatistics {
        self.state_stats.read().unwrap().clone()
    }

    /// 保存状态到文件
    pub async fn save_state(&self) -> TrayResult<()> {
        if !self.config.enable_persistence {
            return Ok(());
        }

        let state_file_path = self.config.state_file_path
            .as_ref()
            .ok_or_else(|| TrayError::configuration_error(
                "state_file_path",
                "状态文件路径未配置",
            ))?;

        let state_data = StateData {
            current_state: self.get_current_state(),
            history: self.get_state_history(),
            statistics: self.get_statistics(),
            saved_at: SystemTime::now(),
        };

        let json_data = serde_json::to_string_pretty(&state_data)?;
        tokio::fs::write(state_file_path, json_data).await?;

        *self.last_save_time.write().unwrap() = SystemTime::now();
        Ok(())
    }

    /// 从文件加载状态
    pub async fn load_state(&self) -> TrayResult<()> {
        if !self.config.enable_persistence {
            return Ok(());
        }

        let state_file_path = self.config.state_file_path
            .as_ref()
            .ok_or_else(|| TrayError::configuration_error(
                "state_file_path",
                "状态文件路径未配置",
            ))?;

        if !tokio::fs::try_exists(state_file_path).await? {
            return Ok(()); // 文件不存在，跳过加载
        }

        let json_data = tokio::fs::read_to_string(state_file_path).await?;
        let state_data: StateData = serde_json::from_str(&json_data)?;

        // 恢复状态（但不恢复当前状态，因为需要重新初始化）
        *self.state_history.write().unwrap() = state_data.history;
        *self.state_stats.write().unwrap() = state_data.statistics;

        Ok(())
    }

    /// 重置状态管理器
    pub async fn reset(&self) -> TrayResult<()> {
        *self.current_state.write().unwrap() = TrayStatus::Uninitialized;
        self.clear_history();
        *self.state_stats.write().unwrap() = StateStatistics::default();
        
        if self.config.enable_persistence {
            self.save_state().await?;
        }
        
        Ok(())
    }

    /// 更新历史记录
    async fn update_history(
        &self,
        previous_state: &TrayStatus,
        new_state: &TrayStatus,
        reason: Option<String>,
    ) -> TrayResult<()> {
        let mut history = self.state_history.write().unwrap();
        
        // 更新上一个状态的持续时间
        if let Some(last_item) = history.last_mut() {
            if last_item.duration.is_none() {
                last_item.duration = Some(
                    SystemTime::now()
                        .duration_since(last_item.timestamp)
                        .unwrap_or_default()
                );
            }
        }

        // 添加新的历史记录项
        let new_item = StateHistoryItem {
            state: new_state.clone(),
            timestamp: SystemTime::now(),
            duration: None,
            reason,
        };
        
        history.push(new_item);

        // 限制历史记录大小
        if history.len() > self.config.max_history_size {
            history.remove(0);
        }

        Ok(())
    }

    /// 更新统计信息
    async fn update_statistics(&self, previous_state: &TrayStatus, new_state: &TrayStatus) {
        let mut stats = self.state_stats.write().unwrap();
        
        stats.total_changes += 1;
        stats.last_updated = SystemTime::now();

        if new_state.is_error() {
            stats.error_count += 1;
        }

        // 更新状态持续时间统计
        if let Some(last_item) = self.state_history.read().unwrap().last() {
            if let Some(duration) = last_item.duration {
                match previous_state {
                    TrayStatus::Visible => {
                        stats.visible_duration += duration;
                    }
                    TrayStatus::Hidden => {
                        stats.hidden_duration += duration;
                    }
                    _ => {}
                }

                // 更新平均持续时间
                let total_duration = stats.visible_duration + stats.hidden_duration;
                if stats.total_changes > 0 {
                    stats.average_state_duration = total_duration / stats.total_changes as u32;
                }
            }
        }
    }

    /// 自动保存
    async fn auto_save(&self) -> TrayResult<()> {
        let last_save = *self.last_save_time.read().unwrap();
        let now = SystemTime::now();
        
        if let Ok(elapsed) = now.duration_since(last_save) {
            if elapsed.as_secs() >= self.config.auto_save_interval {
                self.save_state().await?;
            }
        }
        
        Ok(())
    }
}

/// 状态数据（用于序列化）
#[derive(Debug, Serialize, Deserialize)]
struct StateData {
    current_state: TrayStatus,
    history: Vec<StateHistoryItem>,
    statistics: StateStatistics,
    saved_at: SystemTime,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[test]
    fn test_tray_status_methods() {
        assert!(TrayStatus::Visible.is_active());
        assert!(TrayStatus::Hidden.is_active());
        assert!(!TrayStatus::Uninitialized.is_active());

        assert!(TrayStatus::Error("test".to_string()).is_error());
        assert!(!TrayStatus::Visible.is_error());

        assert!(TrayStatus::Visible.is_operational());
        assert!(!TrayStatus::Uninitialized.is_operational());
    }

    #[test]
    fn test_state_transitions() {
        let state = TrayState::with_default_config();

        // 有效转换
        assert!(state.is_valid_transition(
            &TrayStatus::Uninitialized,
            &TrayStatus::Initializing
        ));
        assert!(state.is_valid_transition(
            &TrayStatus::Visible,
            &TrayStatus::Hidden
        ));

        // 无效转换
        assert!(!state.is_valid_transition(
            &TrayStatus::Uninitialized,
            &TrayStatus::Visible
        ));
        assert!(!state.is_valid_transition(
            &TrayStatus::Destroyed,
            &TrayStatus::Visible
        ));
    }

    #[tokio::test]
    async fn test_state_management() {
        let state = TrayState::with_default_config();

        // 初始状态
        assert_eq!(state.get_current_state(), TrayStatus::Uninitialized);

        // 设置状态
        state.set_state(TrayStatus::Initializing, Some("开始初始化".to_string()))
            .await
            .unwrap();
        assert_eq!(state.get_current_state(), TrayStatus::Initializing);

        // 检查历史记录
        let history = state.get_state_history();
        assert_eq!(history.len(), 1);
        assert_eq!(history[0].state, TrayStatus::Initializing);
    }

    #[tokio::test]
    async fn test_safe_state_transitions() {
        let state = TrayState::with_default_config();

        // 有效转换
        state.safe_set_state(TrayStatus::Initializing, None)
            .await
            .unwrap();

        // 无效转换应该失败
        let result = state.safe_set_state(TrayStatus::Visible, None).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_state_change_events() {
        let state = TrayState::with_default_config();
        let mut receiver = state.subscribe_state_changes();

        // 设置状态
        state.set_state(TrayStatus::Initializing, Some("测试".to_string()))
            .await
            .unwrap();

        // 接收事件
        let event = receiver.recv().await.unwrap();
        assert_eq!(event.previous_state, TrayStatus::Uninitialized);
        assert_eq!(event.current_state, TrayStatus::Initializing);
        assert_eq!(event.reason, Some("测试".to_string()));
    }

    #[tokio::test]
    async fn test_history_management() {
        let mut config = TrayStateConfig::default();
        config.max_history_size = 2;
        let state = TrayState::new(config);

        // 添加多个状态变更
        state.set_state(TrayStatus::Initializing, None).await.unwrap();
        state.set_state(TrayStatus::Initialized, None).await.unwrap();
        state.set_state(TrayStatus::Visible, None).await.unwrap();

        // 检查历史记录限制
        let history = state.get_state_history();
        assert!(history.len() <= 2);
    }

    #[tokio::test]
    async fn test_statistics_update() {
        let state = TrayState::with_default_config();

        // 进行一些状态变更
        state.set_state(TrayStatus::Initializing, None).await.unwrap();
        state.set_state(TrayStatus::Error("测试错误".to_string()), None).await.unwrap();

        let stats = state.get_statistics();
        assert_eq!(stats.total_changes, 2);
        assert_eq!(stats.error_count, 1);
    }
}