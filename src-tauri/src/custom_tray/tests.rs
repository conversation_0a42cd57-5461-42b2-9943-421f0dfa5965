/// 托盘模块测试
/// 
/// 提供全面的测试覆盖，包括单元测试、集成测试、性能测试和边界条件测试

#[cfg(test)]
mod tests {
    use std::time::Duration;
    use std::sync::Arc;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use tokio::time::Instant;

    use crate::tray::{
        config::{TrayConfig, TrayConfigBuilder},
        errors::{TrayError, ErrorSeverity},
        events::{TrayEvent, TrayEventType, TrayEventHandler, SimpleEventHandler},
        icons::{TrayIcon, TrayIconFormat, TrayIconType, TrayIconManager},
        manager::{TrayManager, TrayManagerBuilder},
        menu::{TrayMenu, TrayMenuItem, TrayMenuBuilder},
        platform::PlatformTrayFactory,
        state::{TrayStatus, TrayState},
    };

    /// 基础功能测试
    mod basic_tests {
        use super::*;

        #[tokio::test]
        async fn test_tray_manager_creation() {
            if !PlatformTrayFactory::is_supported() {
                println!("跳过测试：当前平台不支持系统托盘");
                return;
            }

            let config = TrayConfigBuilder::new()
                .title("测试托盘")
                .icon_data("test_icon_data".to_string())
                .build()
                .expect("构建配置失败");

            let manager = TrayManager::new(config).await;
            assert!(manager.is_ok());

            let manager = manager.unwrap();
            assert_eq!(manager.get_current_status(), TrayStatus::Initialized);
            assert!(!manager.is_running());
        }

        #[tokio::test]
        async fn test_icon_management() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            // 先初始化托盘
            manager.initialize().await.unwrap();

            let icon = TrayIcon::new(
                "test_icon",
                TrayIconType::Default,
                TrayIconFormat::Png,
                create_test_png_data(),
                (16, 16),
            );

            let result = manager.set_icon(icon).await;
            assert!(result.is_ok());
        }

        #[tokio::test]
        async fn test_menu_creation() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            // 先初始化托盘
            manager.initialize().await.unwrap();

            let menu = TrayMenuBuilder::new()
                .add_item(TrayMenuItem::new("show", "显示")).unwrap()
                .add_separator().unwrap()
                .add_item(TrayMenuItem::new("quit", "退出")).unwrap()
                .build().unwrap();

            let result = manager.set_menu(menu).await;
            assert!(result.is_ok());
        }

        #[tokio::test]
        async fn test_event_handling() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            let handler = Box::new(SimpleEventHandler::new(
                "test_handler",
                vec![TrayEventType::LeftClick],
                100,
            ));

            let result = manager.add_event_handler(handler).await;
            assert!(result.is_ok());

            let event = TrayEvent::new(TrayEventType::LeftClick);
            let result = manager.send_event(event).await;
            assert!(result.is_ok());
        }
    }

    /// 生命周期测试
    mod lifecycle_tests {
        use super::*;

        #[tokio::test]
        async fn test_complete_lifecycle() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .title("生命周期测试")
                .icon_data("test_icon_data".to_string())
                .build().unwrap();

            let manager = TrayManager::new(config).await.unwrap();

            // 初始化
            manager.initialize().await.unwrap();

            // 显示
            manager.show().await.unwrap();
            assert_eq!(manager.get_current_status(), TrayStatus::Visible);

            // 隐藏
            manager.hide().await.unwrap();
            assert_eq!(manager.get_current_status(), TrayStatus::Hidden);

            // 销毁
            manager.destroy().await.unwrap();
            assert_eq!(manager.get_current_status(), TrayStatus::Destroyed);
        }

        #[tokio::test]
        async fn test_state_transitions() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            // 先初始化托盘
            manager.initialize().await.unwrap();

            // 测试状态转换序列
            let transitions = vec![
                (TrayStatus::Initialized, "show", TrayStatus::Visible),
                (TrayStatus::Visible, "hide", TrayStatus::Hidden),
                (TrayStatus::Hidden, "show", TrayStatus::Visible),
            ];

            for (expected_current, action, expected_next) in transitions {
                assert_eq!(manager.get_current_status(), expected_current);

                match action {
                    "show" => manager.show().await.unwrap(),
                    "hide" => manager.hide().await.unwrap(),
                    _ => {}
                }

                assert_eq!(manager.get_current_status(), expected_next);
            }
        }
    }

    /// 错误处理测试
    mod error_tests {
        use super::*;

        #[test]
        fn test_error_types() {
            let errors = vec![
                TrayError::initialization_failed("测试"),
                TrayError::icon_load_failed("测试", "测试原因"),
                TrayError::menu_creation_failed("测试"),
                TrayError::unsupported_platform("测试平台", "测试操作"),
                TrayError::configuration_error("测试字段", "测试原因"),
            ];

            for error in errors {
                let error_string = format!("{}", error);
                assert!(!error_string.is_empty());

                let severity = error.severity();
                assert!(matches!(severity, 
                    ErrorSeverity::Low | ErrorSeverity::Medium | 
                    ErrorSeverity::High | ErrorSeverity::Critical));
            }
        }

        #[tokio::test]
        async fn test_invalid_icon_handling() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            // 测试空图标数据
            let invalid_icon = TrayIcon::new(
                "invalid",
                TrayIconType::Default,
                TrayIconFormat::Png,
                vec![], // 空数据
                (16, 16),
            );

            let result = manager.set_icon(invalid_icon).await;
            assert!(result.is_err());

            // 验证系统仍然可用
            assert!(manager.is_running() || manager.get_current_status() == TrayStatus::Initialized);
        }
    }

    /// 并发测试
    mod concurrency_tests {
        use super::*;

        #[tokio::test]
        async fn test_concurrent_operations() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = Arc::new(TrayManager::new(config).await.unwrap());

            manager.initialize().await.unwrap();

            let mut tasks = Vec::new();

            // 并发图标设置
            for i in 0..5 {
                let manager = manager.clone();
                tasks.push(tokio::spawn(async move {
                    let icon = TrayIcon::new(
                        format!("icon_{}", i),
                        TrayIconType::Default,
                        TrayIconFormat::Png,
                        create_test_png_data(),
                        (16, 16),
                    );
                    manager.set_icon(icon).await
                }));
            }

            // 并发事件发送
            for i in 0..10 {
                let manager = manager.clone();
                tasks.push(tokio::spawn(async move {
                    let event = TrayEvent::new(TrayEventType::LeftClick)
                        .with_mouse_position((i, i));
                    manager.send_event(event).await
                }));
            }

            // 等待所有任务完成
            let results = futures::future::join_all(tasks).await;
            
            // 检查大部分操作成功
            let success_count = results.iter()
                .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
                .count();
            
            assert!(success_count > 10); // 至少大部分操作成功
        }
    }

    /// 性能测试
    mod performance_tests {
        use super::*;

        #[tokio::test]
        async fn test_event_processing_performance() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = Arc::new(TrayManager::new(config).await.unwrap());
            
            // 添加计数器处理器
            let event_counter = Arc::new(AtomicUsize::new(0));
            let counter_clone = event_counter.clone();
            
            let handler = Box::new(CountingEventHandler::new(
                "counter",
                vec![TrayEventType::LeftClick],
                counter_clone,
            ));
            manager.add_event_handler(handler).await.unwrap();

            // 初始化并启动事件循环
            manager.initialize().await.unwrap();

            let start_time = Instant::now();
            let event_count = 100;

            // 发送事件
            for i in 0..event_count {
                let event = TrayEvent::new(TrayEventType::LeftClick)
                    .with_mouse_position((i % 50, i % 50));
                manager.send_event(event).await.unwrap();
            }

            // 等待处理完成
            let timeout_duration = Duration::from_secs(2);
            let start_wait = Instant::now();
            
            while event_counter.load(Ordering::Relaxed) < event_count as usize {
                if start_wait.elapsed() > timeout_duration {
                    break;
                }
                tokio::time::sleep(Duration::from_millis(10)).await;
            }

            let elapsed = start_time.elapsed();
            let processed_count = event_counter.load(Ordering::Relaxed);

            println!("处理 {} 个事件耗时: {:?}", processed_count, elapsed);
            
            // 验证性能指标
            assert!(processed_count >= (event_count * 80 / 100) as usize); // 至少80%的事件被处理
            assert!(elapsed < Duration::from_secs(1)); // 总时间不超过1秒
        }

        #[tokio::test]
        async fn test_memory_stability() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            // 创建和销毁多个托盘实例
            for i in 0..3 {
                let config = TrayConfigBuilder::new()
                    .title(format!("内存测试 {}", i))
                    .icon_data("test_icon_data".to_string())
                    .build().unwrap();
                
                let manager = TrayManager::new(config).await.unwrap();
                manager.initialize().await.unwrap();
                
                // 添加一些图标
                for j in 0..5 {
                    let icon = TrayIcon::new(
                        format!("icon_{}_{}", i, j),
                        TrayIconType::Default,
                        TrayIconFormat::Png,
                        create_test_png_data(),
                        (16, 16),
                    );
                    let _ = manager.set_icon(icon).await;
                }
                
                manager.show().await.unwrap();
                manager.destroy().await.unwrap();
                
                // 短暂休息
                tokio::time::sleep(Duration::from_millis(50)).await;
            }
        }
    }

    /// 边界条件测试
    mod edge_case_tests {
        use super::*;

        #[test]
        fn test_extreme_configuration_values() {
            // 测试极长字符串
            let very_long_string = "x".repeat(10000);
            let config = TrayConfigBuilder::new()
                .title(&very_long_string)
                .build();
            
            // 验证应该失败或被截断
            assert!(config.is_err() || config.unwrap().title.len() <= 1000);

            // 测试Unicode字符
            let unicode_config = TrayConfigBuilder::new()
                .title("🚀🎉💻测试")
                .icon_data("test_icon_data".to_string())
                .build();
            
            assert!(unicode_config.is_ok());

            // 测试空白字符
            let whitespace_config = TrayConfigBuilder::new()
                .title("   \t\n\r   ")
                .build();
            
            // 应该失败，因为只包含空白字符
            assert!(whitespace_config.is_err());
        }

        #[tokio::test]
        async fn test_invalid_icon_data_scenarios() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();

            // 测试各种无效图标数据
            let invalid_icon_tests = vec![
                ("empty_data", vec![]),
                ("single_byte", vec![0x00]),
                ("random_data", (0..50).map(|i| (i * 7) as u8).collect()),
                ("wrong_png_header", vec![0x00, 0x50, 0x4E, 0x47]),
            ];

            for (test_name, data) in invalid_icon_tests {
                let icon = TrayIcon::new(
                    test_name,
                    TrayIconType::Default,
                    TrayIconFormat::Png,
                    data,
                    (16, 16),
                );

                let result = manager.set_icon(icon).await;
                
                // 大部分应该失败，但系统应该保持稳定
                if result.is_err() {
                    println!("预期的错误 - {}: {:?}", test_name, result.unwrap_err());
                }
                
                // 验证系统仍然可用
                assert!(manager.is_running() || manager.get_current_status() != TrayStatus::Destroyed);
            }
        }

        #[tokio::test]
        async fn test_extreme_mouse_positions() {
            if !PlatformTrayFactory::is_supported() {
                return;
            }

            let config = TrayConfigBuilder::new()
                .icon_data("test_icon_data".to_string())
                .build().unwrap();
            let manager = TrayManager::new(config).await.unwrap();
            manager.initialize().await.unwrap();

            // 测试极端鼠标位置
            let extreme_positions = vec![
                (i32::MIN, i32::MIN),
                (i32::MAX, i32::MAX),
                (-1000000, -1000000),
                (1000000, 1000000),
                (0, 0),
            ];

            for (x, y) in extreme_positions {
                let event = TrayEvent::new(TrayEventType::LeftClick)
                    .with_mouse_position((x, y));
                
                let result = manager.send_event(event).await;
                assert!(result.is_ok(), "极端鼠标位置事件应该被接受: ({}, {})", x, y);
            }
        }
    }

    /// 辅助函数和测试工具
    
    /// 计数事件处理器（用于性能测试）
    struct CountingEventHandler {
        name: String,
        event_types: Vec<TrayEventType>,
        counter: Arc<AtomicUsize>,
    }

    impl CountingEventHandler {
        fn new(name: &str, event_types: Vec<TrayEventType>, counter: Arc<AtomicUsize>) -> Self {
            Self {
                name: name.to_string(),
                event_types,
                counter,
            }
        }
    }

    #[async_trait::async_trait]
    impl TrayEventHandler for CountingEventHandler {
        async fn handle_event(&self, _event: &TrayEvent) -> Result<(), TrayError> {
            self.counter.fetch_add(1, Ordering::Relaxed);
            Ok(())
        }

        fn name(&self) -> &str {
            &self.name
        }

        fn should_handle(&self, event_type: &TrayEventType) -> bool {
            self.event_types.contains(event_type)
        }

        fn priority(&self) -> u32 {
            100
        }
    }

    /// 创建测试用的PNG数据
    fn create_test_png_data() -> Vec<u8> {
        // PNG文件头
        let mut data = vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
        
        // IHDR chunk (简化的16x16 PNG)
        data.extend_from_slice(&[
            0x00, 0x00, 0x00, 0x0D, // chunk length
            0x49, 0x48, 0x44, 0x52, // "IHDR"
            0x00, 0x00, 0x00, 0x10, // width: 16
            0x00, 0x00, 0x00, 0x10, // height: 16
            0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, etc.
            0x90, 0x91, 0x68, 0x36, // CRC
        ]);

        // IEND chunk
        data.extend_from_slice(&[
            0x00, 0x00, 0x00, 0x00, // IEND chunk length
            0x49, 0x45, 0x4E, 0x44, // "IEND"
            0xAE, 0x42, 0x60, 0x82, // CRC
        ]);

        data
    }
}