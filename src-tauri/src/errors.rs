use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum VaultError {
    #[error("Database error: {0}")]
    Database(#[from] rusqlite::Error),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Encryption error: {0}")]
    Encryption(String), // aes_gcm::Error is not std::error::Error compatible directly
    #[error("Decryption error: {0}")]
    Decryption(String),
    #[error("Password hashing error: {0}")]
    Hashing(String), // 修改为 String 类型
    #[error("Password verification failed")]
    PasswordVerificationFailed,
    #[error("Hex decoding error: {0}")]
    HexDecode(#[from] hex::FromHexError),
    #[error("Base64 decoding error: {0}")]
    Base64Decode(#[from] base64::DecodeError),
    #[error("Vault is locked")]
    VaultLocked,
    #[error("Vault not initialized")]
    VaultNotInitialized,
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Configuration error: {0}")]
    ConfigError(String),
    #[error("Internal error: {0}")]
    InternalError(String), // General purpose error
    #[error("Not found: {0}")]
    NotFound(String),
    #[error("Keychain error: {0}")]
    KeychainError(String), // 密钥链相关错误
    #[error("Serialization error: {0}")]
    SerializationError(String), // 序列化错误
    #[error("Authentication error: {0}")]
    AuthenticationError(String), // 认证错误
    #[error("Crypto operation failed: {0}")]
    CryptoOperationFailed(String), // 加密操作失败
    #[error("Invalid key size: {0}")]
    InvalidKeySize(String), // 无效的密钥大小
    #[error("Invalid data: {0}")]
    InvalidData(String), // 无效的数据格式
}

/// 应用错误类型（用于auth模块等）
#[derive(Error, Debug)]
pub enum AppError {
    #[error("Storage error: {0}")]
    Storage(String),
    #[error("Serialization error: {0}")]
    Serialization(String),
    #[error("Crypto error: {0}")]
    Crypto(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Not found: {0}")]
    NotFound(String),
    #[error("Conflict: {0}")]
    Conflict(String),
    #[error("Authentication error: {0}")]
    Authentication(String),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
    #[error("Network error: {0}")]
    Network(String),
}

// Tauri 命令通常需要返回 Result<T, String>，所以我们实现一个转换
impl From<VaultError> for String {
    fn from(error: VaultError) -> Self {
        error.to_string()
    }
}

impl From<AppError> for String {
    fn from(error: AppError) -> Self {
        error.to_string()
    }
}

// Helper to convert aes_gcm::Error to VaultError
impl From<aes_gcm::Error> for VaultError {
    fn from(err: aes_gcm::Error) -> Self {
        VaultError::Encryption(err.to_string()) // Or Decryption depending on context
    }
}

// Helper to convert serde_json::Error to VaultError
impl From<serde_json::Error> for VaultError {
    fn from(err: serde_json::Error) -> Self {
        VaultError::SerializationError(err.to_string())
    }
}

// Helper to convert keyring::Error to VaultError
#[cfg(feature = "keyring")]
impl From<keyring::Error> for VaultError {
    fn from(err: keyring::Error) -> Self {
        VaultError::KeychainError(err.to_string())
    }
}

pub type VaultResult<T> = Result<T, VaultError>;
