use crate::http::interceptor::{
    AuthInterceptor, CompositeInterceptor, DeviceInfoInterceptor, LoggingInterceptor,
};
use crate::http::types::{
    HttpError, HttpMethod, HttpRequest, HttpResponse, RequestBody, ResponseBody,
};
use std::collections::HashMap;
// use std::sync::Arc; // 暂时未使用
use std::time::Duration;
use tauri_plugin_http::reqwest::{Client as ReqwestClient, RequestBuilder};

/// HTTP 客户端构建器
pub struct HttpClientBuilder {
    base_url: Option<String>,
    timeout: Option<Duration>,
    interceptor: CompositeInterceptor,
}

impl Default for HttpClientBuilder {
    fn default() -> Self {
        let mut interceptor = CompositeInterceptor::new();

        // 默认添加设备信息拦截器（优先级最高，确保所有请求都包含设备信息）
        interceptor.add_request_interceptor(DeviceInfoInterceptor::new());

        // 默认添加日志拦截器
        interceptor.add_request_interceptor(LoggingInterceptor::new());
        interceptor.add_response_interceptor(LoggingInterceptor::new());

        Self {
            base_url: None,
            timeout: Some(Duration::from_secs(30)),
            interceptor,
        }
    }
}

impl HttpClientBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置基础 URL
    pub fn base_url(mut self, base_url: impl Into<String>) -> Self {
        self.base_url = Some(base_url.into());
        self
    }

    /// 设置超时时间
    pub fn timeout(mut self, timeout: Duration) -> Self {
        self.timeout = Some(timeout);
        self
    }

    /// 添加认证拦截器
    pub fn with_auth(mut self, auth_interceptor: AuthInterceptor) -> Self {
        self.interceptor.add_request_interceptor(auth_interceptor);
        self
    }

    /// 构建 HTTP 客户端
    pub fn build(self) -> HttpClient {
        let client = ReqwestClient::builder()
            .timeout(self.timeout.unwrap_or(Duration::from_secs(30)))
            .danger_accept_invalid_certs(true) // 开发环境可能需要
            .build()
            .expect("Failed to build HTTP client");

        HttpClient {
            client,
            base_url: self.base_url,
            interceptor: self.interceptor,
        }
    }
}

/// HTTP 客户端
pub struct HttpClient {
    client: ReqwestClient,
    base_url: Option<String>,
    interceptor: CompositeInterceptor,
}

impl HttpClient {
    /// 创建新的客户端构建器
    pub fn builder() -> HttpClientBuilder {
        HttpClientBuilder::new()
    }

    /// 发送 GET 请求
    pub async fn get<T: ResponseBody>(
        &self,
        url: impl Into<String>,
    ) -> Result<HttpResponse<T>, HttpError> {
        self.request(HttpRequest {
            url: url.into(),
            method: HttpMethod::Get,
            config: Default::default(),
            body: None::<()>,
        })
        .await
    }

    /// 发送 POST 请求
    pub async fn post<T: ResponseBody, B: RequestBody>(
        &self,
        url: impl Into<String>,
        body: B,
    ) -> Result<HttpResponse<T>, HttpError> {
        self.request(HttpRequest {
            url: url.into(),
            method: HttpMethod::Post,
            config: Default::default(),
            body: Some(body),
        })
        .await
    }

    /// 发送请求
    pub async fn request<T: ResponseBody, B: RequestBody>(
        &self,
        request: HttpRequest<B>,
    ) -> Result<HttpResponse<T>, HttpError> {
        // 转换为可拦截的请求格式
        let interceptable_request = HttpRequest {
            url: request.url,
            method: request.method,
            config: request.config,
            body: match request.body {
                Some(body) => Some(
                    serde_json::to_value(&body).map_err(|e| HttpError::SerializationError(e))?,
                ),
                None => None,
            },
        };

        // 应用请求拦截器
        let intercepted_request = self.interceptor.on_request(interceptable_request).await?;

        // 构建完整 URL
        let url = self.build_url(&intercepted_request.url)?;

        // 构建请求
        let mut builder = self.build_request(intercepted_request.method, &url)?;

        // 添加请求头
        for (key, value) in intercepted_request.config.headers {
            builder = builder.header(&key, value);
        }

        // 添加查询参数
        for (key, value) in intercepted_request.config.query_params {
            builder = builder.query(&[(key, value)]);
        }

        // 添加请求体
        if let Some(body) = intercepted_request.body {
            builder = builder
                .header("Content-Type", "application/json")
                .body(serde_json::to_string(&body).map_err(|e| HttpError::SerializationError(e))?);
        }

        // 发送请求
        let response = builder
            .send()
            .await
            .map_err(|e| HttpError::RequestError(e.to_string()))?;

        // 处理响应
        let http_response = self.handle_response(response).await?;

        // 应用响应拦截器
        let intercepted_response = self.interceptor.on_response(http_response).await?;

        // 转换响应体类型
        let typed_response = HttpResponse {
            status: intercepted_response.status,
            headers: intercepted_response.headers,
            body: serde_json::from_value::<T>(intercepted_response.body)
                .map_err(|e| HttpError::SerializationError(e))?,
        };

        Ok(typed_response)
    }

    /// 构建完整 URL
    fn build_url(&self, path: &str) -> Result<String, HttpError> {
        match &self.base_url {
            Some(base_url) => {
                if path.starts_with("http://") || path.starts_with("https://") {
                    Ok(path.to_string())
                } else {
                    let base = base_url.trim_end_matches('/');
                    let path = path.trim_start_matches('/');
                    Ok(format!("{}/{}", base, path))
                }
            }
            None => {
                if path.starts_with("http://") || path.starts_with("https://") {
                    Ok(path.to_string())
                } else {
                    Err(HttpError::RequestError(
                        "URL must be absolute when no base_url is set".to_string(),
                    ))
                }
            }
        }
    }

    /// 构建请求
    fn build_request(&self, method: HttpMethod, url: &str) -> Result<RequestBuilder, HttpError> {
        let builder = match method {
            HttpMethod::Get => self.client.get(url),
            HttpMethod::Post => self.client.post(url),
            HttpMethod::Put => self.client.put(url),
            HttpMethod::Delete => self.client.delete(url),
        };
        Ok(builder)
    }

    /// 处理响应
    async fn handle_response(
        &self,
        response: tauri_plugin_http::reqwest::Response,
    ) -> Result<HttpResponse<serde_json::Value>, HttpError> {
        let status = response.status().as_u16();

        // 获取响应头
        let mut headers = HashMap::new();
        for (key, value) in response.headers() {
            if let Ok(value_str) = value.to_str() {
                headers.insert(key.to_string(), value_str.to_string());
            }
        }

        // 获取响应体
        let body_text = response
            .text()
            .await
            .map_err(|e| HttpError::ResponseError(e.to_string()))?;

        // 解析 JSON
        let body: serde_json::Value = if body_text.is_empty() {
            serde_json::Value::Null
        } else {
            serde_json::from_str(&body_text).map_err(|e| HttpError::SerializationError(e))?
        };

        Ok(HttpResponse {
            status,
            headers,
            body,
        })
    }
}
