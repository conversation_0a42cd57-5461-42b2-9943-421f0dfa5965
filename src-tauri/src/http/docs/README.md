# HTTP 模块

本模块提供了完整的 HTTP 客户端功能，包括自动设备信息头部注入、认证拦截器和日志记录。

## 功能特性

### 设备信息自动注入

所有通过 `HttpClient` 发送的 HTTP 请求都会自动包含以下设备信息头部：

- `X-Device-ID`: 设备唯一标识符（基于硬件的机器UID，使用 machine-uid 库）
- `X-Device-Type`: 设备类型（windows, macos, linux, android, ios, web, unknown）
- `X-App-Version`: 应用版本（从 Cargo.toml 获取）

### 拦截器系统

- **设备信息拦截器** (`DeviceInfoInterceptor`): 自动添加设备相关头部
- **认证拦截器** (`AuthInterceptor`): 自动添加 Authorization 头部
- **日志拦截器** (`LoggingInterceptor`): 记录请求和响应日志

## 使用示例

### 基本 HTTP 客户端

```rust
use crate::http::HttpClientBuilder;

// 创建基本 HTTP 客户端（自动包含设备信息头部）
let client = HttpClientBuilder::new()
    .base_url("https://api.example.com")
    .build();

// 发送 GET 请求
let response = client.get::<serde_json::Value>("/api/status").await?;
```

### 带认证的 HTTP 客户端

```rust
use crate::http::{HttpClientBuilder, AuthInterceptor};

// 创建认证拦截器
let auth_interceptor = AuthInterceptor::new();

// 创建带认证的 HTTP 客户端
let client = HttpClientBuilder::new()
    .base_url("https://api.example.com")
    .with_auth(auth_interceptor)
    .build();

// 发送需要认证的请求
let response = client.post::<ApiResponse, _>("/api/data", request_data).await?;
```

## 自动添加的头部信息

每个 HTTP 请求都会自动包含以下头部：

```http
X-Device-ID: 963E696A-9F67-5F41-9D83-3693A1650162
X-Device-Type: macos
X-App-Version: 0.1.0
```

## 设备信息技术实现

### 设备唯一标识

使用 `machine-uid` 库获取基于硬件的唯一标识符：
- 基于机器硬件特征生成
- 在同一台机器上保持一致
- 跨应用重启持久化
- 如果获取失败，自动回退到 UUID v4

### 设备类型检测

使用编译时条件检测，与 `tauri-plugin-os` 兼容：
- 支持 Windows, macOS, Linux, Android, iOS, Web
- 编译时确定，运行时高效
- 未知平台返回 "unknown"

### 扩展功能（可选）

启用 `tauri-os` 特性可获取更详细的设备信息：

```rust
// 启用 tauri-os 特性后可用
use crate::http::device_info::get_device_info_async;

let detailed_info = get_device_info_async().await?;
println!("平台: {}", detailed_info.platform);
println!("架构: {}", detailed_info.arch);
println!("系统族: {}", detailed_info.family);
```

## 模块结构

- `client.rs`: HTTP 客户端实现
- `device_info.rs`: 设备信息管理器（使用 machine-uid 和 tauri-plugin-os）
- `interceptor.rs`: 请求/响应拦截器
- `types.rs`: HTTP 类型定义
- `integration_test.rs`: 集成测试

## 测试

运行 HTTP 模块测试：

```bash
cargo test http --lib
```

运行设备信息相关测试：

```bash
cargo test device_info --lib
```

运行带 tauri-os 特性的测试：

```bash
cargo test device_info --lib --features tauri-os
```

## 依赖

- `machine-uid`: 获取机器唯一标识
- `tauri-plugin-os`: 平台信息检测（可选特性）
- `uuid`: 备用设备ID生成

## 注意事项

1. **设备 ID 持久化**: 使用 `machine-uid` 库，设备ID基于硬件特征，在同一台机器上保持一致
2. **所有 HTTP 客户端默认包含设备信息拦截器**，无需手动添加
3. **认证拦截器需要手动添加**到需要认证的客户端中
4. **tauri-os 特性**：启用后可获取更详细的平台信息，但为可选功能 