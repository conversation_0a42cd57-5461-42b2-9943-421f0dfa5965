#[cfg(test)]
mod tests {
    use crate::http::{device_headers, HttpClientBuilder};

    /// 模拟 HTTP 服务器响应，用于测试头部信息
    fn _create_mock_response() -> serde_json::Value {
        serde_json::json!({
            "status": "ok",
            "message": "Request received successfully"
        })
    }

    #[tokio::test]
    async fn test_device_info_headers_in_http_client() {
        // 创建 HTTP 客户端
        let _client = HttpClientBuilder::new()
            .base_url("https://httpbin.org")
            .build();

        // 验证设备信息管理器是否正常工作
        let device_info = crate::http::get_global_device_info();

        assert!(!device_info.get_device_id().is_empty());
        assert!(!device_info.get_device_type().is_empty());
        assert!(!device_info.get_app_version().is_empty());

        println!("设备ID: {}", device_info.get_device_id());
        println!("设备类型: {}", device_info.get_device_type());
        println!("应用版本: {}", device_info.get_app_version());

        // 验证头部常量
        assert_eq!(device_headers::DEVICE_ID, "X-Device-ID");
        assert_eq!(device_headers::DEVICE_TYPE, "X-Device-Type");
        assert_eq!(device_headers::APP_VERSION, "X-App-Version");
    }

    #[test]
    fn test_device_info_interceptor_creation() {
        use crate::http::DeviceInfoInterceptor;

        // 验证设备信息拦截器可以正常创建
        let _interceptor = DeviceInfoInterceptor::new();

        // 这里只是验证创建过程不会出错
        // 实际的头部添加逻辑在 on_request 方法中，需要异步测试
        assert!(true);
    }

    #[tokio::test]
    async fn test_device_info_interceptor_adds_headers() {
        use crate::http::interceptor::RequestInterceptor;
        use crate::http::{
            types::{HttpMethod, HttpRequest, RequestConfig},
            DeviceInfoInterceptor,
        };

        // 创建设备信息拦截器
        let interceptor = DeviceInfoInterceptor::new();

        // 创建测试请求（使用默认的 serde_json::Value 类型）
        let request = HttpRequest {
            url: "https://example.com/test".to_string(),
            method: HttpMethod::Get,
            config: RequestConfig::default(),
            body: None,
        };

        // 应用拦截器
        let intercepted_request = interceptor.on_request(request).await.unwrap();

        // 验证设备信息头部是否被添加
        assert!(intercepted_request
            .config
            .headers
            .contains_key("X-Device-ID"));
        assert!(intercepted_request
            .config
            .headers
            .contains_key("X-Device-Type"));
        assert!(intercepted_request
            .config
            .headers
            .contains_key("X-App-Version"));

        // 验证头部值不为空
        let device_id = intercepted_request
            .config
            .headers
            .get("X-Device-ID")
            .unwrap();
        let device_type = intercepted_request
            .config
            .headers
            .get("X-Device-Type")
            .unwrap();
        let app_version = intercepted_request
            .config
            .headers
            .get("X-App-Version")
            .unwrap();

        assert!(!device_id.is_empty());
        assert!(!device_type.is_empty());
        assert!(!app_version.is_empty());

        // 验证应用版本是否正确
        assert_eq!(app_version, "0.1.0");

        // 验证设备类型是否为预期值之一
        let valid_types = [
            "windows", "macos", "linux", "android", "ios", "web", "unknown",
        ];
        assert!(valid_types.contains(&device_type.as_str()));

        println!("拦截器添加的头部信息:");
        println!("  X-Device-ID: {}", device_id);
        println!("  X-Device-Type: {}", device_type);
        println!("  X-App-Version: {}", app_version);
    }

    #[test]
    fn test_http_client_builder_includes_device_interceptor() {
        // 验证 HTTP 客户端构建器默认包含设备信息拦截器
        let _client = HttpClientBuilder::new().build();

        // 这里只是验证构建过程不会出错
        // 实际的拦截器功能需要通过发送请求来测试
        assert!(true);
    }

    #[tokio::test]
    async fn test_auth_service_http_clients_have_device_info() {
        use crate::auth::services::RemoteAuthService;

        // 创建远程认证服务
        let _auth_service = RemoteAuthService::new("https://httpbin.org");

        // 验证服务创建成功
        // 由于 HTTP 客户端构建器默认添加设备信息拦截器，
        // 所以认证服务的所有 HTTP 客户端都应该包含设备信息
        assert!(true);

        println!("远程认证服务创建成功，HTTP 客户端已包含设备信息拦截器");
    }
}
