use serde::{de::DeserializeOwned, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// HTTP 方法枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum HttpMethod {
    Get,
    Post,
    Put,
    Delete,
}

impl From<HttpMethod> for String {
    fn from(method: HttpMethod) -> Self {
        match method {
            HttpMethod::Get => "GET".to_string(),
            HttpMethod::Post => "POST".to_string(),
            HttpMethod::Put => "PUT".to_string(),
            HttpMethod::Delete => "DELETE".to_string(),
        }
    }
}

impl std::fmt::Display for HttpMethod {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HttpMethod::Get => write!(f, "GET"),
            HttpMethod::Post => write!(f, "POST"),
            HttpMethod::Put => write!(f, "PUT"),
            HttpMethod::Delete => write!(f, "DELETE"),
        }
    }
}

/// HTTP 请求配置
#[derive(Debug, Clone)]
pub struct RequestConfig {
    /// 请求超时时间（毫秒）
    pub timeout: Option<u64>,
    /// 请求头
    pub headers: HashMap<String, String>,
    /// 查询参数
    pub query_params: HashMap<String, String>,
}

impl Default for RequestConfig {
    fn default() -> Self {
        Self {
            timeout: Some(30000), // 默认 30 秒超时
            headers: HashMap::new(),
            query_params: HashMap::new(),
        }
    }
}

/// HTTP 请求结构
#[derive(Debug, Clone)]
pub struct HttpRequest<T = serde_json::Value> {
    /// 请求 URL
    pub url: String,
    /// 请求方法
    pub method: HttpMethod,
    /// 请求配置
    pub config: RequestConfig,
    /// 请求体
    pub body: Option<T>,
}

/// HTTP 响应结构
#[derive(Debug, Clone)]
pub struct HttpResponse<T> {
    /// 响应状态码
    pub status: u16,
    /// 响应头
    pub headers: HashMap<String, String>,
    /// 响应体
    pub body: T,
}

/// HTTP 错误类型
#[derive(Error, Debug)]
pub enum HttpError {
    #[error("请求错误: {0}")]
    RequestError(String),

    #[error("响应错误: {0}")]
    ResponseError(String),

    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("超时错误: {0}")]
    TimeoutError(String),

    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 请求体类型约束
pub trait RequestBody: Serialize + Send + Sync {}
impl<T: Serialize + Send + Sync> RequestBody for T {}

/// 响应体类型约束
pub trait ResponseBody: DeserializeOwned + Send + Sync {}
impl<T: DeserializeOwned + Send + Sync> ResponseBody for T {}
