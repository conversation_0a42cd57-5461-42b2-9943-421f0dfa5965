use crate::hybrid_storage::{LoginCredential, VaultInfo};
use crate::state::AppState;
use serde::{Deserialize, Serialize};
use tauri::State;

/// 前端登录凭据输入结构
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginCredentialInput {
    pub name: String,
    pub username: Option<String>,
    pub password: String,
    pub website: Option<String>,
    pub notes: Option<String>,
    pub favorite: bool,
}

/// 前端登录凭据输出结构（与hybrid_storage的LoginCredential兼容）
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginCredentialOutput {
    pub id: i32,
    pub name: String,
    pub username: Option<String>,
    pub password: String, // 解密后的密码
    pub website: Option<String>,
    pub notes: Option<String>,
    pub favorite: bool,
    pub created_at: String, // ISO格式日期字符串
    pub updated_at: String, // ISO格式日期字符串
}

impl From<LoginCredential> for LoginCredentialOutput {
    fn from(credential: LoginCredential) -> Self {
        Self {
            id: credential.id,
            name: credential.name,
            username: credential.username,
            password: credential.password,
            website: credential.website,
            notes: credential.notes,
            favorite: credential.favorite,
            created_at: credential.created_at.to_rfc3339(),
            updated_at: credential.updated_at.to_rfc3339(),
        }
    }
}

/// 获取用户的对称密钥
async fn get_user_symmetric_key(state: &AppState) -> Result<[u8; crate::crypto::KEY_SIZE], String> {
    state
        .get_user_symmetric_key()
        .await
        .map_err(|e| format!("Failed to get symmetric key from keychain: {}", e))
}

/// 获取 ORM 服务的引用
async fn get_orm_service(
    state: &AppState,
) -> Result<tokio::sync::MutexGuard<'_, Option<crate::hybrid_storage::OrmPasswordService>>, String>
{
    Ok(state.orm_service_lock().await)
}

/// 获取默认密码库
async fn get_default_vault(state: &AppState) -> Result<VaultInfo, String> {
    let service_guard = get_orm_service(state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let vaults = service
        .get_all_vaults()
        .await
        .map_err(|e| format!("Failed to get vaults: {}", e))?;

    vaults.first().cloned().ok_or("No vaults found".to_string())
}

/// 获取所有密码库
#[tauri::command]
pub async fn get_all_vaults_hybrid(state: State<'_, AppState>) -> Result<Vec<VaultInfo>, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .get_all_vaults()
        .await
        .map_err(|e| format!("Failed to get vaults: {}", e))
}

/// 创建新密码库
#[tauri::command]
pub async fn create_vault_hybrid(
    name: String,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<VaultInfo, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .create_vault(name, description)
        .await
        .map_err(|e| format!("Failed to create vault: {}", e))
}

/// 更新密码库
#[tauri::command]
pub async fn update_vault_hybrid(
    id: i32,
    name: Option<String>,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<VaultInfo, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .update_vault(id, name, description)
        .await
        .map_err(|e| format!("Failed to update vault: {}", e))
}

/// 删除密码库
#[tauri::command]
pub async fn delete_vault_hybrid(id: i32, state: State<'_, AppState>) -> Result<(), String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .delete_vault(id)
        .await
        .map_err(|e| format!("Failed to delete vault: {}", e))
}

/// 获取指定密码库的所有登录凭据
#[tauri::command]
pub async fn get_login_credentials_by_vault_hybrid(
    vault_id: i32,
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credentials = service
        .get_login_credentials_by_vault_with_key(vault_id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to get credentials: {}", e))?;

    Ok(credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 获取所有登录凭据（使用默认密码库）
#[tauri::command]
pub async fn get_all_login_credentials_hybrid(
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let default_vault = get_default_vault(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credentials = service
        .get_login_credentials_by_vault_with_key(default_vault.id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to get credentials: {}", e))?;

    Ok(credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 根据ID获取登录凭据
#[tauri::command]
pub async fn get_login_credential_by_id_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<Option<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credential = service
        .get_login_credential_by_id_with_key(id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to get credential: {}", e))?;

    Ok(credential.map(LoginCredentialOutput::from))
}

/// 保存新的登录凭据
#[tauri::command]
pub async fn save_login_credential_hybrid(
    vault_id: i32,
    credential: LoginCredentialInput,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let saved_credential = service
        .save_login_credential_with_key(
            vault_id,
            credential.name,
            credential.username,
            credential.password,
            credential.website,
            credential.notes,
            credential.favorite,
            &symmetric_key,
        )
        .await
        .map_err(|e| format!("Failed to save credential: {}", e))?;

    Ok(LoginCredentialOutput::from(saved_credential))
}

/// 保存新的登录凭据（使用默认密码库）
#[tauri::command]
pub async fn add_login_credential_hybrid(
    credential: LoginCredentialInput,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let default_vault = get_default_vault(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let saved_credential = service
        .save_login_credential_with_key(
            default_vault.id,
            credential.name,
            credential.username,
            credential.password,
            credential.website,
            credential.notes,
            credential.favorite,
            &symmetric_key,
        )
        .await
        .map_err(|e| format!("Failed to save credential: {}", e))?;

    Ok(LoginCredentialOutput::from(saved_credential))
}

/// 更新登录凭据
#[tauri::command]
pub async fn update_login_credential_hybrid(
    id: i32,
    name: Option<String>,
    username: Option<String>,
    password: Option<String>,
    website: Option<String>,
    notes: Option<String>,
    favorite: Option<bool>,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let updated_credential = service
        .update_login_credential_with_key(
            id,
            name,
            username,
            password,
            website,
            notes,
            favorite,
            &symmetric_key,
        )
        .await
        .map_err(|e| format!("Failed to update credential: {}", e))?;

    Ok(LoginCredentialOutput::from(updated_credential))
}

/// 删除登录凭据
#[tauri::command]
pub async fn delete_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .delete_login_credential(id)
        .await
        .map_err(|e| format!("Failed to delete credential: {}", e))
}

/// 软删除登录凭据（放入回收站）
#[tauri::command]
pub async fn soft_delete_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let deleted_credential = service
        .soft_delete_login_credential(id)
        .await
        .map_err(|e| format!("Failed to soft delete credential: {}", e))?;

    Ok(LoginCredentialOutput::from(deleted_credential))
}

/// 从回收站恢复登录凭据
#[tauri::command]
pub async fn restore_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let restored_credential = service
        .restore_login_credential_with_key(id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to restore credential: {}", e))?;

    Ok(LoginCredentialOutput::from(restored_credential))
}

/// 获取回收站中的登录凭据
#[tauri::command]
pub async fn get_trash_login_credentials_hybrid(
    vault_id: i32,
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let trash_credentials = service
        .get_trash_login_credentials(vault_id)
        .await
        .map_err(|e| format!("Failed to get trash credentials: {}", e))?;

    Ok(trash_credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 获取所有回收站中的登录凭据（使用默认密码库）
#[tauri::command]
pub async fn get_all_trash_login_credentials_hybrid(
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let default_vault = get_default_vault(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let trash_credentials = service
        .get_trash_login_credentials(default_vault.id)
        .await
        .map_err(|e| format!("Failed to get trash credentials: {}", e))?;

    Ok(trash_credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 永久删除登录凭据（物理删除）
#[tauri::command]
pub async fn permanently_delete_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    service
        .permanently_delete_login_credential(id)
        .await
        .map_err(|e| format!("Failed to permanently delete credential: {}", e))
}

/// 清理过期的已删除项目（超过30天自动永久删除）
#[tauri::command]
pub async fn cleanup_expired_deleted_items_hybrid(
    state: State<'_, AppState>,
) -> Result<u64, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let deleted_count = service
        .cleanup_expired_deleted_items()
        .await
        .map_err(|e| format!("Failed to cleanup expired items: {}", e))?;

    Ok(deleted_count)
}

/// 获取需要永久删除的项目数量
#[tauri::command]
pub async fn get_items_pending_permanent_deletion_count_hybrid(
    state: State<'_, AppState>,
) -> Result<usize, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let count = service
        .get_items_pending_permanent_deletion_count()
        .await
        .map_err(|e| format!("Failed to get pending deletion count: {}", e))?;

    Ok(count)
}

/// 根据域名搜索凭据
#[tauri::command]
pub async fn search_credentials_by_domain_hybrid(
    domain: String,
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credentials = service
        .search_credentials_by_domain_with_key(&domain, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to search credentials: {}", e))?;

    Ok(credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 获取收藏的凭据
#[tauri::command]
pub async fn get_favorite_credentials_hybrid(
    vault_id: i32,
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credentials = service
        .get_favorite_credentials_with_key(vault_id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to get favorite credentials: {}", e))?;

    Ok(credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 获取收藏的凭据（使用默认密码库）
#[tauri::command]
pub async fn get_all_favorite_credentials_hybrid(
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let default_vault = get_default_vault(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let credentials = service
        .get_favorite_credentials_with_key(default_vault.id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to get favorite credentials: {}", e))?;

    Ok(credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 封存登录凭据
#[tauri::command]
pub async fn archive_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let archived_credential = service
        .archive_login_credential(id)
        .await
        .map_err(|e| format!("Failed to archive credential: {}", e))?;

    Ok(LoginCredentialOutput::from(archived_credential))
}

/// 从封存中恢复登录凭据
#[tauri::command]
pub async fn unarchive_login_credential_hybrid(
    id: i32,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String> {
    let symmetric_key = get_user_symmetric_key(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let restored_credential = service
        .unarchive_login_credential_with_key(id, &symmetric_key)
        .await
        .map_err(|e| format!("Failed to unarchive credential: {}", e))?;

    Ok(LoginCredentialOutput::from(restored_credential))
}

/// 获取已封存的登录凭据
#[tauri::command]
pub async fn get_archived_login_credentials_hybrid(
    vault_id: i32,
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let archived_credentials = service
        .get_archived_login_credentials(vault_id)
        .await
        .map_err(|e| format!("Failed to get archived credentials: {}", e))?;

    Ok(archived_credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 获取所有已封存的登录凭据（使用默认密码库）
#[tauri::command]
pub async fn get_all_archived_login_credentials_hybrid(
    state: State<'_, AppState>,
) -> Result<Vec<LoginCredentialOutput>, String> {
    let default_vault = get_default_vault(&state).await?;
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let archived_credentials = service
        .get_archived_login_credentials(default_vault.id)
        .await
        .map_err(|e| format!("Failed to get archived credentials: {}", e))?;

    Ok(archived_credentials
        .into_iter()
        .map(LoginCredentialOutput::from)
        .collect())
}

/// 批量封存登录凭据
#[tauri::command]
pub async fn batch_archive_login_credentials_hybrid(
    ids: Vec<i32>,
    state: State<'_, AppState>,
) -> Result<u64, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let affected_rows = service
        .batch_archive_login_credentials(ids)
        .await
        .map_err(|e| format!("Failed to batch archive credentials: {}", e))?;

    Ok(affected_rows)
}

/// 批量从封存中恢复登录凭据
#[tauri::command]
pub async fn batch_unarchive_login_credentials_hybrid(
    ids: Vec<i32>,
    state: State<'_, AppState>,
) -> Result<u64, String> {
    let service_guard = get_orm_service(&state).await?;
    let service = service_guard
        .as_ref()
        .ok_or("ORM service not initialized")?;

    let affected_rows = service
        .batch_unarchive_login_credentials(ids)
        .await
        .map_err(|e| format!("Failed to batch unarchive credentials: {}", e))?;

    Ok(affected_rows)
}
