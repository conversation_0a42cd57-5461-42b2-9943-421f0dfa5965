use crate::state::AppState;
use serde::{Deserialize, Serialize};
use tauri::{Emitter, Manager, State};

/// ORM 服务状态信息
#[derive(Debug, Serialize, Deserialize)]
pub struct OrmServiceStatus {
    /// 是否已初始化
    pub is_initialized: bool,
    /// 是否可用
    pub is_available: bool,
    /// 加密系统状态
    pub crypto_state: String,
    /// 错误信息（如果有）
    pub error: Option<String>,
}

/// 获取 ORM 服务状态
#[tauri::command]
pub async fn get_orm_service_status(state: State<'_, AppState>) -> Result<OrmServiceStatus, String> {
    log::info!("获取 ORM 服务状态");

    // 检查 ORM 服务是否已初始化
    let service_guard = state.orm_service_lock().await;
    let is_initialized = service_guard.is_some();
    drop(service_guard);

    // 获取加密系统状态
    let crypto_arc = state.crypto_arc();
    let crypto_state = crypto_arc.state().await;
    let crypto_state_str = format!("{:?}", crypto_state);

    // 检查是否可用（已初始化且加密系统已解锁）
    let is_available = is_initialized && matches!(crypto_state, crate::crypto::vault_crypto::CryptoState::Unlocked);

    let status = OrmServiceStatus {
        is_initialized,
        is_available,
        crypto_state: crypto_state_str,
        error: None,
    };

    log::info!("ORM 服务状态: {:?}", status);
    Ok(status)
}

/// 重试 ORM 服务初始化
#[tauri::command]
pub async fn retry_orm_service_initialization(
    app_handle: tauri::AppHandle,
    state: State<'_, AppState>,
) -> Result<String, String> {
    log::info!("手动重试 ORM 服务初始化");

    // 发送重试开始事件
    if let Some(window) = app_handle.get_webview_window("main") {
        let _ = window.emit("orm_initialization_status", serde_json::json!({
            "status": "checking_crypto_system",
            "message": "手动重试 ORM 服务初始化..."
        }));
    }

    // 检查是否已经初始化
    let service_guard = state.orm_service_lock().await;
    if service_guard.is_some() {
        drop(service_guard);
        log::info!("ORM 服务已经初始化，无需重试");
        
        // 发送已初始化事件
        if let Some(window) = app_handle.get_webview_window("main") {
            let _ = window.emit("orm_initialization_status", serde_json::json!({
                "status": "success",
                "message": "ORM 服务已经初始化"
            }));
        }
        
        return Ok("ORM 服务已经初始化".to_string());
    }
    drop(service_guard);

    // 使用异步任务管理器重新初始化
    let handle_clone = app_handle.clone();
    let state_clone = state.inner().clone();
    
    crate::async_handler::AsyncTaskManager::spawn_task(async move {
        match crate::initialize_orm_service(&handle_clone, &state_clone).await {
            Ok(_) => {
                log::info!("ORM 服务重试初始化成功");
            }
            Err(e) => {
                log::error!("ORM 服务重试初始化失败: {}", e);
                
                // 发送失败事件
                if let Some(window) = handle_clone.get_webview_window("main") {
                    let _ = window.emit("orm_initialization_status", serde_json::json!({
                        "status": "database_error",
                        "message": format!("重试初始化失败: {}", e),
                        "error": true
                    }));
                }
            }
        }
    });

    Ok("ORM 服务重试初始化已启动".to_string())
}

/// 清理 ORM 服务（用于重置状态）
#[tauri::command]
pub async fn reset_orm_service(state: State<'_, AppState>) -> Result<String, String> {
    log::info!("重置 ORM 服务状态");

    // 清除当前的 ORM 服务
    let mut service_guard = state.orm_service_lock().await;
    *service_guard = None;
    drop(service_guard);

    log::info!("ORM 服务状态已重置");
    Ok("ORM 服务状态已重置".to_string())
}

/// 检查加密系统是否可以解锁
#[tauri::command]
pub async fn check_crypto_unlock_capability(state: State<'_, AppState>) -> Result<bool, String> {
    let crypto_arc = state.crypto_arc();
    let crypto_state = crypto_arc.state().await;
    
    match crypto_state {
        crate::crypto::vault_crypto::CryptoState::Locked => {
            // 检查是否有存储的密钥信息可以用于解锁
            // 这里可以添加更复杂的逻辑来检查密钥链或其他存储
            Ok(true) // 暂时返回 true，表示可能可以解锁
        }
        crate::crypto::vault_crypto::CryptoState::Unlocked => Ok(true),
        crate::crypto::vault_crypto::CryptoState::Uninitialized => Ok(false),
        crate::crypto::vault_crypto::CryptoState::Error(_) => Ok(false),
    }
} 