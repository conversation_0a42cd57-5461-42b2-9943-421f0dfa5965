# 封存功能实现文档

## 概述

本文档详细描述了密码管理应用中封存功能的完整实现，包括数据库层、后端服务层、API层和前端界面的所有组件。

## 功能特性

### 核心功能
- **单个封存**：将不常用的凭据封存，从主界面隐藏
- **批量封存**：支持选择多个凭据进行批量封存操作
- **封存恢复**：可以随时将封存的凭据恢复到主界面
- **批量恢复**：支持批量恢复多个封存的凭据
- **封存搜索**：在封存页面搜索已封存的凭据
- **安全存储**：封存的凭据仍然加密存储，保证安全性

### 用户体验
- **界面整洁**：封存的凭据不在主界面显示，保持界面整洁
- **快速操作**：提供快捷的封存和恢复操作
- **批量管理**：支持批量选择和操作
- **状态提示**：清晰的状态标识和操作反馈
- **帮助信息**：提供详细的功能说明和使用指导

## 技术架构

### 1. 数据库层

#### 数据库迁移
**文件**: `src-tauri/src/hybrid_storage/migrations/m20241202_000002_add_archive_fields.rs`

```rust
// 为 items 表添加封存相关字段
- is_archived: boolean (默认 false)
- archived_at: timestamp (可为空)

// 性能优化索引
- idx_items_is_archived: 封存状态索引
- idx_items_archived_at: 封存时间索引
```

#### 实体更新
**文件**: `src-tauri/src/hybrid_storage/entities/item.rs`

```rust
pub struct Model {
    // ... 现有字段
    pub is_archived: bool,
    pub archived_at: Option<DateTime>,
}
```

### 2. 数据库管理层

**文件**: `src-tauri/src/hybrid_storage/orm_database.rs`

#### 核心方法
```rust
// 单个封存操作
pub async fn archive_item(&self, id: i32) -> Result<item::Model>
pub async fn unarchive_item(&self, id: i32) -> Result<item::Model>

// 批量封存操作
pub async fn batch_archive_items(&self, ids: Vec<i32>) -> Result<u64>
pub async fn batch_unarchive_items(&self, ids: Vec<i32>) -> Result<u64>

// 查询封存项目
pub async fn get_archived_items(&self, vault_id: i32) -> Result<Vec<item::Model>>
```

#### 查询优化
- 所有主要查询都排除已封存的项目
- 使用索引优化封存状态查询性能
- 支持按封存时间排序

### 3. 业务服务层

**文件**: `src-tauri/src/hybrid_storage/orm_services.rs`

#### 核心服务
```rust
// 封存凭据（返回脱敏信息）
pub async fn archive_login_credential(&self, id: i32) -> ServiceResult<LoginCredential>

// 恢复凭据（解密密码）
pub async fn unarchive_login_credential_with_key(
    &self, id: i32, symmetric_key: &[u8; KEY_SIZE]
) -> ServiceResult<LoginCredential>

// 获取封存列表（脱敏显示）
pub async fn get_archived_login_credentials(&self, vault_id: i32) -> ServiceResult<Vec<LoginCredential>>

// 批量操作
pub async fn batch_archive_login_credentials(&self, ids: Vec<i32>) -> ServiceResult<u64>
pub async fn batch_unarchive_login_credentials(&self, ids: Vec<i32>) -> ServiceResult<u64>
```

#### 安全特性
- 封存的凭据密码显示为 `***ARCHIVED***`
- 恢复时需要对称密钥进行解密
- 支持旧数据的兼容性处理

### 4. API命令层

**文件**: `src-tauri/src/hybrid_storage/commands/hybrid_commands.rs`

#### Tauri命令
```rust
#[tauri::command]
pub async fn archive_login_credential_hybrid(id: i32, state: State<'_, AppState>) -> Result<LoginCredentialOutput, String>

#[tauri::command]
pub async fn unarchive_login_credential_hybrid(id: i32, state: State<'_, AppState>) -> Result<LoginCredentialOutput, String>

#[tauri::command]
pub async fn get_all_archived_login_credentials_hybrid(state: State<'_, AppState>) -> Result<Vec<LoginCredentialOutput>, String>

#[tauri::command]
pub async fn batch_archive_login_credentials_hybrid(ids: Vec<i32>, state: State<'_, AppState>) -> Result<u64, String>

#[tauri::command]
pub async fn batch_unarchive_login_credentials_hybrid(ids: Vec<i32>, state: State<'_, AppState>) -> Result<u64, String>
```

### 5. 前端API层

**文件**: `src/api/hybrid-api.ts`

#### API函数
```typescript
// 单个操作
export async function archiveLoginCredential(id: number): Promise<LoginCredentialOutput>
export async function unarchiveLoginCredential(id: number): Promise<LoginCredentialOutput>

// 查询操作
export async function getAllArchivedLoginCredentials(): Promise<LoginCredentialOutput[]>

// 批量操作
export async function batchArchiveLoginCredentials(ids: number[]): Promise<number>
export async function batchUnarchiveLoginCredentials(ids: number[]): Promise<number>
```

### 6. 状态管理层

**文件**: `src/contexts/HybridCredentialsContext.tsx`

#### 状态扩展
```typescript
interface HybridCredentialsState {
    // ... 现有状态
    archivedCredentials: LoginCredentialOutput[];
}

interface HybridCredentialsActions {
    // ... 现有操作
    loadArchivedCredentials: () => Promise<void>;
    archiveCredential: (id: number) => Promise<void>;
    unarchiveCredential: (id: number) => Promise<void>;
    batchArchiveCredentials: (ids: number[]) => Promise<number>;
    batchUnarchiveCredentials: (ids: number[]) => Promise<number>;
}
```

#### 状态同步
- 封存操作后自动更新本地状态
- 批量操作后重新加载相关数据
- 提供操作成功的用户反馈

### 7. 用户界面层

#### ArchivedView组件
**文件**: `src/components/main/ArchivedView.tsx`

**功能特性**:
- 表格形式展示封存的凭据
- 支持搜索过滤
- 单个和批量恢复操作
- 行选择和批量操作工具栏
- 封存功能说明弹窗

**界面元素**:
- 搜索框：支持按名称、用户名、网站搜索
- 操作按钮：刷新、关于封存、批量恢复
- 数据表格：显示封存凭据的基本信息
- 状态标签：显示封存状态和收藏状态

#### CredentialGrid组件扩展
**文件**: `src/components/main/CredentialGrid.tsx`

**新增功能**:
- 封存按钮：每个凭据卡片添加封存操作
- 确认对话框：封存前的确认提示
- 状态更新：封存后自动从主界面移除

## 数据流程

### 封存流程
1. 用户在主界面点击封存按钮
2. 前端显示确认对话框
3. 调用 `archiveLoginCredential` API
4. 后端更新数据库字段 `is_archived = true, archived_at = now()`
5. 前端更新状态，从主界面移除该凭据
6. 显示成功提示

### 恢复流程
1. 用户在封存页面点击恢复按钮
2. 前端显示确认对话框
3. 调用 `unarchiveLoginCredential` API
4. 后端解密凭据并更新数据库字段 `is_archived = false, archived_at = null`
5. 前端更新状态，将凭据添加到主界面
6. 显示成功提示

### 批量操作流程
1. 用户选择多个凭据
2. 点击批量操作按钮
3. 调用批量API进行数据库更新
4. 重新加载相关数据确保状态同步
5. 显示操作结果

## 安全考虑

### 数据安全
- 封存的凭据仍然使用相同的加密算法存储
- 封存状态不影响数据的加密级别
- 恢复时需要用户的对称密钥进行解密

### 访问控制
- 只有凭据的所有者可以进行封存和恢复操作
- 所有操作都需要用户认证
- 批量操作限制在用户拥有的凭据范围内

### 数据完整性
- 封存操作是原子性的，要么成功要么失败
- 批量操作使用事务确保数据一致性
- 提供数据恢复机制防止意外丢失

## 性能优化

### 数据库优化
- 为封存状态字段添加索引
- 主查询默认排除封存项目，提高查询速度
- 封存页面查询使用专门的索引

### 前端优化
- 使用 React.memo 优化组件渲染
- 批量操作后智能更新状态
- 搜索功能使用防抖优化

### 内存管理
- 封存的凭据不在主界面状态中保存
- 按需加载封存页面数据
- 及时清理不需要的状态

## 用户体验设计

### 操作反馈
- 所有操作都有明确的成功/失败提示
- 批量操作显示影响的项目数量
- 加载状态的可视化反馈

### 界面设计
- 封存按钮使用直观的图标
- 封存状态使用特殊标签显示
- 提供详细的功能说明

### 错误处理
- 网络错误的友好提示
- 操作失败的具体原因说明
- 提供重试机制

## 测试策略

### 单元测试
- 数据库操作的单元测试
- 业务逻辑的单元测试
- API接口的单元测试

### 集成测试
- 前后端集成测试
- 数据库迁移测试
- 完整流程测试

### 用户测试
- 封存和恢复操作的用户体验测试
- 批量操作的性能测试
- 错误场景的处理测试

## 部署注意事项

### 数据库迁移
- 确保迁移脚本在生产环境正确执行
- 备份现有数据
- 验证新字段的默认值设置

### 版本兼容性
- 新版本需要兼容旧版本的数据
- 提供数据迁移工具
- 渐进式功能发布

### 监控和日志
- 添加封存操作的日志记录
- 监控批量操作的性能
- 错误报告和分析

## 未来扩展

### 功能扩展
- 自动封存规则（基于时间、使用频率）
- 封存分类和标签
- 封存历史记录
- 导入/导出封存数据

### 性能扩展
- 分页加载大量封存数据
- 虚拟滚动优化
- 缓存策略优化

### 安全扩展
- 封存数据的额外加密层
- 封存操作的审计日志
- 权限细分管理

## 总结

封存功能的实现涵盖了从数据库到用户界面的完整技术栈，提供了安全、高效、用户友好的密码管理体验。通过合理的架构设计和优化策略，确保了功能的可靠性和可扩展性。

该实现遵循了以下设计原则：
- **安全第一**：封存不影响数据安全性
- **用户体验**：简单直观的操作流程
- **性能优化**：高效的数据查询和状态管理
- **可维护性**：清晰的代码结构和文档
- **可扩展性**：为未来功能扩展预留空间 