# 域名匹配优化实现文档

## 概述

本文档描述了对 `search_credentials_by_domain_hybrid` 功能的域名匹配规则优化，实现了智能的域名匹配和优先级排序。

## 优化目标

当用户搜索域名 `dl.reg.163.com` 时，返回结果应按以下优先级排序：

1. **带协议的完整URL匹配**（最高优先级）：`https://dl.reg.163.com`、`http://dl.reg.163.com/index.html#1231`
2. **完全匹配**（第二高优先级）：`dl.reg.163.com`
3. **逐级域名匹配**：
   - `reg.163.com`（匹配后3级）
   - `163.com`（匹配后2级）
4. **模糊匹配**（最低优先级）：包含关系匹配

## 实现架构

### 1. 数据库层优化 (`orm_database.rs`)

#### 原始实现问题
```rust
// 原始实现使用简单的 LIKE 查询
let domain_pattern = format!("%{}%", domain);
let results = item::Entity::find()
    .filter(login::Column::Website.like(&domain_pattern))
    .all(&self.db)
    .await?;
```

#### 优化后实现
```rust
pub async fn search_login_entries_by_domain(
    &self,
    domain: &str,
) -> Result<Vec<(item::Model, login::Model)>> {
    // 1. 获取所有登录条目
    let results = item::Entity::find()
        .filter(item::Column::ItemType.eq("login"))
        .filter(item::Column::IsDeleted.eq(false))
        .filter(item::Column::IsArchived.eq(false))
        .find_also_related(login::Entity)
        .all(&self.db)
        .await?;

    // 2. 智能域名匹配和排序
    let mut matched_entries = Vec::new();
    let domain_lower = domain.to_lowercase();

    for (item, login) in all_entries {
        if let Some(ref website) = login.website {
            let website_lower = website.to_lowercase();
            
            // 计算匹配优先级
            let priority = calculate_domain_match_priority(&domain_lower, &website_lower);
            
            if priority > 0 {
                matched_entries.push((item, login, priority));
            }
        }
    }

    // 3. 按优先级排序（优先级越高越靠前）
    matched_entries.sort_by(|a, b| b.2.cmp(&a.2));

    // 4. 返回排序后的结果
    Ok(matched_entries.into_iter().map(|(item, login, _)| (item, login)).collect())
}
```

### 2. 域名匹配算法

#### 核心函数：`calculate_domain_match_priority`

```rust
fn calculate_domain_match_priority(target_domain: &str, website: &str) -> u32 {
    let cleaned_website = clean_url(website);
    
    // 1. 带协议的完整URL匹配（优先级：1000）
    if website != cleaned_website && cleaned_website == target_domain {
        return 1000;
    }
    
    // 2. 完全匹配（优先级：950）
    if cleaned_website == target_domain && website == target_domain {
        return 950;
    }
    
    // 3. 完整URL匹配（优先级：900）
    if cleaned_website != target_domain && website.contains(target_domain) {
        if is_complete_domain_match(target_domain, website) {
            return 900;
        }
    }
    
    // 4. 逐级域名匹配（优先级：800-600）
    let target_parts = extract_domain_parts(target_domain);
    let website_parts = extract_domain_parts(&cleaned_website);
    
    for i in 0..target_parts.len() {
        let target_suffix = &target_parts[i..];
        
        if website_parts.len() >= target_suffix.len() {
            let website_suffix = &website_parts[website_parts.len() - target_suffix.len()..];
            
            if target_suffix == website_suffix {
                let matched_levels = target_suffix.len();
                // 只有当匹配的层级大于1时才认为是有效匹配（避免只匹配顶级域名）
                if matched_levels > 1 {
                    return 800 - (i as u32 * 100) + (matched_levels as u32 * 10);
                }
            }
        }
    }
    
    // 5. 模糊匹配（优先级：100）
    if cleaned_website.contains(target_domain) || target_domain.contains(&cleaned_website) {
        return 100;
    }
    
    0
}
```

#### 辅助函数

1. **URL清理函数** (`clean_url`)
   - 移除协议（https://、http://、//）
   - 移除路径、查询参数和锚点
   - 移除端口号
   - 转换为小写

2. **域名分割函数** (`extract_domain_parts`)
   - 按点分割域名
   - 过滤空字符串

3. **完整域名匹配检查** (`is_complete_domain_match`)
   - 确保匹配的是完整的域名，而不是子字符串

## 匹配优先级示例

以搜索 `dl.reg.163.com` 为例：

| 网站URL | 清理后域名 | 匹配类型 | 优先级 | 说明 |
|---------|------------|----------|--------|------|
| `https://dl.reg.163.com` | `dl.reg.163.com` | 带协议URL匹配 | 1000 | 最高优先级 |
| `http://dl.reg.163.com/index.html` | `dl.reg.163.com` | 带协议URL匹配 | 1000 | 最高优先级 |
| `dl.reg.163.com` | `dl.reg.163.com` | 完全匹配 | 950 | 第二高优先级 |
| `reg.163.com` | `reg.163.com` | 逐级匹配（3级） | 730 | 匹配后3级域名 |
| `163.com` | `163.com` | 逐级匹配（2级） | 620 | 匹配后2级域名 |
| `other.163.com` | `other.163.com` | 逐级匹配（2级） | 620 | 同级域名 |
| `google.com` | `google.com` | 无匹配 | 0 | 不匹配 |

## 性能考虑

### 优化策略
1. **内存中排序**：避免复杂的数据库查询，在应用层进行智能匹配
2. **早期退出**：匹配到高优先级结果时立即返回
3. **缓存友好**：算法设计考虑了CPU缓存效率

### 性能影响
- **查询性能**：从数据库获取所有条目，然后在内存中进行匹配和排序
- **内存使用**：需要将所有登录条目加载到内存中
- **CPU使用**：增加了域名解析和匹配的计算开销

## 测试覆盖

### 单元测试
1. **URL清理测试** (`test_clean_url`)
2. **域名分割测试** (`test_extract_domain_parts`)
3. **优先级计算测试** (`test_domain_match_priority`)
4. **完整域名匹配测试** (`test_is_complete_domain_match`)
5. **综合场景测试** (`test_domain_matching_scenarios`)

### 测试用例
```rust
let test_cases = vec![
    ("dl.reg.163.com", 950),                     // 完全匹配
    ("https://dl.reg.163.com", 1000),            // HTTPS
    ("http://dl.reg.163.com", 1000),             // HTTP
    ("https://dl.reg.163.com/", 1000),           // 带路径
    ("https://dl.reg.163.com/index.html", 1000), // 带文件
    ("https://dl.reg.163.com/path?q=1#a", 1000), // 带查询和锚点
    ("reg.163.com", 730),                        // 上级域名
    ("163.com", 620),                            // 顶级域名
    ("other.163.com", 620),                      // 同级域名
    ("google.com", 0),                           // 不匹配
];
```

## 安全考虑

1. **输入验证**：对域名输入进行清理和验证
2. **防止注入**：避免直接使用用户输入构造查询
3. **性能限制**：防止恶意输入导致的性能问题

## 兼容性

- **向后兼容**：保持原有API接口不变
- **数据兼容**：支持现有数据库中的各种URL格式
- **前端兼容**：前端调用方式无需修改

## 部署注意事项

1. **数据库迁移**：无需数据库结构变更
2. **性能监控**：监控查询性能和内存使用
3. **缓存策略**：考虑添加查询结果缓存

## 未来扩展

1. **缓存机制**：添加查询结果缓存以提高性能
2. **模糊搜索**：支持更复杂的模糊匹配算法
3. **用户偏好**：允许用户自定义匹配优先级
4. **国际化域名**：支持IDN（国际化域名）
5. **正则表达式**：支持正则表达式匹配模式

## 总结

本次优化实现了智能的域名匹配和排序功能，显著提升了用户体验。通过精心设计的优先级算法，确保用户能够快速找到最相关的密码条目。同时，完善的测试覆盖保证了功能的稳定性和可靠性。 