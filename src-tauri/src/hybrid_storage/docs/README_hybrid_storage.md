# Hybrid Storage System

A modern, scalable storage architecture for the Secure Password Manager built with Tauri, featuring both traditional SQLite and SeaORM implementations for maximum flexibility and performance.

## 🏗️ Architecture Overview

The hybrid storage system provides two complementary storage implementations:

### 1. **Legacy SQLite Implementation**
- Direct `rusqlite` integration
- Raw SQL queries for fine-grained control
- Existing data compatibility
- Located in `src-tauri/src/hybrid_storage/`

### 2. **Modern SeaORM Implementation**
- Type-safe ORM with compile-time query validation
- Automatic migrations and schema management
- Modern async/await patterns
- Rich relationship handling
- Located in `src-tauri/src/hybrid_storage/orm_*` files

## 📁 Project Structure

```
src-tauri/src/hybrid_storage/
├── mod.rs                      # Module exports and re-exports
├── models.rs                   # Legacy data models and types
├── database.rs                 # Legacy SQLite database manager
├── repositories.rs             # Legacy data access layer
├── services.rs                 # Legacy business logic layer
├── entities/                   # SeaORM entity definitions
│   ├── vault.rs               # Vault entity
│   ├── item.rs                # Item entity  
│   ├── login.rs               # Login credentials entity
│   ├── sync_status.rs         # Sync status entity
│   └── mod.rs                 # Entity module exports
├── migrations/                 # Database schema migrations
│   ├── m20241201_000001_create_tables.rs
│   └── mod.rs                 # Migration module
├── orm_database.rs            # SeaORM database manager
└── orm_services.rs            # SeaORM business logic layer
```

## 🚀 Features

### Core Functionality
- **Multi-Vault Support**: Organize credentials into separate vaults
- **Encrypted Storage**: All passwords encrypted using industry-standard algorithms
- **Full CRUD Operations**: Create, read, update, delete for all entities
- **Advanced Search**: Domain-based credential lookup
- **Favorites System**: Quick access to frequently used credentials
- **Sync Status Tracking**: Cloud synchronization metadata

### SeaORM Advantages
- **Type Safety**: Compile-time validation of database queries
- **Automatic Migrations**: Schema evolution with rollback support
- **Relationship Management**: Foreign keys with cascade delete
- **Async Performance**: Non-blocking database operations
- **Connection Pooling**: Efficient resource management

## 🛠️ Dependencies

Add these to your `Cargo.toml`:

```toml
[dependencies]
# Core SeaORM
sea-orm = { version = "1.1", features = [
    "sqlx-sqlite",
    "runtime-tokio-rustls", 
    "macros",
    "chrono",
    "uuid"
]}
sea-orm-migration = "1.1"

# Legacy SQLite
rusqlite = { version = "0.32", features = ["bundled", "chrono", "uuid"] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization & Crypto
serde = { version = "1.0", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
thiserror = "1.0"
```

## 📖 Usage Guide

### Initialization

```rust
use hybrid_storage::{OrmDatabaseManager, OrmPasswordService};
use std::path::PathBuf;

// Initialize SeaORM database
let db_path = PathBuf::from("./password_vault.db");
let db_manager = OrmDatabaseManager::new(db_path).await?;

// Create service layer
let master_key = "your-master-key".to_string();
let service = OrmPasswordService::new(db_manager, master_key);
```

### Vault Management

```rust
// Create a new vault
let vault = service.create_vault(
    "Work".to_string(),
    Some("Work-related credentials".to_string())
).await?;

// List all vaults
let vaults = service.get_all_vaults().await?;

// Update vault
let updated_vault = service.update_vault(
    vault.id,
    Some("Updated Work".to_string()),
    None
).await?;
```

### Credential Management

```rust
// Save login credential
let credential = service.save_login_credential(
    vault.id,                           // vault_id
    "GitHub Account".to_string(),       // name
    Some("<EMAIL>".to_string()), // username
    "super_secure_password".to_string(),       // password (will be encrypted)
    Some("https://github.com".to_string()),    // website
    Some("Main GitHub account".to_string()),   // notes
    true                                       // favorite
).await?;

// Retrieve credentials by vault
let credentials = service.get_login_credentials_by_vault(vault.id).await?;

// Search credentials by domain
let github_creds = service.search_credentials_by_domain("github.com").await?;

// Get favorite credentials
let favorites = service.get_favorite_credentials(vault.id).await?;

// Update credential
let updated = service.update_login_credential(
    credential.id,
    Some("Updated GitHub Account".to_string()),
    None, // username unchanged
    Some("new_password".to_string()),
    None, // website unchanged
    None, // notes unchanged
    Some(false) // remove from favorites
).await?;

// Delete credential
service.delete_login_credential(credential.id).await?;
```

### Advanced Queries

```rust
// Get specific credential
if let Some(cred) = service.get_login_credential_by_id(123).await? {
    println!("Found: {}", cred.name);
}

// Sync status management
service.update_sync_status(
    Some(chrono::Utc::now()),
    Some("success".to_string()),
    Some("sync_token_abc123".to_string())
).await?;

let sync_status = service.get_sync_status().await?;
```

## 🔐 Security Features

### Encryption
- **Password Encryption**: All passwords encrypted before storage
- **Master Key**: Single key derivation for all encryption operations
- **Transparent Decryption**: Passwords automatically decrypted on retrieval

### Data Integrity
- **Foreign Key Constraints**: Referential integrity enforcement
- **Cascade Deletes**: Automatic cleanup of related data
- **Input Validation**: Comprehensive validation at service layer

## 🗄️ Database Schema

### Tables

#### `vaults`
```sql
CREATE TABLE vaults (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_synced_at TIMESTAMP
);
```

#### `items`
```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vault_id INTEGER NOT NULL,
    item_type TEXT NOT NULL,
    name TEXT NOT NULL,
    favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vault_id) REFERENCES vaults(id) ON DELETE CASCADE
);
```

#### `logins`
```sql
CREATE TABLE logins (
    item_id INTEGER PRIMARY KEY,
    username TEXT,
    encrypted_password TEXT NOT NULL,
    website TEXT,
    notes TEXT,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);
```

#### `sync_status`
```sql
CREATE TABLE sync_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    last_sync_time TIMESTAMP,
    last_sync_status TEXT,
    sync_token TEXT
);
```

### Indexes
- `idx_items_vault_id`: Fast vault-based queries
- `idx_items_type`: Efficient item type filtering

## 🔄 Migration System

Migrations are automatically executed on database initialization:

```rust
// Located in migrations/m20241201_000001_create_tables.rs
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Create tables with foreign keys and indexes
    }
    
    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Rollback schema changes
    }
}
```

## 🧪 Testing

```bash
# Run all tests
cargo test

# Test SeaORM integration
cargo test orm_

# Test legacy SQLite implementation  
cargo test database_

# Run with logging
RUST_LOG=debug cargo test
```

## 🚀 Performance Considerations

### SeaORM Optimizations
- **Connection Pooling**: Automatic connection management
- **Lazy Loading**: Relationships loaded on demand
- **Query Optimization**: Compile-time query validation
- **Batch Operations**: Efficient bulk inserts/updates

### Indexing Strategy
- Index on `vault_id` for fast vault queries
- Index on `item_type` for type-based filtering
- Foreign key indexes for join performance

## 🔧 Configuration

### Environment Variables
```bash
# Database configuration
DATABASE_URL=sqlite://./password_vault.db
RUST_LOG=info

# Encryption settings
MASTER_KEY_LENGTH=32
ENCRYPTION_ALGORITHM=AES-256-GCM
```

### Feature Flags
```toml
[features]
default = ["orm", "legacy"]
orm = ["sea-orm", "sea-orm-migration"]
legacy = ["rusqlite"]
```

## 🤝 Contributing

1. **Code Style**: Follow Rust conventions and use `rustfmt`
2. **Testing**: Add tests for new features
3. **Documentation**: Update docs for API changes
4. **Migrations**: Create migration files for schema changes

## 📄 License

This project is part of the Secure Password Manager and follows the same licensing terms.

## 🔮 Future Enhancements

- [ ] **Multi-Database Support**: PostgreSQL, MySQL backends
- [ ] **Horizontal Scaling**: Database sharding capabilities
- [ ] **Advanced Caching**: Redis integration for performance
- [ ] **Real-time Sync**: WebSocket-based synchronization
- [ ] **Audit Logging**: Comprehensive operation tracking
- [ ] **Backup/Restore**: Automated backup strategies