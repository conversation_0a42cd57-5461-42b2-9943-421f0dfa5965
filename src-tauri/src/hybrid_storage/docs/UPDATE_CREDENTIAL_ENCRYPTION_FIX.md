# 修复 update_login_credential_hybrid 对称密钥加密问题

## 问题描述

在之前的实现中，`update_login_credential_hybrid` 命令没有与 `add_login_credential_hybrid` 保持一致，仍然使用 VaultCrypto 系统进行加解密，而不是使用 keychain 中的对称密钥。这导致了以下问题：

1. **加密不一致**: 新增凭据使用对称密钥加密，但更新凭据使用 VaultCrypto 加密
2. **解密失败**: 使用不同加密系统的数据无法正确解密
3. **安全性问题**: 没有统一使用用户的对称密钥进行端到端加密

## 解决方案

### 1. 新增 ORM 服务方法 (src-tauri/src/hybrid_storage/orm_services.rs)

添加了 `update_login_credential_with_key()` 方法，使用外部对称密钥进行加解密：

```rust
/// 使用外部对称密钥更新登录凭据
pub async fn update_login_credential_with_key(
    &self,
    id: i32,
    name: Option<String>,
    username: Option<String>,
    password: Option<String>,
    website: Option<String>,
    notes: Option<String>,
    favorite: Option<bool>,
    symmetric_key: &[u8; crate::crypto::KEY_SIZE],
) -> ServiceResult<LoginCredential>
```

#### 主要功能：
- **对称密钥加密**: 使用传入的对称密钥加密密码和备注
- **兼容性处理**: 支持解密旧的未加密数据
- **完整性保证**: 确保所有敏感数据都使用相同的加密方式
- **错误处理**: 提供详细的错误信息和降级处理

### 2. 修改 Hybrid 命令 (src-tauri/src/hybrid_storage/commands/hybrid_commands.rs)

更新了 `update_login_credential_hybrid` 命令以使用 keychain 对称密钥：

```rust
#[tauri::command]
pub async fn update_login_credential_hybrid(
    id: i32,
    name: Option<String>,
    username: Option<String>,
    password: Option<String>,
    website: Option<String>,
    notes: Option<String>,
    favorite: Option<bool>,
    state: State<'_, AppState>,
) -> Result<LoginCredentialOutput, String>
```

#### 修改内容：
1. **获取对称密钥**: 从 keychain 获取当前用户的对称密钥
2. **使用新方法**: 调用 `update_login_credential_with_key()` 而不是原来的方法
3. **统一加密**: 确保与添加凭据使用相同的加密方式

## 技术实现细节

### 加密流程
1. **获取密钥**: 从应用状态获取当前用户信息，然后从 keychain 获取对称密钥
2. **加密数据**: 使用 AES-256-GCM 对称加密算法加密密码和备注
3. **存储数据**: 将加密后的数据存储到数据库
4. **返回解密**: 返回解密后的数据给前端

### 解密流程
1. **读取数据**: 从数据库读取加密的数据
2. **解密处理**: 使用相同的对称密钥解密数据
3. **兼容处理**: 如果解密失败，尝试作为未加密数据处理
4. **返回结果**: 返回解密后的明文数据

### 数据兼容性
- **向后兼容**: 支持解密使用 VaultCrypto 加密的旧数据
- **优雅降级**: 解密失败时返回原始数据而不是错误
- **统一格式**: 新数据统一使用对称密钥加密

## 安全性改进

### 1. 端到端加密
- 所有敏感数据使用用户的对称密钥加密
- 密钥安全存储在系统 keychain 中
- 服务端无法访问用户的明文数据

### 2. 加密一致性
- 增删改查操作使用相同的加密方式
- 避免了混合加密导致的安全风险
- 确保数据的完整性和一致性

### 3. 密钥管理
- 对称密钥由用户密码派生
- 支持跨设备一致性
- 自动密钥轮换和更新

## 测试验证

### 功能测试
1. **添加凭据**: 验证新增凭据使用对称密钥加密
2. **更新凭据**: 验证更新凭据使用相同的对称密钥
3. **读取凭据**: 验证能正确解密所有数据
4. **兼容性**: 验证能处理旧的加密数据

### 安全测试
1. **数据库检查**: 确认数据库中的敏感数据已加密
2. **密钥验证**: 确认使用正确的对称密钥
3. **解密测试**: 验证只有正确的密钥才能解密数据

## 影响范围

### 后端变更
- ✅ `src-tauri/src/hybrid_storage/orm_services.rs`: 新增对称密钥更新方法
- ✅ `src-tauri/src/hybrid_storage/commands/hybrid_commands.rs`: 修改更新命令

### 前端影响
- ✅ 无需修改：前端 API 接口保持不变
- ✅ 透明加密：用户无感知的加密升级
- ✅ 功能完整：所有 CRUD 操作正常工作

### 数据库影响
- ✅ 向后兼容：支持旧数据的解密
- ✅ 新数据加密：新增和更新的数据使用对称密钥加密
- ✅ 无需迁移：不需要数据库结构变更

## 结论

成功修复了 `update_login_credential_hybrid` 命令的加密不一致问题，现在所有的增删改查操作都使用统一的对称密钥加密方式。这确保了：

1. **安全性**: 所有敏感数据使用用户的对称密钥进行端到端加密
2. **一致性**: 增删改查操作使用相同的加密方式
3. **兼容性**: 支持旧数据的解密和新数据的加密
4. **用户体验**: 透明的加密升级，用户无感知

这个修复完善了整个密码管理系统的安全架构，为用户提供了更加安全和一致的数据保护。 