use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sync_status")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub last_sync_time: Option<DateTime>,
    pub last_sync_status: Option<String>,
    pub sync_token: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
