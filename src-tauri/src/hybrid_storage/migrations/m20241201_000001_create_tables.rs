use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 创建 vaults 表
        manager
            .create_table(
                Table::create()
                    .table(Alias::new("vaults"))
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Vault::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Vault::Name).string().not_null())
                    .col(ColumnDef::new(Vault::Description).string())
                    .col(
                        ColumnDef::new(Vault::CreatedAt)
                            .timestamp()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(Vault::UpdatedAt)
                            .timestamp()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(ColumnDef::new(Vault::LastSyncedAt).timestamp())
                    .to_owned(),
            )
            .await?;

        // 创建 items 表
        manager
            .create_table(
                Table::create()
                    .table(Alias::new("items"))
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Item::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Item::VaultId).integer().not_null())
                    .col(ColumnDef::new(Item::ItemType).string().not_null())
                    .col(ColumnDef::new(Item::Name).string().not_null())
                    .col(
                        ColumnDef::new(Item::Favorite)
                            .boolean()
                            .not_null()
                            .default(false),
                    )
                    .col(
                        ColumnDef::new(Item::CreatedAt)
                            .timestamp()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(Item::UpdatedAt)
                            .timestamp()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_items_vault_id")
                            .from(Alias::new("items"), Item::VaultId)
                            .to(Alias::new("vaults"), Vault::Id)
                            .on_delete(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await?;

        // 创建 logins 表
        manager
            .create_table(
                Table::create()
                    .table(Alias::new("logins"))
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Login::ItemId)
                            .integer()
                            .not_null()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Login::Username).string())
                    .col(ColumnDef::new(Login::EncryptedPassword).string().not_null())
                    .col(ColumnDef::new(Login::Website).string())
                    .col(ColumnDef::new(Login::Notes).text())
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_logins_item_id")
                            .from(Alias::new("logins"), Login::ItemId)
                            .to(Alias::new("items"), Item::Id)
                            .on_delete(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await?;

        // 创建 sync_status 表
        manager
            .create_table(
                Table::create()
                    .table(Alias::new("sync_status"))
                    .if_not_exists()
                    .col(
                        ColumnDef::new(SyncStatus::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(SyncStatus::LastSyncTime).timestamp())
                    .col(ColumnDef::new(SyncStatus::LastSyncStatus).string())
                    .col(ColumnDef::new(SyncStatus::SyncToken).string())
                    .to_owned(),
            )
            .await?;

        // 创建索引
        manager
            .create_index(
                Index::create()
                    .name("idx_items_vault_id")
                    .table(Alias::new("items"))
                    .col(Item::VaultId)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .name("idx_items_type")
                    .table(Alias::new("items"))
                    .col(Item::ItemType)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Alias::new("logins")).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Alias::new("items")).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Alias::new("vaults")).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Alias::new("sync_status")).to_owned())
            .await?;

        Ok(())
    }
}

#[derive(DeriveIden)]
#[allow(dead_code)]
enum Vault {
    Table,
    Id,
    Name,
    Description,
    CreatedAt,
    UpdatedAt,
    LastSyncedAt,
}

#[derive(DeriveIden)]
#[allow(dead_code)]
enum Item {
    Table,
    Id,
    VaultId,
    ItemType,
    Name,
    Favorite,
    CreatedAt,
    UpdatedAt,
}

#[derive(DeriveIden)]
#[allow(dead_code)]
enum Login {
    Table,
    ItemId,
    Username,
    EncryptedPassword,
    Website,
    Notes,
}

#[derive(DeriveIden)]
#[allow(dead_code)]
enum SyncStatus {
    Table,
    Id,
    LastSyncTime,
    LastSyncStatus,
    SyncToken,
}
