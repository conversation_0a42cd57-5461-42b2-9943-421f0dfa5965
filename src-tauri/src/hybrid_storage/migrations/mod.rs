use sea_orm_migration::prelude::*;

mod m20241201_000001_create_tables;
mod m20241202_000001_add_soft_delete;
mod m20241202_000002_add_archive_fields;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20241201_000001_create_tables::Migration),
            Box::new(m20241202_000001_add_soft_delete::Migration),
            Box::new(m20241202_000002_add_archive_fields::Migration),
        ]
    }
}
