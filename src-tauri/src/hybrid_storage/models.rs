use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 存储层类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum StorageLayer {
    /// 本地SQLite数据库
    Local,
    /// 内存缓存
    Memory,
    /// 文件系统存储
    FileSystem,
}

/// 数据分类枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum DataCategory {
    /// 高频访问数据（密码条目的基本信息）
    HighFrequency,
    /// 中频访问数据（密码历史、标签）
    MediumFrequency,
    /// 低频访问数据（审计日志、备份信息）
    LowFrequency,
    /// 配置数据
    Configuration,
    /// 临时数据
    Temporary,
}

/// 存储策略配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StorageStrategy {
    pub primary_layer: StorageLayer,
    pub cache_layer: Option<StorageLayer>,
    pub backup_layer: Option<StorageLayer>,
    pub ttl_seconds: Option<u64>,
    pub compression_enabled: bool,
    pub encryption_required: bool,
}

impl Default for StorageStrategy {
    fn default() -> Self {
        Self {
            primary_layer: StorageLayer::Local,
            cache_layer: Some(StorageLayer::Memory),
            backup_layer: None,
            ttl_seconds: None,
            compression_enabled: false,
            encryption_required: true,
        }
    }
}

/// 加密上下文
#[derive(Debug, Clone)]
pub struct EncryptionContext {
    pub algorithm: String,
    pub key_id: String,
    pub nonce: Vec<u8>,
    pub additional_data: Option<Vec<u8>>,
}

/// 存储元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageMetadata {
    pub id: Uuid,
    pub data_category: DataCategory,
    pub storage_strategy: StorageStrategy,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub accessed_at: DateTime<Utc>,
    pub access_count: u64,
    pub size_bytes: u64,
    pub checksum: String,
    pub tags: HashMap<String, String>,
}

impl StorageMetadata {
    pub fn new(data_category: DataCategory, strategy: StorageStrategy) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            data_category,
            storage_strategy: strategy,
            created_at: now,
            updated_at: now,
            accessed_at: now,
            access_count: 0,
            size_bytes: 0,
            checksum: String::new(),
            tags: HashMap::new(),
        }
    }

    pub fn touch(&mut self) {
        self.accessed_at = Utc::now();
        self.access_count += 1;
    }

    pub fn update(&mut self) {
        self.updated_at = Utc::now();
        self.touch();
    }
}

/// 存储条目
#[derive(Debug, Clone)]
pub struct StorageEntry<T> {
    pub metadata: StorageMetadata,
    pub data: T,
    pub encryption_context: Option<EncryptionContext>,
}

impl<T> StorageEntry<T> {
    pub fn new(data: T, category: DataCategory, strategy: StorageStrategy) -> Self {
        Self {
            metadata: StorageMetadata::new(category, strategy),
            data,
            encryption_context: None,
        }
    }

    pub fn with_encryption(mut self, context: EncryptionContext) -> Self {
        self.encryption_context = Some(context);
        self
    }
}

/// 查询条件
#[derive(Debug, Clone, Default)]
pub struct QueryCriteria {
    pub ids: Option<Vec<Uuid>>,
    pub categories: Option<Vec<DataCategory>>,
    pub storage_layers: Option<Vec<StorageLayer>>,
    pub tags: Option<HashMap<String, String>>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub updated_after: Option<DateTime<Utc>>,
    pub updated_before: Option<DateTime<Utc>>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

/// 存储操作结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageResult {
    pub success: bool,
    pub message: String,
    pub affected_ids: Vec<Uuid>,
    pub operation_time_ms: u64,
}

/// 存储统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    pub total_entries: u64,
    pub total_size_bytes: u64,
    pub entries_by_category: HashMap<DataCategory, u64>,
    pub entries_by_layer: HashMap<StorageLayer, u64>,
    pub cache_hit_rate: f64,
    pub average_access_time_ms: f64,
}

/// 密码库模型 - 对应vaults表
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vault {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_synced_at: Option<DateTime<Utc>>,
}

impl Vault {
    pub fn new(name: String, description: Option<String>) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description,
            created_at: now,
            updated_at: now,
            last_synced_at: None,
        }
    }

    pub fn update(&mut self) {
        self.updated_at = Utc::now();
    }
}

/// 密码项模型 - 对应items表
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Item {
    pub id: String,
    pub vault_id: String,
    pub item_type: ItemType,
    pub name: String,
    pub favorite: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 密码项类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ItemType {
    #[serde(rename = "login")]
    Login,
    #[serde(rename = "card")]
    Card,
    #[serde(rename = "note")]
    Note,
    #[serde(rename = "identity")]
    Identity,
}

impl std::fmt::Display for ItemType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ItemType::Login => write!(f, "login"),
            ItemType::Card => write!(f, "card"),
            ItemType::Note => write!(f, "note"),
            ItemType::Identity => write!(f, "identity"),
        }
    }
}

impl Item {
    pub fn new(vault_id: String, item_type: ItemType, name: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            vault_id,
            item_type,
            name,
            favorite: false,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update(&mut self) {
        self.updated_at = Utc::now();
    }
}

/// 登录信息模型 - 对应logins表
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Login {
    pub item_id: String,
    pub username: Option<String>,
    pub encrypted_password: String,
    pub website: Option<String>,
    pub notes: Option<String>,
}

impl Login {
    pub fn new(
        item_id: String,
        username: Option<String>,
        encrypted_password: String,
        website: Option<String>,
        notes: Option<String>,
    ) -> Self {
        Self {
            item_id,
            username,
            encrypted_password,
            website,
            notes,
        }
    }
}

/// 同步状态模型 - 对应sync_status表
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatus {
    pub id: String,
    pub last_sync_time: Option<DateTime<Utc>>,
    pub last_sync_status: SyncStatusType,
    pub sync_token: Option<String>,
}

/// 同步状态类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SyncStatusType {
    #[serde(rename = "never")]
    Never,
    #[serde(rename = "success")]
    Success,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "in_progress")]
    InProgress,
}

impl std::fmt::Display for SyncStatusType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SyncStatusType::Never => write!(f, "never"),
            SyncStatusType::Success => write!(f, "success"),
            SyncStatusType::Failed => write!(f, "failed"),
            SyncStatusType::InProgress => write!(f, "in_progress"),
        }
    }
}

/// 完整的登录条目（包含Item和Login信息）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginEntry {
    pub item: Item,
    pub login: Login,
}

impl LoginEntry {
    pub fn new(
        vault_id: String,
        name: String,
        username: Option<String>,
        encrypted_password: String,
        website: Option<String>,
        notes: Option<String>,
    ) -> Self {
        let item = Item::new(vault_id, ItemType::Login, name);
        let login = Login::new(
            item.id.clone(),
            username,
            encrypted_password,
            website,
            notes,
        );

        Self { item, login }
    }
}

/// 数据库查询结果
#[derive(Debug, Clone)]
pub struct QueryResult<T> {
    pub data: Vec<T>,
    pub total_count: usize,
}

impl<T> QueryResult<T> {
    pub fn new(data: Vec<T>) -> Self {
        let total_count = data.len();
        Self { data, total_count }
    }
}

/// 数据库操作错误
#[derive(Debug, thiserror::Error)]
pub enum DatabaseError {
    #[error("Connection error: {0}")]
    Connection(String),

    #[error("Query error: {0}")]
    Query(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Constraint violation: {0}")]
    ConstraintViolation(String),

    #[error("Internal error: {0}")]
    Internal(String),
}

impl From<rusqlite::Error> for DatabaseError {
    fn from(err: rusqlite::Error) -> Self {
        match err {
            rusqlite::Error::SqliteFailure(ffi_err, msg) => {
                if ffi_err.extended_code == rusqlite::ffi::SQLITE_CONSTRAINT {
                    DatabaseError::ConstraintViolation(
                        msg.unwrap_or_else(|| "Constraint violation".to_string()),
                    )
                } else {
                    DatabaseError::Query(
                        msg.unwrap_or_else(|| format!("SQLite error: {:?}", ffi_err)),
                    )
                }
            }
            _ => DatabaseError::Query(err.to_string()),
        }
    }
}
