use sea_orm::*;
use sea_orm_migration::MigratorTrait;
use std::path::PathBuf;
use thiserror::Error;

use super::entities::{item, login, sync_status, vault};
use super::migrations::Migrator;

#[derive(Debug, Error)]
pub enum OrmDatabaseError {
    #[error("Database error: {0}")]
    Database(#[from] DbErr),
    #[error("Connection error: {0}")]
    Connection(String),
    #[error("Migration error: {0}")]
    Migration(String),
    #[error("Not found")]
    NotFound,
}

pub type Result<T> = std::result::Result<T, OrmDatabaseError>;

pub struct OrmDatabaseManager {
    db: DatabaseConnection,
}

impl OrmDatabaseManager {
    pub async fn new(db_path: PathBuf) -> Result<Self> {
        let database_url = format!("sqlite://{}?mode=rwc", db_path.display());

        let db = Database::connect(&database_url)
            .await
            .map_err(|e| OrmDatabaseError::Connection(e.to_string()))?;

        // 运行迁移
        Migrator::up(&db, None)
            .await
            .map_err(|e| OrmDatabaseError::Migration(e.to_string()))?;

        let manager = Self { db };

        // 确保有默认的 vault
        manager.ensure_default_vault().await?;

        Ok(manager)
    }

    async fn ensure_default_vault(&self) -> Result<()> {
        let existing_vault = vault::Entity::find()
            .filter(vault::Column::Name.eq("Personal"))
            .one(&self.db)
            .await?;

        if existing_vault.is_none() {
            let new_vault = vault::ActiveModel {
                name: Set("Personal".to_string()),
                description: Set(Some("Personal vault".to_string())),
                created_at: Set(chrono::Utc::now().naive_utc()),
                updated_at: Set(chrono::Utc::now().naive_utc()),
                ..Default::default()
            };

            vault::Entity::insert(new_vault).exec(&self.db).await?;
        }

        Ok(())
    }

    // Vault 操作
    pub async fn get_all_vaults(&self) -> Result<Vec<vault::Model>> {
        Ok(vault::Entity::find().all(&self.db).await?)
    }

    pub async fn get_vault_by_id(&self, id: i32) -> Result<Option<vault::Model>> {
        Ok(vault::Entity::find_by_id(id).one(&self.db).await?)
    }

    pub async fn create_vault(
        &self,
        name: String,
        description: Option<String>,
    ) -> Result<vault::Model> {
        let new_vault = vault::ActiveModel {
            name: Set(name),
            description: Set(description),
            created_at: Set(chrono::Utc::now().naive_utc()),
            updated_at: Set(chrono::Utc::now().naive_utc()),
            ..Default::default()
        };

        let result = vault::Entity::insert(new_vault)
            .exec_with_returning(&self.db)
            .await?;

        Ok(result)
    }

    pub async fn update_vault(
        &self,
        id: i32,
        name: Option<String>,
        description: Option<String>,
    ) -> Result<vault::Model> {
        let vault = vault::Entity::find_by_id(id)
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut vault: vault::ActiveModel = vault.into();

        if let Some(name) = name {
            vault.name = Set(name);
        }
        if let Some(description) = description {
            vault.description = Set(Some(description));
        }
        vault.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(vault.update(&self.db).await?)
    }

    pub async fn delete_vault(&self, id: i32) -> Result<()> {
        vault::Entity::delete_by_id(id).exec(&self.db).await?;
        Ok(())
    }

    // Item 操作 - 更新为排除已删除的项目
    pub async fn get_items_by_vault(&self, vault_id: i32) -> Result<Vec<item::Model>> {
        Ok(item::Entity::find()
            .filter(item::Column::VaultId.eq(vault_id))
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .filter(item::Column::IsArchived.eq(false)) // 排除已封存的项目
            .all(&self.db)
            .await?)
    }

    pub async fn get_item_by_id(&self, id: i32) -> Result<Option<item::Model>> {
        Ok(item::Entity::find_by_id(id)
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .filter(item::Column::IsArchived.eq(false)) // 排除已封存的项目
            .one(&self.db)
            .await?)
    }

    pub async fn create_item(
        &self,
        vault_id: i32,
        item_type: String,
        name: String,
        favorite: bool,
    ) -> Result<item::Model> {
        let new_item = item::ActiveModel {
            vault_id: Set(vault_id),
            item_type: Set(item_type),
            name: Set(name),
            favorite: Set(favorite),
            created_at: Set(chrono::Utc::now().naive_utc()),
            updated_at: Set(chrono::Utc::now().naive_utc()),
            is_deleted: Set(false),
            deleted_at: Set(None),
            ..Default::default()
        };

        let result = item::Entity::insert(new_item)
            .exec_with_returning(&self.db)
            .await?;

        Ok(result)
    }

    pub async fn update_item(
        &self,
        id: i32,
        name: Option<String>,
        favorite: Option<bool>,
    ) -> Result<item::Model> {
        let item = item::Entity::find_by_id(id)
            .filter(item::Column::IsDeleted.eq(false)) // 确保不更新已删除的项目
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut item: item::ActiveModel = item.into();

        if let Some(name) = name {
            item.name = Set(name);
        }
        if let Some(favorite) = favorite {
            item.favorite = Set(favorite);
        }
        item.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(item.update(&self.db).await?)
    }

    /// 软删除项目 - 标记为已删除但不物理删除
    pub async fn soft_delete_item(&self, id: i32) -> Result<item::Model> {
        let item = item::Entity::find_by_id(id)
            .filter(item::Column::IsDeleted.eq(false)) // 确保项目未被删除
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut item: item::ActiveModel = item.into();
        item.is_deleted = Set(true);
        item.deleted_at = Set(Some(chrono::Utc::now().naive_utc()));
        item.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(item.update(&self.db).await?)
    }

    /// 恢复软删除的项目
    pub async fn restore_item(&self, id: i32) -> Result<item::Model> {
        let item = item::Entity::find_by_id(id)
            .filter(item::Column::IsDeleted.eq(true)) // 确保项目已被软删除
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut item: item::ActiveModel = item.into();
        item.is_deleted = Set(false);
        item.deleted_at = Set(None);
        item.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(item.update(&self.db).await?)
    }

    /// 获取回收站中的项目（已软删除但未超过30天）
    pub async fn get_trash_items(&self, vault_id: i32) -> Result<Vec<item::Model>> {
        Ok(item::Entity::find()
            .filter(item::Column::VaultId.eq(vault_id))
            .filter(item::Column::IsDeleted.eq(true))
            .order_by_desc(item::Column::DeletedAt)
            .all(&self.db)
            .await?)
    }

    /// 获取需要永久删除的项目（软删除超过30天）
    pub async fn get_items_for_permanent_deletion(&self) -> Result<Vec<item::Model>> {
        let thirty_days_ago = chrono::Utc::now().naive_utc() - chrono::Duration::days(30);

        Ok(item::Entity::find()
            .filter(item::Column::IsDeleted.eq(true))
            .filter(item::Column::DeletedAt.lt(thirty_days_ago))
            .all(&self.db)
            .await?)
    }

    /// 永久删除项目（物理删除）
    pub async fn permanently_delete_item(&self, id: i32) -> Result<()> {
        item::Entity::delete_by_id(id).exec(&self.db).await?;
        Ok(())
    }

    /// 清理过期的软删除项目（超过30天的自动永久删除）
    pub async fn cleanup_expired_deleted_items(&self) -> Result<u64> {
        let thirty_days_ago = chrono::Utc::now().naive_utc() - chrono::Duration::days(30);

        let result = item::Entity::delete_many()
            .filter(item::Column::IsDeleted.eq(true))
            .filter(item::Column::DeletedAt.lt(thirty_days_ago))
            .exec(&self.db)
            .await?;

        Ok(result.rows_affected)
    }

    /// 原有的硬删除方法（保留用于特殊情况）
    pub async fn delete_item(&self, id: i32) -> Result<()> {
        item::Entity::delete_by_id(id).exec(&self.db).await?;
        Ok(())
    }

    // Login 操作
    pub async fn get_login_by_item_id(&self, item_id: i32) -> Result<Option<login::Model>> {
        Ok(login::Entity::find_by_id(item_id).one(&self.db).await?)
    }

    pub async fn create_login(
        &self,
        item_id: i32,
        username: Option<String>,
        encrypted_password: String,
        website: Option<String>,
        notes: Option<String>,
    ) -> Result<login::Model> {
        let new_login = login::ActiveModel {
            item_id: Set(item_id),
            username: Set(username),
            encrypted_password: Set(encrypted_password),
            website: Set(website),
            notes: Set(notes),
        };

        let result = login::Entity::insert(new_login)
            .exec_with_returning(&self.db)
            .await?;

        Ok(result)
    }

    pub async fn update_login(
        &self,
        item_id: i32,
        username: Option<String>,
        encrypted_password: Option<String>,
        website: Option<String>,
        notes: Option<String>,
    ) -> Result<login::Model> {
        let login = login::Entity::find_by_id(item_id)
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut login: login::ActiveModel = login.into();

        if let Some(username) = username {
            login.username = Set(Some(username));
        }
        if let Some(encrypted_password) = encrypted_password {
            login.encrypted_password = Set(encrypted_password);
        }
        if let Some(website) = website {
            login.website = Set(Some(website));
        }
        if let Some(notes) = notes {
            login.notes = Set(Some(notes));
        }

        Ok(login.update(&self.db).await?)
    }

    pub async fn delete_login(&self, item_id: i32) -> Result<()> {
        login::Entity::delete_by_id(item_id).exec(&self.db).await?;
        Ok(())
    }

    // 组合查询 - 获取登录信息和对应的条目（排除已删除的项目）
    pub async fn get_login_entries_by_vault(
        &self,
        vault_id: i32,
    ) -> Result<Vec<(item::Model, Option<login::Model>)>> {
        let items_with_logins = item::Entity::find()
            .filter(item::Column::VaultId.eq(vault_id))
            .filter(item::Column::ItemType.eq("login"))
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .filter(item::Column::IsArchived.eq(false)) // 排除已封存的项目
            .find_also_related(login::Entity)
            .all(&self.db)
            .await?;

        Ok(items_with_logins)
    }

    pub async fn search_login_entries_by_domain(
        &self,
        domain: &str,
    ) -> Result<Vec<(item::Model, login::Model)>> {
        // 获取所有登录条目
        let results = item::Entity::find()
            .filter(item::Column::ItemType.eq("login"))
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .filter(item::Column::IsArchived.eq(false)) // 排除已封存的项目
            .find_also_related(login::Entity)
            .all(&self.db)
            .await?;

        // 过滤掉没有关联登录信息的条目
        let all_entries: Vec<(item::Model, login::Model)> = results
            .into_iter()
            .filter_map(|(item, login_opt)| login_opt.map(|login| (item, login)))
            .collect();

        // 如果域名为空，返回空结果
        if domain.trim().is_empty() {
            return Ok(Vec::new());
        }

        // 智能域名匹配和排序
        let mut matched_entries = Vec::new();
        let domain_lower = domain.to_lowercase();

        for (item, login) in all_entries {
            if let Some(ref website) = login.website {
                let website_lower = website.to_lowercase();

                // 计算匹配优先级
                let priority = calculate_domain_match_priority(&domain_lower, &website_lower);

                if priority > 0 {
                    matched_entries.push((item, login, priority));
                }
            }
        }

        // 按优先级排序（优先级越高越靠前）
        matched_entries.sort_by(|a, b| b.2.cmp(&a.2));

        // 返回排序后的结果（去掉优先级字段）
        Ok(matched_entries
            .into_iter()
            .map(|(item, login, _)| (item, login))
            .collect())
    }

    /// 获取收藏的条目（排除已删除的项目）
    pub async fn get_favorite_items(&self, vault_id: i32) -> Result<Vec<item::Model>> {
        Ok(item::Entity::find()
            .filter(item::Column::VaultId.eq(vault_id))
            .filter(item::Column::Favorite.eq(true))
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .filter(item::Column::IsArchived.eq(false)) // 排除已封存的项目
            .all(&self.db)
            .await?)
    }

    /// 封存项目
    pub async fn archive_item(&self, id: i32) -> Result<item::Model> {
        let item = item::Entity::find_by_id(id)
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut item: item::ActiveModel = item.into();
        item.is_archived = Set(true);
        item.archived_at = Set(Some(chrono::Utc::now().naive_utc()));
        item.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(item.update(&self.db).await?)
    }

    /// 从封存中恢复项目
    pub async fn unarchive_item(&self, id: i32) -> Result<item::Model> {
        let item = item::Entity::find_by_id(id)
            .one(&self.db)
            .await?
            .ok_or(OrmDatabaseError::NotFound)?;

        let mut item: item::ActiveModel = item.into();
        item.is_archived = Set(false);
        item.archived_at = Set(None);
        item.updated_at = Set(chrono::Utc::now().naive_utc());

        Ok(item.update(&self.db).await?)
    }

    /// 获取已封存的项目
    pub async fn get_archived_items(&self, vault_id: i32) -> Result<Vec<item::Model>> {
        Ok(item::Entity::find()
            .filter(item::Column::VaultId.eq(vault_id))
            .filter(item::Column::IsArchived.eq(true))
            .filter(item::Column::IsDeleted.eq(false)) // 排除已删除的项目
            .order_by_desc(item::Column::ArchivedAt)
            .all(&self.db)
            .await?)
    }

    /// 批量封存项目
    pub async fn batch_archive_items(&self, ids: Vec<i32>) -> Result<u64> {
        let now = chrono::Utc::now().naive_utc();

        let result = item::Entity::update_many()
            .set(item::ActiveModel {
                is_archived: Set(true),
                archived_at: Set(Some(now)),
                updated_at: Set(now),
                ..Default::default()
            })
            .filter(item::Column::Id.is_in(ids))
            .filter(item::Column::IsDeleted.eq(false)) // 只封存未删除的项目
            .exec(&self.db)
            .await?;

        Ok(result.rows_affected)
    }

    /// 批量从封存中恢复项目
    pub async fn batch_unarchive_items(&self, ids: Vec<i32>) -> Result<u64> {
        let now = chrono::Utc::now().naive_utc();

        let result = item::Entity::update_many()
            .set(item::ActiveModel {
                is_archived: Set(false),
                archived_at: Set(None),
                updated_at: Set(now),
                ..Default::default()
            })
            .filter(item::Column::Id.is_in(ids))
            .filter(item::Column::IsArchived.eq(true)) // 只恢复已封存的项目
            .exec(&self.db)
            .await?;

        Ok(result.rows_affected)
    }

    // 同步状态操作
    pub async fn get_sync_status(&self) -> Result<Option<sync_status::Model>> {
        Ok(sync_status::Entity::find().one(&self.db).await?)
    }

    pub async fn update_sync_status(
        &self,
        last_sync_time: Option<chrono::DateTime<chrono::Utc>>,
        status: Option<String>,
        token: Option<String>,
    ) -> Result<sync_status::Model> {
        let existing = sync_status::Entity::find().one(&self.db).await?;

        if let Some(existing) = existing {
            let mut sync_status: sync_status::ActiveModel = existing.into();

            if let Some(time) = last_sync_time {
                sync_status.last_sync_time = Set(Some(time.naive_utc()));
            }
            if let Some(status) = status {
                sync_status.last_sync_status = Set(Some(status));
            }
            if let Some(token) = token {
                sync_status.sync_token = Set(Some(token));
            }

            Ok(sync_status.update(&self.db).await?)
        } else {
            let new_sync_status = sync_status::ActiveModel {
                last_sync_time: Set(last_sync_time.map(|t| t.naive_utc())),
                last_sync_status: Set(status),
                sync_token: Set(token),
                ..Default::default()
            };

            let result = sync_status::Entity::insert(new_sync_status)
                .exec_with_returning(&self.db)
                .await?;

            Ok(result)
        }
    }
}

/// 计算域名匹配优先级
/// 返回值越大，优先级越高
/// 0 表示不匹配
fn calculate_domain_match_priority(target_domain: &str, website: &str) -> u32 {
    // 清理和标准化URL
    let cleaned_website = clean_url(website);

    // 1. 带协议的完整URL匹配（最高优先级）
    if website != cleaned_website && cleaned_website == target_domain {
        return 1000;
    }

    // 2. 完全匹配（第二高优先级）
    if cleaned_website == target_domain && website == target_domain {
        return 950;
    }

    // 3. 完整URL匹配（包含协议和路径，但域名不完全匹配）
    if cleaned_website != target_domain && website.contains(target_domain) {
        // 检查是否是完整的域名匹配（不是子字符串）
        if is_complete_domain_match(target_domain, website) {
            return 900;
        }
    }

    // 4. 提取域名层级进行匹配
    let target_parts = extract_domain_parts(target_domain);
    let website_parts = extract_domain_parts(&cleaned_website);

    if target_parts.is_empty() || website_parts.is_empty() {
        return 0;
    }

    // 5. 逐级域名匹配
    let max_parts = target_parts.len();
    for i in 0..max_parts {
        let target_suffix = &target_parts[i..];

        // 检查网站域名是否以这个后缀结尾
        if website_parts.len() >= target_suffix.len() {
            let website_suffix = &website_parts[website_parts.len() - target_suffix.len()..];

            if target_suffix == website_suffix {
                // 只有当匹配的层级大于1时才认为是有效匹配（避免只匹配顶级域名）
                let matched_levels = target_suffix.len();
                if matched_levels > 1 {
                    return 800 - (i as u32 * 100) + (matched_levels as u32 * 10);
                }
            }
        }
    }

    // 6. 模糊匹配（包含关系）
    if cleaned_website.contains(target_domain) || target_domain.contains(&cleaned_website) {
        return 100;
    }

    0
}

/// 清理URL，提取域名部分
fn clean_url(url: &str) -> String {
    let mut cleaned = url.to_lowercase();

    // 移除协议
    if cleaned.starts_with("https://") {
        cleaned = cleaned[8..].to_string();
    } else if cleaned.starts_with("http://") {
        cleaned = cleaned[7..].to_string();
    } else if cleaned.starts_with("//") {
        cleaned = cleaned[2..].to_string();
    }

    // 移除路径、查询参数和锚点
    if let Some(pos) = cleaned.find('/') {
        cleaned = cleaned[..pos].to_string();
    }
    if let Some(pos) = cleaned.find('?') {
        cleaned = cleaned[..pos].to_string();
    }
    if let Some(pos) = cleaned.find('#') {
        cleaned = cleaned[..pos].to_string();
    }

    // 移除端口号
    if let Some(pos) = cleaned.rfind(':') {
        // 确保不是IPv6地址
        if !cleaned.contains('[') {
            cleaned = cleaned[..pos].to_string();
        }
    }

    cleaned
}

/// 提取域名部分（按点分割）
fn extract_domain_parts(domain: &str) -> Vec<String> {
    domain
        .split('.')
        .filter(|part| !part.is_empty())
        .map(|part| part.to_string())
        .collect()
}

/// 检查是否是完整的域名匹配
fn is_complete_domain_match(target_domain: &str, website: &str) -> bool {
    // 检查目标域名在网站中的位置
    if let Some(pos) = website.find(target_domain) {
        let before = pos == 0
            || !website
                .chars()
                .nth(pos - 1)
                .unwrap_or(' ')
                .is_alphanumeric();
        let after_pos = pos + target_domain.len();
        let after = after_pos >= website.len()
            || !website
                .chars()
                .nth(after_pos)
                .unwrap_or(' ')
                .is_alphanumeric();

        before && after
    } else {
        false
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clean_url() {
        assert_eq!(clean_url("https://dl.reg.163.com"), "dl.reg.163.com");
        assert_eq!(
            clean_url("http://dl.reg.163.com/index.html"),
            "dl.reg.163.com"
        );
        assert_eq!(
            clean_url("https://dl.reg.163.com/path?query=1#anchor"),
            "dl.reg.163.com"
        );
        assert_eq!(clean_url("dl.reg.163.com:8080"), "dl.reg.163.com");
        assert_eq!(clean_url("//dl.reg.163.com"), "dl.reg.163.com");
        assert_eq!(clean_url("dl.reg.163.com"), "dl.reg.163.com");
    }

    #[test]
    fn test_extract_domain_parts() {
        assert_eq!(
            extract_domain_parts("dl.reg.163.com"),
            vec!["dl", "reg", "163", "com"]
        );
        assert_eq!(extract_domain_parts("163.com"), vec!["163", "com"]);
        assert_eq!(extract_domain_parts("com"), vec!["com"]);
    }

    #[test]
    fn test_domain_match_priority() {
        let target = "dl.reg.163.com";

        // 带协议的完整URL匹配 - 最高优先级
        let https_match = calculate_domain_match_priority(target, "https://dl.reg.163.com");
        println!("HTTPS match priority: {}", https_match);
        assert_eq!(https_match, 1000);

        let http_path_match =
            calculate_domain_match_priority(target, "http://dl.reg.163.com/index.html");
        println!("HTTP with path match priority: {}", http_path_match);
        assert_eq!(http_path_match, 1000);

        // 完全匹配 - 第二高优先级
        let exact_match = calculate_domain_match_priority(target, "dl.reg.163.com");
        println!("Exact match priority: {}", exact_match);
        assert_eq!(exact_match, 950);

        // 逐级域名匹配
        // reg.163.com 匹配 (从第1级开始，匹配3级)
        let priority_reg = calculate_domain_match_priority(target, "reg.163.com");
        println!("reg.163.com priority: {}", priority_reg);
        assert!(priority_reg > 700 && priority_reg < 800);

        // 163.com 匹配 (从第2级开始，匹配2级)
        let priority_163 = calculate_domain_match_priority(target, "163.com");
        println!("163.com priority: {}", priority_163);
        assert!(priority_163 > 600 && priority_163 < 700);

        // 优先级顺序验证
        assert!(https_match > exact_match);
        assert!(exact_match > priority_reg);
        assert!(priority_reg > priority_163);

        // 不匹配
        let google_match = calculate_domain_match_priority(target, "google.com");
        println!("google.com priority: {}", google_match);
        assert_eq!(google_match, 0);

        let baidu_match = calculate_domain_match_priority(target, "baidu.com");
        println!("baidu.com priority: {}", baidu_match);
        assert_eq!(baidu_match, 0);
    }

    #[test]
    fn test_is_complete_domain_match() {
        assert!(is_complete_domain_match("163.com", "dl.reg.163.com"));
        assert!(is_complete_domain_match("reg.163.com", "dl.reg.163.com"));
        assert!(!is_complete_domain_match("63.com", "dl.reg.163.com")); // 部分匹配
        assert!(!is_complete_domain_match("g.163.com", "dl.reg.163.com")); // 不匹配
    }

    #[test]
    fn test_domain_matching_scenarios() {
        let target = "dl.reg.163.com";

        // 测试各种URL格式
        let test_cases = vec![
            ("dl.reg.163.com", 950),                     // 完全匹配
            ("https://dl.reg.163.com", 1000),            // HTTPS
            ("http://dl.reg.163.com", 1000),             // HTTP
            ("https://dl.reg.163.com/", 1000),           // 带路径
            ("https://dl.reg.163.com/index.html", 1000), // 带文件
            ("https://dl.reg.163.com/path?q=1#a", 1000), // 带查询和锚点
            ("reg.163.com", 730),                        // 上级域名
            ("163.com", 620),                            // 顶级域名
            ("other.163.com", 620),                      // 同级域名
            ("google.com", 0),                           // 不匹配
        ];

        for (website, expected_min) in test_cases {
            let priority = calculate_domain_match_priority(target, website);
            if expected_min == 0 {
                assert_eq!(priority, 0, "Expected no match for {}", website);
            } else {
                assert!(
                    priority >= expected_min - 50 && priority <= expected_min + 50,
                    "Priority for {} should be around {}, got {}",
                    website,
                    expected_min,
                    priority
                );
            }
        }
    }
}
