use crate::hybrid_storage::database::DatabaseManager;
use crate::hybrid_storage::models::{DatabaseError, LoginEntry, QueryResult, Vault};
use std::sync::Arc;

/// 密码库仓储
/// 提供对密码库数据的访问接口
pub struct VaultRepository {
    db: Arc<DatabaseManager>,
}

impl VaultRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 获取所有密码库
    pub async fn get_all(&self) -> Result<QueryResult<Vault>, DatabaseError> {
        self.db.get_vaults().await
    }

    /// 根据ID获取密码库
    pub async fn get_by_id(&self, vault_id: &str) -> Result<Option<Vault>, DatabaseError> {
        self.db.get_vault_by_id(vault_id).await
    }

    /// 获取默认密码库ID
    pub async fn get_default_id(&self) -> Result<String, DatabaseError> {
        self.db.get_default_vault_id().await
    }
}

/// 登录条目仓储
/// 提供对登录条目数据的访问接口
pub struct LoginRepository {
    db: Arc<DatabaseManager>,
}

impl LoginRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 获取指定密码库的所有登录条目
    pub async fn get_by_vault_id(
        &self,
        vault_id: &str,
    ) -> Result<QueryResult<LoginEntry>, DatabaseError> {
        self.db.get_login_entries(vault_id).await
    }

    /// 根据域名获取登录条目
    pub async fn get_by_domain(
        &self,
        domain: &str,
    ) -> Result<QueryResult<LoginEntry>, DatabaseError> {
        self.db.get_login_entries_by_domain(domain).await
    }

    /// 添加登录条目
    pub async fn add(&self, entry: &LoginEntry) -> Result<(), DatabaseError> {
        self.db.add_login_entry(entry).await
    }

    /// 根据ID获取登录条目
    pub async fn get_by_id(&self, item_id: &str) -> Result<Option<LoginEntry>, DatabaseError> {
        // 首先获取默认密码库的所有条目，然后筛选
        let vault_id = self.db.get_default_vault_id().await?;
        let entries = self.db.get_login_entries(&vault_id).await?;

        for entry in entries.data {
            if entry.item.id == item_id {
                return Ok(Some(entry));
            }
        }

        Ok(None)
    }
}
