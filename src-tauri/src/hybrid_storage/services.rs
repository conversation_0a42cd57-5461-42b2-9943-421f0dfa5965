use crate::crypto::{EncryptedD<PERSON>, VaultCrypto};
use crate::errors::{VaultError, VaultResult};
use crate::hybrid_storage::models::LoginEntry;
use crate::hybrid_storage::repositories::{LoginRepository, VaultRepository};
use std::sync::Arc;

/// 密码管理服务
/// 提供高级的密码管理业务逻辑
pub struct PasswordService {
    vault_repo: VaultRepository,
    login_repo: LoginRepository,
    crypto: Arc<VaultCrypto>,
}

impl PasswordService {
    pub fn new(
        vault_repo: VaultRepository,
        login_repo: LoginRepository,
        crypto: Arc<VaultCrypto>,
    ) -> Self {
        Self {
            vault_repo,
            login_repo,
            crypto,
        }
    }

    /// 添加登录凭据
    pub async fn add_login_credential(
        &self,
        name: String,
        username: Option<String>,
        password: String,
        website: Option<String>,
        notes: Option<String>,
    ) -> VaultResult<String> {
        // 获取默认密码库ID
        let vault_id = self.vault_repo.get_default_id().await.map_err(|e| {
            VaultError::InternalError(format!("Failed to get default vault: {}", e))
        })?;

        // 使用新的加密系统加密密码
        let encrypted_data =
            self.crypto.encrypt(&password).await.map_err(|e| {
                VaultError::InternalError(format!("Failed to encrypt password: {}", e))
            })?;

        // 将 EncryptedData 序列化为字符串存储
        let encrypted_password = encrypted_data.to_base64();

        // 创建登录条目
        let login_entry =
            LoginEntry::new(vault_id, name, username, encrypted_password, website, notes);

        let item_id = login_entry.item.id.clone();

        // 保存到数据库
        self.login_repo
            .add(&login_entry)
            .await
            .map_err(|e| VaultError::InternalError(format!("Failed to save login entry: {}", e)))?;

        log::info!("Added login credential with ID: {}", item_id);
        Ok(item_id)
    }

    /// 获取所有登录凭据
    pub async fn get_all_login_credentials(&self) -> VaultResult<Vec<LoginCredential>> {
        // 获取默认密码库ID
        let vault_id = self.vault_repo.get_default_id().await.map_err(|e| {
            VaultError::InternalError(format!("Failed to get default vault: {}", e))
        })?;

        // 获取所有登录条目
        let entries = self
            .login_repo
            .get_by_vault_id(&vault_id)
            .await
            .map_err(|e| {
                VaultError::InternalError(format!("Failed to get login entries: {}", e))
            })?;

        // 转换为前端格式（不包含解密的密码）
        let credentials = entries
            .data
            .into_iter()
            .map(|entry| LoginCredential {
                id: entry.item.id,
                name: entry.item.name,
                username: entry.login.username,
                website: entry.login.website,
                notes: entry.login.notes,
                favorite: entry.item.favorite,
                created_at: entry.item.created_at,
                updated_at: entry.item.updated_at,
            })
            .collect();

        Ok(credentials)
    }

    /// 获取解密后的密码
    pub async fn get_decrypted_password(&self, item_id: &str) -> VaultResult<String> {
        // 获取登录条目
        let entry =
            self.login_repo.get_by_id(item_id).await.map_err(|e| {
                VaultError::InternalError(format!("Failed to get login entry: {}", e))
            })?;

        let entry =
            entry.ok_or_else(|| VaultError::NotFound("Login entry not found".to_string()))?;

        // 从 base64 字符串反序列化 EncryptedData
        let encrypted_data =
            EncryptedData::from_base64(&entry.login.encrypted_password).map_err(|e| {
                VaultError::InternalError(format!("Failed to parse encrypted data: {}", e))
            })?;

        // 使用新的加密系统解密密码
        let decrypted_password =
            self.crypto.decrypt(&encrypted_data).await.map_err(|e| {
                VaultError::InternalError(format!("Failed to decrypt password: {}", e))
            })?;

        Ok(decrypted_password)
    }

    /// 根据域名获取凭据
    pub async fn get_credentials_for_domain(
        &self,
        domain: &str,
    ) -> VaultResult<Vec<LoginCredential>> {
        let entries = self.login_repo.get_by_domain(domain).await.map_err(|e| {
            VaultError::InternalError(format!("Failed to get credentials for domain: {}", e))
        })?;

        let credentials = entries
            .data
            .into_iter()
            .map(|entry| LoginCredential {
                id: entry.item.id,
                name: entry.item.name,
                username: entry.login.username,
                website: entry.login.website,
                notes: entry.login.notes,
                favorite: entry.item.favorite,
                created_at: entry.item.created_at,
                updated_at: entry.item.updated_at,
            })
            .collect();

        Ok(credentials)
    }
}

/// 前端登录凭据模型（不包含加密的密码）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct LoginCredential {
    pub id: String,
    pub name: String,
    pub username: Option<String>,
    pub website: Option<String>,
    pub notes: Option<String>,
    pub favorite: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}
