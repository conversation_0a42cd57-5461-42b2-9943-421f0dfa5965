//! 移动平台Tauri命令模块
//! 
//! 提供前端调用的移动平台功能接口

use crate::mobile::{
    errors::{MobileError, MobileResult},
    feature_manager::MobileFeatureManager,
    platform::MobilePlatform,
    device_info::MobileDeviceInfo,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 移动平台命令状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MobileCommandStatus {
    /// 是否成功
    pub success: bool,
    /// 错误消息
    pub error_message: Option<String>,
    /// 数据
    pub data: Option<serde_json::Value>,
}

impl MobileCommandStatus {
    /// 创建成功状态
    /// 
    /// # 参数
    /// * `data` - 可选的数据
    /// 
    /// # 返回值
    /// 返回成功状态
    pub fn success(data: Option<serde_json::Value>) -> Self {
        Self {
            success: true,
            error_message: None,
            data,
        }
    }

    /// 创建错误状态
    /// 
    /// # 参数
    /// * `error` - 错误消息
    /// 
    /// # 返回值
    /// 返回错误状态
    pub fn error(error: String) -> Self {
        Self {
            success: false,
            error_message: Some(error),
            data: None,
        }
    }
}

/// 全局功能管理器实例
static FEATURE_MANAGER: once_cell::sync::Lazy<Arc<RwLock<Option<MobileFeatureManager>>>> =
    once_cell::sync::Lazy::new(|| Arc::new(RwLock::new(None)));

/// 初始化移动平台功能
/// 
/// # 返回值
/// 返回初始化状态
#[tauri::command]
pub async fn initialize_mobile_platform() -> MobileCommandStatus {
    log::info!("初始化移动平台功能");

    match initialize_platform_internal().await {
        Ok(_) => {
            log::info!("移动平台功能初始化成功");
            MobileCommandStatus::success(None)
        }
        Err(e) => {
            log::error!("移动平台功能初始化失败: {}", e);
            MobileCommandStatus::error(e.to_string())
        }
    }
}

/// 内部初始化函数
async fn initialize_platform_internal() -> MobileResult<()> {
    let mut manager_guard = FEATURE_MANAGER.write().await;
    
    if manager_guard.is_some() {
        log::info!("移动平台功能已初始化");
        return Ok(());
    }

    // 根据当前平台创建功能管理器
    let platform = crate::mobile::platform::PlatformDetector::detect_current_platform();
    let mut manager = create_feature_manager_for_platform(platform)?;
    
    // 初始化功能管理器
    manager.initialize().await?;
    
    *manager_guard = Some(manager);
    Ok(())
}

/// 根据平台创建功能管理器
fn create_feature_manager_for_platform(platform: MobilePlatform) -> MobileResult<MobileFeatureManager> {
    use crate::mobile::feature_manager::MobileFeatureManagerBuilder;
    
    let builder = MobileFeatureManagerBuilder::new();
    
    match platform {
        MobilePlatform::IOS => {
            use crate::mobile::ios::{
                IOSSecureStorageFactory, IOSNotificationManagerFactory, IOSBiometricManagerFactory
            };
            
            let secure_storage = IOSSecureStorageFactory::create_default();
            let notification_manager = IOSNotificationManagerFactory::create_default();
            let biometric_manager = IOSBiometricManagerFactory::create_default();
            
            Ok(builder
                .with_secure_storage(Box::new(secure_storage))
                .with_notification_provider(Box::new(notification_manager))
                .with_biometric_provider(Box::new(biometric_manager))
                .build())
        }
        MobilePlatform::Android => {
            use crate::mobile::android::{
                AndroidSecureStorageFactory, AndroidNotificationManagerFactory, AndroidBiometricManagerFactory
            };
            
            let secure_storage = AndroidSecureStorageFactory::create_default();
            let notification_manager = AndroidNotificationManagerFactory::create_default();
            let biometric_manager = AndroidBiometricManagerFactory::create_default();
            
            Ok(builder
                .with_secure_storage(Box::new(secure_storage))
                .with_notification_provider(Box::new(notification_manager))
                .with_biometric_provider(Box::new(biometric_manager))
                .build())
        }
        MobilePlatform::Unknown => {
            // 对于未知平台，创建一个基本的管理器
            Ok(builder.build())
        }
    }
}

/// 获取设备信息
/// 
/// # 返回值
/// 返回设备信息
#[tauri::command]
pub async fn get_device_info() -> MobileCommandStatus {
    log::info!("获取设备信息");

    match get_device_info_internal().await {
        Ok(device_info) => {
            match serde_json::to_value(device_info) {
                Ok(data) => MobileCommandStatus::success(Some(data)),
                Err(e) => MobileCommandStatus::error(format!("序列化设备信息失败: {}", e)),
            }
        }
        Err(e) => {
            log::error!("获取设备信息失败: {}", e);
            MobileCommandStatus::error(e.to_string())
        }
    }
}

/// 内部获取设备信息函数
async fn get_device_info_internal() -> MobileResult<MobileDeviceInfo> {
    let mut collector = crate::mobile::device_info::DeviceInfoCollector::new();
    collector.collect_device_info().await
}

/// 存储安全数据
/// 
/// # 参数
/// * `key` - 键名
/// * `value` - 值
/// 
/// # 返回值
/// 返回存储状态
#[tauri::command]
pub async fn store_secure_data(key: String, value: String) -> MobileCommandStatus {
    log::info!("存储安全数据: {}", key);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.store_secure_data(&key, &value).await {
            Ok(_) => {
                log::info!("安全数据存储成功: {}", key);
                MobileCommandStatus::success(None)
            }
            Err(e) => {
                log::error!("安全数据存储失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 检索安全数据
/// 
/// # 参数
/// * `key` - 键名
/// 
/// # 返回值
/// 返回检索到的数据
#[tauri::command]
pub async fn retrieve_secure_data(key: String) -> MobileCommandStatus {
    log::info!("检索安全数据: {}", key);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.retrieve_secure_data(&key).await {
            Ok(value) => {
                log::info!("安全数据检索成功: {}", key);
                let data = serde_json::json!({ "value": value });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("安全数据检索失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 删除安全数据
/// 
/// # 参数
/// * `key` - 键名
/// 
/// # 返回值
/// 返回删除状态
#[tauri::command]
pub async fn remove_secure_data(key: String) -> MobileCommandStatus {
    log::info!("删除安全数据: {}", key);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.remove_secure_data(&key).await {
            Ok(removed) => {
                log::info!("安全数据删除结果: {} - {}", key, removed);
                let data = serde_json::json!({ "removed": removed });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("安全数据删除失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 发送通知
/// 
/// # 参数
/// * `title` - 通知标题
/// * `message` - 通知消息
/// * `data` - 可选的额外数据
/// 
/// # 返回值
/// 返回通知ID
#[tauri::command]
pub async fn send_notification(
    title: String,
    message: String,
    data: Option<HashMap<String, String>>,
) -> MobileCommandStatus {
    log::info!("发送通知: {}", title);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.send_notification(&title, &message, data).await {
            Ok(notification_id) => {
                log::info!("通知发送成功: {}", notification_id);
                let data = serde_json::json!({ "notification_id": notification_id });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("通知发送失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 调度通知
/// 
/// # 参数
/// * `title` - 通知标题
/// * `message` - 通知消息
/// * `delay_seconds` - 延迟秒数
/// * `data` - 可选的额外数据
/// 
/// # 返回值
/// 返回通知ID
#[tauri::command]
pub async fn schedule_notification(
    title: String,
    message: String,
    delay_seconds: u64,
    data: Option<HashMap<String, String>>,
) -> MobileCommandStatus {
    log::info!("调度通知: {} (延迟{}秒)", title, delay_seconds);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.schedule_notification(&title, &message, delay_seconds, data).await {
            Ok(notification_id) => {
                log::info!("通知调度成功: {}", notification_id);
                let data = serde_json::json!({ "notification_id": notification_id });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("通知调度失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 生物识别认证
/// 
/// # 参数
/// * `reason` - 认证原因
/// 
/// # 返回值
/// 返回认证结果
#[tauri::command]
pub async fn authenticate_biometric(reason: String) -> MobileCommandStatus {
    log::info!("生物识别认证: {}", reason);

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.authenticate_with_biometric(&reason).await {
            Ok(success) => {
                log::info!("生物识别认证结果: {}", success);
                let data = serde_json::json!({ "authenticated": success });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("生物识别认证失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 检查生物识别可用性
/// 
/// # 返回值
/// 返回可用性状态
#[tauri::command]
pub async fn is_biometric_available() -> MobileCommandStatus {
    log::info!("检查生物识别可用性");

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.is_biometric_authentication_available().await {
            Ok(available) => {
                log::info!("生物识别可用性: {}", available);
                let data = serde_json::json!({ "available": available });
                MobileCommandStatus::success(Some(data))
            }
            Err(e) => {
                log::error!("检查生物识别可用性失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        MobileCommandStatus::error("移动平台功能未初始化".to_string())
    }
}

/// 获取移动平台状态
/// 
/// # 返回值
/// 返回平台状态
#[tauri::command]
pub async fn get_mobile_platform_status() -> MobileCommandStatus {
    log::info!("获取移动平台状态");

    let manager_guard = FEATURE_MANAGER.read().await;
    if let Some(manager) = manager_guard.as_ref() {
        match manager.get_status().await {
            Ok(status) => {
                log::info!("移动平台状态获取成功");
                match serde_json::to_value(status) {
                    Ok(data) => MobileCommandStatus::success(Some(data)),
                    Err(e) => MobileCommandStatus::error(format!("序列化状态失败: {}", e)),
                }
            }
            Err(e) => {
                log::error!("获取移动平台状态失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        let data = serde_json::json!({ "initialized": false });
        MobileCommandStatus::success(Some(data))
    }
}

/// 重新初始化移动平台功能
/// 
/// # 返回值
/// 返回重新初始化状态
#[tauri::command]
pub async fn reinitialize_mobile_platform() -> MobileCommandStatus {
    log::info!("重新初始化移动平台功能");

    let mut manager_guard = FEATURE_MANAGER.write().await;
    if let Some(manager) = manager_guard.as_mut() {
        match manager.reinitialize().await {
            Ok(_) => {
                log::info!("移动平台功能重新初始化成功");
                MobileCommandStatus::success(None)
            }
            Err(e) => {
                log::error!("移动平台功能重新初始化失败: {}", e);
                MobileCommandStatus::error(e.to_string())
            }
        }
    } else {
        // 如果没有初始化过，则进行初始化
        drop(manager_guard);
        initialize_mobile_platform().await
    }
}

/// 获取所有Tauri命令
/// 
/// # 返回值
/// 返回命令列表
pub fn get_mobile_commands() -> Vec<Box<dyn Fn(tauri::Invoke) + Send + Sync>> {
    vec![
        Box::new(tauri::generate_handler![
            initialize_mobile_platform,
            get_device_info,
            store_secure_data,
            retrieve_secure_data,
            remove_secure_data,
            send_notification,
            schedule_notification,
            authenticate_biometric,
            is_biometric_available,
            get_mobile_platform_status,
            reinitialize_mobile_platform
        ]),
    ]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_mobile_command_status() {
        let success_status = MobileCommandStatus::success(None);
        assert!(success_status.success);
        assert!(success_status.error_message.is_none());

        let error_status = MobileCommandStatus::error("Test error".to_string());
        assert!(!error_status.success);
        assert!(error_status.error_message.is_some());
    }

    #[tokio::test]
    async fn test_create_feature_manager_for_platform() {
        let ios_manager = create_feature_manager_for_platform(MobilePlatform::IOS);
        assert!(ios_manager.is_ok());

        let android_manager = create_feature_manager_for_platform(MobilePlatform::Android);
        assert!(android_manager.is_ok());

        let unknown_manager = create_feature_manager_for_platform(MobilePlatform::Unknown);
        assert!(unknown_manager.is_ok());
    }

    #[tokio::test]
    async fn test_get_device_info_internal() {
        let result = get_device_info_internal().await;
        assert!(result.is_ok());
        
        let device_info = result.unwrap();
        assert!(!device_info.device_id.is_empty());
        assert!(!device_info.device_name.is_empty());
    }
} 