/// 移动端设备信息管理
/// 
/// 提供移动端设备信息的获取和管理功能

use serde::{Deserialize, Serialize};
use crate::mobile::platform::MobilePlatform;

/// 移动端设备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MobileDeviceInfo {
    /// 平台类型
    pub platform: MobilePlatform,
    /// 是否支持生物识别
    pub supports_biometric: bool,
    /// 是否支持安全存储
    pub supports_secure_storage: bool,
    /// 应用版本
    pub app_version: String,
    /// 设备型号
    pub device_model: Option<String>,
    /// 系统版本
    pub system_version: Option<String>,
    /// 设备唯一标识符
    pub device_id: Option<String>,
    /// 可用存储空间（字节）
    pub available_storage: Option<u64>,
    /// 总存储空间（字节）
    pub total_storage: Option<u64>,
    /// 内存大小（字节）
    pub memory_size: Option<u64>,
    /// 屏幕分辨率
    pub screen_resolution: Option<(u32, u32)>,
    /// 屏幕密度
    pub screen_density: Option<f32>,
    /// 是否支持硬件加密
    pub supports_hardware_encryption: bool,
    /// 网络连接类型
    pub network_type: Option<String>,
    /// 电池电量百分比
    pub battery_level: Option<f32>,
    /// 是否正在充电
    pub is_charging: Option<bool>,
}

impl Default for MobileDeviceInfo {
    fn default() -> Self {
        Self {
            platform: MobilePlatform::current().unwrap_or_else(|| {
                #[cfg(target_os = "ios")]
                return MobilePlatform::iOS;
                #[cfg(target_os = "android")]
                return MobilePlatform::Android;
                #[cfg(not(any(target_os = "ios", target_os = "android")))]
                panic!("不支持的平台");
            }),
            supports_biometric: false,
            supports_secure_storage: false,
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            device_model: None,
            system_version: None,
            device_id: None,
            available_storage: None,
            total_storage: None,
            memory_size: None,
            screen_resolution: None,
            screen_density: None,
            supports_hardware_encryption: false,
            network_type: None,
            battery_level: None,
            is_charging: None,
        }
    }
}

impl MobileDeviceInfo {
    /// 创建新的设备信息实例
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 返回设备信息实例
    pub fn new(platform: MobilePlatform) -> Self {
        Self {
            platform,
            supports_biometric: Self::detect_biometric_support(&platform),
            supports_secure_storage: Self::detect_secure_storage_support(&platform),
            supports_hardware_encryption: Self::detect_hardware_encryption_support(&platform),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            ..Default::default()
        }
    }

    /// 检测生物识别支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_biometric_support(platform: &MobilePlatform) -> bool {
        match platform {
            #[cfg(target_os = "ios")]
            MobilePlatform::iOS => {
                // iOS 生物识别检查逻辑
                // TODO: 实现实际的生物识别检测
                true // 假设支持
            }
            #[cfg(target_os = "android")]
            MobilePlatform::Android => {
                // Android 生物识别检查逻辑
                // TODO: 实现实际的生物识别检测
                true // 假设支持
            }
        }
    }

    /// 检测安全存储支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_secure_storage_support(platform: &MobilePlatform) -> bool {
        match platform {
            #[cfg(target_os = "ios")]
            MobilePlatform::iOS => true, // iOS Keychain
            #[cfg(target_os = "android")]
            MobilePlatform::Android => true, // Android Keystore
        }
    }

    /// 检测硬件加密支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_hardware_encryption_support(platform: &MobilePlatform) -> bool {
        match platform {
            #[cfg(target_os = "ios")]
            MobilePlatform::iOS => {
                // iOS 硬件加密检查逻辑
                // TODO: 实现实际的硬件加密检测
                true // 假设支持
            }
            #[cfg(target_os = "android")]
            MobilePlatform::Android => {
                // Android 硬件加密检查逻辑
                // TODO: 实现实际的硬件加密检测
                true // 假设支持
            }
        }
    }

    /// 更新设备型号
    /// 
    /// # 参数
    /// * `model` - 设备型号
    pub fn set_device_model(&mut self, model: String) {
        self.device_model = Some(model);
    }

    /// 更新系统版本
    /// 
    /// # 参数
    /// * `version` - 系统版本
    pub fn set_system_version(&mut self, version: String) {
        self.system_version = Some(version);
    }

    /// 更新设备ID
    /// 
    /// # 参数
    /// * `id` - 设备唯一标识符
    pub fn set_device_id(&mut self, id: String) {
        self.device_id = Some(id);
    }

    /// 更新存储信息
    /// 
    /// # 参数
    /// * `available` - 可用存储空间（字节）
    /// * `total` - 总存储空间（字节）
    pub fn set_storage_info(&mut self, available: u64, total: u64) {
        self.available_storage = Some(available);
        self.total_storage = Some(total);
    }

    /// 更新内存信息
    /// 
    /// # 参数
    /// * `size` - 内存大小（字节）
    pub fn set_memory_size(&mut self, size: u64) {
        self.memory_size = Some(size);
    }

    /// 更新屏幕信息
    /// 
    /// # 参数
    /// * `width` - 屏幕宽度
    /// * `height` - 屏幕高度
    /// * `density` - 屏幕密度
    pub fn set_screen_info(&mut self, width: u32, height: u32, density: f32) {
        self.screen_resolution = Some((width, height));
        self.screen_density = Some(density);
    }

    /// 更新网络信息
    /// 
    /// # 参数
    /// * `network_type` - 网络连接类型
    pub fn set_network_type(&mut self, network_type: String) {
        self.network_type = Some(network_type);
    }

    /// 更新电池信息
    /// 
    /// # 参数
    /// * `level` - 电池电量百分比（0.0-1.0）
    /// * `is_charging` - 是否正在充电
    pub fn set_battery_info(&mut self, level: f32, is_charging: bool) {
        self.battery_level = Some(level.clamp(0.0, 1.0));
        self.is_charging = Some(is_charging);
    }

    /// 获取存储使用率
    /// 
    /// # 返回值
    /// 返回存储使用率（0.0-1.0），如果信息不可用返回None
    pub fn storage_usage_ratio(&self) -> Option<f32> {
        if let (Some(available), Some(total)) = (self.available_storage, self.total_storage) {
            if total > 0 {
                let used = total.saturating_sub(available);
                Some(used as f32 / total as f32)
            } else {
                None
            }
        } else {
            None
        }
    }

    /// 检查存储空间是否充足
    /// 
    /// # 参数
    /// * `required_bytes` - 需要的字节数
    /// 
    /// # 返回值
    /// 充足返回true，不足或信息不可用返回false
    pub fn has_sufficient_storage(&self, required_bytes: u64) -> bool {
        self.available_storage
            .map(|available| available >= required_bytes)
            .unwrap_or(false)
    }

    /// 获取设备能力摘要
    /// 
    /// # 返回值
    /// 返回设备能力的字符串描述
    pub fn capabilities_summary(&self) -> String {
        let mut capabilities = Vec::new();
        
        if self.supports_biometric {
            capabilities.push("生物识别");
        }
        if self.supports_secure_storage {
            capabilities.push("安全存储");
        }
        if self.supports_hardware_encryption {
            capabilities.push("硬件加密");
        }
        
        if capabilities.is_empty() {
            "无特殊能力".to_string()
        } else {
            capabilities.join("、")
        }
    }

    /// 转换为JSON字符串
    /// 
    /// # 返回值
    /// 成功返回JSON字符串，失败返回错误信息
    pub fn to_json(&self) -> Result<String, String> {
        serde_json::to_string_pretty(self)
            .map_err(|e| format!("序列化设备信息失败: {}", e))
    }

    /// 从JSON字符串创建
    /// 
    /// # 参数
    /// * `json` - JSON字符串
    /// 
    /// # 返回值
    /// 成功返回设备信息实例，失败返回错误信息
    pub fn from_json(json: &str) -> Result<Self, String> {
        serde_json::from_str(json)
            .map_err(|e| format!("反序列化设备信息失败: {}", e))
    }
}

/// 设备信息收集器
pub struct DeviceInfoCollector;

impl DeviceInfoCollector {
    /// 收集当前设备信息
    /// 
    /// # 返回值
    /// 返回收集到的设备信息
    pub async fn collect() -> MobileDeviceInfo {
        if let Some(platform) = MobilePlatform::current() {
            let mut info = MobileDeviceInfo::new(platform.clone());
            
            // 根据平台收集特定信息
            match platform {
                #[cfg(target_os = "ios")]
                MobilePlatform::iOS => {
                    Self::collect_ios_info(&mut info).await;
                }
                #[cfg(target_os = "android")]
                MobilePlatform::Android => {
                    Self::collect_android_info(&mut info).await;
                }
            }
            
            info
        } else {
            MobileDeviceInfo::default()
        }
    }

    /// 收集iOS设备信息
    /// 
    /// # 参数
    /// * `info` - 设备信息实例
    #[cfg(target_os = "ios")]
    async fn collect_ios_info(info: &mut MobileDeviceInfo) {
        // TODO: 实现iOS设备信息收集
        info.set_device_model("iPhone".to_string());
        info.set_system_version("iOS 17.0".to_string());
    }

    /// 收集Android设备信息
    /// 
    /// # 参数
    /// * `info` - 设备信息实例
    #[cfg(target_os = "android")]
    async fn collect_android_info(info: &mut MobileDeviceInfo) {
        // TODO: 实现Android设备信息收集
        info.set_device_model("Android Device".to_string());
        info.set_system_version("Android 14".to_string());
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_device_info_creation() {
        if let Some(platform) = MobilePlatform::current() {
            let info = MobileDeviceInfo::new(platform);
            assert!(!info.app_version.is_empty());
            assert!(info.supports_secure_storage);
        }
    }

    #[test]
    fn test_device_info_updates() {
        if let Some(platform) = MobilePlatform::current() {
            let mut info = MobileDeviceInfo::new(platform);
            
            info.set_device_model("Test Device".to_string());
            assert_eq!(info.device_model, Some("Test Device".to_string()));
            
            info.set_storage_info(1000, 2000);
            assert_eq!(info.storage_usage_ratio(), Some(0.5));
            assert!(info.has_sufficient_storage(500));
            assert!(!info.has_sufficient_storage(1500));
        }
    }

    #[test]
    fn test_device_info_serialization() {
        if let Some(platform) = MobilePlatform::current() {
            let info = MobileDeviceInfo::new(platform);
            let json = info.to_json().unwrap();
            let restored = MobileDeviceInfo::from_json(&json).unwrap();
            
            assert_eq!(info.platform, restored.platform);
            assert_eq!(info.app_version, restored.app_version);
        }
    }

    #[test]
    fn test_capabilities_summary() {
        if let Some(platform) = MobilePlatform::current() {
            let info = MobileDeviceInfo::new(platform);
            let summary = info.capabilities_summary();
            assert!(!summary.is_empty());
        }
    }

    #[tokio::test]
    async fn test_device_info_collector() {
        let info = DeviceInfoCollector::collect().await;
        assert!(!info.app_version.is_empty());
    }
} 