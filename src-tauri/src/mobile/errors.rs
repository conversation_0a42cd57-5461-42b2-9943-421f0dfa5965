/// 移动端错误定义
/// 
/// 定义移动端模块的统一错误类型和处理机制

use thiserror::Error;

/// 移动端错误类型
#[derive(Error, Debug, Clone)]
pub enum MobileError {
    /// 平台不支持错误
    #[error("当前平台不支持此功能: {feature}")]
    UnsupportedPlatform { feature: String },

    /// 安全存储错误
    #[error("安全存储操作失败: {reason}")]
    SecureStorageError { reason: String },

    /// 通知错误
    #[error("通知操作失败: {reason}")]
    NotificationError { reason: String },

    /// 生物识别错误
    #[error("生物识别操作失败: {reason}")]
    BiometricError { reason: String },

    /// 权限错误
    #[error("权限不足: {permission}")]
    PermissionError { permission: String },

    /// 初始化错误
    #[error("初始化失败: {reason}")]
    InitializationError { reason: String },

    /// 配置错误
    #[error("配置错误: {reason}")]
    ConfigurationError { reason: String },

    /// 网络错误
    #[error("网络错误: {reason}")]
    NetworkError { reason: String },

    /// 数据格式错误
    #[error("数据格式错误: {reason}")]
    DataFormatError { reason: String },

    /// 超时错误
    #[error("操作超时: {operation}")]
    TimeoutError { operation: String },

    /// 内部错误
    #[error("内部错误: {reason}")]
    InternalError { reason: String },

    /// 未知错误
    #[error("未知错误: {reason}")]
    UnknownError { reason: String },
}

impl MobileError {
    /// 创建平台不支持错误
    /// 
    /// # 参数
    /// * `feature` - 不支持的功能名称
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn unsupported_platform(feature: impl Into<String>) -> Self {
        Self::UnsupportedPlatform {
            feature: feature.into(),
        }
    }

    /// 创建安全存储错误
    /// 
    /// # 参数
    /// * `reason` - 错误原因
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn secure_storage_error(reason: impl Into<String>) -> Self {
        Self::SecureStorageError {
            reason: reason.into(),
        }
    }

    /// 创建通知错误
    /// 
    /// # 参数
    /// * `reason` - 错误原因
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn notification_error(reason: impl Into<String>) -> Self {
        Self::NotificationError {
            reason: reason.into(),
        }
    }

    /// 创建生物识别错误
    /// 
    /// # 参数
    /// * `reason` - 错误原因
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn biometric_error(reason: impl Into<String>) -> Self {
        Self::BiometricError {
            reason: reason.into(),
        }
    }

    /// 创建权限错误
    /// 
    /// # 参数
    /// * `permission` - 缺少的权限
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn permission_error(permission: impl Into<String>) -> Self {
        Self::PermissionError {
            permission: permission.into(),
        }
    }

    /// 创建初始化错误
    /// 
    /// # 参数
    /// * `reason` - 错误原因
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn initialization_error(reason: impl Into<String>) -> Self {
        Self::InitializationError {
            reason: reason.into(),
        }
    }

    /// 创建内部错误
    /// 
    /// # 参数
    /// * `reason` - 错误原因
    /// 
    /// # 返回值
    /// 返回MobileError实例
    pub fn internal_error(reason: impl Into<String>) -> Self {
        Self::InternalError {
            reason: reason.into(),
        }
    }

    /// 检查是否为可恢复错误
    /// 
    /// # 返回值
    /// 可恢复返回true，不可恢复返回false
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::UnsupportedPlatform { .. } => false,
            Self::PermissionError { .. } => false,
            Self::ConfigurationError { .. } => false,
            Self::DataFormatError { .. } => false,
            _ => true,
        }
    }

    /// 获取错误严重程度
    /// 
    /// # 返回值
    /// 返回错误严重程度等级
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            Self::UnsupportedPlatform { .. } => ErrorSeverity::Critical,
            Self::InitializationError { .. } => ErrorSeverity::Critical,
            Self::PermissionError { .. } => ErrorSeverity::High,
            Self::SecureStorageError { .. } => ErrorSeverity::High,
            Self::BiometricError { .. } => ErrorSeverity::Medium,
            Self::NotificationError { .. } => ErrorSeverity::Low,
            Self::NetworkError { .. } => ErrorSeverity::Medium,
            Self::TimeoutError { .. } => ErrorSeverity::Medium,
            Self::ConfigurationError { .. } => ErrorSeverity::High,
            Self::DataFormatError { .. } => ErrorSeverity::Medium,
            Self::InternalError { .. } => ErrorSeverity::High,
            Self::UnknownError { .. } => ErrorSeverity::Medium,
        }
    }
}

/// 错误严重程度
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    /// 低严重程度
    Low,
    /// 中等严重程度
    Medium,
    /// 高严重程度
    High,
    /// 严重程度
    Critical,
}

impl std::fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Low => write!(f, "低"),
            Self::Medium => write!(f, "中"),
            Self::High => write!(f, "高"),
            Self::Critical => write!(f, "严重"),
        }
    }
}

/// 移动端结果类型
pub type MobileResult<T> = Result<T, MobileError>;

/// 从字符串转换为MobileError
impl From<String> for MobileError {
    fn from(reason: String) -> Self {
        Self::UnknownError { reason }
    }
}

/// 从&str转换为MobileError
impl From<&str> for MobileError {
    fn from(reason: &str) -> Self {
        Self::UnknownError {
            reason: reason.to_string(),
        }
    }
}

/// 从MobileError转换为String（用于兼容现有代码）
impl From<MobileError> for String {
    fn from(error: MobileError) -> Self {
        error.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = MobileError::unsupported_platform("test_feature");
        assert!(matches!(error, MobileError::UnsupportedPlatform { .. }));
        assert!(!error.is_recoverable());
        assert_eq!(error.severity(), ErrorSeverity::Critical);
    }

    #[test]
    fn test_error_severity() {
        let critical_error = MobileError::unsupported_platform("test");
        let low_error = MobileError::notification_error("test");
        
        assert!(critical_error.severity() > low_error.severity());
    }

    #[test]
    fn test_error_conversion() {
        let error_str = "test error";
        let mobile_error: MobileError = error_str.into();
        let back_to_string: String = mobile_error.into();
        
        assert!(back_to_string.contains("test error"));
    }
} 