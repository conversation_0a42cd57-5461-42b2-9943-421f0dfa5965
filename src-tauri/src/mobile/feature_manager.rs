/// 移动端功能管理器
/// 
/// 提供移动端功能的统一管理和初始化

use tauri::AppHandle;
use log::{info, warn, error};
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::mobile::{
    platform::MobilePlatform,
    device_info::{MobileDeviceInfo, DeviceInfoCollector},
    errors::{MobileError, MobileResult},
    traits::{PlatformProvider, SecureStorageProvider, NotificationProvider, BiometricProvider},
};

/// 移动端功能管理器
pub struct MobileFeatureManager {
    /// 平台类型
    platform: Option<MobilePlatform>,
    /// Tauri应用句柄
    app_handle: AppHandle,
    /// 设备信息
    device_info: Arc<RwLock<Option<MobileDeviceInfo>>>,
    /// 安全存储提供者
    secure_storage: Option<Arc<dyn SecureStorageProvider>>,
    /// 通知提供者
    notification_provider: Option<Arc<dyn NotificationProvider>>,
    /// 生物识别提供者
    biometric_provider: Option<Arc<dyn BiometricProvider>>,
    /// 平台提供者
    platform_provider: Option<Arc<dyn PlatformProvider>>,
    /// 初始化状态
    is_initialized: Arc<RwLock<bool>>,
}

impl MobileFeatureManager {
    /// 创建新的移动端功能管理器
    /// 
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    /// 
    /// # 返回值
    /// 返回功能管理器实例
    pub fn new(app_handle: AppHandle) -> Self {
        let platform = MobilePlatform::current();
        
        if let Some(ref platform) = platform {
            info!("初始化移动端功能管理器，平台: {}", platform.name());
        } else {
            warn!("当前不是移动端平台");
        }
        
        Self {
            platform,
            app_handle,
            device_info: Arc::new(RwLock::new(None)),
            secure_storage: None,
            notification_provider: None,
            biometric_provider: None,
            platform_provider: None,
            is_initialized: Arc::new(RwLock::new(false)),
        }
    }

    /// 初始化移动端特定功能
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    pub async fn initialize(&mut self) -> MobileResult<()> {
        match &self.platform {
            Some(platform) => {
                info!("开始初始化 {} 平台特定功能", platform.name());
                
                // 收集设备信息
                self.collect_device_info().await?;
                
                // 初始化平台特定功能
                match platform {
                    #[cfg(target_os = "ios")]
                    MobilePlatform::iOS => self.initialize_ios().await?,
                    #[cfg(target_os = "android")]
                    MobilePlatform::Android => self.initialize_android().await?,
                }
                
                // 标记为已初始化
                *self.is_initialized.write().await = true;
                
                info!("移动端功能初始化完成");
                Ok(())
            }
            None => {
                warn!("非移动端平台，跳过移动端功能初始化");
                Err(MobileError::unsupported_platform("移动端功能"))
            }
        }
    }

    /// 收集设备信息
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn collect_device_info(&self) -> MobileResult<()> {
        info!("开始收集设备信息");
        
        let device_info = DeviceInfoCollector::collect().await;
        *self.device_info.write().await = Some(device_info);
        
        info!("设备信息收集完成");
        Ok(())
    }

    /// iOS 平台特定初始化
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    #[cfg(target_os = "ios")]
    async fn initialize_ios(&mut self) -> MobileResult<()> {
        info!("初始化 iOS 特定功能");
        
        // 初始化iOS安全存储
        if let Ok(storage) = self.create_ios_secure_storage().await {
            self.secure_storage = Some(Arc::new(storage));
            info!("iOS 安全存储初始化成功");
        } else {
            warn!("iOS 安全存储初始化失败");
        }
        
        // 初始化iOS通知
        if let Ok(notification) = self.create_ios_notification_provider().await {
            self.notification_provider = Some(Arc::new(notification));
            info!("iOS 通知服务初始化成功");
        } else {
            warn!("iOS 通知服务初始化失败");
        }
        
        // 初始化iOS生物识别
        if let Ok(biometric) = self.create_ios_biometric_provider().await {
            self.biometric_provider = Some(Arc::new(biometric));
            info!("iOS 生物识别服务初始化成功");
        } else {
            warn!("iOS 生物识别服务初始化失败");
        }
        
        Ok(())
    }

    /// Android 平台特定初始化
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    #[cfg(target_os = "android")]
    async fn initialize_android(&mut self) -> MobileResult<()> {
        info!("初始化 Android 特定功能");
        
        // 初始化Android安全存储
        if let Ok(storage) = self.create_android_secure_storage().await {
            self.secure_storage = Some(Arc::new(storage));
            info!("Android 安全存储初始化成功");
        } else {
            warn!("Android 安全存储初始化失败");
        }
        
        // 初始化Android通知
        if let Ok(notification) = self.create_android_notification_provider().await {
            self.notification_provider = Some(Arc::new(notification));
            info!("Android 通知服务初始化成功");
        } else {
            warn!("Android 通知服务初始化失败");
        }
        
        // 初始化Android生物识别
        if let Ok(biometric) = self.create_android_biometric_provider().await {
            self.biometric_provider = Some(Arc::new(biometric));
            info!("Android 生物识别服务初始化成功");
        } else {
            warn!("Android 生物识别服务初始化失败");
        }
        
        Ok(())
    }

    /// 获取移动端设备信息
    /// 
    /// # 返回值
    /// 返回设备信息，如果未初始化返回默认值
    pub async fn get_device_info(&self) -> MobileDeviceInfo {
        if let Some(info) = self.device_info.read().await.as_ref() {
            info.clone()
        } else {
            match &self.platform {
                Some(platform) => MobileDeviceInfo::new(platform.clone()),
                None => MobileDeviceInfo::default(),
            }
        }
    }

    /// 检查是否支持生物识别认证
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    pub async fn supports_biometric(&self) -> bool {
        if let Some(provider) = &self.biometric_provider {
            provider.is_available().await.unwrap_or(false)
        } else {
            self.get_device_info().await.supports_biometric
        }
    }

    /// 检查是否支持安全存储
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    pub async fn supports_secure_storage(&self) -> bool {
        self.secure_storage.is_some() || 
        self.get_device_info().await.supports_secure_storage
    }

    /// 获取安全存储提供者
    /// 
    /// # 返回值
    /// 返回安全存储提供者的引用，如果不可用返回None
    pub fn get_secure_storage(&self) -> Option<Arc<dyn SecureStorageProvider>> {
        self.secure_storage.clone()
    }

    /// 获取通知提供者
    /// 
    /// # 返回值
    /// 返回通知提供者的引用，如果不可用返回None
    pub fn get_notification_provider(&self) -> Option<Arc<dyn NotificationProvider>> {
        self.notification_provider.clone()
    }

    /// 获取生物识别提供者
    /// 
    /// # 返回值
    /// 返回生物识别提供者的引用，如果不可用返回None
    pub fn get_biometric_provider(&self) -> Option<Arc<dyn BiometricProvider>> {
        self.biometric_provider.clone()
    }

    /// 检查是否已初始化
    /// 
    /// # 返回值
    /// 已初始化返回true，未初始化返回false
    pub async fn is_initialized(&self) -> bool {
        *self.is_initialized.read().await
    }

    /// 获取平台类型
    /// 
    /// # 返回值
    /// 返回平台类型，如果不是移动端平台返回None
    pub fn get_platform(&self) -> Option<&MobilePlatform> {
        self.platform.as_ref()
    }

    /// 获取应用句柄
    /// 
    /// # 返回值
    /// 返回Tauri应用句柄的引用
    pub fn get_app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 重新初始化功能管理器
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    pub async fn reinitialize(&mut self) -> MobileResult<()> {
        info!("重新初始化移动端功能管理器");
        
        // 重置初始化状态
        *self.is_initialized.write().await = false;
        
        // 清空现有提供者
        self.secure_storage = None;
        self.notification_provider = None;
        self.biometric_provider = None;
        self.platform_provider = None;
        
        // 重新初始化
        self.initialize().await
    }

    /// 获取功能状态摘要
    /// 
    /// # 返回值
    /// 返回功能状态的字符串描述
    pub async fn get_status_summary(&self) -> String {
        let is_init = self.is_initialized().await;
        let has_storage = self.secure_storage.is_some();
        let has_notification = self.notification_provider.is_some();
        let has_biometric = self.biometric_provider.is_some();
        
        format!(
            "初始化: {}, 安全存储: {}, 通知: {}, 生物识别: {}",
            if is_init { "是" } else { "否" },
            if has_storage { "可用" } else { "不可用" },
            if has_notification { "可用" } else { "不可用" },
            if has_biometric { "可用" } else { "不可用" }
        )
    }

    // 平台特定的提供者创建方法（占位符实现）
    
    #[cfg(target_os = "ios")]
    async fn create_ios_secure_storage(&self) -> MobileResult<impl SecureStorageProvider> {
        // TODO: 实现iOS安全存储提供者
        Err(MobileError::internal_error("iOS安全存储提供者未实现"))
    }

    #[cfg(target_os = "ios")]
    async fn create_ios_notification_provider(&self) -> MobileResult<impl NotificationProvider> {
        // TODO: 实现iOS通知提供者
        Err(MobileError::internal_error("iOS通知提供者未实现"))
    }

    #[cfg(target_os = "ios")]
    async fn create_ios_biometric_provider(&self) -> MobileResult<impl BiometricProvider> {
        // TODO: 实现iOS生物识别提供者
        Err(MobileError::internal_error("iOS生物识别提供者未实现"))
    }

    #[cfg(target_os = "android")]
    async fn create_android_secure_storage(&self) -> MobileResult<impl SecureStorageProvider> {
        // TODO: 实现Android安全存储提供者
        Err(MobileError::internal_error("Android安全存储提供者未实现"))
    }

    #[cfg(target_os = "android")]
    async fn create_android_notification_provider(&self) -> MobileResult<impl NotificationProvider> {
        // TODO: 实现Android通知提供者
        Err(MobileError::internal_error("Android通知提供者未实现"))
    }

    #[cfg(target_os = "android")]
    async fn create_android_biometric_provider(&self) -> MobileResult<impl BiometricProvider> {
        // TODO: 实现Android生物识别提供者
        Err(MobileError::internal_error("Android生物识别提供者未实现"))
    }
}

/// 功能管理器构建器
pub struct MobileFeatureManagerBuilder {
    app_handle: Option<AppHandle>,
    auto_initialize: bool,
}

impl MobileFeatureManagerBuilder {
    /// 创建新的构建器
    /// 
    /// # 返回值
    /// 返回构建器实例
    pub fn new() -> Self {
        Self {
            app_handle: None,
            auto_initialize: true,
        }
    }

    /// 设置应用句柄
    /// 
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    /// 
    /// # 返回值
    /// 返回构建器实例
    pub fn with_app_handle(mut self, app_handle: AppHandle) -> Self {
        self.app_handle = Some(app_handle);
        self
    }

    /// 设置是否自动初始化
    /// 
    /// # 参数
    /// * `auto_init` - 是否自动初始化
    /// 
    /// # 返回值
    /// 返回构建器实例
    pub fn auto_initialize(mut self, auto_init: bool) -> Self {
        self.auto_initialize = auto_init;
        self
    }

    /// 构建功能管理器
    /// 
    /// # 返回值
    /// 成功返回功能管理器实例，失败返回错误信息
    pub async fn build(self) -> MobileResult<MobileFeatureManager> {
        let app_handle = self.app_handle
            .ok_or_else(|| MobileError::configuration_error("缺少应用句柄"))?;
        
        let mut manager = MobileFeatureManager::new(app_handle);
        
        if self.auto_initialize {
            manager.initialize().await?;
        }
        
        Ok(manager)
    }
}

impl Default for MobileFeatureManagerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // 注意：这些测试需要在实际的移动端环境中运行才能完全验证

    #[test]
    fn test_feature_manager_creation() {
        // 这个测试在非移动端环境中会创建一个带有None平台的管理器
        // 在实际的移动端环境中会检测到正确的平台
    }

    #[test]
    fn test_builder_pattern() {
        let builder = MobileFeatureManagerBuilder::new()
            .auto_initialize(false);
        
        // 验证构建器配置
        assert!(!builder.auto_initialize);
    }

    #[tokio::test]
    async fn test_device_info_collection() {
        // 测试设备信息收集功能
        let info = DeviceInfoCollector::collect().await;
        assert!(!info.app_version.is_empty());
    }
} 