use crate::mobile::{
    errors::{<PERSON>Error, MobileResult},
    traits::BiometricProvider,
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};

/// iOS生物识别类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum IOSBiometricType {
    /// Touch ID
    TouchID,
    /// Face ID
    FaceID,
    /// 不支持
    None,
}

impl Default for IOSBiometricType {
    fn default() -> Self {
        Self::None
    }
}

/// iOS生物识别配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IOSBiometricConfig {
    /// 提示信息
    pub prompt_message: String,
    /// 取消按钮文本
    pub cancel_button_title: String,
    /// 回退按钮文本
    pub fallback_button_title: Option<String>,
    /// 是否允许设备密码回退
    pub allow_device_credential: bool,
    /// 生物识别策略
    pub policy: IOSBiometricPolicy,
}

impl Default for IOSBiometricConfig {
    fn default() -> Self {
        Self {
            prompt_message: "请使用生物识别验证身份".to_string(),
            cancel_button_title: "取消".to_string(),
            fallback_button_title: Some("使用密码".to_string()),
            allow_device_credential: true,
            policy: IOSBiometricPolicy::DeviceOwnerAuthenticationWithBiometrics,
        }
    }
}

/// iOS生物识别策略
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum IOSBiometricPolicy {
    /// 仅生物识别
    DeviceOwnerAuthenticationWithBiometrics,
    /// 生物识别或设备密码
    DeviceOwnerAuthentication,
}

/// iOS生物识别认证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IOSBiometricAuthResult {
    /// 是否成功
    pub success: bool,
    /// 错误信息
    pub error_message: Option<String>,
    /// 错误代码
    pub error_code: Option<i32>,
    /// 使用的生物识别类型
    pub biometric_type: IOSBiometricType,
}

/// iOS生物识别管理器
#[derive(Debug)]
pub struct IOSBiometricManager {
    /// 配置信息
    config: IOSBiometricConfig,
    /// 是否已初始化
    initialized: bool,
    /// 可用的生物识别类型
    available_biometric_type: IOSBiometricType,
    /// 是否支持生物识别
    biometric_available: bool,
}

impl IOSBiometricManager {
    /// 创建新的iOS生物识别管理器
    /// 
    /// # 参数
    /// * `config` - 生物识别配置
    /// 
    /// # 返回值
    /// 返回生物识别管理器实例
    pub fn new(config: IOSBiometricConfig) -> Self {
        Self {
            config,
            initialized: false,
            available_biometric_type: IOSBiometricType::None,
            biometric_available: false,
        }
    }

    /// 使用默认配置创建生物识别管理器
    /// 
    /// # 返回值
    /// 返回使用默认配置的生物识别管理器实例
    pub fn with_default_config() -> Self {
        Self::new(IOSBiometricConfig::default())
    }

    /// 检测可用的生物识别类型
    /// 
    /// # 返回值
    /// 返回可用的生物识别类型
    async fn detect_biometric_type(&mut self) -> MobileResult<IOSBiometricType> {
        // TODO: 实际的iOS生物识别类型检测实现
        // 这里需要调用iOS的LocalAuthentication框架
        log::info!("检测iOS生物识别类型");
        
        // 模拟检测结果
        let biometric_type = IOSBiometricType::FaceID; // 或 TouchID
        self.available_biometric_type = biometric_type;
        self.biometric_available = biometric_type != IOSBiometricType::None;
        
        Ok(biometric_type)
    }

    /// 检查生物识别可用性
    /// 
    /// # 返回值
    /// 返回是否可用及错误信息
    async fn check_biometric_availability(&self) -> MobileResult<(bool, Option<String>)> {
        // TODO: 实际的iOS生物识别可用性检查实现
        log::info!("检查iOS生物识别可用性");
        
        if self.available_biometric_type == IOSBiometricType::None {
            return Ok((false, Some("设备不支持生物识别".to_string())));
        }
        
        // 模拟检查结果
        Ok((true, None))
    }

    /// 执行生物识别认证
    /// 
    /// # 参数
    /// * `reason` - 认证原因
    /// 
    /// # 返回值
    /// 返回认证结果
    async fn authenticate_with_biometrics(&self, reason: &str) -> MobileResult<IOSBiometricAuthResult> {
        if !self.biometric_available {
            return Err(MobileError::BiometricError(
                "生物识别不可用".to_string()
            ));
        }

        // TODO: 实际的iOS生物识别认证实现
        log::info!("执行iOS生物识别认证: {}", reason);
        
        // 模拟认证结果
        let result = IOSBiometricAuthResult {
            success: true,
            error_message: None,
            error_code: None,
            biometric_type: self.available_biometric_type,
        };
        
        Ok(result)
    }

    /// 获取生物识别错误描述
    /// 
    /// # 参数
    /// * `error_code` - 错误代码
    /// 
    /// # 返回值
    /// 返回错误描述
    fn get_error_description(&self, error_code: i32) -> String {
        match error_code {
            -1 => "用户取消认证".to_string(),
            -2 => "用户选择回退认证".to_string(),
            -3 => "系统取消认证".to_string(),
            -4 => "密码未设置".to_string(),
            -5 => "生物识别不可用".to_string(),
            -6 => "生物识别未注册".to_string(),
            -7 => "生物识别被锁定".to_string(),
            -8 => "应用取消认证".to_string(),
            -9 => "认证无效".to_string(),
            _ => format!("未知错误: {}", error_code),
        }
    }

    /// 获取生物识别类型描述
    /// 
    /// # 返回值
    /// 返回生物识别类型的中文描述
    pub fn get_biometric_type_description(&self) -> String {
        match self.available_biometric_type {
            IOSBiometricType::TouchID => "Touch ID".to_string(),
            IOSBiometricType::FaceID => "Face ID".to_string(),
            IOSBiometricType::None => "不支持".to_string(),
        }
    }

    /// 更新配置
    /// 
    /// # 参数
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: IOSBiometricConfig) {
        self.config = config;
        log::info!("更新iOS生物识别配置");
    }
}

#[async_trait]
impl BiometricProvider for IOSBiometricManager {
    async fn initialize(&mut self) -> MobileResult<()> {
        if self.initialized {
            return Ok(());
        }

        log::info!("初始化iOS生物识别管理器");

        // 检测生物识别类型
        self.detect_biometric_type().await?;

        // 检查可用性
        let (available, error_msg) = self.check_biometric_availability().await?;
        if !available {
            log::warn!("生物识别不可用: {:?}", error_msg);
        }

        self.initialized = true;
        log::info!("iOS生物识别管理器初始化完成");
        Ok(())
    }

    async fn is_available(&self) -> MobileResult<bool> {
        if !self.initialized {
            return Err(MobileError::BiometricError(
                "生物识别管理器未初始化".to_string()
            ));
        }

        let (available, _) = self.check_biometric_availability().await?;
        Ok(available)
    }

    async fn authenticate(&self, reason: &str) -> MobileResult<bool> {
        if !self.initialized {
            return Err(MobileError::BiometricError(
                "生物识别管理器未初始化".to_string()
            ));
        }

        let result = self.authenticate_with_biometrics(reason).await?;
        Ok(result.success)
    }

    async fn get_available_biometric_types(&self) -> MobileResult<Vec<String>> {
        if !self.initialized {
            return Err(MobileError::BiometricError(
                "生物识别管理器未初始化".to_string()
            ));
        }

        let mut types = Vec::new();
        if self.biometric_available {
            types.push(self.get_biometric_type_description());
        }
        Ok(types)
    }

    async fn is_enrolled(&self) -> MobileResult<bool> {
        if !self.initialized {
            return Err(MobileError::BiometricError(
                "生物识别管理器未初始化".to_string()
            ));
        }

        // TODO: 实际的iOS生物识别注册状态检查实现
        log::info!("检查iOS生物识别注册状态");
        Ok(self.biometric_available)
    }

    async fn can_authenticate(&self) -> MobileResult<bool> {
        if !self.initialized {
            return Err(MobileError::BiometricError(
                "生物识别管理器未初始化".to_string()
            ));
        }

        let (available, _) = self.check_biometric_availability().await?;
        Ok(available && self.biometric_available)
    }
}

/// iOS生物识别管理器工厂
pub struct IOSBiometricManagerFactory;

impl IOSBiometricManagerFactory {
    /// 创建默认的iOS生物识别管理器
    /// 
    /// # 返回值
    /// 返回生物识别管理器实例
    pub fn create_default() -> IOSBiometricManager {
        IOSBiometricManager::with_default_config()
    }

    /// 使用自定义配置创建iOS生物识别管理器
    /// 
    /// # 参数
    /// * `config` - 生物识别配置
    /// 
    /// # 返回值
    /// 返回生物识别管理器实例
    pub fn create_with_config(config: IOSBiometricConfig) -> IOSBiometricManager {
        IOSBiometricManager::new(config)
    }

    /// 创建Touch ID专用管理器
    /// 
    /// # 返回值
    /// 返回配置为Touch ID的生物识别管理器实例
    pub fn create_for_touch_id() -> IOSBiometricManager {
        let mut config = IOSBiometricConfig::default();
        config.prompt_message = "请使用Touch ID验证身份".to_string();
        IOSBiometricManager::new(config)
    }

    /// 创建Face ID专用管理器
    /// 
    /// # 返回值
    /// 返回配置为Face ID的生物识别管理器实例
    pub fn create_for_face_id() -> IOSBiometricManager {
        let mut config = IOSBiometricConfig::default();
        config.prompt_message = "请使用Face ID验证身份".to_string();
        IOSBiometricManager::new(config)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_ios_biometric_manager_creation() {
        let manager = IOSBiometricManager::with_default_config();
        assert!(!manager.initialized);
        assert!(!manager.biometric_available);
        assert_eq!(manager.available_biometric_type, IOSBiometricType::None);
    }

    #[tokio::test]
    async fn test_ios_biometric_manager_initialization() {
        let mut manager = IOSBiometricManager::with_default_config();
        let result = manager.initialize().await;
        assert!(result.is_ok());
        assert!(manager.initialized);
    }

    #[tokio::test]
    async fn test_biometric_type_detection() {
        let mut manager = IOSBiometricManager::with_default_config();
        let biometric_type = manager.detect_biometric_type().await.unwrap();
        assert_ne!(biometric_type, IOSBiometricType::None);
        assert_eq!(manager.available_biometric_type, biometric_type);
    }

    #[tokio::test]
    async fn test_authenticate_without_initialization() {
        let manager = IOSBiometricManager::with_default_config();
        let result = manager.authenticate("Test").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_authenticate_after_initialization() {
        let mut manager = IOSBiometricManager::with_default_config();
        manager.initialize().await.unwrap();
        
        let result = manager.authenticate("Test authentication").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_is_available() {
        let mut manager = IOSBiometricManager::with_default_config();
        manager.initialize().await.unwrap();
        
        let result = manager.is_available().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_available_biometric_types() {
        let mut manager = IOSBiometricManager::with_default_config();
        manager.initialize().await.unwrap();
        
        let result = manager.get_available_biometric_types().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_factory_creation() {
        let manager = IOSBiometricManagerFactory::create_default();
        assert!(!manager.initialized);
        
        let touch_id_manager = IOSBiometricManagerFactory::create_for_touch_id();
        assert!(touch_id_manager.config.prompt_message.contains("Touch ID"));
        
        let face_id_manager = IOSBiometricManagerFactory::create_for_face_id();
        assert!(face_id_manager.config.prompt_message.contains("Face ID"));
    }

    #[tokio::test]
    async fn test_biometric_config_default() {
        let config = IOSBiometricConfig::default();
        assert!(!config.prompt_message.is_empty());
        assert!(!config.cancel_button_title.is_empty());
        assert!(config.allow_device_credential);
    }

    #[tokio::test]
    async fn test_biometric_type_description() {
        let mut manager = IOSBiometricManager::with_default_config();
        manager.available_biometric_type = IOSBiometricType::TouchID;
        assert_eq!(manager.get_biometric_type_description(), "Touch ID");
        
        manager.available_biometric_type = IOSBiometricType::FaceID;
        assert_eq!(manager.get_biometric_type_description(), "Face ID");
        
        manager.available_biometric_type = IOSBiometricType::None;
        assert_eq!(manager.get_biometric_type_description(), "不支持");
    }

    #[tokio::test]
    async fn test_config_update() {
        let mut manager = IOSBiometricManager::with_default_config();
        let new_config = IOSBiometricConfig {
            prompt_message: "新的提示信息".to_string(),
            ..Default::default()
        };
        
        manager.update_config(new_config.clone());
        assert_eq!(manager.config.prompt_message, "新的提示信息");
    }
} 