//! iOS平台特定实现模块

pub mod biometric;
pub mod notification;
pub mod secure_storage;

// 重新导出主要类型
pub use biometric::{
    IOSBiometricConfig, IOSBiometricManager, IOSBiometricManagerFactory, IOSBiometricType,
};
pub use notification::{
    IOSNotificationConfig, IOSNotificationData, IOSNotificationManager,
    IOSNotificationManagerFactory,
};
pub use secure_storage::{
    IOSSecureStorage, IOSSecureStorageConfig, IOSSecureStorageFactory,
}; 