//! 移动平台功能模块
//! 
//! 提供跨平台的移动设备功能支持，包括：
//! - 安全存储 (iOS Keychain / Android KeyStore)
//! - 生物识别认证 (Touch ID / Face ID / 指纹识别)
//! - 推送通知管理
//! - 设备信息收集
//! - 平台特定功能抽象

// 核心模块
pub mod commands;
pub mod device_info;
pub mod errors;
pub mod feature_manager;
pub mod platform;
pub mod traits;

// 平台特定实现
pub mod android;
pub mod ios;

// 测试模块
#[cfg(test)]
pub mod tests;

// 重新导出核心类型和功能
pub use commands::{
    get_mobile_commands, MobileCommandStatus,
    // Tauri命令函数
    authenticate_biometric, get_device_info, get_mobile_platform_status,
    initialize_mobile_platform, is_biometric_available, reinitialize_mobile_platform,
    remove_secure_data, retrieve_secure_data, schedule_notification,
    send_notification, store_secure_data,
};

pub use device_info::{
    DeviceInfoCollector, MobileDeviceInfo, StorageInfo,
};

pub use errors::{
    ErrorSeverity, MobileError, MobileResult,
};

pub use feature_manager::{
    MobileFeatureManager, MobileFeatureManagerBuilder,
};

pub use platform::{
    MobilePlatform, PlatformDetector,
};

pub use traits::{
    BiometricProvider, NotificationProvider, PlatformProvider, SecureStorageProvider,
};

// 重新导出平台特定类型
pub use android::{
    AndroidBiometricConfig, AndroidBiometricManager, AndroidBiometricManagerFactory,
    AndroidBiometricType, AndroidNotificationConfig, AndroidNotificationData,
    AndroidNotificationManager, AndroidNotificationManagerFactory,
    AndroidSecureStorage, AndroidSecureStorageConfig, AndroidSecureStorageFactory,
};

pub use ios::{
    IOSBiometricConfig, IOSBiometricManager, IOSBiometricManagerFactory, IOSBiometricType,
    IOSNotificationConfig, IOSNotificationData, IOSNotificationManager,
    IOSNotificationManagerFactory, IOSSecureStorage, IOSSecureStorageConfig,
    IOSSecureStorageFactory,
};

/// 移动平台模块版本信息
pub const MOBILE_MODULE_VERSION: &str = "1.0.0";

/// 移动平台模块名称
pub const MOBILE_MODULE_NAME: &str = "secure-password-mobile";

/// 初始化移动平台模块
/// 
/// 这个函数应该在应用启动时调用，用于初始化移动平台的各种功能。
/// 它会自动检测当前平台并配置相应的功能提供者。
/// 
/// # 返回值
/// 返回初始化结果
/// 
/// # 示例
/// ```rust
/// use crate::mobile;
/// 
/// #[tokio::main]
/// async fn main() {
///     match mobile::initialize_mobile_module().await {
///         Ok(_) => println!("移动平台模块初始化成功"),
///         Err(e) => eprintln!("移动平台模块初始化失败: {}", e),
///     }
/// }
/// ```
pub async fn initialize_mobile_module() -> MobileResult<()> {
    log::info!("初始化移动平台模块 v{}", MOBILE_MODULE_VERSION);
    
    // 检测当前平台
    let platform = PlatformDetector::detect_current_platform();
    log::info!("检测到平台: {}", platform);
    
    // 根据平台进行特定初始化
    match platform {
        MobilePlatform::IOS => {
            log::info!("初始化iOS平台功能");
            // iOS特定的初始化逻辑
        }
        MobilePlatform::Android => {
            log::info!("初始化Android平台功能");
            // Android特定的初始化逻辑
        }
        MobilePlatform::Unknown => {
            log::warn!("未知平台，使用基础功能");
            // 基础功能初始化
        }
    }
    
    log::info!("移动平台模块初始化完成");
    Ok(())
}

/// 获取移动平台模块信息
/// 
/// # 返回值
/// 返回包含模块信息的HashMap
pub fn get_module_info() -> std::collections::HashMap<String, String> {
    let mut info = std::collections::HashMap::new();
    info.insert("name".to_string(), MOBILE_MODULE_NAME.to_string());
    info.insert("version".to_string(), MOBILE_MODULE_VERSION.to_string());
    info.insert("platform".to_string(), PlatformDetector::detect_current_platform().to_string());
    info.insert("features".to_string(), "secure_storage,biometric,notification".to_string());
    info
}

/// 创建默认的功能管理器
/// 
/// 根据当前平台自动创建并配置功能管理器
/// 
/// # 返回值
/// 返回配置好的功能管理器
pub fn create_default_feature_manager() -> MobileResult<MobileFeatureManager> {
    let platform = PlatformDetector::detect_current_platform();
    let builder = MobileFeatureManagerBuilder::new();
    
    match platform {
        MobilePlatform::IOS => {
            let secure_storage = ios::IOSSecureStorageFactory::create_default();
            let notification_manager = ios::IOSNotificationManagerFactory::create_default();
            let biometric_manager = ios::IOSBiometricManagerFactory::create_default();
            
            Ok(builder
                .with_secure_storage(Box::new(secure_storage))
                .with_notification_provider(Box::new(notification_manager))
                .with_biometric_provider(Box::new(biometric_manager))
                .build())
        }
        MobilePlatform::Android => {
            let secure_storage = android::AndroidSecureStorageFactory::create_default();
            let notification_manager = android::AndroidNotificationManagerFactory::create_default();
            let biometric_manager = android::AndroidBiometricManagerFactory::create_default();
            
            Ok(builder
                .with_secure_storage(Box::new(secure_storage))
                .with_notification_provider(Box::new(notification_manager))
                .with_biometric_provider(Box::new(biometric_manager))
                .build())
        }
        MobilePlatform::Unknown => {
            // 对于未知平台，创建一个基本的管理器
            Ok(builder.build())
        }
    }
}

/// 检查平台功能支持
/// 
/// # 返回值
/// 返回平台支持的功能列表
pub fn check_platform_support() -> Vec<String> {
    let platform = PlatformDetector::detect_current_platform();
    let mut features = Vec::new();
    
    if platform.supports_secure_storage() {
        features.push("secure_storage".to_string());
    }
    
    if platform.supports_biometric() {
        features.push("biometric".to_string());
    }
    
    if platform.supports_push_notifications() {
        features.push("push_notifications".to_string());
    }
    
    features
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_module_initialization() {
        let result = initialize_mobile_module().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_module_info() {
        let info = get_module_info();
        assert_eq!(info.get("name").unwrap(), MOBILE_MODULE_NAME);
        assert_eq!(info.get("version").unwrap(), MOBILE_MODULE_VERSION);
        assert!(info.contains_key("platform"));
        assert!(info.contains_key("features"));
    }

    #[test]
    fn test_create_default_feature_manager() {
        let result = create_default_feature_manager();
        assert!(result.is_ok());
        
        let manager = result.unwrap();
        assert!(!manager.is_initialized());
    }

    #[test]
    fn test_check_platform_support() {
        let features = check_platform_support();
        // 至少应该有一些基础功能
        assert!(!features.is_empty());
    }

    #[test]
    fn test_module_constants() {
        assert!(!MOBILE_MODULE_VERSION.is_empty());
        assert!(!MOBILE_MODULE_NAME.is_empty());
        assert_eq!(MOBILE_MODULE_NAME, "secure-password-mobile");
    }
} 