/// 移动端平台定义和通用功能
/// 
/// 定义移动端平台类型和平台检测功能

use log::{info, warn};
use serde::{Deserialize, Serialize};

/// 移动端平台类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
#[allow(non_camel_case_types)]
pub enum MobilePlatform {
    /// iOS平台
    #[cfg(target_os = "ios")]
    iOS,
    /// Android平台
    #[cfg(target_os = "android")]
    Android,
}

impl MobilePlatform {
    /// 获取当前移动平台
    /// 
    /// # 返回值
    /// 如果当前是移动端平台，返回Some(平台类型)，否则返回None
    pub fn current() -> Option<Self> {
        #[cfg(target_os = "ios")]
        {
            info!("检测到iOS平台");
            return Some(Self::iOS);
        }
        
        #[cfg(target_os = "android")]
        {
            info!("检测到Android平台");
            return Some(Self::Android);
        }
        
        #[cfg(not(any(target_os = "ios", target_os = "android")))]
        {
            warn!("当前不是移动端平台");
            return None;
        }
    }
    
    /// 获取平台名称
    /// 
    /// # 返回值
    /// 返回平台名称字符串
    pub fn name(&self) -> &'static str {
        match self {
            #[cfg(target_os = "ios")]
            Self::iOS => "iOS",
            #[cfg(target_os = "android")]
            Self::Android => "Android",
        }
    }

    /// 获取平台标识符
    /// 
    /// # 返回值
    /// 返回平台标识符字符串
    pub fn identifier(&self) -> &'static str {
        match self {
            #[cfg(target_os = "ios")]
            Self::iOS => "ios",
            #[cfg(target_os = "android")]
            Self::Android => "android",
        }
    }

    /// 检查是否为iOS平台
    /// 
    /// # 返回值
    /// 是iOS平台返回true，否则返回false
    pub fn is_ios(&self) -> bool {
        matches!(self, Self::iOS)
    }

    /// 检查是否为Android平台
    /// 
    /// # 返回值
    /// 是Android平台返回true，否则返回false
    pub fn is_android(&self) -> bool {
        matches!(self, Self::Android)
    }

    /// 获取平台支持的功能列表
    /// 
    /// # 返回值
    /// 返回平台支持的功能名称列表
    pub fn supported_features(&self) -> Vec<&'static str> {
        match self {
            #[cfg(target_os = "ios")]
            Self::iOS => vec![
                "secure_storage",
                "biometric_auth",
                "local_notification",
                "keychain",
                "face_id",
                "touch_id",
                "app_store",
            ],
            #[cfg(target_os = "android")]
            Self::Android => vec![
                "secure_storage",
                "biometric_auth",
                "local_notification",
                "keystore",
                "fingerprint",
                "face_unlock",
                "google_play",
            ],
        }
    }

    /// 检查平台是否支持指定功能
    /// 
    /// # 参数
    /// * `feature` - 功能名称
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    pub fn supports_feature(&self, feature: &str) -> bool {
        self.supported_features().contains(&feature)
    }

    /// 获取平台的默认配置
    /// 
    /// # 返回值
    /// 返回平台默认配置的键值对
    pub fn default_config(&self) -> std::collections::HashMap<String, String> {
        let mut config = std::collections::HashMap::new();
        
        match self {
            #[cfg(target_os = "ios")]
            Self::iOS => {
                config.insert("keychain_service".to_string(), "com.secure-password.app".to_string());
                config.insert("notification_category".to_string(), "SECURE_PASSWORD".to_string());
                config.insert("biometric_prompt".to_string(), "使用生物识别验证身份".to_string());
                config.insert("max_storage_size".to_string(), "10485760".to_string()); // 10MB
            },
            #[cfg(target_os = "android")]
            Self::Android => {
                config.insert("keystore_alias".to_string(), "secure_password_key".to_string());
                config.insert("notification_channel".to_string(), "SECURE_PASSWORD_CHANNEL".to_string());
                config.insert("biometric_prompt".to_string(), "使用生物识别验证身份".to_string());
                config.insert("max_storage_size".to_string(), "10485760".to_string()); // 10MB
            },
        }
        
        config
    }
}

impl std::fmt::Display for MobilePlatform {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.name())
    }
}

/// 平台检测工具
pub struct PlatformDetector;

impl PlatformDetector {
    /// 检测当前是否为移动端平台
    /// 
    /// # 返回值
    /// 是移动端平台返回true，否则返回false
    pub fn is_mobile() -> bool {
        MobilePlatform::current().is_some()
    }

    /// 检测当前是否为桌面端平台
    /// 
    /// # 返回值
    /// 是桌面端平台返回true，否则返回false
    pub fn is_desktop() -> bool {
        !Self::is_mobile()
    }

    /// 获取当前平台信息
    /// 
    /// # 返回值
    /// 返回平台信息的键值对
    pub fn get_platform_info() -> std::collections::HashMap<String, String> {
        let mut info = std::collections::HashMap::new();
        
        if let Some(platform) = MobilePlatform::current() {
            info.insert("platform_type".to_string(), "mobile".to_string());
            info.insert("platform_name".to_string(), platform.name().to_string());
            info.insert("platform_id".to_string(), platform.identifier().to_string());
            info.insert("is_mobile".to_string(), "true".to_string());
            
            // 添加支持的功能
            let features = platform.supported_features();
            info.insert("supported_features".to_string(), features.join(","));
            info.insert("feature_count".to_string(), features.len().to_string());
        } else {
            info.insert("platform_type".to_string(), "desktop".to_string());
            info.insert("platform_name".to_string(), std::env::consts::OS.to_string());
            info.insert("is_mobile".to_string(), "false".to_string());
        }
        
        // 添加通用信息
        info.insert("arch".to_string(), std::env::consts::ARCH.to_string());
        info.insert("family".to_string(), std::env::consts::FAMILY.to_string());
        
        info
    }

    /// 检查平台兼容性
    /// 
    /// # 参数
    /// * `required_features` - 需要的功能列表
    /// 
    /// # 返回值
    /// 兼容返回Ok(())，不兼容返回错误信息
    pub fn check_compatibility(required_features: &[&str]) -> Result<(), String> {
        if let Some(platform) = MobilePlatform::current() {
            let supported = platform.supported_features();
            let missing: Vec<&str> = required_features
                .iter()
                .filter(|&feature| !supported.contains(feature))
                .copied()
                .collect();
            
            if missing.is_empty() {
                Ok(())
            } else {
                Err(format!(
                    "平台 {} 不支持以下功能: {}",
                    platform.name(),
                    missing.join(", ")
                ))
            }
        } else {
            Err("当前不是移动端平台".to_string())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_platform_detection() {
        let platform = MobilePlatform::current();
        
        #[cfg(any(target_os = "ios", target_os = "android"))]
        {
            assert!(platform.is_some());
            let platform = platform.unwrap();
            assert!(!platform.name().is_empty());
            assert!(!platform.identifier().is_empty());
            assert!(!platform.supported_features().is_empty());
        }
        
        #[cfg(not(any(target_os = "ios", target_os = "android")))]
        {
            assert!(platform.is_none());
        }
    }

    #[test]
    fn test_platform_detector() {
        let is_mobile = PlatformDetector::is_mobile();
        let is_desktop = PlatformDetector::is_desktop();
        
        // 应该是互斥的
        assert_ne!(is_mobile, is_desktop);
        
        let info = PlatformDetector::get_platform_info();
        assert!(info.contains_key("platform_type"));
        assert!(info.contains_key("is_mobile"));
    }

    #[test]
    fn test_feature_support() {
        if let Some(platform) = MobilePlatform::current() {
            assert!(platform.supports_feature("secure_storage"));
            assert!(!platform.supports_feature("non_existent_feature"));
        }
    }

    #[test]
    fn test_compatibility_check() {
        let required_features = ["secure_storage", "biometric_auth"];
        let result = PlatformDetector::check_compatibility(&required_features);
        
        #[cfg(any(target_os = "ios", target_os = "android"))]
        {
            assert!(result.is_ok());
        }
        
        #[cfg(not(any(target_os = "ios", target_os = "android")))]
        {
            assert!(result.is_err());
        }
    }

    #[test]
    fn test_platform_config() {
        if let Some(platform) = MobilePlatform::current() {
            let config = platform.default_config();
            assert!(!config.is_empty());
            assert!(config.contains_key("biometric_prompt"));
        }
    }
} 