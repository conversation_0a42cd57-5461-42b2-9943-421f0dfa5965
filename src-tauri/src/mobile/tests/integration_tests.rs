//! 移动平台集成测试

#[cfg(test)]
mod tests {
    use crate::mobile::{
        feature_manager::{MobileFeatureManager, MobileFeatureManagerBuilder},
        platform::MobilePlatform,
        traits::{SecureStorageProvider, NotificationProvider, BiometricProvider},
        ios::{IOSSecureStorageFactory, IOSNotificationManagerFactory, IOSBiometricManagerFactory},
        android::{AndroidSecureStorageFactory, AndroidNotificationManagerFactory, AndroidBiometricManagerFactory},
        errors::MobileError,
    };
    use std::sync::Arc;
    use tokio::sync::RwLock;

    #[tokio::test]
    async fn test_feature_manager_ios_integration() {
        // 创建iOS平台的功能管理器
        let secure_storage = IOSSecureStorageFactory::create_default();
        let notification_manager = IOSNotificationManagerFactory::create_default();
        let biometric_manager = IOSBiometricManagerFactory::create_default();

        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .with_notification_provider(Box::new(notification_manager))
            .with_biometric_provider(Box::new(biometric_manager))
            .build();

        // 初始化功能管理器
        let init_result = manager.initialize().await;
        assert!(init_result.is_ok());

        // 验证所有功能都已初始化
        assert!(manager.is_secure_storage_available().await.unwrap());
        assert!(manager.is_notification_available().await.unwrap());
        assert!(manager.is_biometric_available().await.unwrap());
    }

    #[tokio::test]
    async fn test_feature_manager_android_integration() {
        // 创建Android平台的功能管理器
        let secure_storage = AndroidSecureStorageFactory::create_default();
        let notification_manager = AndroidNotificationManagerFactory::create_default();
        let biometric_manager = AndroidBiometricManagerFactory::create_default();

        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .with_notification_provider(Box::new(notification_manager))
            .with_biometric_provider(Box::new(biometric_manager))
            .build();

        // 初始化功能管理器
        let init_result = manager.initialize().await;
        assert!(init_result.is_ok());

        // 验证所有功能都已初始化
        assert!(manager.is_secure_storage_available().await.unwrap());
        assert!(manager.is_notification_available().await.unwrap());
        assert!(manager.is_biometric_available().await.unwrap());
    }

    #[tokio::test]
    async fn test_cross_platform_compatibility() {
        // 测试跨平台兼容性
        let platforms = vec![MobilePlatform::IOS, MobilePlatform::Android];

        for platform in platforms {
            match platform {
                MobilePlatform::IOS => {
                    let storage = IOSSecureStorageFactory::create_default();
                    assert!(!storage.is_initialized());
                }
                MobilePlatform::Android => {
                    let storage = AndroidSecureStorageFactory::create_default();
                    assert!(!storage.is_initialized());
                }
                _ => {}
            }
        }
    }

    #[tokio::test]
    async fn test_feature_manager_partial_initialization() {
        // 测试部分功能初始化
        let secure_storage = IOSSecureStorageFactory::create_default();

        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .build();

        let init_result = manager.initialize().await;
        assert!(init_result.is_ok());

        // 只有安全存储应该可用
        assert!(manager.is_secure_storage_available().await.unwrap());
        assert!(!manager.is_notification_available().await.unwrap());
        assert!(!manager.is_biometric_available().await.unwrap());
    }

    #[tokio::test]
    async fn test_feature_manager_reinitialization() {
        let secure_storage = IOSSecureStorageFactory::create_default();
        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .build();

        // 第一次初始化
        let init_result1 = manager.initialize().await;
        assert!(init_result1.is_ok());

        // 重新初始化应该成功
        let reinit_result = manager.reinitialize().await;
        assert!(reinit_result.is_ok());

        // 功能应该仍然可用
        assert!(manager.is_secure_storage_available().await.unwrap());
    }

    #[tokio::test]
    async fn test_feature_manager_status_reporting() {
        let secure_storage = IOSSecureStorageFactory::create_default();
        let notification_manager = IOSNotificationManagerFactory::create_default();

        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .with_notification_provider(Box::new(notification_manager))
            .build();

        manager.initialize().await.unwrap();

        // 获取状态报告
        let status = manager.get_status().await;
        assert!(status.is_ok());

        let status_map = status.unwrap();
        assert!(status_map.contains_key("secure_storage"));
        assert!(status_map.contains_key("notification"));
        assert!(!status_map.contains_key("biometric")); // 未配置生物识别
    }

    #[tokio::test]
    async fn test_end_to_end_secure_storage_workflow() {
        // 端到端安全存储工作流测试
        let secure_storage = IOSSecureStorageFactory::create_high_security();
        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .build();

        manager.initialize().await.unwrap();

        // 模拟完整的用户数据存储流程
        let user_data = vec![
            ("username", "test_user"),
            ("password", "secure_password_123"),
            ("token", "jwt_token_here"),
            ("preferences", r#"{"theme":"dark","language":"zh-CN"}"#),
        ];

        // 存储用户数据
        for (key, value) in &user_data {
            let result = manager.store_secure_data(key, value).await;
            assert!(result.is_ok());
        }

        // 验证数据存储
        for (key, expected_value) in &user_data {
            let result = manager.retrieve_secure_data(key).await;
            assert!(result.is_ok());
            let retrieved_value = result.unwrap();
            assert!(retrieved_value.is_some());
            assert_eq!(&retrieved_value.unwrap(), expected_value);
        }

        // 清理测试数据
        for (key, _) in &user_data {
            let result = manager.remove_secure_data(key).await;
            assert!(result.is_ok());
            assert!(result.unwrap());
        }
    }

    #[tokio::test]
    async fn test_end_to_end_notification_workflow() {
        // 端到端通知工作流测试
        let notification_manager = IOSNotificationManagerFactory::create_default();
        let mut manager = MobileFeatureManagerBuilder::new()
            .with_notification_provider(Box::new(notification_manager))
            .build();

        manager.initialize().await.unwrap();

        // 发送即时通知
        let notification_id = manager.send_notification(
            "安全提醒",
            "您的密码将在3天后过期",
            None,
        ).await;
        assert!(notification_id.is_ok());

        // 调度延迟通知
        let scheduled_id = manager.schedule_notification(
            "定时提醒",
            "请检查您的安全设置",
            300, // 5分钟后
            None,
        ).await;
        assert!(scheduled_id.is_ok());

        // 获取待处理通知
        let pending = manager.get_pending_notifications().await;
        assert!(pending.is_ok());

        // 取消通知
        let cancel_result = manager.cancel_notification(&scheduled_id.unwrap()).await;
        assert!(cancel_result.is_ok());
        assert!(cancel_result.unwrap());
    }

    #[tokio::test]
    async fn test_end_to_end_biometric_workflow() {
        // 端到端生物识别工作流测试
        let biometric_manager = IOSBiometricManagerFactory::create_default();
        let mut manager = MobileFeatureManagerBuilder::new()
            .with_biometric_provider(Box::new(biometric_manager))
            .build();

        manager.initialize().await.unwrap();

        // 检查生物识别可用性
        let is_available = manager.is_biometric_authentication_available().await;
        assert!(is_available.is_ok());

        if is_available.unwrap() {
            // 获取可用的生物识别类型
            let types = manager.get_available_biometric_types().await;
            assert!(types.is_ok());
            assert!(!types.unwrap().is_empty());

            // 执行生物识别认证
            let auth_result = manager.authenticate_with_biometric("访问安全数据").await;
            assert!(auth_result.is_ok());
            assert!(auth_result.unwrap());
        }
    }

    #[tokio::test]
    async fn test_error_handling_integration() {
        // 集成错误处理测试
        let mut manager = MobileFeatureManagerBuilder::new().build();

        // 未初始化时的操作应该失败
        let store_result = manager.store_secure_data("key", "value").await;
        assert!(store_result.is_err());

        let notification_result = manager.send_notification("title", "message", None).await;
        assert!(notification_result.is_err());

        let biometric_result = manager.authenticate_with_biometric("reason").await;
        assert!(biometric_result.is_err());
    }

    #[tokio::test]
    async fn test_concurrent_feature_access() {
        // 并发功能访问测试
        let secure_storage = IOSSecureStorageFactory::create_default();
        let notification_manager = IOSNotificationManagerFactory::create_default();

        let mut manager = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .with_notification_provider(Box::new(notification_manager))
            .build();

        manager.initialize().await.unwrap();

        // 并发执行多个操作
        let storage_task = manager.store_secure_data("concurrent_key", "concurrent_value");
        let notification_task = manager.send_notification("并发测试", "并发通知消息", None);

        let (storage_result, notification_result) = tokio::join!(storage_task, notification_task);

        assert!(storage_result.is_ok());
        assert!(notification_result.is_ok());
    }

    #[tokio::test]
    async fn test_feature_manager_builder_validation() {
        // 测试构建器验证
        let builder = MobileFeatureManagerBuilder::new();
        
        // 空构建器应该能创建管理器
        let manager = builder.build();
        assert!(!manager.is_initialized());

        // 验证构建器的链式调用
        let secure_storage = IOSSecureStorageFactory::create_default();
        let manager_with_storage = MobileFeatureManagerBuilder::new()
            .with_secure_storage(Box::new(secure_storage))
            .build();
        
        assert!(!manager_with_storage.is_initialized());
    }

    #[tokio::test]
    async fn test_platform_specific_features() {
        // 测试平台特定功能
        
        // iOS特定测试
        let ios_storage = IOSSecureStorageFactory::create_default();
        assert!(!ios_storage.is_initialized());

        // Android特定测试
        let android_storage = AndroidSecureStorageFactory::create_high_security();
        assert!(!android_storage.is_initialized());

        // 验证工厂方法创建的对象类型正确
        let ios_biometric = IOSBiometricManagerFactory::create_for_face_id();
        assert!(!ios_biometric.is_initialized());

        let android_biometric = AndroidBiometricManagerFactory::create_for_fingerprint();
        assert!(!android_biometric.is_initialized());
    }
} 