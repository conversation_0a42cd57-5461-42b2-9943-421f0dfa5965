//! 平台相关功能测试

#[cfg(test)]
mod tests {
    use crate::mobile::{
        platform::{MobilePlatform, PlatformDetector},
        device_info::{DeviceInfoCollector, MobileDeviceInfo},
        errors::MobileError,
    };

    #[tokio::test]
    async fn test_platform_detection() {
        let platform = PlatformDetector::detect_current_platform();
        
        // 在测试环境中，应该检测到Unknown平台
        assert_eq!(platform, MobilePlatform::Unknown);
    }

    #[tokio::test]
    async fn test_platform_compatibility() {
        let ios_platform = MobilePlatform::IOS;
        let android_platform = MobilePlatform::Android;
        
        assert!(ios_platform.is_mobile());
        assert!(android_platform.is_mobile());
        assert!(!MobilePlatform::Unknown.is_mobile());
        
        assert!(ios_platform.supports_biometric());
        assert!(android_platform.supports_biometric());
        assert!(!MobilePlatform::Unknown.supports_biometric());
    }

    #[tokio::test]
    async fn test_platform_string_conversion() {
        assert_eq!(MobilePlatform::IOS.to_string(), "iOS");
        assert_eq!(MobilePlatform::Android.to_string(), "Android");
        assert_eq!(MobilePlatform::Unknown.to_string(), "Unknown");
    }

    #[tokio::test]
    async fn test_device_info_collection() {
        let mut collector = DeviceInfoCollector::new();
        let result = collector.collect_device_info().await;
        
        assert!(result.is_ok());
        let device_info = result.unwrap();
        
        // 验证设备信息的基本字段
        assert!(!device_info.device_id.is_empty());
        assert!(!device_info.device_name.is_empty());
        assert_eq!(device_info.platform, MobilePlatform::Unknown);
    }

    #[tokio::test]
    async fn test_device_capabilities() {
        let device_info = MobileDeviceInfo::default();
        
        // 测试设备能力检测
        let capabilities = device_info.get_device_capabilities();
        assert!(capabilities.contains_key("biometric_support"));
        assert!(capabilities.contains_key("secure_storage_support"));
        assert!(capabilities.contains_key("notification_support"));
    }

    #[tokio::test]
    async fn test_storage_info() {
        let device_info = MobileDeviceInfo::default();
        let storage_info = device_info.get_storage_info();
        
        assert!(storage_info.total_space >= 0);
        assert!(storage_info.available_space >= 0);
        assert!(storage_info.available_space <= storage_info.total_space);
    }

    #[tokio::test]
    async fn test_platform_config() {
        let ios_config = MobilePlatform::IOS.get_platform_config();
        let android_config = MobilePlatform::Android.get_platform_config();
        
        assert!(ios_config.contains_key("keychain_service"));
        assert!(android_config.contains_key("keystore_alias_prefix"));
    }

    #[tokio::test]
    async fn test_platform_detector_caching() {
        // 测试平台检测器的缓存功能
        let platform1 = PlatformDetector::detect_current_platform();
        let platform2 = PlatformDetector::detect_current_platform();
        
        assert_eq!(platform1, platform2);
    }

    #[tokio::test]
    async fn test_device_info_serialization() {
        let device_info = MobileDeviceInfo::default();
        
        // 测试序列化
        let serialized = serde_json::to_string(&device_info);
        assert!(serialized.is_ok());
        
        // 测试反序列化
        let deserialized: Result<MobileDeviceInfo, _> = 
            serde_json::from_str(&serialized.unwrap());
        assert!(deserialized.is_ok());
    }

    #[tokio::test]
    async fn test_platform_feature_support() {
        let ios = MobilePlatform::IOS;
        let android = MobilePlatform::Android;
        let unknown = MobilePlatform::Unknown;
        
        // 测试各平台功能支持
        assert!(ios.supports_secure_storage());
        assert!(android.supports_secure_storage());
        assert!(!unknown.supports_secure_storage());
        
        assert!(ios.supports_push_notifications());
        assert!(android.supports_push_notifications());
        assert!(!unknown.supports_push_notifications());
    }

    #[tokio::test]
    async fn test_error_handling() {
        // 测试错误处理
        let error = MobileError::PlatformNotSupported("Test platform".to_string());
        assert!(matches!(error, MobileError::PlatformNotSupported(_)));
        
        let error_string = error.to_string();
        assert!(error_string.contains("Test platform"));
    }
} 