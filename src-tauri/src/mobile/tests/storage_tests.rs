//! 安全存储功能测试

#[cfg(test)]
mod tests {
    use crate::mobile::{
        traits::SecureStorageProvider,
        ios::{IOSSecureStorage, IOSSecureStorageFactory},
        android::{AndroidSecureStorage, AndroidSecureStorageFactory},
        errors::MobileError,
    };

    #[tokio::test]
    async fn test_ios_secure_storage_basic_operations() {
        let mut storage = IOSSecureStorageFactory::create_default();
        
        // 初始化
        let init_result = storage.initialize().await;
        assert!(init_result.is_ok());
        
        // 存储数据
        let store_result = storage.store("test_key", "test_value").await;
        assert!(store_result.is_ok());
        
        // 检查存在性
        let exists_result = storage.exists("test_key").await;
        assert!(exists_result.is_ok());
        assert!(exists_result.unwrap());
        
        // 检索数据
        let retrieve_result = storage.retrieve("test_key").await;
        assert!(retrieve_result.is_ok());
        assert_eq!(retrieve_result.unwrap(), Some("test_value".to_string()));
        
        // 删除数据
        let remove_result = storage.remove("test_key").await;
        assert!(remove_result.is_ok());
        assert!(remove_result.unwrap());
        
        // 验证删除
        let exists_after_remove = storage.exists("test_key").await;
        assert!(exists_after_remove.is_ok());
        assert!(!exists_after_remove.unwrap());
    }

    #[tokio::test]
    async fn test_android_secure_storage_basic_operations() {
        let mut storage = AndroidSecureStorageFactory::create_default();
        
        // 初始化
        let init_result = storage.initialize().await;
        assert!(init_result.is_ok());
        
        // 存储数据
        let store_result = storage.store("test_key", "test_value").await;
        assert!(store_result.is_ok());
        
        // 检查存在性
        let exists_result = storage.exists("test_key").await;
        assert!(exists_result.is_ok());
        assert!(exists_result.unwrap());
        
        // 检索数据
        let retrieve_result = storage.retrieve("test_key").await;
        assert!(retrieve_result.is_ok());
        // 注意：Android实现返回模拟数据
        assert!(retrieve_result.unwrap().is_some());
        
        // 删除数据
        let remove_result = storage.remove("test_key").await;
        assert!(remove_result.is_ok());
        assert!(remove_result.unwrap());
    }

    #[tokio::test]
    async fn test_storage_without_initialization() {
        let mut ios_storage = IOSSecureStorageFactory::create_default();
        let mut android_storage = AndroidSecureStorageFactory::create_default();
        
        // 测试未初始化时的操作
        let ios_store_result = ios_storage.store("key", "value").await;
        assert!(ios_store_result.is_err());
        assert!(matches!(ios_store_result.unwrap_err(), MobileError::SecureStorageError(_)));
        
        let android_store_result = android_storage.store("key", "value").await;
        assert!(android_store_result.is_err());
        assert!(matches!(android_store_result.unwrap_err(), MobileError::SecureStorageError(_)));
    }

    #[tokio::test]
    async fn test_storage_multiple_keys() {
        let mut storage = IOSSecureStorageFactory::create_default();
        storage.initialize().await.unwrap();
        
        // 存储多个键值对
        let keys_values = vec![
            ("key1", "value1"),
            ("key2", "value2"),
            ("key3", "value3"),
        ];
        
        for (key, value) in &keys_values {
            let result = storage.store(key, value).await;
            assert!(result.is_ok());
        }
        
        // 获取所有键
        let all_keys_result = storage.get_all_keys().await;
        assert!(all_keys_result.is_ok());
        let all_keys = all_keys_result.unwrap();
        
        // 验证所有键都存在
        for (key, _) in &keys_values {
            assert!(all_keys.contains(&key.to_string()));
        }
        
        // 清除所有数据
        let clear_result = storage.clear().await;
        assert!(clear_result.is_ok());
        
        // 验证清除后没有键
        let keys_after_clear = storage.get_all_keys().await.unwrap();
        assert!(keys_after_clear.is_empty());
    }

    #[tokio::test]
    async fn test_storage_factory_configurations() {
        // 测试iOS工厂
        let ios_default = IOSSecureStorageFactory::create_default();
        assert!(!ios_default.is_initialized());
        
        let ios_high_security = IOSSecureStorageFactory::create_high_security();
        assert!(!ios_high_security.is_initialized());
        
        // 测试Android工厂
        let android_default = AndroidSecureStorageFactory::create_default();
        assert!(!android_default.is_initialized());
        
        let android_high_security = AndroidSecureStorageFactory::create_high_security();
        assert!(!android_high_security.is_initialized());
        
        let android_basic_security = AndroidSecureStorageFactory::create_basic_security();
        assert!(!android_basic_security.is_initialized());
    }

    #[tokio::test]
    async fn test_storage_edge_cases() {
        let mut storage = IOSSecureStorageFactory::create_default();
        storage.initialize().await.unwrap();
        
        // 测试空键
        let empty_key_result = storage.store("", "value").await;
        assert!(empty_key_result.is_ok());
        
        // 测试空值
        let empty_value_result = storage.store("key", "").await;
        assert!(empty_value_result.is_ok());
        
        // 测试特殊字符
        let special_chars_result = storage.store("key_with_特殊字符_123", "value_with_特殊字符").await;
        assert!(special_chars_result.is_ok());
        
        // 测试长键名
        let long_key = "a".repeat(1000);
        let long_key_result = storage.store(&long_key, "value").await;
        assert!(long_key_result.is_ok());
        
        // 测试长值
        let long_value = "b".repeat(10000);
        let long_value_result = storage.store("key", &long_value).await;
        assert!(long_value_result.is_ok());
    }

    #[tokio::test]
    async fn test_storage_retrieve_nonexistent() {
        let mut storage = IOSSecureStorageFactory::create_default();
        storage.initialize().await.unwrap();
        
        // 检索不存在的键
        let result = storage.retrieve("nonexistent_key").await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
        
        // 检查不存在的键
        let exists_result = storage.exists("nonexistent_key").await;
        assert!(exists_result.is_ok());
        assert!(!exists_result.unwrap());
        
        // 删除不存在的键
        let remove_result = storage.remove("nonexistent_key").await;
        assert!(remove_result.is_ok());
        // 删除不存在的键应该返回false
        assert!(!remove_result.unwrap());
    }

    #[tokio::test]
    async fn test_storage_concurrent_operations() {
        let mut storage = IOSSecureStorageFactory::create_default();
        storage.initialize().await.unwrap();
        
        // 并发存储操作
        let mut handles = vec![];
        for i in 0..10 {
            let key = format!("concurrent_key_{}", i);
            let value = format!("concurrent_value_{}", i);
            
            // 注意：这里需要克隆storage或使用Arc<Mutex<>>来支持真正的并发
            // 当前实现仅作为测试结构示例
            let store_result = storage.store(&key, &value).await;
            assert!(store_result.is_ok());
        }
        
        // 验证所有数据都存储成功
        for i in 0..10 {
            let key = format!("concurrent_key_{}", i);
            let exists_result = storage.exists(&key).await;
            assert!(exists_result.is_ok());
            assert!(exists_result.unwrap());
        }
    }

    #[tokio::test]
    async fn test_storage_error_scenarios() {
        // 测试各种错误场景
        let error = MobileError::SecureStorageError("Test error".to_string());
        assert!(matches!(error, MobileError::SecureStorageError(_)));
        
        let error_string = error.to_string();
        assert!(error_string.contains("Test error"));
    }

    #[tokio::test]
    async fn test_storage_performance() {
        let mut storage = IOSSecureStorageFactory::create_default();
        storage.initialize().await.unwrap();
        
        let start_time = std::time::Instant::now();
        
        // 执行大量操作
        for i in 0..100 {
            let key = format!("perf_key_{}", i);
            let value = format!("perf_value_{}", i);
            
            storage.store(&key, &value).await.unwrap();
            storage.retrieve(&key).await.unwrap();
            storage.exists(&key).await.unwrap();
        }
        
        let elapsed = start_time.elapsed();
        
        // 验证操作在合理时间内完成（这里设置为5秒，实际可根据需要调整）
        assert!(elapsed.as_secs() < 5);
        
        println!("存储性能测试完成，耗时: {:?}", elapsed);
    }
} 