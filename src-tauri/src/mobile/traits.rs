/// 移动端公共trait定义
/// 
/// 定义移动端平台的抽象接口，用于统一iOS和Android的功能实现

use async_trait::async_trait;

/// 移动端平台错误类型
pub type MobileResult<T> = Result<T, String>;

/// 安全存储trait
/// 
/// 定义移动端安全存储的统一接口
#[async_trait]
pub trait SecureStorageProvider: Send + Sync {
    /// 存储安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// * `value` - 存储值
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn store(&self, key: &str, value: &str) -> MobileResult<()>;

    /// 获取安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 成功返回存储的值，不存在返回None，失败返回错误信息
    async fn get(&self, key: &str) -> MobileResult<Option<String>>;

    /// 删除安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn delete(&self, key: &str) -> MobileResult<()>;

    /// 检查键是否存在
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 存在返回true，不存在返回false
    async fn exists(&self, key: &str) -> MobileResult<bool>;

    /// 清空所有数据
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn clear(&self) -> MobileResult<()>;
}

/// 通知管理trait
/// 
/// 定义移动端通知功能的统一接口
#[async_trait]
pub trait NotificationProvider: Send + Sync {
    /// 显示通知
    /// 
    /// # 参数
    /// * `title` - 通知标题
    /// * `message` - 通知内容
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn show_notification(&self, title: &str, message: &str) -> MobileResult<()>;

    /// 显示带图标的通知
    /// 
    /// # 参数
    /// * `title` - 通知标题
    /// * `message` - 通知内容
    /// * `icon` - 图标路径或数据
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn show_notification_with_icon(&self, title: &str, message: &str, icon: &str) -> MobileResult<()>;

    /// 取消通知
    /// 
    /// # 参数
    /// * `notification_id` - 通知ID
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn cancel_notification(&self, notification_id: &str) -> MobileResult<()>;

    /// 检查通知权限
    /// 
    /// # 返回值
    /// 有权限返回true，无权限返回false
    async fn has_notification_permission(&self) -> MobileResult<bool>;

    /// 请求通知权限
    /// 
    /// # 返回值
    /// 成功获取权限返回true，失败返回false
    async fn request_notification_permission(&self) -> MobileResult<bool>;
}

/// 生物识别认证trait
/// 
/// 定义移动端生物识别功能的统一接口
#[async_trait]
pub trait BiometricProvider: Send + Sync {
    /// 检查生物识别是否可用
    /// 
    /// # 返回值
    /// 可用返回true，不可用返回false
    async fn is_available(&self) -> MobileResult<bool>;

    /// 检查是否已注册生物识别
    /// 
    /// # 返回值
    /// 已注册返回true，未注册返回false
    async fn is_enrolled(&self) -> MobileResult<bool>;

    /// 执行生物识别认证
    /// 
    /// # 参数
    /// * `reason` - 认证原因说明
    /// 
    /// # 返回值
    /// 认证成功返回true，失败返回false
    async fn authenticate(&self, reason: &str) -> MobileResult<bool>;

    /// 获取支持的生物识别类型
    /// 
    /// # 返回值
    /// 返回支持的生物识别类型列表
    async fn get_supported_types(&self) -> MobileResult<Vec<String>>;
}

/// 平台特定功能trait
/// 
/// 定义移动端平台特定功能的统一接口
#[async_trait]
pub trait PlatformProvider: Send + Sync {
    /// 初始化平台特定功能
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn initialize(&self) -> MobileResult<()>;

    /// 获取平台名称
    /// 
    /// # 返回值
    /// 返回平台名称字符串
    fn get_platform_name(&self) -> &'static str;

    /// 获取平台版本
    /// 
    /// # 返回值
    /// 返回平台版本字符串
    async fn get_platform_version(&self) -> MobileResult<String>;

    /// 检查平台特定功能是否支持
    /// 
    /// # 参数
    /// * `feature` - 功能名称
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    async fn is_feature_supported(&self, feature: &str) -> MobileResult<bool>;

    /// 获取设备信息
    /// 
    /// # 返回值
    /// 返回设备信息的键值对
    async fn get_device_info(&self) -> MobileResult<std::collections::HashMap<String, String>>;
} 