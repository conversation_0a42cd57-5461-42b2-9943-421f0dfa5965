# Native Messaging 模块化系统

这是一个使用 `async_handler` 实现的模块化 Native Messaging 系统，用于与 Chrome 扩展进行通信，不会阻塞主进程。

## 特性

- **模块化设计**: 分离协议、处理器和服务器逻辑
- **异步处理**: 使用 `async_handler` 在后台处理消息
- **错误处理**: 完善的错误处理和重试机制
- **统计监控**: 内置消息处理统计和健康检查
- **向后兼容**: 保持与旧 API 的兼容性

## 模块结构

```
native_messaging/
├── mod.rs          # 主模块和管理器
├── protocol.rs     # 消息协议定义
├── handler.rs      # 消息处理器
└── server.rs       # 通信服务器
```

## 使用方法

### 1. 基本启动

在 `lib.rs` 的 `setup` 函数中启动服务：

```rust
use native_messaging;

// 在 setup 闭包中
let app_handle = app.handle().clone();

// 启动 Native Messaging 服务
if let Err(e) = native_messaging::setup_native_messaging(app_handle) {
    log::error!("启动 Native Messaging 失败: {}", e);
} else {
    log::info!("Native Messaging 服务已启动");
}
```

### 2. 手动管理

```rust
use native_messaging::{NativeMessagingManager, NativeMessagingStatus};

// 创建管理器
let mut manager = NativeMessagingManager::new();

// 启动服务
match manager.start(app_handle) {
    Ok(_) => log::info!("服务启动成功"),
    Err(e) => log::error!("服务启动失败: {}", e),
}

// 检查状态
if manager.status() == NativeMessagingStatus::Running {
    log::info!("服务正在运行");
}

// 停止服务
manager.stop();
```

### 3. 自定义消息处理

```rust
use native_messaging::{MessageHandler, NativeRequest, NativeResponse, MessageType};
use serde_json::Value;

// 创建处理器
let handler = MessageHandler::new(app_handle);

// 处理消息
let response = handler.handle_message(message_json).await;
```

## 支持的消息类型

### 获取域名密码请求

**请求格式:**
```json
{
    "type": "getPasswordsForDomainRequest",
    "requestId": "unique-request-id",
    "options": {
        "domain": "example.com"
    }
}
```

**响应格式:**
```json
{
    "type": "getPasswordsForDomainResponse",
    "requestId": "unique-request-id",
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "Example Account",
            "username": "<EMAIL>",
            "password": "encrypted-password",
            "url": "https://example.com"
        }
    ]
}
```

## 错误处理

系统提供多层错误处理：

1. **协议验证**: 验证消息格式和必需字段
2. **超时处理**: 30秒消息处理超时
3. **重试机制**: 服务启动失败时自动重试（最多3次）
4. **错误响应**: 标准化的错误响应格式

**错误响应格式:**
```json
{
    "type": "genericErrorResponse",
    "requestId": "request-id",
    "success": false,
    "error": "错误描述"
}
```

## 监控和统计

### 获取统计信息

```rust
use native_messaging::NativeMessagingServer;

let server = NativeMessagingServer::new(app_handle);

// 获取统计信息
if let Ok(stats) = server.get_stats() {
    log::info!("总消息数: {}", stats.total_messages);
    log::info!("成功率: {:.1}%", stats.success_rate() * 100.0);
    log::info!("平均处理时间: {:.1}ms", stats.average_processing_time_ms);
}

// 健康检查
let health = server.health_check().await;
log::info!("健康状态: {}", health.health);
```

### 健康状态

- **healthy**: 成功率 ≥ 95% 且平均处理时间 < 1000ms
- **warning**: 成功率 ≥ 80% 且平均处理时间 < 2000ms  
- **unhealthy**: 其他情况
- **unknown**: 无法获取统计信息

## 配置选项

### 消息大小限制

默认最大消息大小为 1MB，可以在 `server.rs` 中修改：

```rust
// 验证消息长度合理性
if length > 1024 * 1024 {  // 1MB 限制
    return Err("消息长度过大".to_string());
}
```

### 超时设置

默认消息处理超时为 30 秒，可以在 `handler.rs` 中修改：

```rust
let handle = AsyncTaskManager::spawn_task_with_timeout(
    async move {
        handler.process_request(request).await
    },
    Duration::from_secs(30), // 30 秒超时
);
```

### 重试配置

服务启动重试配置在 `mod.rs` 中：

```rust
AsyncTaskManager::spawn_task_with_retry(
    // ... 任务逻辑
    3, // 最多重试 3 次
    Duration::from_secs(2), // 重试间隔 2 秒
);
```

## 向后兼容性

为了保持向后兼容，提供了兼容的函数：

```rust
// 旧的 API 仍然可用
let response = native_messaging::handle_message(app_handle, message_json).await;
```

## 最佳实践

1. **错误处理**: 始终检查服务启动结果
2. **日志记录**: 启用适当的日志级别进行调试
3. **资源管理**: 在应用关闭时停止服务
4. **监控**: 定期检查健康状态和统计信息
5. **测试**: 使用提供的测试工具验证功能

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查 `async_handler` 模块是否正确初始化
   - 确认没有其他实例在运行

2. **消息处理超时**
   - 检查数据库连接状态
   - 验证 `get_credentials_for_domain` 命令是否正常工作

3. **连接断开**
   - 检查 Chrome 扩展是否正常运行
   - 验证 stdin/stdout 管道是否正常

### 调试技巧

启用详细日志：

```rust
// 在 main.rs 或 lib.rs 中
env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
```

查看消息流：

```rust
log::debug!("收到消息: {:?}", message);
log::debug!("发送响应: {:?}", response);
```

## 性能优化

1. **批量处理**: 对于大量请求，考虑批量处理
2. **缓存**: 缓存频繁访问的数据
3. **连接池**: 使用数据库连接池
4. **异步优化**: 避免在异步任务中使用阻塞操作

## 安全考虑

1. **输入验证**: 严格验证所有输入数据
2. **权限控制**: 确保只有授权的扩展可以访问
3. **数据加密**: 敏感数据传输时进行加密
4. **审计日志**: 记录所有重要操作用于审计 