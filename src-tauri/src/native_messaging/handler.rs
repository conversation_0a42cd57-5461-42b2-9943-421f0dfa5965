use super::protocol::{
    GetPasswordsForDomainOptions, MessageType, NativeRequest, NativeResponse, ProtocolValidator,
};
/// Native Messaging 消息处理器
///
/// 使用 async_handler 在后台处理不同类型的消息
use crate::async_handler::AsyncTaskManager;
use serde_json::Value;
use std::time::Duration;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 消息处理器
#[derive(Clone)]
pub struct MessageHandler {
    app_handle: AppHandle,
}

impl MessageHandler {
    /// 创建新的消息处理器
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }

    /// 处理接收到的消息
    ///
    /// 使用 AsyncTaskManager 在后台处理消息，避免阻塞主进程
    pub async fn handle_message(&self, message_json: Value) -> Value {
        log::debug!("收到 Native Messaging 消息: {:?}", message_json);

        // 解析请求
        let request = match self.parse_request(message_json).await {
            Ok(req) => req,
            Err(response) => return response,
        };

        // 验证请求
        if let Err(error) = ProtocolValidator::validate_request(&request) {
            log::error!("请求验证失败: {}", error);
            return self.create_error_response(
                &request.request_id,
                &error,
                MessageType::GenericErrorResponse,
            );
        }

        // 使用 AsyncTaskManager 处理消息
        let handler = self.clone();
        let handle = AsyncTaskManager::spawn_task_with_timeout(
            async move { handler.process_request(request).await },
            Duration::from_secs(30), // 30 秒超时
        );

        match handle.await {
            Ok(Ok(response)) => response,
            Ok(Err(_)) => {
                log::error!("消息处理超时");
                self.create_error_response(
                    "timeout",
                    "消息处理超时",
                    MessageType::GenericErrorResponse,
                )
            }
            Err(e) => {
                log::error!("消息处理任务失败: {:?}", e);
                self.create_error_response(
                    "task_failed",
                    "消息处理任务失败",
                    MessageType::GenericErrorResponse,
                )
            }
        }
    }

    /// 解析请求消息
    async fn parse_request(&self, message_json: Value) -> Result<NativeRequest, Value> {
        match serde_json::from_value::<NativeRequest>(message_json.clone()) {
            Ok(request) => {
                log::debug!("成功解析请求: {:?}", request);
                Ok(request)
            }
            Err(e) => {
                log::error!("解析请求 JSON 失败: {}", e);

                // 尝试从原始 JSON 中获取 request_id
                let request_id = message_json
                    .get("requestId")
                    .and_then(|v| v.as_str())
                    .unwrap_or("unknown_request_id");

                let error_msg = format!("解析请求 JSON 失败: {}", e);
                Err(self.create_error_response(
                    request_id,
                    &error_msg,
                    MessageType::GenericErrorResponse,
                ))
            }
        }
    }

    /// 处理具体的请求
    async fn process_request(&self, request: NativeRequest) -> Value {
        log::info!(
            "处理消息类型: {} (请求 ID: {})",
            request.message_type,
            request.request_id
        );

        match request.get_message_type() {
            MessageType::GetPasswordsForDomainRequest => {
                self.handle_get_passwords_for_domain(request).await
            }
            MessageType::Unknown(msg_type) => {
                log::warn!("未知消息类型: {}", msg_type);
                self.create_error_response(
                    &request.request_id,
                    &format!("未知消息类型: {}", msg_type),
                    MessageType::GenericErrorResponse,
                )
            }
            _ => {
                log::warn!("不支持的消息类型: {:?}", request.get_message_type());
                self.create_error_response(
                    &request.request_id,
                    "不支持的消息类型",
                    MessageType::GenericErrorResponse,
                )
            }
        }
    }

    /// 处理获取域名密码的请求
    async fn handle_get_passwords_for_domain(&self, request: NativeRequest) -> Value {
        log::info!("处理获取域名密码请求 (请求 ID: {})", request.request_id);

        // 解析域名选项
        let domain_options = match request.parse_options::<GetPasswordsForDomainOptions>() {
            Ok(options) => options,
            Err(error) => {
                log::error!("解析域名选项失败: {}", error);
                return self.create_error_response(
                    &request.request_id,
                    &error,
                    MessageType::GetPasswordsForDomainResponse,
                );
            }
        };

        log::info!("获取域名 {} 的密码", domain_options.domain);

        // 使用 AsyncTaskManager 处理可能的阻塞操作
        let app_handle = self.app_handle.clone();
        let domain = domain_options.domain.clone();
        let request_id = request.request_id.clone();

        let handle = AsyncTaskManager::spawn_task(async move {
            // 调用 Tauri 命令获取密码
            match crate::hybrid_storage::commands::search_credentials_by_domain_hybrid(
                domain.clone(),
                app_handle.state::<crate::state::AppState>(),
            )
            .await
            {
                Ok(credentials) => {
                    log::info!("成功获取域名 {} 的密码", domain);

                    // 序列化密码数据
                    match serde_json::to_value(credentials) {
                        Ok(data) => Ok(data),
                        Err(e) => {
                            log::error!("序列化密码数据失败: {}", e);
                            Err(format!("序列化密码数据失败: {}", e))
                        }
                    }
                }
                Err(e) => {
                    log::error!("获取域名 {} 的密码失败: {}", domain, e);
                    Err(format!("获取密码失败: {}", e))
                }
            }
        });

        match handle.await {
            Ok(Ok(data)) => {
                log::info!("成功处理域名 {} 的密码请求", domain_options.domain);
                self.create_success_response(
                    &request_id,
                    data,
                    MessageType::GetPasswordsForDomainResponse,
                )
            }
            Ok(Err(error)) => {
                log::error!("处理域名密码请求失败: {}", error);
                self.create_error_response(
                    &request_id,
                    &error,
                    MessageType::GetPasswordsForDomainResponse,
                )
            }
            Err(e) => {
                log::error!("域名密码请求任务失败: {:?}", e);
                self.create_error_response(
                    &request_id,
                    "内部任务执行失败",
                    MessageType::GetPasswordsForDomainResponse,
                )
            }
        }
    }

    /// 创建成功响应
    fn create_success_response(
        &self,
        request_id: &str,
        data: Value,
        message_type: MessageType,
    ) -> Value {
        let response = NativeResponse::success(request_id.to_string(), data, message_type.clone());

        match response.to_value() {
            Ok(value) => {
                log::debug!("创建成功响应: {:?}", value);
                value
            }
            Err(e) => {
                log::error!("序列化成功响应失败: {}", e);
                NativeResponse::serialization_fallback(request_id, message_type, &e.to_string())
            }
        }
    }

    /// 创建错误响应
    fn create_error_response(
        &self,
        request_id: &str,
        error_message: &str,
        message_type: MessageType,
    ) -> Value {
        let response = NativeResponse::error(
            request_id.to_string(),
            error_message.to_string(),
            message_type.clone(),
        );

        match response.to_value() {
            Ok(value) => {
                log::debug!("创建错误响应: {:?}", value);
                value
            }
            Err(e) => {
                log::error!("序列化错误响应失败: {}", e);
                NativeResponse::serialization_fallback(request_id, message_type, &e.to_string())
            }
        }
    }
}

/// 消息处理统计
#[derive(Debug, Default, Clone)]
pub struct MessageStats {
    pub total_messages: u64,
    pub successful_messages: u64,
    pub failed_messages: u64,
    pub average_processing_time_ms: f64,
}

impl MessageStats {
    /// 记录成功的消息处理
    pub fn record_success(&mut self, processing_time_ms: f64) {
        self.total_messages += 1;
        self.successful_messages += 1;
        self.update_average_time(processing_time_ms);
    }

    /// 记录失败的消息处理
    pub fn record_failure(&mut self, processing_time_ms: f64) {
        self.total_messages += 1;
        self.failed_messages += 1;
        self.update_average_time(processing_time_ms);
    }

    /// 更新平均处理时间
    fn update_average_time(&mut self, processing_time_ms: f64) {
        let total_time = self.average_processing_time_ms * (self.total_messages - 1) as f64;
        self.average_processing_time_ms =
            (total_time + processing_time_ms) / self.total_messages as f64;
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_messages == 0 {
            0.0
        } else {
            self.successful_messages as f64 / self.total_messages as f64
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_message_stats() {
        let mut stats = MessageStats::default();

        stats.record_success(100.0);
        stats.record_success(200.0);
        stats.record_failure(150.0);

        assert_eq!(stats.total_messages, 3);
        assert_eq!(stats.successful_messages, 2);
        assert_eq!(stats.failed_messages, 1);
        assert_eq!(stats.success_rate(), 2.0 / 3.0);
        assert_eq!(stats.average_processing_time_ms, 150.0);
    }

    #[test]
    fn test_message_stats_empty() {
        let stats = MessageStats::default();
        assert_eq!(stats.success_rate(), 0.0);
        assert_eq!(stats.average_processing_time_ms, 0.0);
    }
}
