/// 模块化的 Native Messaging 系统
///
/// 使用 async_handler 在后台处理 Chrome 扩展消息，不阻塞主进程
use crate::async_handler::AsyncTaskManager;
use serde_json::Value;
use std::time::Duration;
use tauri::AppHandle;

pub mod handler;
pub mod protocol;
pub mod server;

pub use handler::MessageHandler;
pub use protocol::{GetPasswordsForDomainOptions, MessageType, NativeRequest, NativeResponse};
pub use server::{HealthStatus, NativeMessagingServer};

/// Native Messaging 管理器
pub struct NativeMessagingManager {
    server: Option<NativeMessagingServer>,
    is_running: std::sync::Arc<std::sync::atomic::AtomicBool>,
}

impl NativeMessagingManager {
    /// 创建新的 Native Messaging 管理器
    pub fn new() -> Self {
        Self {
            server: None,
            is_running: std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false)),
        }
    }

    /// 启动 Native Messaging 服务
    ///
    /// 这个方法会在后台启动服务，不会阻塞主进程
    pub fn start(&mut self, app_handle: AppHandle) -> Result<(), String> {
        if self.is_running.load(std::sync::atomic::Ordering::Relaxed) {
            return Err("Native Messaging 服务已经在运行".to_string());
        }

        log::info!("启动 Native Messaging 服务");

        let server = NativeMessagingServer::new(app_handle);
        let running_flag = self.is_running.clone();

        // 使用 AsyncTaskManager 在后台启动服务
        let server_clone = server.clone();
        AsyncTaskManager::spawn_task_with_retry(
            move || {
                let server = server_clone.clone();
                let flag = running_flag.clone();
                async move {
                    flag.store(true, std::sync::atomic::Ordering::Relaxed);

                    match server.run().await {
                        Ok(_) => {
                            log::info!("Native Messaging 服务正常结束");
                            Ok(())
                        }
                        Err(e) => {
                            log::error!("Native Messaging 服务错误: {}", e);
                            flag.store(false, std::sync::atomic::Ordering::Relaxed);
                            Err(e)
                        }
                    }
                }
            },
            3,                      // 最多重试 3 次
            Duration::from_secs(2), // 重试间隔 2 秒
        );

        self.server = Some(server);
        Ok(())
    }

    /// 停止 Native Messaging 服务
    pub fn stop(&mut self) {
        if self.is_running.load(std::sync::atomic::Ordering::Relaxed) {
            log::info!("停止 Native Messaging 服务");
            self.is_running
                .store(false, std::sync::atomic::Ordering::Relaxed);
            self.server = None;
        }
    }

    /// 检查服务是否正在运行
    pub fn is_running(&self) -> bool {
        self.is_running.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 获取服务状态
    pub fn status(&self) -> NativeMessagingStatus {
        if self.is_running() {
            NativeMessagingStatus::Running
        } else {
            NativeMessagingStatus::Stopped
        }
    }
}

impl Default for NativeMessagingManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Native Messaging 服务状态
#[derive(Debug, Clone, PartialEq)]
pub enum NativeMessagingStatus {
    Running,
    Stopped,
}

/// 便捷函数：初始化并启动 Native Messaging 服务
pub fn setup_native_messaging(app_handle: AppHandle) -> Result<NativeMessagingManager, String> {
    let mut manager = NativeMessagingManager::new();
    manager.start(app_handle)?;
    Ok(manager)
}

/// 向后兼容的函数：处理消息
pub async fn handle_message(app_handle: AppHandle, message_json: Value) -> Value {
    let handler = MessageHandler::new(app_handle);
    handler.handle_message(message_json).await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_manager_creation() {
        let manager = NativeMessagingManager::new();
        assert!(!manager.is_running());
        assert_eq!(manager.status(), NativeMessagingStatus::Stopped);
    }

    #[test]
    fn test_default_manager() {
        let manager = NativeMessagingManager::default();
        assert!(!manager.is_running());
    }
}
