/// Native Messaging 协议定义
///
/// 定义与 Chrome 扩展通信的消息格式
use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 从 Chrome 扩展接收的请求消息
#[derive(Deserialize, Debug, Clone)]
pub struct NativeRequest {
    #[serde(rename = "type")]
    pub message_type: String,
    #[serde(rename = "requestId")]
    pub request_id: String,
    pub options: Option<Value>,
}

/// 发送给 Chrome 扩展的响应消息
#[derive(Serialize, Debug)]
pub struct NativeResponse {
    #[serde(rename = "type")]
    pub message_type: String,
    #[serde(rename = "requestId")]
    pub request_id: String,
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

/// 获取域名密码的请求选项
#[derive(Deserialize, Debug)]
pub struct GetPasswordsForDomainOptions {
    pub domain: String,
}

/// 消息类型枚举
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum MessageType {
    GetPasswordsForDomainRequest,
    GetPasswordsForDomainResponse,
    GenericErrorResponse,
    Unknown(String),
}

impl From<&str> for MessageType {
    fn from(s: &str) -> Self {
        match s {
            "getPasswordsForDomainRequest" => MessageType::GetPasswordsForDomainRequest,
            "getPasswordsForDomainResponse" => MessageType::GetPasswordsForDomainResponse,
            "genericErrorResponse" => MessageType::GenericErrorResponse,
            other => MessageType::Unknown(other.to_string()),
        }
    }
}

impl From<MessageType> for String {
    fn from(msg_type: MessageType) -> Self {
        match msg_type {
            MessageType::GetPasswordsForDomainRequest => "getPasswordsForDomainRequest".to_string(),
            MessageType::GetPasswordsForDomainResponse => {
                "getPasswordsForDomainResponse".to_string()
            }
            MessageType::GenericErrorResponse => "genericErrorResponse".to_string(),
            MessageType::Unknown(s) => s,
        }
    }
}

impl NativeResponse {
    /// 创建成功响应
    pub fn success(request_id: String, data: Value, message_type: MessageType) -> Self {
        Self {
            message_type: message_type.into(),
            request_id,
            success: true,
            data: Some(data),
            error: None,
        }
    }

    /// 创建错误响应
    pub fn error(request_id: String, error_message: String, message_type: MessageType) -> Self {
        Self {
            message_type: message_type.into(),
            request_id,
            success: false,
            data: None,
            error: Some(error_message),
        }
    }

    /// 转换为 JSON Value
    pub fn to_value(&self) -> Result<Value, serde_json::Error> {
        serde_json::to_value(self)
    }

    /// 创建序列化失败的备用响应
    pub fn serialization_fallback(
        request_id: &str,
        message_type: MessageType,
        original_error: &str,
    ) -> Value {
        serde_json::json!({
            "type": String::from(message_type),
            "requestId": request_id,
            "success": false,
            "error": format!("序列化失败: {}. 原始错误: {}", "内部错误", original_error)
        })
    }
}

impl NativeRequest {
    /// 解析请求选项为特定类型
    pub fn parse_options<T>(&self) -> Result<T, String>
    where
        T: for<'de> Deserialize<'de>,
    {
        match &self.options {
            Some(options) => {
                serde_json::from_value(options.clone()).map_err(|e| format!("解析选项失败: {}", e))
            }
            None => Err("缺少请求选项".to_string()),
        }
    }

    /// 获取消息类型枚举
    pub fn get_message_type(&self) -> MessageType {
        MessageType::from(self.message_type.as_str())
    }
}

/// 协议验证器
pub struct ProtocolValidator;

impl ProtocolValidator {
    /// 验证请求消息格式
    pub fn validate_request(request: &NativeRequest) -> Result<(), String> {
        if request.request_id.is_empty() {
            return Err("请求 ID 不能为空".to_string());
        }

        if request.message_type.is_empty() {
            return Err("消息类型不能为空".to_string());
        }

        match request.get_message_type() {
            MessageType::GetPasswordsForDomainRequest => {
                // 验证域名请求需要选项
                if request.options.is_none() {
                    return Err("域名请求需要提供选项".to_string());
                }

                // 尝试解析域名选项
                request.parse_options::<GetPasswordsForDomainOptions>()?;
            }
            MessageType::Unknown(_) => {
                log::warn!("收到未知消息类型: {}", request.message_type);
            }
            _ => {}
        }

        Ok(())
    }

    /// 验证响应消息格式
    pub fn validate_response(response: &NativeResponse) -> Result<(), String> {
        if response.request_id.is_empty() {
            return Err("响应请求 ID 不能为空".to_string());
        }

        if response.message_type.is_empty() {
            return Err("响应消息类型不能为空".to_string());
        }

        if response.success && response.data.is_none() {
            return Err("成功响应必须包含数据".to_string());
        }

        if !response.success && response.error.is_none() {
            return Err("错误响应必须包含错误信息".to_string());
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_message_type_conversion() {
        assert_eq!(
            MessageType::from("getPasswordsForDomainRequest"),
            MessageType::GetPasswordsForDomainRequest
        );

        let msg_type = MessageType::GetPasswordsForDomainResponse;
        let string_repr: String = msg_type.into();
        assert_eq!(string_repr, "getPasswordsForDomainResponse");
    }

    #[test]
    fn test_native_response_creation() {
        let response = NativeResponse::success(
            "test-123".to_string(),
            serde_json::json!({"result": "test"}),
            MessageType::GetPasswordsForDomainResponse,
        );

        assert!(response.success);
        assert_eq!(response.request_id, "test-123");
        assert!(response.data.is_some());
        assert!(response.error.is_none());
    }

    #[test]
    fn test_native_response_error() {
        let response = NativeResponse::error(
            "test-456".to_string(),
            "测试错误".to_string(),
            MessageType::GenericErrorResponse,
        );

        assert!(!response.success);
        assert_eq!(response.request_id, "test-456");
        assert!(response.data.is_none());
        assert!(response.error.is_some());
    }

    #[test]
    fn test_request_options_parsing() {
        let request = NativeRequest {
            message_type: "getPasswordsForDomainRequest".to_string(),
            request_id: "test-789".to_string(),
            options: Some(serde_json::json!({"domain": "example.com"})),
        };

        let options: GetPasswordsForDomainOptions = request.parse_options().unwrap();
        assert_eq!(options.domain, "example.com");
    }

    #[test]
    fn test_protocol_validation() {
        let valid_request = NativeRequest {
            message_type: "getPasswordsForDomainRequest".to_string(),
            request_id: "test-validation".to_string(),
            options: Some(serde_json::json!({"domain": "test.com"})),
        };

        assert!(ProtocolValidator::validate_request(&valid_request).is_ok());

        let invalid_request = NativeRequest {
            message_type: "".to_string(),
            request_id: "test".to_string(),
            options: None,
        };

        assert!(ProtocolValidator::validate_request(&invalid_request).is_err());
    }
}
