/// Native Messaging 服务器
///
/// 处理与 Chrome 扩展的底层通信
use super::handler::{MessageHandler, MessageStats};
use serde_json::Value;
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tauri::AppHandle;
use tokio::io::{AsyncReadExt, AsyncWriteExt, BufReader};

/// Native Messaging 服务器
#[derive(Clone)]
pub struct NativeMessagingServer {
    handler: MessageHandler,
    stats: Arc<Mutex<MessageStats>>,
    is_running: Arc<std::sync::atomic::AtomicBool>,
}

impl NativeMessagingServer {
    /// 创建新的服务器实例
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            handler: MessageHandler::new(app_handle),
            stats: Arc::new(Mutex::new(MessageStats::default())),
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
        }
    }

    /// 运行服务器主循环
    pub async fn run(&self) -> Result<(), String> {
        log::info!("Native Messaging 服务器启动");

        self.is_running
            .store(true, std::sync::atomic::Ordering::Relaxed);

        let stdin = tokio::io::stdin();
        let stdout = tokio::io::stdout();
        let mut reader = BufReader::new(stdin);
        let mut writer = stdout;

        while self.is_running.load(std::sync::atomic::Ordering::Relaxed) {
            // 直接处理消息，不使用 spawn_task 避免移动问题
            match self.handle_single_message(&mut reader, &mut writer).await {
                Ok(()) => {
                    // 消息处理成功，继续下一个消息
                }
                Err(error) => {
                    log::error!("处理消息时发生错误: {}", error);

                    if error.contains("stdin closed") || error.contains("connection closed") {
                        log::info!("客户端连接关闭，退出服务器循环");
                        break;
                    }

                    // 其他错误继续处理下一个消息
                }
            }
        }

        log::info!("Native Messaging 服务器停止");
        self.is_running
            .store(false, std::sync::atomic::Ordering::Relaxed);

        Ok(())
    }

    /// 处理单个消息
    async fn handle_single_message(
        &self,
        reader: &mut BufReader<tokio::io::Stdin>,
        writer: &mut tokio::io::Stdout,
    ) -> Result<(), String> {
        let start_time = Instant::now();

        // 1. 读取消息长度 (4 bytes, little-endian)
        let length = self.read_message_length(reader).await?;

        // 2. 读取消息内容
        let message_bytes = self.read_message_content(reader, length).await?;

        // 3. 解析并处理消息
        let response = self.process_message_bytes(message_bytes).await;

        // 4. 发送响应
        self.send_response(writer, response).await?;

        // 5. 记录统计信息
        let processing_time = start_time.elapsed().as_millis() as f64;
        self.record_message_stats(processing_time, true);

        Ok(())
    }

    /// 读取消息长度
    async fn read_message_length(
        &self,
        reader: &mut BufReader<tokio::io::Stdin>,
    ) -> Result<u32, String> {
        let mut length_bytes = [0u8; 4];

        match reader.read_exact(&mut length_bytes).await {
            Ok(_) => {
                let length = u32::from_le_bytes(length_bytes);
                log::debug!("读取到消息长度: {} 字节", length);

                // 验证消息长度合理性
                if length > 1024 * 1024 {
                    // 1MB 限制
                    return Err("消息长度过大".to_string());
                }

                Ok(length)
            }
            Err(e) => {
                if e.kind() == std::io::ErrorKind::UnexpectedEof {
                    Err("stdin closed".to_string())
                } else {
                    Err(format!("读取消息长度失败: {}", e))
                }
            }
        }
    }

    /// 读取消息内容
    async fn read_message_content(
        &self,
        reader: &mut BufReader<tokio::io::Stdin>,
        length: u32,
    ) -> Result<Vec<u8>, String> {
        let mut message_bytes = vec![0u8; length as usize];

        match reader.read_exact(&mut message_bytes).await {
            Ok(_) => {
                log::debug!("成功读取 {} 字节的消息内容", length);
                Ok(message_bytes)
            }
            Err(e) => Err(format!("读取消息内容失败: {}", e)),
        }
    }

    /// 处理消息字节
    async fn process_message_bytes(&self, message_bytes: Vec<u8>) -> Value {
        // 解析 JSON 消息
        match serde_json::from_slice::<Value>(&message_bytes) {
            Ok(message) => {
                log::debug!("收到消息: {:?}", message);

                // 使用处理器处理消息
                self.handler.handle_message(message).await
            }
            Err(e) => {
                log::error!("解析 JSON 消息失败: {}", e);

                // 创建错误响应
                serde_json::json!({
                    "type": "genericErrorResponse",
                    "requestId": "parse_error",
                    "success": false,
                    "error": format!("解析 JSON 消息失败: {}", e)
                })
            }
        }
    }

    /// 发送响应
    async fn send_response(
        &self,
        writer: &mut tokio::io::Stdout,
        response: Value,
    ) -> Result<(), String> {
        log::debug!("发送响应: {:?}", response);

        // 序列化响应
        let response_bytes = match serde_json::to_vec(&response) {
            Ok(bytes) => bytes,
            Err(e) => {
                log::error!("序列化响应失败: {}", e);

                // 创建备用错误响应
                let fallback_response = serde_json::json!({
                    "type": "genericErrorResponse",
                    "requestId": "serialization_error",
                    "success": false,
                    "error": format!("序列化响应失败: {}", e)
                });

                serde_json::to_vec(&fallback_response)
                    .map_err(|e| format!("创建备用响应失败: {}", e))?
            }
        };

        // 发送响应长度
        let response_length = response_bytes.len() as u32;
        if let Err(e) = writer.write_all(&response_length.to_le_bytes()).await {
            return Err(format!("发送响应长度失败: {}", e));
        }

        // 发送响应内容
        if let Err(e) = writer.write_all(&response_bytes).await {
            return Err(format!("发送响应内容失败: {}", e));
        }

        // 刷新输出缓冲区
        if let Err(e) = writer.flush().await {
            return Err(format!("刷新输出缓冲区失败: {}", e));
        }

        log::debug!("成功发送 {} 字节的响应", response_bytes.len());
        Ok(())
    }

    /// 记录消息统计信息
    fn record_message_stats(&self, processing_time_ms: f64, success: bool) {
        if let Ok(mut stats) = self.stats.lock() {
            if success {
                stats.record_success(processing_time_ms);
            } else {
                stats.record_failure(processing_time_ms);
            }

            // 每处理 100 条消息记录一次统计
            if stats.total_messages % 100 == 0 {
                log::info!(
                    "消息处理统计: 总计 {}, 成功 {}, 失败 {}, 成功率 {:.1}%, 平均处理时间 {:.1}ms",
                    stats.total_messages,
                    stats.successful_messages,
                    stats.failed_messages,
                    stats.success_rate() * 100.0,
                    stats.average_processing_time_ms
                );
            }
        }
    }

    /// 停止服务器
    pub fn stop(&self) {
        log::info!("请求停止 Native Messaging 服务器");
        self.is_running
            .store(false, std::sync::atomic::Ordering::Relaxed);
    }

    /// 检查服务器是否正在运行
    pub fn is_running(&self) -> bool {
        self.is_running.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 获取服务器统计信息
    pub fn get_stats(&self) -> Result<MessageStats, String> {
        self.stats
            .lock()
            .map(|stats| stats.clone())
            .map_err(|e| format!("获取统计信息失败: {}", e))
    }

    /// 重置统计信息
    pub fn reset_stats(&self) -> Result<(), String> {
        match self.stats.lock() {
            Ok(mut stats) => {
                *stats = MessageStats::default();
                log::info!("已重置消息处理统计信息");
                Ok(())
            }
            Err(e) => Err(format!("重置统计信息失败: {}", e)),
        }
    }
}

/// 服务器健康检查
impl NativeMessagingServer {
    /// 执行健康检查
    pub async fn health_check(&self) -> HealthStatus {
        let mut status = HealthStatus::default();

        // 检查服务器是否运行
        status.is_running = self.is_running();

        // 检查统计信息
        if let Ok(stats) = self.get_stats() {
            status.total_messages = stats.total_messages;
            status.success_rate = stats.success_rate();
            status.average_processing_time_ms = stats.average_processing_time_ms;

            // 判断健康状态
            if status.success_rate >= 0.95 && status.average_processing_time_ms < 1000.0 {
                status.health = "healthy".to_string();
            } else if status.success_rate >= 0.8 && status.average_processing_time_ms < 2000.0 {
                status.health = "warning".to_string();
            } else {
                status.health = "unhealthy".to_string();
            }
        } else {
            status.health = "unknown".to_string();
        }

        status
    }
}

/// 健康状态
#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub is_running: bool,
    pub health: String,
    pub total_messages: u64,
    pub success_rate: f64,
    pub average_processing_time_ms: f64,
}

impl Default for HealthStatus {
    fn default() -> Self {
        Self {
            is_running: false,
            health: "unknown".to_string(),
            total_messages: 0,
            success_rate: 0.0,
            average_processing_time_ms: 0.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_health_status_default() {
        let status = HealthStatus::default();
        assert!(!status.is_running);
        assert_eq!(status.health, "unknown");
        assert_eq!(status.total_messages, 0);
    }

    #[tokio::test]
    async fn test_message_length_validation() {
        // 这个测试需要 mock Tauri AppHandle，在实际项目中可能需要使用测试框架
        // 这里只测试基本逻辑
    }
}
