//! 三路合并组件实现
//!
//! 提供智能的数据合并算法，支持多种合并策略

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// 保留凭据类型导入以备将来的智能合并功能使用
#[allow(unused_imports)]
use crate::sync::types::{
    DigitalWalletCredential, LoginCredential, PasskeyCredential, ServerCredential,
    TwoFactorCredential,
};
use crate::sync::{OperationType, SyncError, SyncRecord};
// 保留以下导入以备将来的功能扩展
#[allow(unused_imports)]
use crate::sync::{CredentialType, VectorClock};

/// 合并操作结果
pub type MergeResult<T> = Result<T, SyncError>;

/// 合并策略枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MergeStrategy {
    /// 最后写入获胜
    LastWriteWins,
    /// 最新时间戳获胜
    LatestTimestamp,
    /// 向量时钟优先
    VectorClockPriority,
    /// 手动解决
    Manual,
    /// 自动合并（智能合并）
    AutoMerge,
    /// 保留所有版本
    KeepAll,
}

impl Default for MergeStrategy {
    fn default() -> Self {
        MergeStrategy::AutoMerge
    }
}

/// 合并配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeConfig {
    /// 默认合并策略
    pub default_strategy: MergeStrategy,
    /// 按数据类型的合并策略
    pub type_strategies: HashMap<String, MergeStrategy>,
    /// 是否启用智能合并
    pub enable_smart_merge: bool,
    /// 冲突阈值（毫秒）
    pub conflict_threshold_ms: i64,
    /// 是否保留合并历史
    pub keep_merge_history: bool,
    /// 最大合并深度
    pub max_merge_depth: usize,
}

impl Default for MergeConfig {
    fn default() -> Self {
        Self {
            default_strategy: MergeStrategy::AutoMerge,
            type_strategies: HashMap::new(),
            enable_smart_merge: true,
            conflict_threshold_ms: 1000,
            keep_merge_history: true,
            max_merge_depth: 10,
        }
    }
}

/// 冲突类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConflictType {
    /// 数据冲突
    DataConflict,
    /// 时间戳冲突
    TimestampConflict,
    /// 向量时钟冲突
    VectorClockConflict,
    /// 删除冲突
    DeleteConflict,
    /// 类型冲突
    TypeConflict,
    /// 版本冲突
    VersionConflict,
}

/// 合并冲突描述
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeConflict {
    /// 冲突类型
    pub conflict_type: ConflictType,
    /// 冲突描述
    pub description: String,
    /// 本地记录
    pub local_record: SyncRecord,
    /// 远程记录
    pub remote_record: SyncRecord,
    /// 基础记录（如果有）
    pub base_record: Option<SyncRecord>,
    /// 建议的解决方案
    pub suggested_resolution: Option<SyncRecord>,
    /// 冲突严重程度（0-10）
    pub severity: u8,
}

/// 合并结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeOutcome {
    /// 合并后的记录
    pub merged_record: Option<SyncRecord>,
    /// 是否有冲突
    pub has_conflicts: bool,
    /// 冲突列表
    pub conflicts: Vec<MergeConflict>,
    /// 使用的合并策略
    pub strategy_used: MergeStrategy,
    /// 合并统计
    pub stats: MergeStats,
}

/// 合并统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeStats {
    /// 合并开始时间
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// 合并结束时间
    pub end_time: chrono::DateTime<chrono::Utc>,
    /// 处理的记录数
    pub records_processed: usize,
    /// 成功合并数
    pub successful_merges: usize,
    /// 冲突数
    pub conflicts_found: usize,
    /// 自动解决的冲突数
    pub auto_resolved_conflicts: usize,
}

impl Default for MergeStats {
    fn default() -> Self {
        let now = chrono::Utc::now();
        Self {
            start_time: now,
            end_time: now,
            records_processed: 0,
            successful_merges: 0,
            conflicts_found: 0,
            auto_resolved_conflicts: 0,
        }
    }
}

/// 三路合并器特征
///
/// 定义了三路合并的核心接口
#[async_trait]
pub trait ThreeWayMerger: Send + Sync {
    /// 执行三路合并
    ///
    /// # 参数
    /// * `local` - 本地记录
    /// * `remote` - 远程记录
    /// * `base` - 基础记录（共同祖先）
    /// * `strategy` - 合并策略
    ///
    /// # 返回
    /// 合并结果
    async fn merge_three_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
        strategy: &MergeStrategy,
    ) -> MergeResult<MergeOutcome>;

    /// 执行双路合并
    ///
    /// # 参数
    /// * `local` - 本地记录
    /// * `remote` - 远程记录
    /// * `strategy` - 合并策略
    ///
    /// # 返回
    /// 合并结果
    async fn merge_two_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        strategy: &MergeStrategy,
    ) -> MergeResult<MergeOutcome>;

    /// 批量合并记录
    ///
    /// # 参数
    /// * `local_records` - 本地记录列表
    /// * `remote_records` - 远程记录列表
    /// * `strategy` - 合并策略
    ///
    /// # 返回
    /// 批量合并结果
    async fn merge_batch(
        &self,
        local_records: &[SyncRecord],
        remote_records: &[SyncRecord],
        strategy: &MergeStrategy,
    ) -> MergeResult<Vec<MergeOutcome>>;

    /// 检测冲突
    ///
    /// # 参数
    /// * `local` - 本地记录
    /// * `remote` - 远程记录
    /// * `base` - 基础记录
    ///
    /// # 返回
    /// 冲突列表
    async fn detect_conflicts(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
    ) -> MergeResult<Vec<MergeConflict>>;

    /// 自动解决冲突
    ///
    /// # 参数
    /// * `conflicts` - 冲突列表
    /// * `strategy` - 合并策略
    ///
    /// # 返回
    /// 解决后的记录
    async fn auto_resolve_conflicts(
        &self,
        conflicts: &[MergeConflict],
        strategy: &MergeStrategy,
    ) -> MergeResult<Option<SyncRecord>>;
}

/// 智能三路合并器实现
pub struct SmartThreeWayMerger {
    config: MergeConfig,
}

impl SmartThreeWayMerger {
    /// 创建新的智能三路合并器
    ///
    /// # 参数
    /// * `config` - 合并配置
    pub fn new(config: MergeConfig) -> Self {
        Self { config }
    }

    /// 比较两个凭据数据是否相同
    fn compare_data_content(&self, data1: &str, data2: &str) -> bool {
        // 简单的字符串比较，实际实现中可能需要更复杂的逻辑
        data1 == data2
    }

    /// 智能合并凭据数据
    fn smart_merge_credential_data(
        &self,
        local_data: &str,
        remote_data: &str,
        base_data: Option<&str>,
    ) -> MergeResult<String> {
        // 如果数据相同，直接返回
        if local_data == remote_data {
            return Ok(local_data.to_string());
        }

        // 尝试解析为JSON并进行字段级合并
        match (
            serde_json::from_str::<serde_json::Value>(local_data),
            serde_json::from_str::<serde_json::Value>(remote_data),
        ) {
            (Ok(local_json), Ok(remote_json)) => {
                let base_json =
                    base_data.and_then(|data| serde_json::from_str::<serde_json::Value>(data).ok());

                let merged_json =
                    self.merge_json_objects(&local_json, &remote_json, base_json.as_ref())?;
                serde_json::to_string(&merged_json).map_err(|_e| SyncError::Serialization {
                    operation: "json_merge".to_string(),
                    data_type: "JSON".to_string(),
                })
            }
            _ => {
                // 如果无法解析为JSON，使用简单策略
                if let Some(base) = base_data {
                    if base == local_data {
                        Ok(remote_data.to_string())
                    } else if base == remote_data {
                        Ok(local_data.to_string())
                    } else {
                        // 三方都不同，选择更新的
                        Ok(remote_data.to_string())
                    }
                } else {
                    // 没有基础版本，选择远程版本
                    Ok(remote_data.to_string())
                }
            }
        }
    }

    /// 合并JSON对象
    fn merge_json_objects(
        &self,
        local: &serde_json::Value,
        remote: &serde_json::Value,
        base: Option<&serde_json::Value>,
    ) -> MergeResult<serde_json::Value> {
        match (local, remote) {
            (serde_json::Value::Object(local_obj), serde_json::Value::Object(remote_obj)) => {
                let mut merged = serde_json::Map::new();

                // 获取所有字段
                let mut all_keys = std::collections::HashSet::new();
                all_keys.extend(local_obj.keys());
                all_keys.extend(remote_obj.keys());

                for key in all_keys {
                    let local_val = local_obj.get(key);
                    let remote_val = remote_obj.get(key);
                    let base_val = base.and_then(|b| b.as_object()).and_then(|b| b.get(key));

                    match (local_val, remote_val) {
                        (Some(l), Some(r)) => {
                            if l == r {
                                merged.insert(key.clone(), l.clone());
                            } else {
                                // 递归合并
                                let merged_val = self.merge_json_objects(l, r, base_val)?;
                                merged.insert(key.clone(), merged_val);
                            }
                        }
                        (Some(l), None) => {
                            // 本地有，远程没有
                            if base_val.is_some() {
                                // 基础版本有，远程删除了，保持删除
                            } else {
                                // 基础版本没有，本地新增，保留
                                merged.insert(key.clone(), l.clone());
                            }
                        }
                        (None, Some(r)) => {
                            // 远程有，本地没有
                            if base_val.is_some() {
                                // 基础版本有，本地删除了，保持删除
                            } else {
                                // 基础版本没有，远程新增，保留
                                merged.insert(key.clone(), r.clone());
                            }
                        }
                        (None, None) => {
                            // 都没有，跳过
                        }
                    }
                }

                Ok(serde_json::Value::Object(merged))
            }
            (serde_json::Value::Array(local_arr), serde_json::Value::Array(remote_arr)) => {
                // 数组合并：去重并保持顺序
                let mut merged = local_arr.clone();
                for item in remote_arr {
                    if !merged.contains(item) {
                        merged.push(item.clone());
                    }
                }
                Ok(serde_json::Value::Array(merged))
            }
            _ => {
                // 不同类型或基本类型，选择远程版本
                Ok(remote.clone())
            }
        }
    }

    /// 检测特定类型的冲突
    fn detect_specific_conflicts(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
    ) -> Vec<MergeConflict> {
        let mut conflicts = Vec::new();

        // 检查凭据ID冲突
        if local.credential_id != remote.credential_id {
            conflicts.push(MergeConflict {
                conflict_type: ConflictType::DataConflict,
                description: "凭据ID不匹配".to_string(),
                local_record: local.clone(),
                remote_record: remote.clone(),
                base_record: base.cloned(),
                suggested_resolution: None,
                severity: 9,
            });
        }

        // 检查凭据类型冲突
        if local.credential_type != remote.credential_type {
            conflicts.push(MergeConflict {
                conflict_type: ConflictType::TypeConflict,
                description: "凭据类型不匹配".to_string(),
                local_record: local.clone(),
                remote_record: remote.clone(),
                base_record: base.cloned(),
                suggested_resolution: None,
                severity: 8,
            });
        }

        // 检查操作类型冲突
        if local.operation_type != remote.operation_type {
            let severity = match (local.operation_type, remote.operation_type) {
                (OperationType::Delete, _) | (_, OperationType::Delete) => 7,
                _ => 3,
            };

            conflicts.push(MergeConflict {
                conflict_type: ConflictType::DataConflict,
                description: "操作类型冲突".to_string(),
                local_record: local.clone(),
                remote_record: remote.clone(),
                base_record: base.cloned(),
                suggested_resolution: None,
                severity,
            });
        }

        // 检查数据内容冲突
        if let (Some(local_data), Some(remote_data)) = (&local.data, &remote.data) {
            if !self.compare_data_content(local_data, remote_data) {
                let base_data = base.and_then(|b| b.data.as_ref());

                // 检查是否是真正的冲突（不是基于基础版本的正常更新）
                let is_conflict = if let Some(base_data) = base_data {
                    local_data != base_data && remote_data != base_data && local_data != remote_data
                } else {
                    true
                };

                if is_conflict {
                    conflicts.push(MergeConflict {
                        conflict_type: ConflictType::DataConflict,
                        description: "数据内容冲突".to_string(),
                        local_record: local.clone(),
                        remote_record: remote.clone(),
                        base_record: base.cloned(),
                        suggested_resolution: None,
                        severity: 5,
                    });
                }
            }
        }

        // 检查向量时钟冲突
        if let (Some(local_vc), Some(remote_vc)) = (&local.vector_clock, &remote.vector_clock) {
            use crate::sync::vector_clock::CausalRelation;
            match local_vc.compare(remote_vc) {
                CausalRelation::Concurrent => {
                    conflicts.push(MergeConflict {
                        conflict_type: ConflictType::VectorClockConflict,
                        description: "向量时钟并发冲突".to_string(),
                        local_record: local.clone(),
                        remote_record: remote.clone(),
                        base_record: base.cloned(),
                        suggested_resolution: None,
                        severity: 4,
                    });
                }
                _ => {}
            }
        }

        // 检查时间戳冲突
        let time_diff = (local.local_timestamp.timestamp_millis()
            - remote.local_timestamp.timestamp_millis())
        .abs();
        if time_diff < self.config.conflict_threshold_ms && local.data != remote.data {
            conflicts.push(MergeConflict {
                conflict_type: ConflictType::TimestampConflict,
                description: format!("时间戳过于接近（{}ms内）但数据不同", time_diff),
                local_record: local.clone(),
                remote_record: remote.clone(),
                base_record: base.cloned(),
                suggested_resolution: None,
                severity: 3,
            });
        }

        conflicts
    }

    /// 应用合并策略
    fn apply_merge_strategy(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        strategy: &MergeStrategy,
    ) -> MergeResult<SyncRecord> {
        match strategy {
            MergeStrategy::LastWriteWins => {
                // 选择版本号更高的
                if local.version >= remote.version {
                    Ok(local.clone())
                } else {
                    Ok(remote.clone())
                }
            }
            MergeStrategy::LatestTimestamp => {
                // 选择时间戳更新的
                if local.local_timestamp >= remote.local_timestamp {
                    Ok(local.clone())
                } else {
                    Ok(remote.clone())
                }
            }
            MergeStrategy::VectorClockPriority => {
                // 基于向量时钟决定
                if let (Some(local_vc), Some(remote_vc)) =
                    (&local.vector_clock, &remote.vector_clock)
                {
                    use crate::sync::vector_clock::CausalRelation;
                    match local_vc.compare(remote_vc) {
                        CausalRelation::After => Ok(local.clone()),
                        CausalRelation::Before => Ok(remote.clone()),
                        CausalRelation::Equal => Ok(local.clone()),
                        CausalRelation::Concurrent => {
                            // 并发情况下，使用时间戳
                            self.apply_merge_strategy(
                                local,
                                remote,
                                &MergeStrategy::LatestTimestamp,
                            )
                        }
                    }
                } else {
                    // 没有向量时钟，回退到时间戳策略
                    self.apply_merge_strategy(local, remote, &MergeStrategy::LatestTimestamp)
                }
            }
            MergeStrategy::AutoMerge => {
                // 智能合并
                let mut merged = local.clone();

                // 合并数据
                if let (Some(local_data), Some(remote_data)) = (&local.data, &remote.data) {
                    let merged_data =
                        self.smart_merge_credential_data(local_data, remote_data, None)?;
                    merged.set_data(merged_data);
                } else if remote.data.is_some() {
                    merged.data = remote.data.clone();
                    merged.data_hash = remote.data_hash.clone();
                }

                // 更新版本和时间戳
                merged.version = local.version.max(remote.version) + 1;
                merged.updated_at = chrono::Utc::now();

                // 合并向量时钟
                if let Some(remote_vc) = &remote.vector_clock {
                    if let Some(ref mut local_vc) = merged.vector_clock {
                        local_vc.merge(remote_vc);
                    } else {
                        merged.vector_clock = Some(remote_vc.clone());
                    }
                }

                // 合并因果依赖
                for dep in &remote.causal_dependencies {
                    merged.add_causal_dependency(*dep);
                }

                // 合并元数据
                for (key, value) in &remote.metadata {
                    if !merged.metadata.contains_key(key) {
                        merged.add_metadata(key.clone(), value.clone());
                    }
                }

                Ok(merged)
            }
            MergeStrategy::Manual => {
                // 手动解决，返回错误要求人工干预
                Err(SyncError::ConflictResolution {
                    conflict_count: 1,
                    conflict_type: "manual_resolution_required".to_string(),
                    credential_id: Some(local.credential_id.clone()),
                })
            }
            MergeStrategy::KeepAll => {
                // 保留所有版本，这里简单返回本地版本
                // 实际实现中可能需要创建多个记录
                Ok(local.clone())
            }
        }
    }
}

#[async_trait]
impl ThreeWayMerger for SmartThreeWayMerger {
    async fn merge_three_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
        strategy: &MergeStrategy,
    ) -> MergeResult<MergeOutcome> {
        let start_time = chrono::Utc::now();
        let mut stats = MergeStats {
            start_time,
            records_processed: 1,
            ..Default::default()
        };

        // 检测冲突
        let conflicts = self.detect_specific_conflicts(local, remote, base);
        stats.conflicts_found = conflicts.len();

        let merged_record = if conflicts.is_empty() {
            // 没有冲突，直接合并
            match self.apply_merge_strategy(local, remote, strategy) {
                Ok(record) => {
                    stats.successful_merges = 1;
                    Some(record)
                }
                Err(_) => None,
            }
        } else {
            // 有冲突，尝试自动解决
            match self.auto_resolve_conflicts(&conflicts, strategy).await? {
                Some(record) => {
                    stats.auto_resolved_conflicts = conflicts.len();
                    stats.successful_merges = 1;
                    Some(record)
                }
                None => None,
            }
        };

        stats.end_time = chrono::Utc::now();

        Ok(MergeOutcome {
            merged_record,
            has_conflicts: !conflicts.is_empty(),
            conflicts,
            strategy_used: *strategy,
            stats,
        })
    }

    async fn merge_two_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        strategy: &MergeStrategy,
    ) -> MergeResult<MergeOutcome> {
        // 双路合并就是没有基础版本的三路合并
        self.merge_three_way(local, remote, None, strategy).await
    }

    async fn merge_batch(
        &self,
        local_records: &[SyncRecord],
        remote_records: &[SyncRecord],
        strategy: &MergeStrategy,
    ) -> MergeResult<Vec<MergeOutcome>> {
        let mut outcomes = Vec::new();

        // 创建远程记录的索引
        let mut remote_map: HashMap<String, &SyncRecord> = HashMap::new();
        for record in remote_records {
            remote_map.insert(record.credential_id.clone(), record);
        }

        // 处理本地记录
        for local_record in local_records {
            if let Some(remote_record) = remote_map.get(&local_record.credential_id) {
                // 找到匹配的远程记录，进行合并
                let outcome = self
                    .merge_two_way(local_record, remote_record, strategy)
                    .await?;
                outcomes.push(outcome);
                remote_map.remove(&local_record.credential_id);
            } else {
                // 没有匹配的远程记录，本地记录保持不变
                let mut stats = MergeStats::default();
                stats.records_processed = 1;
                stats.successful_merges = 1;
                stats.end_time = chrono::Utc::now();

                outcomes.push(MergeOutcome {
                    merged_record: Some(local_record.clone()),
                    has_conflicts: false,
                    conflicts: Vec::new(),
                    strategy_used: *strategy,
                    stats,
                });
            }
        }

        // 处理剩余的远程记录（本地没有的）
        for (_, remote_record) in remote_map {
            let mut stats = MergeStats::default();
            stats.records_processed = 1;
            stats.successful_merges = 1;
            stats.end_time = chrono::Utc::now();

            outcomes.push(MergeOutcome {
                merged_record: Some(remote_record.clone()),
                has_conflicts: false,
                conflicts: Vec::new(),
                strategy_used: *strategy,
                stats,
            });
        }

        Ok(outcomes)
    }

    async fn detect_conflicts(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
    ) -> MergeResult<Vec<MergeConflict>> {
        Ok(self.detect_specific_conflicts(local, remote, base))
    }

    async fn auto_resolve_conflicts(
        &self,
        conflicts: &[MergeConflict],
        strategy: &MergeStrategy,
    ) -> MergeResult<Option<SyncRecord>> {
        if conflicts.is_empty() {
            return Ok(None);
        }

        // 找到第一个冲突的记录进行解决
        let first_conflict = &conflicts[0];

        match strategy {
            MergeStrategy::Manual => {
                // 手动策略不自动解决
                Ok(None)
            }
            _ => {
                // 其他策略尝试自动解决
                match self.apply_merge_strategy(
                    &first_conflict.local_record,
                    &first_conflict.remote_record,
                    strategy,
                ) {
                    Ok(resolved) => Ok(Some(resolved)),
                    Err(_) => Ok(None),
                }
            }
        }
    }
}

/// 合并管理器
///
/// 提供合并操作的统一管理接口
pub struct MergeManager {
    merger: Box<dyn ThreeWayMerger>,
    config: MergeConfig,
}

impl MergeManager {
    /// 创建新的合并管理器
    ///
    /// # 参数
    /// * `merger` - 三路合并器实现
    /// * `config` - 合并配置
    pub fn new(merger: Box<dyn ThreeWayMerger>, config: MergeConfig) -> Self {
        Self { merger, config }
    }

    /// 创建使用智能合并器的管理器
    ///
    /// # 参数
    /// * `config` - 合并配置
    pub fn with_smart_merger(config: MergeConfig) -> Self {
        Self::new(Box::new(SmartThreeWayMerger::new(config.clone())), config)
    }

    /// 执行三路合并
    pub async fn merge_three_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
        strategy: Option<&MergeStrategy>,
    ) -> MergeResult<MergeOutcome> {
        let strategy = strategy.unwrap_or(&self.config.default_strategy);
        self.merger
            .merge_three_way(local, remote, base, strategy)
            .await
    }

    /// 执行双路合并
    pub async fn merge_two_way(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        strategy: Option<&MergeStrategy>,
    ) -> MergeResult<MergeOutcome> {
        let strategy = strategy.unwrap_or(&self.config.default_strategy);
        self.merger.merge_two_way(local, remote, strategy).await
    }

    /// 批量合并
    pub async fn merge_batch(
        &self,
        local_records: &[SyncRecord],
        remote_records: &[SyncRecord],
        strategy: Option<&MergeStrategy>,
    ) -> MergeResult<Vec<MergeOutcome>> {
        let strategy = strategy.unwrap_or(&self.config.default_strategy);
        self.merger
            .merge_batch(local_records, remote_records, strategy)
            .await
    }

    /// 检测冲突
    pub async fn detect_conflicts(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        base: Option<&SyncRecord>,
    ) -> MergeResult<Vec<MergeConflict>> {
        self.merger.detect_conflicts(local, remote, base).await
    }

    /// 获取配置
    pub fn get_config(&self) -> &MergeConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: MergeConfig) {
        self.config = config;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 创建测试用的同步记录
    fn create_test_record(id: &str, username: &str, password: &str) -> SyncRecord {
        SyncRecord::builder(
            format!("cred_{}", id),
            CredentialType::Login,
            OperationType::Create,
            "test_device".to_string(),
        )
        .data(
            serde_json::to_string(&LoginCredential {
                name: "Test Login".to_string(),
                username: username.to_string(),
                password: password.to_string(),
                url: Some("https://example.com".to_string()),
                notes: None,
                tags: vec!["test".to_string()],
                folder_id: None,
                favorite: false,
                custom_fields: HashMap::new(),
                password_history: Vec::new(),
                last_used: None,
            })
            .unwrap(),
        )
        .build()
        .unwrap()
    }

    #[tokio::test]
    async fn test_smart_merger_no_conflict() {
        let config = MergeConfig::default();
        let merger = SmartThreeWayMerger::new(config);

        let record1 = create_test_record("1", "user1", "pass1");
        let record2 = record1.clone();

        let outcome = merger
            .merge_two_way(&record1, &record2, &MergeStrategy::AutoMerge)
            .await
            .unwrap();
        assert!(!outcome.has_conflicts);
        assert!(outcome.merged_record.is_some());
    }

    #[tokio::test]
    async fn test_smart_merger_data_conflict() {
        let config = MergeConfig::default();
        let merger = SmartThreeWayMerger::new(config);

        let record1 = create_test_record("1", "user1", "pass1");
        let record2 = create_test_record("1", "user1", "pass2");

        let outcome = merger
            .merge_two_way(&record1, &record2, &MergeStrategy::AutoMerge)
            .await
            .unwrap();
        assert!(outcome.merged_record.is_some());
    }

    #[tokio::test]
    async fn test_merge_strategies() {
        let config = MergeConfig::default();
        let merger = SmartThreeWayMerger::new(config);

        let mut record1 = create_test_record("1", "user1", "pass1");
        let mut record2 = create_test_record("1", "user1", "pass2");

        record1.version = 1;
        record2.version = 2;

        // 测试LastWriteWins策略
        let outcome = merger
            .merge_two_way(&record1, &record2, &MergeStrategy::LastWriteWins)
            .await
            .unwrap();
        assert!(outcome.merged_record.is_some());
        assert_eq!(outcome.merged_record.unwrap().version, 2);

        // 测试LatestTimestamp策略
        record2.local_timestamp = chrono::Utc::now() + chrono::Duration::seconds(1);
        let outcome = merger
            .merge_two_way(&record1, &record2, &MergeStrategy::LatestTimestamp)
            .await
            .unwrap();
        assert!(outcome.merged_record.is_some());
    }

    #[tokio::test]
    async fn test_conflict_detection() {
        let config = MergeConfig::default();
        let merger = SmartThreeWayMerger::new(config);

        let record1 = create_test_record("1", "user1", "pass1");
        let record2 = create_test_record("2", "user2", "pass2"); // 不同的凭据ID

        let conflicts = merger
            .detect_conflicts(&record1, &record2, None)
            .await
            .unwrap();
        assert!(!conflicts.is_empty());
    }

    #[tokio::test]
    async fn test_batch_merge() {
        let config = MergeConfig::default();
        let merger = SmartThreeWayMerger::new(config);

        let local_records = vec![
            create_test_record("1", "user1", "pass1"),
            create_test_record("2", "user2", "pass2"),
        ];

        let remote_records = vec![
            create_test_record("1", "user1", "newpass1"),
            create_test_record("3", "user3", "pass3"),
        ];

        let outcomes = merger
            .merge_batch(&local_records, &remote_records, &MergeStrategy::AutoMerge)
            .await
            .unwrap();
        assert_eq!(outcomes.len(), 3); // 2 local + 1 remote only
    }

    #[tokio::test]
    async fn test_merge_manager() {
        let config = MergeConfig::default();
        let manager = MergeManager::with_smart_merger(config);

        let record1 = create_test_record("1", "user1", "pass1");
        let record2 = create_test_record("1", "user1", "pass2");

        let outcome = manager
            .merge_two_way(&record1, &record2, None)
            .await
            .unwrap();
        assert!(outcome.merged_record.is_some());
    }

    #[test]
    fn test_merge_config() {
        let config = MergeConfig::default();
        assert_eq!(config.default_strategy, MergeStrategy::AutoMerge);
        assert!(config.enable_smart_merge);
        assert_eq!(config.conflict_threshold_ms, 1000);
    }

    #[test]
    fn test_conflict_types() {
        let conflict = MergeConflict {
            conflict_type: ConflictType::DataConflict,
            description: "Test conflict".to_string(),
            local_record: create_test_record("1", "user1", "pass1"),
            remote_record: create_test_record("1", "user1", "pass2"),
            base_record: None,
            suggested_resolution: None,
            severity: 5,
        };

        assert_eq!(conflict.conflict_type, ConflictType::DataConflict);
        assert_eq!(conflict.severity, 5);
    }
}
