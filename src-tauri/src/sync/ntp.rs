//! NTP时间同步组件实现
//!
//! 提供高精度的网络时间同步功能

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
// 保留以下导入以备将来的功能扩展
#[allow(unused_imports)]
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::{Mutex, RwLock};
// 保留以下导入以备将来的定时功能使用
#[allow(unused_imports)]
use tokio::time::{sleep, timeout};

use crate::sync::{NtpError, SyncError};

/// NTP操作结果类型
pub type TimeResult<T> = Result<T, SyncError>;

/// NTP服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpServerConfig {
    /// 服务器地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 超时时间（毫秒）
    pub timeout_ms: u64,
    /// 重试次数
    pub retry_count: u32,
    /// 权重（用于多服务器时的选择）
    pub weight: f64,
}

impl Default for NtpServerConfig {
    fn default() -> Self {
        Self {
            host: "pool.ntp.org".to_string(),
            port: 123,
            timeout_ms: 5000,
            retry_count: 3,
            weight: 1.0,
        }
    }
}

/// NTP同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpConfig {
    /// NTP服务器列表
    pub servers: Vec<NtpServerConfig>,
    /// 同步间隔（秒）
    pub sync_interval_secs: u64,
    /// 最大时间偏差（毫秒）
    pub max_offset_ms: i64,
    /// 是否启用自动同步
    pub auto_sync: bool,
    /// 本地时钟调整阈值（毫秒）
    pub adjustment_threshold_ms: i64,
}

impl Default for NtpConfig {
    fn default() -> Self {
        Self {
            servers: vec![
                NtpServerConfig {
                    host: "pool.ntp.org".to_string(),
                    ..Default::default()
                },
                NtpServerConfig {
                    host: "time.google.com".to_string(),
                    ..Default::default()
                },
                NtpServerConfig {
                    host: "time.cloudflare.com".to_string(),
                    ..Default::default()
                },
            ],
            sync_interval_secs: 300, // 5分钟
            max_offset_ms: 1000,     // 1秒
            auto_sync: true,
            adjustment_threshold_ms: 100, // 100毫秒
        }
    }
}

/// 时间同步结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSync {
    /// 本地时间
    pub local_time: DateTime<Utc>,
    /// 网络时间
    pub network_time: DateTime<Utc>,
    /// 时间偏差（毫秒）
    pub offset_ms: i64,
    /// 往返时间（毫秒）
    pub round_trip_ms: u64,
    /// 同步精度（毫秒）
    pub precision_ms: f64,
    /// 使用的服务器
    pub server: String,
    /// 同步时间戳
    pub sync_timestamp: DateTime<Utc>,
}

/// NTP统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpStats {
    /// 总同步次数
    pub total_syncs: u64,
    /// 成功同步次数
    pub successful_syncs: u64,
    /// 失败同步次数
    pub failed_syncs: u64,
    /// 平均偏差（毫秒）
    pub average_offset_ms: f64,
    /// 最大偏差（毫秒）
    pub max_offset_ms: i64,
    /// 最小偏差（毫秒）
    pub min_offset_ms: i64,
    /// 最后同步时间
    pub last_sync_time: Option<DateTime<Utc>>,
    /// 服务器统计
    pub server_stats: HashMap<String, ServerStats>,
}

/// 服务器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerStats {
    /// 请求次数
    pub requests: u64,
    /// 成功次数
    pub successes: u64,
    /// 失败次数
    pub failures: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
}

impl Default for NtpStats {
    fn default() -> Self {
        Self {
            total_syncs: 0,
            successful_syncs: 0,
            failed_syncs: 0,
            average_offset_ms: 0.0,
            max_offset_ms: 0,
            min_offset_ms: 0,
            last_sync_time: None,
            server_stats: HashMap::new(),
        }
    }
}

/// 时间提供者特征
///
/// 定义获取当前时间的统一接口
#[async_trait]
pub trait TimeProvider: Send + Sync {
    /// 获取当前时间
    async fn get_current_time(&self) -> TimeResult<DateTime<Utc>>;

    /// 获取时间精度（毫秒）
    fn get_precision_ms(&self) -> u64;

    /// 获取提供者名称
    fn get_name(&self) -> &str;

    /// 检查提供者是否可用
    async fn is_available(&self) -> bool;

    /// 获取延迟（毫秒）
    async fn get_latency_ms(&self) -> Option<u64>;
}

/// 系统时间提供者
pub struct SystemTimeProvider {
    name: String,
    /// 统计信息 - 保留以备将来的功能扩展
    #[allow(dead_code)]
    stats: NtpStats,
}

impl SystemTimeProvider {
    /// 创建新的系统时间提供者
    pub fn new() -> Self {
        Self {
            name: "SystemTime".to_string(),
            stats: NtpStats::default(),
        }
    }
}

impl Default for SystemTimeProvider {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl TimeProvider for SystemTimeProvider {
    async fn get_current_time(&self) -> TimeResult<DateTime<Utc>> {
        Ok(Utc::now())
    }

    fn get_precision_ms(&self) -> u64 {
        1 // 系统时间精度约为1毫秒
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    async fn is_available(&self) -> bool {
        true // 系统时间总是可用的
    }

    async fn get_latency_ms(&self) -> Option<u64> {
        Some(0) // 系统时间没有网络延迟
    }
}

/// HTTP时间提供者
pub struct HttpTimeProvider {
    name: String,
    urls: Vec<String>,
    client: reqwest::Client,
    stats: NtpStats,
    last_sync: Option<TimeSync>,
}

impl HttpTimeProvider {
    /// 创建新的HTTP时间提供者
    pub fn new(config: NtpConfig) -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_millis(
                config.servers.first().map(|s| s.timeout_ms).unwrap_or(5000),
            ))
            .build()
            .unwrap_or_default();

        Self {
            name: "HttpTime".to_string(),
            urls: config
                .servers
                .iter()
                .map(|s| format!("{}:{}", s.host, s.port))
                .collect(),
            client,
            stats: NtpStats::default(),
            last_sync: None,
        }
    }

    /// 从指定URL获取时间
    async fn get_time_from_url(&self, url: &str) -> TimeResult<DateTime<Utc>> {
        let _start_time = std::time::Instant::now();

        let response = tokio::time::timeout(
            Duration::from_millis(5000), // 固定5秒超时
            self.client.head(url).send(),
        )
        .await
        .map_err(|_| SyncError::Timeout {
            operation: "http_time_request".to_string(),
            timeout_ms: 5000,
        })?
        .map_err(|e| SyncError::Network {
            url: url.to_string(),
            status_code: None,
            source: Box::new(e),
        })?;

        if let Some(date_header) = response.headers().get("date") {
            if let Ok(date_str) = date_header.to_str() {
                DateTime::parse_from_rfc2822(date_str)
                    .map(|dt| dt.with_timezone(&Utc))
                    .map_err(|_| SyncError::Ntp {
                        operation: "parse date header".to_string(),
                        source: Box::new(NtpError::ResponseParseFailed {
                            reason: "Failed to parse date header".to_string(),
                        }),
                    })
            } else {
                Err(SyncError::Ntp {
                    operation: "parse date header".to_string(),
                    source: Box::new(NtpError::ResponseParseFailed {
                        reason: "Invalid date header".to_string(),
                    }),
                })
            }
        } else {
            Err(SyncError::Ntp {
                operation: "get date header".to_string(),
                source: Box::new(NtpError::ResponseParseFailed {
                    reason: "No date header found".to_string(),
                }),
            })
        }
    }

    /// 更新统计信息 - 保留以备将来的功能扩展
    #[allow(dead_code)]
    fn update_stats(&mut self, sync_result: &Result<TimeSync, SyncError>) {
        self.stats.total_syncs += 1;

        match sync_result {
            Ok(sync) => {
                self.stats.successful_syncs += 1;
                self.stats.last_sync_time = Some(sync.sync_timestamp);

                // 更新偏差统计
                if self.stats.successful_syncs == 1 {
                    self.stats.average_offset_ms = sync.offset_ms as f64;
                    self.stats.max_offset_ms = sync.offset_ms;
                    self.stats.min_offset_ms = sync.offset_ms;
                } else {
                    self.stats.average_offset_ms = (self.stats.average_offset_ms
                        * (self.stats.successful_syncs - 1) as f64
                        + sync.offset_ms as f64)
                        / self.stats.successful_syncs as f64;
                    self.stats.max_offset_ms = self.stats.max_offset_ms.max(sync.offset_ms);
                    self.stats.min_offset_ms = self.stats.min_offset_ms.min(sync.offset_ms);
                }

                // 更新服务器统计
                let server_stats = self
                    .stats
                    .server_stats
                    .entry(sync.server.clone())
                    .or_insert_with(|| ServerStats {
                        requests: 0,
                        successes: 0,
                        failures: 0,
                        avg_response_time_ms: 0.0,
                        last_used: None,
                    });

                server_stats.requests += 1;
                server_stats.successes += 1;
                server_stats.avg_response_time_ms = (server_stats.avg_response_time_ms
                    * (server_stats.successes - 1) as f64
                    + sync.round_trip_ms as f64)
                    / server_stats.successes as f64;
                server_stats.last_used = Some(sync.sync_timestamp);
            }
            Err(_) => {
                self.stats.failed_syncs += 1;
            }
        }
    }
}

#[async_trait]
impl TimeProvider for HttpTimeProvider {
    async fn get_current_time(&self) -> TimeResult<DateTime<Utc>> {
        if let Some(ref last_sync) = self.last_sync {
            // 如果有最近的同步结果，使用调整后的时间
            let elapsed = Utc::now().signed_duration_since(last_sync.sync_timestamp);
            let adjusted_time = last_sync.network_time + elapsed;
            Ok(adjusted_time)
        } else {
            // 如果没有同步过，返回系统时间
            Ok(Utc::now())
        }
    }

    fn get_precision_ms(&self) -> u64 {
        1000 // HTTP时间精度约为1秒
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    async fn is_available(&self) -> bool {
        for url in &self.urls {
            if self.get_time_from_url(url).await.is_ok() {
                return true;
            }
        }
        false
    }

    async fn get_latency_ms(&self) -> Option<u64> {
        let start = std::time::Instant::now();
        if let Some(url) = self.urls.first() {
            if self.get_time_from_url(url).await.is_ok() {
                return Some(start.elapsed().as_millis() as u64);
            }
        }
        None
    }
}

/// 时间同步统计
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct TimeStats {
    /// 总同步次数
    pub total_syncs: u64,
    /// 成功同步次数
    pub successful_syncs: u64,
    /// 失败同步次数
    pub failed_syncs: u64,
    /// 平均延迟（毫秒）
    pub average_latency_ms: f64,
    /// 最后同步时间
    pub last_sync_time: Option<DateTime<Utc>>,
    /// 当前时间偏差（毫秒）
    pub current_offset_ms: i64,
    /// 最后错误
    pub last_error: Option<String>,
}

/// 时间管理器
///
/// 管理多个时间提供者，提供统一的时间同步接口
pub struct TimeManager {
    /// 时间提供者列表
    providers: Vec<Box<dyn TimeProvider>>,
    /// 当前时间偏差
    time_offset: Arc<RwLock<i64>>,
    /// 统计信息
    stats: Arc<RwLock<TimeStats>>,
    /// 自动同步任务句柄
    auto_sync_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
    /// 配置
    config: NtpConfig,
}

impl TimeManager {
    /// 创建新的时间管理器
    pub fn new(config: NtpConfig) -> Self {
        Self {
            providers: Vec::new(),
            time_offset: Arc::new(RwLock::new(0)),
            stats: Arc::new(RwLock::new(TimeStats::default())),
            auto_sync_handle: Arc::new(Mutex::new(None)),
            config,
        }
    }

    /// 使用系统时间创建时间管理器
    pub fn with_system_time() -> Self {
        let mut manager = Self::new(NtpConfig::default());
        manager.add_provider(Box::new(SystemTimeProvider::new()));
        manager
    }

    /// 添加时间提供者
    pub fn add_provider(&mut self, provider: Box<dyn TimeProvider>) {
        self.providers.push(provider);
    }

    /// 获取当前时间（已校正）
    pub async fn get_current_time(&self) -> TimeResult<DateTime<Utc>> {
        let base_time = Utc::now();
        let offset = *self.time_offset.read().await;

        let corrected_time = base_time + chrono::Duration::milliseconds(offset);
        Ok(corrected_time)
    }

    /// 同步时间
    pub async fn sync_time(&mut self) -> TimeResult<()> {
        let mut stats = self.stats.write().await;
        stats.total_syncs += 1;

        let mut successful_providers = Vec::new();
        let mut last_error = None;

        // 尝试从所有提供者获取时间
        for provider in &self.providers {
            if provider.is_available().await {
                match provider.get_current_time().await {
                    Ok(provider_time) => {
                        let latency = provider.get_latency_ms().await.unwrap_or(0);
                        successful_providers.push((provider_time, latency));
                    }
                    Err(e) => {
                        last_error = Some(e);
                    }
                }
            }
        }

        if successful_providers.is_empty() {
            stats.failed_syncs += 1;
            return Err(last_error.unwrap_or_else(|| SyncError::Ntp {
                operation: "sync time".to_string(),
                source: Box::new(NtpError::NoTimeSourceAvailable),
            }));
        }

        // 计算时间偏差
        let local_time = Utc::now();
        let mut total_offset = 0i64;
        let mut total_weight = 0f64;

        for (provider_time, latency) in &successful_providers {
            // 补偿网络延迟
            let adjusted_time =
                *provider_time + chrono::Duration::milliseconds(*latency as i64 / 2);
            let offset = adjusted_time
                .signed_duration_since(local_time)
                .num_milliseconds();

            // 根据延迟给予权重（延迟越低权重越高）
            let weight = 1.0 / (1.0 + *latency as f64 / 1000.0);
            total_offset += (offset as f64 * weight) as i64;
            total_weight += weight;
        }

        if total_weight > 0.0 {
            let average_offset = (total_offset as f64 / total_weight) as i64;

            // 检查偏差是否在允许范围内
            if average_offset.abs() > self.config.max_offset_ms {
                return Err(SyncError::Ntp {
                    operation: "check time offset".to_string(),
                    source: Box::new(NtpError::TimeOffsetTooLarge {
                        offset_ms: average_offset,
                        max_offset_ms: self.config.max_offset_ms,
                    }),
                });
            }

            // 更新时间偏差
            *self.time_offset.write().await = average_offset;

            // 更新统计信息
            stats.successful_syncs += 1;
            stats.last_sync_time = Some(Utc::now());
            stats.current_offset_ms = average_offset;

            // 计算平均延迟
            let total_latency: u64 = successful_providers
                .iter()
                .map(|(_, latency)| latency)
                .sum();
            let average_latency = total_latency as f64 / successful_providers.len() as f64;
            stats.average_latency_ms =
                (stats.average_latency_ms * (stats.successful_syncs - 1) as f64 + average_latency)
                    / stats.successful_syncs as f64;

            stats.last_error = None;
        }

        Ok(())
    }

    /// 启动自动同步
    pub async fn start_auto_sync(&self) -> TimeResult<()> {
        if !self.config.auto_sync {
            return Ok(());
        }

        let mut handle_guard = self.auto_sync_handle.lock().await;
        if handle_guard.is_some() {
            return Err(SyncError::Internal {
                message: "自动同步已经启动".to_string(),
                error_code: None,
                source: None,
            });
        }

        let _time_offset = Arc::clone(&self.time_offset);
        let stats = Arc::clone(&self.stats);
        let _providers: Vec<Box<dyn TimeProvider>> = Vec::new(); // 这里需要克隆提供者，但由于特征对象的限制，暂时留空
        let config = self.config.clone();

        let handle = tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(tokio::time::Duration::from_secs(config.sync_interval_secs));

            loop {
                interval.tick().await;

                // 这里应该执行时间同步逻辑
                // 由于架构限制，暂时只更新统计信息
                let mut stats_guard = stats.write().await;
                stats_guard.total_syncs += 1;
            }
        });

        *handle_guard = Some(handle);
        Ok(())
    }

    /// 停止自动同步
    pub async fn stop_auto_sync(&self) -> TimeResult<()> {
        let mut handle_guard = self.auto_sync_handle.lock().await;
        if let Some(handle) = handle_guard.take() {
            handle.abort();
        }
        Ok(())
    }

    /// 获取时间偏差
    pub async fn get_time_offset(&self) -> i64 {
        *self.time_offset.read().await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> TimeStats {
        self.stats.read().await.clone()
    }

    /// 获取配置
    pub fn get_config(&self) -> &NtpConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: NtpConfig) {
        self.config = config;
    }

    /// 检查时间是否已同步
    pub async fn is_time_synced(&self) -> bool {
        let stats = self.stats.read().await;
        stats.successful_syncs > 0 && stats.last_sync_time.is_some()
    }

    /// 获取提供者数量
    pub fn get_provider_count(&self) -> usize {
        self.providers.len()
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = TimeStats::default();
    }
}

impl Drop for TimeManager {
    fn drop(&mut self) {
        // 停止自动同步
        if let Ok(handle_guard) = self.auto_sync_handle.try_lock() {
            if let Some(handle) = handle_guard.as_ref() {
                handle.abort();
            }
        }
    }
}

/// 计算两个时间之间的差值（毫秒）
pub fn time_diff_ms(time1: DateTime<Utc>, time2: DateTime<Utc>) -> i64 {
    time1.timestamp_millis() - time2.timestamp_millis()
}

/// 格式化时间偏差
pub fn format_time_offset(offset_ms: i64) -> String {
    if offset_ms == 0 {
        "无偏差".to_string()
    } else if offset_ms > 0 {
        format!("快 {}ms", offset_ms)
    } else {
        format!("慢 {}ms", -offset_ms)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_system_time_provider() {
        let provider = SystemTimeProvider::new();

        assert_eq!(provider.get_name(), "SystemTime");
        assert_eq!(provider.get_precision_ms(), 1);
        assert!(provider.is_available().await);
        assert_eq!(provider.get_latency_ms().await, Some(0));

        let time = provider.get_current_time().await.unwrap();
        let now = Utc::now();
        let diff = time_diff_ms(time, now).abs();
        assert!(diff < 1000); // 差异应该小于1秒
    }

    #[tokio::test]
    async fn test_http_time_provider() {
        let urls = vec!["https://httpbin.org/get".to_string()];
        let provider = HttpTimeProvider::new(NtpConfig::default());

        assert_eq!(provider.get_name(), "HttpTime");
        assert_eq!(provider.get_precision_ms(), 1000);

        // 注意：这个测试依赖于网络连接
        if provider.is_available().await {
            let time = provider.get_current_time().await;
            assert!(time.is_ok());
        }
    }

    #[tokio::test]
    async fn test_time_manager() {
        let mut manager = TimeManager::with_system_time();

        assert_eq!(manager.get_provider_count(), 1);
        assert!(!manager.is_time_synced().await);

        let sync_result = manager.sync_time().await;
        assert!(sync_result.is_ok());

        assert!(manager.is_time_synced().await);

        let offset = manager.get_time_offset().await;
        assert!(offset.abs() < 1000); // 系统时间偏差应该很小

        let stats = manager.get_stats().await;
        assert_eq!(stats.total_syncs, 1);
        assert_eq!(stats.successful_syncs, 1);
        assert_eq!(stats.failed_syncs, 0);
    }

    #[tokio::test]
    async fn test_ntp_config() {
        let config = NtpConfig::default();

        assert!(!config.servers.is_empty());
        assert_eq!(config.sync_interval_secs, 300);
        assert_eq!(config.max_offset_ms, 1000);
        assert_eq!(config.auto_sync, true);
    }

    #[test]
    fn test_time_diff_ms() {
        let time1 = Utc::now();
        let time2 = time1 + chrono::Duration::milliseconds(1000);

        assert_eq!(time_diff_ms(time2, time1), 1000);
        assert_eq!(time_diff_ms(time1, time2), -1000);
    }

    #[test]
    fn test_format_time_offset() {
        assert_eq!(format_time_offset(0), "无偏差");
        assert_eq!(format_time_offset(1000), "快 1000ms");
        assert_eq!(format_time_offset(-1000), "慢 1000ms");
    }

    #[tokio::test]
    async fn test_time_manager_auto_sync() {
        let manager = TimeManager::with_system_time();

        let start_result = manager.start_auto_sync().await;
        assert!(start_result.is_ok());

        // 尝试再次启动应该失败
        let duplicate_start = manager.start_auto_sync().await;
        assert!(duplicate_start.is_err());

        let stop_result = manager.stop_auto_sync().await;
        assert!(stop_result.is_ok());
    }

    #[tokio::test]
    async fn test_time_stats() {
        let mut manager = TimeManager::with_system_time();

        // 初始统计
        let initial_stats = manager.get_stats().await;
        assert_eq!(initial_stats.total_syncs, 0);
        assert_eq!(initial_stats.successful_syncs, 0);

        // 执行同步
        manager.sync_time().await.unwrap();

        let updated_stats = manager.get_stats().await;
        assert_eq!(updated_stats.total_syncs, 1);
        assert_eq!(updated_stats.successful_syncs, 1);
        assert!(updated_stats.last_sync_time.is_some());

        // 重置统计
        manager.reset_stats().await;
        let reset_stats = manager.get_stats().await;
        assert_eq!(reset_stats.total_syncs, 0);
    }
}
