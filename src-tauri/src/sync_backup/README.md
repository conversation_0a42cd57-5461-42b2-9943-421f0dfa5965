# 日志同步系统

一个独立的、模块化的日志同步系统，支持多端数据同步、冲突解决和NTP时间同步。

## 🚀 特性

- **独立模块化设计**：完全独立的模块，不干扰现有应用流程
- **多端同步**：支持多设备间的数据同步
- **NTP时间同步**：基于NTP协议的全局时间戳同步
- **冲突解决**：智能冲突检测和多种解决策略
- **异步设计**：全异步实现，性能优良
- **可插拔架构**：轻松集成和移除
- **完善的错误处理**：包含重试机制和错误恢复

## 📁 模块结构

```
src-tauri/src/sync/
├── mod.rs              # 模块入口和公共接口
├── ntp_client.rs       # NTP时间同步客户端
├── sync_manager.rs     # 同步管理器核心
├── log_tracker.rs      # 日志变更追踪
├── conflict_resolver.rs # 冲突解决策略
├── sync_storage.rs     # 同步相关数据存储
├── sync_commands.rs    # Tauri命令接口
└── README.md           # 本文档
```

## 🔧 快速开始

### 1. 添加到项目

在 `src-tauri/src/lib.rs` 中添加：

```rust
pub mod sync;
```

### 2. 基本使用

```rust
use crate::sync::{SyncManager, SyncConfig, SyncModule};

// 创建配置
let config = SyncConfig {
    enabled: true,
    ntp_servers: vec![
        "time.cloudflare.com".to_string(),
        "pool.ntp.org".to_string(),
    ],
    sync_interval: 300, // 5分钟
    sync_server_url: Some("https://your-api.com".to_string()),
    ..Default::default()
};

// 创建同步管理器
let mut sync_manager = SyncManager::new(config, "sync.db").await?;

// 启动同步服务
sync_manager.start().await?;

// 记录数据变更
sync_manager.record_change(
    "users",
    "user_123",
    SyncRecordType::Create,
    Some(r#"{"name": "张三", "email": "<EMAIL>"}"#),
).await?;

// 手动触发同步
let result = sync_manager.trigger_sync().await?;
println!("同步结果: {:?}", result);

// 获取同步状态
let status = sync_manager.get_status().await?;
println!("同步状态: {:?}", status);
```

### 3. 集成到Tauri应用

```rust
use tauri::Builder;
use crate::sync::sync_commands::add_sync_commands;

fn main() {
    Builder::default()
        .setup(|app| {
            // 其他初始化代码...
            Ok(())
        })
        // 添加同步命令（目前暂时注释，等修复AppHandle问题后启用）
        .pipe(add_sync_commands)
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

## 📊 API参考

### SyncManager

主要的同步管理器，实现了 `SyncModule` trait。

#### 方法

- `new(config: SyncConfig, db_path: impl AsRef<Path>) -> Result<Self, SyncError>`
- `start() -> Result<(), SyncError>` - 启动同步服务
- `stop() -> Result<(), SyncError>` - 停止同步服务
- `get_status() -> Result<SyncStatus, SyncError>` - 获取同步状态
- `trigger_sync() -> Result<SyncResult, SyncError>` - 手动触发同步
- `record_change(table_name, record_id, operation_type, data) -> Result<(), SyncError>` - 记录数据变更

### SyncConfig

同步配置结构体。

```rust
pub struct SyncConfig {
    pub enabled: bool,                    // 是否启用同步
    pub ntp_servers: Vec<String>,         // NTP服务器列表
    pub sync_interval: u64,               // 同步间隔（秒）
    pub sync_server_url: Option<String>,  // 服务端API地址
    pub device_id: String,                // 设备唯一标识
    pub max_retries: u32,                 // 最大重试次数
    pub timeout_ms: u64,                  // 连接超时时间（毫秒）
}
```

### SyncRecordType

操作类型枚举。

```rust
pub enum SyncRecordType {
    Create,     // 创建操作
    Update,     // 更新操作
    Delete,     // 删除操作
    SoftDelete, // 软删除操作
}
```

### ConflictResolution

冲突解决策略。

```rust
pub enum ConflictResolution {
    UseServer,  // 使用服务端版本
    UseLocal,   // 使用本地版本
    UseLatest,  // 使用最新时间戳的版本
    Manual,     // 手动解决
}
```

## 🔄 同步流程

1. **变更追踪**：`LogTracker` 监听数据变更并记录到本地数据库
2. **时间同步**：定期通过NTP协议同步时间，确保全局时间戳一致性
3. **数据上传**：将本地未同步的记录上传到服务端
4. **数据下载**：从服务端获取其他设备的变更记录
5. **冲突检测**：检测版本冲突、数据冲突、操作冲突等
6. **冲突解决**：根据配置的策略自动或手动解决冲突

## ⚙️ 配置示例

### 基本配置

```rust
let config = SyncConfig {
    enabled: true,
    ntp_servers: vec![
        "time.cloudflare.com".to_string(),
        "pool.ntp.org".to_string(),
        "time.google.com".to_string(),
    ],
    sync_interval: 300, // 5分钟同步一次
    sync_server_url: Some("https://api.example.com".to_string()),
    max_retries: 3,
    timeout_ms: 5000,
    ..Default::default()
};
```

### 高频同步配置

```rust
let config = SyncConfig {
    enabled: true,
    sync_interval: 60, // 1分钟同步一次
    max_retries: 5,
    timeout_ms: 3000,
    ..Default::default()
};
```

## 🔍 监控和状态管理

### 获取同步状态

```rust
let status = sync_manager.get_status().await?;
println!("正在同步: {}", status.is_syncing);
println!("最后同步时间: {:?}", status.last_sync_time);
println!("待同步记录数: {}", status.pending_records);
println!("NTP时间偏移: {:?} 毫秒", status.ntp_offset_ms);
println!("服务端连接状态: {}", status.server_connected);
```

### 添加变更监听器

```rust
let tracker = sync_manager.get_log_tracker();
tracker.add_listener("my_listener", |event| {
    println!("数据变更: 表={}, 记录ID={}, 操作={:?}", 
             event.table_name, event.record_id, event.operation_type);
});
```

## 🚨 错误处理

系统定义了完善的错误类型：

```rust
pub enum SyncError {
    NtpError(String),           // NTP同步失败
    DatabaseError(String),      // 数据库错误
    NetworkError(String),       // 网络错误
    SerializationError(String), // 序列化错误
    ConfigError(String),        // 配置错误
    ConflictError(String),      // 冲突错误
    Unknown(String),            // 未知错误
}
```

### 错误处理示例

```rust
match sync_manager.trigger_sync().await {
    Ok(result) => {
        println!("同步成功: {} 条记录", result.synced_count);
        if result.conflict_count > 0 {
            println!("发现 {} 个冲突", result.conflict_count);
        }
    }
    Err(SyncError::NetworkError(msg)) => {
        eprintln!("网络错误: {}", msg);
        // 可以稍后重试
    }
    Err(SyncError::ConflictError(msg)) => {
        eprintln!("冲突错误: {}", msg);
        // 需要手动处理冲突
    }
    Err(e) => {
        eprintln!("同步失败: {}", e);
    }
}
```

## 🚀 部署注意事项

### 服务端要求

同步系统需要一个兼容的服务端API，应该提供以下端点：

- `GET /health` - 健康检查
- `POST /sync/records` - 上传同步记录
- `GET /sync/records?device_id=xxx&since=xxx` - 获取同步记录

### 数据库

系统使用SQLite作为本地存储，数据库文件会自动创建。确保应用有足够的磁盘空间和写入权限。

### 网络

- 确保设备能访问配置的NTP服务器
- 确保设备能访问同步服务端API
- 考虑网络不稳定的情况，系统有内置的重试机制

## 🔧 故障排除

### 常见问题

1. **NTP同步失败**
   - 检查网络连接
   - 尝试更换NTP服务器
   - 检查防火墙设置

2. **同步记录堆积**
   - 检查服务端API是否正常
   - 检查网络连接
   - 考虑增加同步频率

3. **冲突频繁**
   - 检查设备时间是否同步
   - 考虑调整冲突解决策略
   - 检查数据变更的频率和模式

### 日志调试

启用详细日志：

```rust
env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
```

## 📈 性能优化

### 建议

1. **合理设置同步间隔**：根据数据变更频率调整
2. **批量处理**：系统自动批量处理同步记录
3. **数据压缩**：大数据可以考虑压缩后存储
4. **定期清理**：清理过期的同步记录

### 清理旧记录

```rust
// 清理30天前的记录
let deleted_count = storage.cleanup_old_records(
    Utc::now() - chrono::Duration::days(30)
).await?;
println!("清理了 {} 条旧记录", deleted_count);
```

## 🧪 测试

运行测试：

```bash
cd src-tauri
cargo test sync
```

运行特定模块测试：

```bash
cargo test sync::ntp_client
cargo test sync::conflict_resolver
```

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。 