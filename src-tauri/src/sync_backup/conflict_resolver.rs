//! 冲突解决模块
//! 
//! 处理数据同步时的冲突，提供多种冲突解决策略

use crate::sync::{ConflictResolution, SyncError, SyncRecord, SyncRecordType};
use crate::sync::three_way_merge::{<PERSON><PERSON><PERSON>Merger, MergeConfig, MergeResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// 冲突信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictInfo {
    /// 冲突ID
    pub id: String,
    /// 表名
    pub table_name: String,
    /// 记录ID
    pub record_id: String,
    /// 本地记录
    pub local_record: SyncRecord,
    /// 服务端记录
    pub server_record: SyncRecord,
    /// 冲突类型
    pub conflict_type: ConflictType,
    /// 冲突描述
    pub description: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 是否已解决
    pub resolved: bool,
    /// 解决方案
    pub resolution: Option<ConflictResolution>,
    /// 解决时间
    pub resolved_at: Option<DateTime<Utc>>,
}

/// 冲突类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConflictType {
    /// 版本冲突（两个设备同时修改）
    VersionConflict,
    /// 数据冲突（内容不一致）
    DataConflict,
    /// 操作冲突（一个删除一个修改）
    OperationConflict,
    /// 时间戳冲突（时间戳异常）
    TimestampConflict,
}

/// 冲突解决结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictResolutionResult {
    /// 是否成功解决
    pub success: bool,
    /// 最终采用的记录
    pub resolved_record: Option<SyncRecord>,
    /// 错误信息
    pub error: Option<String>,
    /// 解决策略
    pub strategy: ConflictResolution,
    /// 详细信息
    pub details: HashMap<String, String>,
}

/// 冲突解决器
pub struct ConflictResolver {
    /// 默认解决策略
    default_strategy: ConflictResolution,
    /// 表级别的解决策略
    table_strategies: HashMap<String, ConflictResolution>,
    /// 自定义解决器
    custom_resolvers: HashMap<String, Box<dyn ConflictResolverTrait + Send + Sync>>,
}

/// 冲突解决器特征
pub trait ConflictResolverTrait {
    /// 解决冲突
    fn resolve_conflict(&self, conflict: &ConflictInfo) -> ConflictResolutionResult;
}

impl ConflictResolver {
    /// 创建新的冲突解决器
    pub fn new(default_strategy: ConflictResolution) -> Self {
        Self {
            default_strategy,
            table_strategies: HashMap::new(),
            custom_resolvers: HashMap::new(),
        }
    }

    /// 设置表级别的解决策略
    pub fn set_table_strategy(&mut self, table_name: &str, strategy: ConflictResolution) {
        self.table_strategies.insert(table_name.to_string(), strategy);
    }

    /// 添加自定义解决器
    pub fn add_custom_resolver<R>(&mut self, name: &str, resolver: R)
    where
        R: ConflictResolverTrait + Send + Sync + 'static,
    {
        self.custom_resolvers.insert(name.to_string(), Box::new(resolver));
    }

    /// 检测冲突
    pub fn detect_conflict(
        &self,
        local_record: &SyncRecord,
        server_record: &SyncRecord,
    ) -> Option<ConflictInfo> {
        // 检查是否为同一记录
        if local_record.table_name != server_record.table_name
            || local_record.record_id != server_record.record_id
        {
            return None;
        }

        let conflict_type = self.determine_conflict_type(local_record, server_record);
        
        if let Some(conflict_type) = conflict_type {
            let description = self.generate_conflict_description(&conflict_type, local_record, server_record);
            
            Some(ConflictInfo {
                id: uuid::Uuid::new_v4().to_string(),
                table_name: local_record.table_name.clone(),
                record_id: local_record.record_id.clone(),
                local_record: local_record.clone(),
                server_record: server_record.clone(),
                conflict_type,
                description,
                created_at: Utc::now(),
                resolved: false,
                resolution: None,
                resolved_at: None,
            })
        } else {
            None
        }
    }

    /// 解决冲突
    pub fn resolve_conflict(&self, conflict: &ConflictInfo) -> Result<ConflictResolutionResult, SyncError> {
        // 获取解决策略
        let strategy = self.get_resolution_strategy(&conflict.table_name);
        
        // 检查是否有自定义解决器
        if let Some(resolver) = self.custom_resolvers.get(&conflict.table_name) {
            return Ok(resolver.resolve_conflict(conflict));
        }

        // 使用内置策略解决冲突
        self.resolve_with_strategy(conflict, &strategy)
    }

    /// 使用指定策略解决冲突
    pub fn resolve_with_strategy(
        &self,
        conflict: &ConflictInfo,
        strategy: &ConflictResolution,
    ) -> Result<ConflictResolutionResult, SyncError> {
        let mut details = HashMap::new();
        details.insert("conflict_id".to_string(), conflict.id.clone());
        details.insert("conflict_type".to_string(), format!("{:?}", conflict.conflict_type));

        let resolved_record = match strategy {
            ConflictResolution::UseServer => {
                details.insert("decision".to_string(), "使用服务端版本".to_string());
                Some(conflict.server_record.clone())
            }
            ConflictResolution::UseLocal => {
                details.insert("decision".to_string(), "使用本地版本".to_string());
                Some(conflict.local_record.clone())
            }
            ConflictResolution::UseLatest => {
                let local_time = conflict.local_record.server_timestamp
                    .unwrap_or(conflict.local_record.local_timestamp);
                let server_time = conflict.server_record.server_timestamp
                    .unwrap_or(conflict.server_record.local_timestamp);

                if local_time >= server_time {
                    details.insert("decision".to_string(), "使用本地版本（时间更新）".to_string());
                    Some(conflict.local_record.clone())
                } else {
                    details.insert("decision".to_string(), "使用服务端版本（时间更新）".to_string());
                    Some(conflict.server_record.clone())
                }
            }
            ConflictResolution::Manual => {
                details.insert("decision".to_string(), "需要手动解决".to_string());
                return Ok(ConflictResolutionResult {
                    success: false,
                    resolved_record: None,
                    error: Some("需要手动解决冲突".to_string()),
                    strategy: strategy.clone(),
                    details,
                });
            }
        };

        Ok(ConflictResolutionResult {
            success: true,
            resolved_record,
            error: None,
            strategy: strategy.clone(),
            details,
        })
    }

    /// 批量解决冲突
    pub fn resolve_conflicts(&self, conflicts: &[ConflictInfo]) -> Vec<ConflictResolutionResult> {
        conflicts
            .iter()
            .map(|conflict| {
                self.resolve_conflict(conflict)
                    .unwrap_or_else(|e| ConflictResolutionResult {
                        success: false,
                        resolved_record: None,
                        error: Some(e.to_string()),
                        strategy: self.default_strategy.clone(),
                        details: HashMap::new(),
                    })
            })
            .collect()
    }

    /// 确定冲突类型
    fn determine_conflict_type(
        &self,
        local_record: &SyncRecord,
        server_record: &SyncRecord,
    ) -> Option<ConflictType> {
        // 检查版本冲突
        if local_record.version != server_record.version {
            // 如果两个记录的版本不同，但都不是基于对方的版本，则为版本冲突
            return Some(ConflictType::VersionConflict);
        }

        // 检查操作冲突
        if self.is_operation_conflict(&local_record.operation_type, &server_record.operation_type) {
            return Some(ConflictType::OperationConflict);
        }

        // 检查数据冲突
        if local_record.data_hash != server_record.data_hash {
            return Some(ConflictType::DataConflict);
        }

        // 检查时间戳冲突
        if self.is_timestamp_conflict(local_record, server_record) {
            return Some(ConflictType::TimestampConflict);
        }

        None
    }

    /// 检查是否为操作冲突
    fn is_operation_conflict(&self, local_op: &SyncRecordType, server_op: &SyncRecordType) -> bool {
        match (local_op, server_op) {
            // 一个删除，一个修改
            (SyncRecordType::Delete, SyncRecordType::Update) => true,
            (SyncRecordType::Update, SyncRecordType::Delete) => true,
            (SyncRecordType::SoftDelete, SyncRecordType::Update) => true,
            (SyncRecordType::Update, SyncRecordType::SoftDelete) => true,
            // 一个删除，一个软删除
            (SyncRecordType::Delete, SyncRecordType::SoftDelete) => true,
            (SyncRecordType::SoftDelete, SyncRecordType::Delete) => true,
            _ => false,
        }
    }

    /// 检查是否为时间戳冲突
    fn is_timestamp_conflict(&self, local_record: &SyncRecord, server_record: &SyncRecord) -> bool {
        // 检查时间戳是否异常（比如未来时间或过于久远的时间）
        let now = Utc::now();
        let max_future_offset = chrono::Duration::hours(1); // 允许1小时的未来时间
        let max_past_offset = chrono::Duration::days(365); // 允许1年的过去时间

        let local_time = local_record.server_timestamp.unwrap_or(local_record.local_timestamp);
        let server_time = server_record.server_timestamp.unwrap_or(server_record.local_timestamp);

        // 检查是否有异常的时间戳
        local_time > now + max_future_offset
            || local_time < now - max_past_offset
            || server_time > now + max_future_offset
            || server_time < now - max_past_offset
    }

    /// 生成冲突描述
    fn generate_conflict_description(
        &self,
        conflict_type: &ConflictType,
        local_record: &SyncRecord,
        server_record: &SyncRecord,
    ) -> String {
        match conflict_type {
            ConflictType::VersionConflict => {
                format!(
                    "版本冲突：本地版本 {} vs 服务端版本 {}",
                    local_record.version, server_record.version
                )
            }
            ConflictType::DataConflict => {
                "数据冲突：本地和服务端的数据内容不一致".to_string()
            }
            ConflictType::OperationConflict => {
                format!(
                    "操作冲突：本地操作 {:?} vs 服务端操作 {:?}",
                    local_record.operation_type, server_record.operation_type
                )
            }
            ConflictType::TimestampConflict => {
                "时间戳冲突：检测到异常的时间戳".to_string()
            }
        }
    }

    /// 获取解决策略
    fn get_resolution_strategy(&self, table_name: &str) -> ConflictResolution {
        self.table_strategies
            .get(table_name)
            .cloned()
            .unwrap_or_else(|| self.default_strategy.clone())
    }
}

/// 智能冲突解决器
pub struct SmartConflictResolver;

impl ConflictResolverTrait for SmartConflictResolver {
    fn resolve_conflict(&self, conflict: &ConflictInfo) -> ConflictResolutionResult {
        let mut details = HashMap::new();
        details.insert("resolver".to_string(), "SmartConflictResolver".to_string());

        let resolved_record = match &conflict.conflict_type {
            ConflictType::VersionConflict => {
                // 对于版本冲突，选择版本号更高的
                if conflict.local_record.version > conflict.server_record.version {
                    details.insert("decision".to_string(), "选择本地版本（版本号更高）".to_string());
                    Some(conflict.local_record.clone())
                } else {
                    details.insert("decision".to_string(), "选择服务端版本（版本号更高）".to_string());
                    Some(conflict.server_record.clone())
                }
            }
            ConflictType::OperationConflict => {
                // 对于操作冲突，删除操作优先级更高
                match (&conflict.local_record.operation_type, &conflict.server_record.operation_type) {
                    (SyncRecordType::Delete, _) => {
                        details.insert("decision".to_string(), "选择本地版本（删除优先）".to_string());
                        Some(conflict.local_record.clone())
                    }
                    (_, SyncRecordType::Delete) => {
                        details.insert("decision".to_string(), "选择服务端版本（删除优先）".to_string());
                        Some(conflict.server_record.clone())
                    }
                    _ => {
                        // 其他情况选择时间更新的
                        let local_time = conflict.local_record.server_timestamp
                            .unwrap_or(conflict.local_record.local_timestamp);
                        let server_time = conflict.server_record.server_timestamp
                            .unwrap_or(conflict.server_record.local_timestamp);

                        if local_time >= server_time {
                            details.insert("decision".to_string(), "选择本地版本（时间更新）".to_string());
                            Some(conflict.local_record.clone())
                        } else {
                            details.insert("decision".to_string(), "选择服务端版本（时间更新）".to_string());
                            Some(conflict.server_record.clone())
                        }
                    }
                }
            }
            _ => {
                // 其他冲突类型，选择时间更新的
                let local_time = conflict.local_record.server_timestamp
                    .unwrap_or(conflict.local_record.local_timestamp);
                let server_time = conflict.server_record.server_timestamp
                    .unwrap_or(conflict.server_record.local_timestamp);

                if local_time >= server_time {
                    details.insert("decision".to_string(), "选择本地版本（时间更新）".to_string());
                    Some(conflict.local_record.clone())
                } else {
                    details.insert("decision".to_string(), "选择服务端版本（时间更新）".to_string());
                    Some(conflict.server_record.clone())
                }
            }
        };

        ConflictResolutionResult {
            success: true,
            resolved_record,
            error: None,
            strategy: ConflictResolution::UseLatest, // 智能解决器使用自定义策略
            details,
        }
    }
}

/// 智能三路合并冲突解决器
pub struct SmartMergeConflictResolver {
    /// 三路合并器
    merger: ThreeWayMerger,
    /// 基础版本查找器
    base_version_finder: Arc<dyn BaseVersionFinder + Send + Sync>,
    /// 回退策略
    fallback_strategy: ConflictResolution,
}

/// 基础版本查找器特征
pub trait BaseVersionFinder {
    /// 查找两个记录的共同基础版本
    fn find_base_version(
        &self,
        local_record: &SyncRecord,
        remote_record: &SyncRecord,
    ) -> Option<SyncRecord>;
}

/// 简单的基础版本查找器（基于版本号）
pub struct SimpleBaseVersionFinder;

impl BaseVersionFinder for SimpleBaseVersionFinder {
    fn find_base_version(
        &self,
        local_record: &SyncRecord,
        remote_record: &SyncRecord,
    ) -> Option<SyncRecord> {
        // 简单实现：使用版本号较小的作为基础版本
        let base_version = std::cmp::min(local_record.version, remote_record.version) - 1;
        
        if base_version <= 0 {
            return None;
        }

        // 创建一个模拟的基础版本记录
        // 在实际实现中，这应该从数据库或版本历史中查找
        let mut base_record = local_record.clone();
        base_record.version = base_version;
        base_record.data = Some(r#"{}"#.to_string()); // 空的基础数据
        
        Some(base_record)
    }
}

/// 高级基础版本查找器（支持版本历史）
pub struct AdvancedBaseVersionFinder {
    /// 版本历史存储
    version_history: HashMap<String, Vec<SyncRecord>>,
}

impl AdvancedBaseVersionFinder {
    pub fn new() -> Self {
        Self {
            version_history: HashMap::new(),
        }
    }

    /// 添加版本历史记录
    pub fn add_version_history(&mut self, record_key: String, history: Vec<SyncRecord>) {
        self.version_history.insert(record_key, history);
    }

    /// 查找最近公共祖先
    fn find_common_ancestor(&self, local: &SyncRecord, remote: &SyncRecord) -> Option<SyncRecord> {
        let record_key = format!("{}:{}", local.table_name, local.record_id);
        
        if let Some(history) = self.version_history.get(&record_key) {
            // 查找两个版本的最近公共祖先
            for record in history.iter().rev() {
                if record.version < local.version && record.version < remote.version {
                    return Some(record.clone());
                }
            }
        }
        
        None
    }
}

impl BaseVersionFinder for AdvancedBaseVersionFinder {
    fn find_base_version(
        &self,
        local_record: &SyncRecord,
        remote_record: &SyncRecord,
    ) -> Option<SyncRecord> {
        self.find_common_ancestor(local_record, remote_record)
    }
}

impl SmartMergeConflictResolver {
    /// 创建新的智能合并冲突解决器
    pub fn new() -> Self {
        Self {
            merger: ThreeWayMerger::new(),
            base_version_finder: Arc::new(SimpleBaseVersionFinder),
            fallback_strategy: ConflictResolution::UseLatest,
        }
    }

    /// 使用自定义配置创建解决器
    pub fn with_config(
        merge_config: MergeConfig,
        base_finder: Arc<dyn BaseVersionFinder + Send + Sync>,
        fallback: ConflictResolution,
    ) -> Self {
        Self {
            merger: ThreeWayMerger::with_config(merge_config),
            base_version_finder: base_finder,
            fallback_strategy: fallback,
        }
    }

    /// 设置基础版本查找器
    pub fn set_base_version_finder(&mut self, finder: Arc<dyn BaseVersionFinder + Send + Sync>) {
        self.base_version_finder = finder;
    }

    /// 设置回退策略
    pub fn set_fallback_strategy(&mut self, strategy: ConflictResolution) {
        self.fallback_strategy = strategy;
    }

    /// 回退解决方案
    fn fallback_resolution(&self, conflict: &ConflictInfo) -> ConflictResolutionResult {
        let mut details = HashMap::new();
        details.insert("fallback_reason".to_string(), "三路合并失败或无基础版本".to_string());
        details.insert("fallback_strategy".to_string(), format!("{:?}", self.fallback_strategy));

        let resolved_record = match self.fallback_strategy {
            ConflictResolution::UseServer => Some(conflict.server_record.clone()),
            ConflictResolution::UseLocal => Some(conflict.local_record.clone()),
            ConflictResolution::UseLatest => {
                let local_time = conflict.local_record.server_timestamp
                    .unwrap_or(conflict.local_record.local_timestamp);
                let server_time = conflict.server_record.server_timestamp
                    .unwrap_or(conflict.server_record.local_timestamp);

                if local_time >= server_time {
                    Some(conflict.local_record.clone())
                } else {
                    Some(conflict.server_record.clone())
                }
            }
            ConflictResolution::Manual => None,
        };

        let success = resolved_record.is_some();
        let error = if resolved_record.is_none() {
            Some("需要手动解决冲突".to_string())
        } else {
            None
        };

        ConflictResolutionResult {
            success,
            resolved_record,
            error,
            strategy: self.fallback_strategy.clone(),
            details,
        }
    }
}

impl ConflictResolverTrait for SmartMergeConflictResolver {
    fn resolve_conflict(&self, conflict: &ConflictInfo) -> ConflictResolutionResult {
        // 尝试找到基础版本
        if let Some(base_record) = self.base_version_finder.find_base_version(
            &conflict.local_record,
            &conflict.server_record,
        ) {
            // 执行三路合并
            match self.merger.merge(&base_record, &conflict.local_record, &conflict.server_record) {
                Ok(merge_result) => {
                    let mut details = HashMap::new();
                    details.insert("merge_method".to_string(), "three_way_merge".to_string());
                    details.insert("base_version".to_string(), base_record.version.to_string());
                    details.insert("merge_statistics".to_string(), 
                        format!("总字段:{}, 合并:{}, 冲突:{}", 
                            merge_result.statistics.total_fields,
                            merge_result.statistics.merged_fields,
                            merge_result.statistics.conflicted_fields));

                    // 添加合并详情
                    for (key, value) in merge_result.details {
                        details.insert(format!("merge_{}", key), value);
                    }

                    // 如果有冲突但不是严格模式，仍然返回合并结果
                    if merge_result.success || !merge_result.conflicts.is_empty() {
                        ConflictResolutionResult {
                            success: merge_result.success,
                            resolved_record: merge_result.merged_record,
                            error: if !merge_result.success {
                                Some(format!("合并包含{}个冲突", merge_result.conflicts.len()))
                            } else {
                                None
                            },
                            strategy: ConflictResolution::UseLatest, // 表示使用智能合并
                            details,
                        }
                    } else {
                        // 合并失败，使用回退策略
                        details.insert("merge_failure_reason".to_string(), "严格模式下存在无法解决的冲突".to_string());
                        let mut fallback_result = self.fallback_resolution(conflict);
                        fallback_result.details.extend(details);
                        fallback_result
                    }
                }
                Err(e) => {
                    // 合并失败，使用回退策略
                    let mut fallback_result = self.fallback_resolution(conflict);
                    fallback_result.details.insert("merge_error".to_string(), e.to_string());
                    fallback_result
                }
            }
        } else {
            // 没有基础版本，使用回退策略
            let mut fallback_result = self.fallback_resolution(conflict);
            fallback_result.details.insert("no_base_version".to_string(), "true".to_string());
            fallback_result
        }
    }
}

impl Default for SmartMergeConflictResolver {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    fn create_test_record(
        version: i64,
        operation_type: SyncRecordType,
        data: Option<&str>,
        timestamp: DateTime<Utc>,
    ) -> SyncRecord {
        SyncRecord {
            id: Uuid::new_v4(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            operation_type,
            data: data.map(|s| s.to_string()),
            local_timestamp: timestamp,
            server_timestamp: Some(timestamp),
            version,
            device_id: "test_device".to_string(),
            synced: false,
            retry_count: 0,
            data_hash: data.map(|d| crate::sync::calculate_data_hash(d)),
            created_at: timestamp,
            updated_at: timestamp,
        }
    }

    #[test]
    fn test_conflict_resolver_creation() {
        let _resolver = ConflictResolver::new(ConflictResolution::UseLatest);
        // 测试创建成功
        assert!(true);
    }

    #[test]
    fn test_version_conflict_detection() {
        let resolver = ConflictResolver::new(ConflictResolution::UseLatest);
        let now = Utc::now();

        let local_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"name": "local"}"#),
            now,
        );

        let server_record = create_test_record(
            2,
            SyncRecordType::Update,
            Some(r#"{"name": "server"}"#),
            now,
        );

        let conflict = resolver.detect_conflict(&local_record, &server_record);
        assert!(conflict.is_some());
        
        let conflict = conflict.unwrap();
        assert_eq!(conflict.conflict_type, ConflictType::VersionConflict);
    }

    #[test]
    fn test_operation_conflict_detection() {
        let resolver = ConflictResolver::new(ConflictResolution::UseLatest);
        let now = Utc::now();

        let local_record = create_test_record(
            1,
            SyncRecordType::Delete,
            None,
            now,
        );

        let server_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"name": "server"}"#),
            now,
        );

        let conflict = resolver.detect_conflict(&local_record, &server_record);
        assert!(conflict.is_some());
        
        let conflict = conflict.unwrap();
        assert_eq!(conflict.conflict_type, ConflictType::OperationConflict);
    }

    #[test]
    fn test_conflict_resolution_use_latest() {
        let resolver = ConflictResolver::new(ConflictResolution::UseLatest);
        let now = Utc::now();
        let earlier = now - chrono::Duration::minutes(10);

        let local_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"name": "local"}"#),
            now,
        );

        let server_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"name": "server"}"#),
            earlier,
        );

        let conflict = ConflictInfo {
            id: "test_conflict".to_string(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            local_record: local_record.clone(),
            server_record,
            conflict_type: ConflictType::DataConflict,
            description: "Test conflict".to_string(),
            created_at: now,
            resolved: false,
            resolution: None,
            resolved_at: None,
        };

        let result = resolver.resolve_conflict(&conflict).unwrap();
        assert!(result.success);
        assert!(result.resolved_record.is_some());
        
        let resolved = result.resolved_record.unwrap();
        assert_eq!(resolved.id, local_record.id); // 应该选择本地记录（时间更新）
    }

    #[test]
    fn test_smart_conflict_resolver() {
        let resolver = SmartConflictResolver;
        let now = Utc::now();

        let local_record = create_test_record(
            2,
            SyncRecordType::Update,
            Some(r#"{"name": "local"}"#),
            now,
        );

        let server_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"name": "server"}"#),
            now,
        );

        let conflict = ConflictInfo {
            id: "test_conflict".to_string(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            local_record: local_record.clone(),
            server_record,
            conflict_type: ConflictType::VersionConflict,
            description: "Test conflict".to_string(),
            created_at: now,
            resolved: false,
            resolution: None,
            resolved_at: None,
        };

        let result = resolver.resolve_conflict(&conflict);
        assert!(result.success);
        assert!(result.resolved_record.is_some());
        
        let resolved = result.resolved_record.unwrap();
        assert_eq!(resolved.id, local_record.id); // 应该选择版本号更高的本地记录
    }

    #[test]
    fn test_smart_merge_conflict_resolver_creation() {
        let resolver = SmartMergeConflictResolver::new();
        assert_eq!(resolver.fallback_strategy, ConflictResolution::UseLatest);
    }

    #[test]
    fn test_smart_merge_successful_resolution() {
        let resolver = SmartMergeConflictResolver::new();
        let now = Utc::now();

        // 创建基础版本
        let base_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"username": "user", "password": "old_pass"}"#),
            now - chrono::Duration::minutes(10),
        );

        // 本地修改了密码
        let local_record = create_test_record(
            2,
            SyncRecordType::Update,
            Some(r#"{"username": "user", "password": "new_pass"}"#),
            now - chrono::Duration::minutes(5),
        );

        // 远程修改了用户名
        let server_record = create_test_record(
            2,
            SyncRecordType::Update,
            Some(r#"{"username": "new_user", "password": "old_pass"}"#),
            now,
        );

        let conflict = ConflictInfo {
            id: "test_conflict".to_string(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            local_record,
            server_record,
            conflict_type: ConflictType::DataConflict,
            description: "Test conflict".to_string(),
            created_at: now,
            resolved: false,
            resolution: None,
            resolved_at: None,
        };

        // 设置自定义基础版本查找器
        let mut advanced_finder = AdvancedBaseVersionFinder::new();
        advanced_finder.add_version_history(
            "test_table:test_record_1".to_string(),
            vec![base_record],
        );

        let mut resolver = SmartMergeConflictResolver::new();
        resolver.set_base_version_finder(Arc::new(advanced_finder));

        let result = resolver.resolve_conflict(&conflict);
        assert!(result.success);
        assert!(result.resolved_record.is_some());

        // 验证合并结果包含两边的修改
        let resolved = result.resolved_record.unwrap();
        let resolved_data: serde_json::Value = serde_json::from_str(
            resolved.data.as_ref().unwrap()
        ).unwrap();
        
        // 应该包含本地的密码修改和远程的用户名修改
        assert_eq!(resolved_data["username"], "new_user");
        assert_eq!(resolved_data["password"], "new_pass");
    }

    #[test]
    fn test_smart_merge_fallback_on_no_base() {
        let resolver = SmartMergeConflictResolver::new();
        let now = Utc::now();

        let local_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"password": "local_pass"}"#),
            now,
        );

        let server_record = create_test_record(
            1,
            SyncRecordType::Update,
            Some(r#"{"password": "server_pass"}"#),
            now + chrono::Duration::minutes(1),
        );

        let conflict = ConflictInfo {
            id: "test_conflict".to_string(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            local_record,
            server_record,
            conflict_type: ConflictType::DataConflict,
            description: "Test conflict".to_string(),
            created_at: now,
            resolved: false,
            resolution: None,
            resolved_at: None,
        };

        let result = resolver.resolve_conflict(&conflict);
        assert!(result.success);
        assert!(result.details.contains_key("no_base_version"));
        assert_eq!(result.details["no_base_version"], "true");
        
        // 应该使用回退策略（UseLatest）
        let resolved = result.resolved_record.unwrap();
        let resolved_data: serde_json::Value = serde_json::from_str(
            resolved.data.as_ref().unwrap()
        ).unwrap();
        assert_eq!(resolved_data["password"], "server_pass"); // 服务端时间更新
    }

    #[test]
    fn test_advanced_base_version_finder() {
        let mut finder = AdvancedBaseVersionFinder::new();
        let now = Utc::now();

        // 添加版本历史
        let base_record = create_test_record(
            1,
            SyncRecordType::Create,
            Some(r#"{"username": "user", "password": "initial"}"#),
            now - chrono::Duration::hours(1),
        );

        let intermediate_record = create_test_record(
            2,
            SyncRecordType::Update,
            Some(r#"{"username": "user", "password": "updated"}"#),
            now - chrono::Duration::minutes(30),
        );

        finder.add_version_history(
            "test_table:test_record_1".to_string(),
            vec![base_record.clone(), intermediate_record],
        );

        let local_record = create_test_record(
            3,
            SyncRecordType::Update,
            Some(r#"{"username": "local_user", "password": "updated"}"#),
            now - chrono::Duration::minutes(10),
        );

        let remote_record = create_test_record(
            3,
            SyncRecordType::Update,
            Some(r#"{"username": "user", "password": "remote_pass"}"#),
            now,
        );

        let found_base = finder.find_base_version(&local_record, &remote_record);
        assert!(found_base.is_some());
        
        let base = found_base.unwrap();
        assert_eq!(base.version, 2); // 应该找到版本2作为公共祖先
    }

    #[test]
    fn test_merge_config_integration() {
        let merge_config = MergeConfig {
            strict_mode: true,
            preserve_history: true,
            max_recursion_depth: 5,
            enable_type_coercion: false,
        };

        let resolver = SmartMergeConflictResolver::with_config(
            merge_config,
            Arc::new(SimpleBaseVersionFinder),
            ConflictResolution::UseServer,
        );

        assert_eq!(resolver.fallback_strategy, ConflictResolution::UseServer);
    }
} 