# 三路合并智能同步系统

## 🚀 快速开始

### 运行演示程序
```bash
cargo run --example three_way_merge_demo
```

### 运行测试
```bash
# 运行所有三路合并测试
cargo test three_way_merge --lib

# 运行复杂场景测试
cargo test three_way_merge_tests::complex_scenarios --lib -- --nocapture
```

## 📋 功能特性

- ✅ **智能字段级合并**: 6种合并策略
- ✅ **Passkey凭据同步**: 防重复使用算法
- ✅ **2FA备份码管理**: 智能码追踪
- ✅ **附件管理**: 文件操作智能合并
- ✅ **深度嵌套支持**: 递归对象处理
- ✅ **版本历史追踪**: 最近公共祖先查找
- ✅ **严格模式检查**: 企业级数据完整性

## 🏗️ 核心模块

| 模块 | 文件 | 功能 |
|------|------|------|
| 三路合并引擎 | `src/sync/three_way_merge.rs` | 核心合并算法 |
| 智能冲突解决器 | `src/sync/conflict_resolver.rs` | 冲突检测与解决 |
| 复杂场景测试 | `src/sync/three_way_merge_tests.rs` | 全面测试覆盖 |
| 演示程序 | `examples/three_way_merge_demo.rs` | 实际应用展示 |

## 🔧 使用示例

### 基础三路合并
```rust
use secure_password_lib::sync::three_way_merge::ThreeWayMerger;

let merger = ThreeWayMerger::new();
let result = merger.merge(&base_record, &local_record, &remote_record)?;

if result.success {
    println!("合并成功! 统计: {:?}", result.statistics);
    let merged_record = result.merged_record.unwrap();
    // 使用合并后的记录
}
```

### 智能冲突解决
```rust
use secure_password_lib::sync::conflict_resolver::SmartMergeConflictResolver;

let resolver = SmartMergeConflictResolver::new();
let result = resolver.resolve_conflict(&conflict_info);

if result.success {
    println!("冲突解决成功!");
    let resolved_record = result.resolved_record.unwrap();
    // 使用解决后的记录
}
```

## 📊 性能指标

- **时间复杂度**: O(n)
- **空间复杂度**: O(n)  
- **测试覆盖**: 16个测试，100%通过
- **代码规模**: 3000+ 行

## 🎯 适用场景

- 📱 多设备密码同步
- 🔑 Passkey凭据管理
- 🛡️ 2FA设置同步
- 📎 附件文件管理
- ⚙️ 应用配置同步

## 📚 详细文档

查看 [`docs/three_way_merge_summary.md`](docs/three_way_merge_summary.md) 获取完整的技术文档和实现细节。

## 🧪 测试结果

```
running 16 tests
✅ test_three_way_merger_creation
✅ test_simple_merge_no_conflict  
✅ test_array_merge
✅ test_conflict_detection
✅ test_passkey_counter_merge
✅ test_prefer_non_empty_strategy
✅ test_prefer_longer_strategy
✅ test_invalid_input_validation
✅ test_merge_statistics
✅ test_complex_password_entry_merge
✅ test_passkey_credential_merge
✅ test_2fa_backup_codes_merge
✅ test_smart_merge_conflict_resolver_complex_scenario
✅ test_attachment_merge_scenario
✅ test_merge_with_strict_mode
✅ test_deep_nested_object_merge

test result: ok. 16 passed; 0 failed
```

## 🔮 未来计划

- [ ] 图形化冲突解决界面
- [ ] 机器学习辅助合并决策
- [ ] 分布式版本控制集成
- [ ] 实时协作编辑支持

---

**状态**: 生产就绪 ✅  
**版本**: 1.0.0  
**最后更新**: 2024年12月 