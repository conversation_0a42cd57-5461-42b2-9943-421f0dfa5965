# 密码管理器同步系统 - 技术规范

## 📋 概述

本文档详细描述了密码管理器多端同步系统的三个核心技术组件的实现规范：
1. 向量时钟 (Vector Clock)
2. 操作依赖图 (Operation Dependency Graph)  
3. 三路合并 (Three-Way Merge)

---

## 🕐 向量时钟技术规范

### 数据结构设计

```rust
// src/sync/vector_clock.rs

/// 向量时钟
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VectorClock {
    /// 设备ID -> 逻辑时钟值的映射
    clocks: HashMap<String, u64>,
}

/// 因果关系枚举
#[derive(Debug, Clone, PartialEq)]
pub enum CausalRelation {
    Before,     // self 发生在 other 之前
    After,      // self 发生在 other 之后  
    Equal,      // 两个事件相等
    Concurrent, // 两个事件并发
}

/// 扩展的同步记录
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VectorSyncRecord {
    pub base_record: SyncRecord,
    pub vector_clock: VectorClock,
    pub causal_dependencies: Vec<Uuid>,
}
```

### 核心算法

#### 时钟递增算法
```rust
impl VectorClock {
    /// 本地事件发生时递增时钟
    pub fn tick(&mut self, device_id: &str) {
        let current = self.clocks.get(device_id).unwrap_or(&0);
        self.clocks.insert(device_id.to_string(), current + 1);
    }
}
```

#### 时钟更新算法
```rust
impl VectorClock {
    /// 接收到远程事件时更新时钟
    pub fn update(&mut self, other: &VectorClock, device_id: &str) {
        // 1. 对于其他设备，取最大值
        for (other_device, &other_time) in &other.clocks {
            if other_device != device_id {
                let current = self.clocks.get(other_device).unwrap_or(&0);
                self.clocks.insert(other_device.clone(), (*current).max(other_time));
            }
        }
        
        // 2. 递增本设备时钟
        self.tick(device_id);
    }
}
```

#### 因果关系比较算法
```rust
impl VectorClock {
    /// 比较两个向量时钟的因果关系
    pub fn compare(&self, other: &VectorClock) -> CausalRelation {
        let mut self_less = false;
        let mut other_less = false;

        let all_devices: HashSet<_> = self.clocks.keys()
            .chain(other.clocks.keys())
            .collect();

        for device in all_devices {
            let self_time = self.clocks.get(device).unwrap_or(&0);
            let other_time = other.clocks.get(device).unwrap_or(&0);

            if self_time < other_time {
                self_less = true;
            } else if self_time > other_time {
                other_less = true;
            }
        }

        match (self_less, other_less) {
            (true, false) => CausalRelation::Before,
            (false, true) => CausalRelation::After,
            (false, false) => CausalRelation::Equal,
            (true, true) => CausalRelation::Concurrent,
        }
    }
}
```

### 集成点

1. **LogTracker集成**：在记录变更时更新向量时钟
2. **ConflictResolver集成**：使用因果关系优化冲突检测
3. **SyncStorage集成**：持久化向量时钟数据

---

## 📊 操作依赖图技术规范

### 数据结构设计

```rust
// src/sync/dependency_graph.rs

/// 操作依赖图
#[derive(Debug, Clone)]
pub struct OperationDependencyGraph {
    /// 节点：操作ID -> 操作记录
    nodes: HashMap<Uuid, VectorSyncRecord>,
    /// 正向边：依赖者 -> 被依赖者集合
    edges: HashMap<Uuid, HashSet<Uuid>>,
    /// 反向边：被依赖者 -> 依赖者集合
    reverse_edges: HashMap<Uuid, HashSet<Uuid>>,
}

/// 依赖类型
#[derive(Debug, Clone, PartialEq)]
pub enum DependencyType {
    /// 创建依赖（如文件夹必须先于文件创建）
    Creation,
    /// 引用依赖（如删除前必须先删除引用）
    Reference,
    /// 顺序依赖（如更新必须在创建之后）
    Sequential,
    /// 因果依赖（基于向量时钟的因果关系）
    Causal,
}
```

### 核心算法

#### 拓扑排序算法 (Kahn算法)
```rust
impl OperationDependencyGraph {
    /// 拓扑排序，返回正确的执行顺序
    pub fn topological_sort(&self) -> Result<Vec<Uuid>, SyncError> {
        let mut in_degree: HashMap<Uuid, usize> = HashMap::new();
        let mut queue = VecDeque::new();
        let mut result = Vec::new();

        // 计算入度
        for &node_id in self.nodes.keys() {
            let degree = self.edges.get(&node_id).map_or(0, |deps| deps.len());
            in_degree.insert(node_id, degree);
            
            if degree == 0 {
                queue.push_back(node_id);
            }
        }

        // Kahn算法主循环
        while let Some(current) = queue.pop_front() {
            result.push(current);

            if let Some(dependents) = self.reverse_edges.get(&current) {
                for &dependent in dependents {
                    if let Some(degree) = in_degree.get_mut(&dependent) {
                        *degree -= 1;
                        if *degree == 0 {
                            queue.push_back(dependent);
                        }
                    }
                }
            }
        }

        // 检查循环依赖
        if result.len() != self.nodes.len() {
            return Err(SyncError::ConflictError("检测到循环依赖".to_string()));
        }

        Ok(result)
    }
}
```

#### 循环依赖检测算法 (Tarjan强连通分量)
```rust
impl OperationDependencyGraph {
    /// 使用Tarjan算法检测强连通分量（循环依赖）
    pub fn detect_cycles(&self) -> Vec<Vec<Uuid>> {
        let mut index = 0;
        let mut stack = Vec::new();
        let mut indices = HashMap::new();
        let mut lowlinks = HashMap::new();
        let mut on_stack = HashSet::new();
        let mut sccs = Vec::new();

        for &node in self.nodes.keys() {
            if !indices.contains_key(&node) {
                self.strongconnect(
                    node, &mut index, &mut stack, &mut indices,
                    &mut lowlinks, &mut on_stack, &mut sccs
                );
            }
        }

        // 过滤出大小 > 1 的强连通分量（真正的循环）
        sccs.into_iter().filter(|scc| scc.len() > 1).collect()
    }
}
```

### 密码管理器专用依赖分析

```rust
/// 密码管理器依赖分析器
pub struct CredentialDependencyAnalyzer;

impl CredentialDependencyAnalyzer {
    /// 分析凭据操作的依赖关系
    pub fn analyze_dependencies(
        &self,
        record: &SyncRecord,
        existing_records: &[SyncRecord],
    ) -> Vec<(Uuid, DependencyType)> {
        let mut dependencies = Vec::new();

        match record.operation_type {
            SyncRecordType::Create => {
                // 文件夹依赖
                dependencies.extend(
                    self.find_folder_dependencies(record, existing_records)
                        .into_iter()
                        .map(|id| (id, DependencyType::Creation))
                );
            }
            SyncRecordType::Update => {
                // 必须依赖创建操作
                dependencies.extend(
                    self.find_creation_dependency(record, existing_records)
                        .into_iter()
                        .map(|id| (id, DependencyType::Sequential))
                );
            }
            SyncRecordType::Delete => {
                // 必须在所有引用删除后执行
                dependencies.extend(
                    self.find_reference_dependencies(record, existing_records)
                        .into_iter()
                        .map(|id| (id, DependencyType::Reference))
                );
            }
            _ => {}
        }

        dependencies
    }
}
```

---

## 🔀 三路合并技术规范

### 数据结构设计

```rust
// src/sync/three_way_merge.rs

/// 三路合并器
pub struct ThreeWayMerger {
    /// 字段级合并策略配置
    field_strategies: HashMap<String, FieldMergeStrategy>,
}

/// 字段合并策略
#[derive(Debug, Clone)]
pub enum FieldMergeStrategy {
    /// 最新优先（基于时间戳）
    LatestWins,
    /// 合并数组（去重合并）
    MergeArrays,
    /// 合并对象（递归合并）
    MergeObjects,
    /// 保留非空值
    PreferNonEmpty,
    /// 自定义合并函数
    Custom(fn(&Value, &Value, &Value) -> Result<Value, MergeError>),
}

/// 合并错误类型
#[derive(Debug, thiserror::Error)]
pub enum MergeError {
    #[error("无法合并冲突的值: {0}")]
    ConflictingValues(String),
    #[error("数据格式错误: {0}")]
    InvalidFormat(String),
    #[error("合并策略不支持: {0}")]
    UnsupportedStrategy(String),
}
```

### 核心合并算法

#### 三路合并主算法
```rust
impl ThreeWayMerger {
    /// 执行三路合并
    pub fn merge(
        &self,
        base: &SyncRecord,      // 共同祖先
        local: &SyncRecord,     // 本地版本
        remote: &SyncRecord,    // 远程版本
    ) -> Result<SyncRecord, MergeError> {
        // 1. 解析JSON数据
        let base_data = self.parse_data(&base.data)?;
        let local_data = self.parse_data(&local.data)?;
        let remote_data = self.parse_data(&remote.data)?;

        // 2. 执行字段级合并
        let merged_data = self.merge_objects(&base_data, &local_data, &remote_data)?;

        // 3. 创建合并后的记录
        let mut merged_record = local.clone();
        merged_record.data = Some(serde_json::to_string(&merged_data)?);
        
        // 4. 更新元数据
        merged_record.version = std::cmp::max(local.version, remote.version) + 1;
        merged_record.updated_at = chrono::Utc::now();
        merged_record.data_hash = Some(calculate_data_hash(
            &merged_record.data.as_ref().unwrap()
        ));

        Ok(merged_record)
    }
}
```

#### 字段级合并算法
```rust
impl ThreeWayMerger {
    /// 合并单个字段
    fn merge_field(
        &self,
        field_name: &str,
        base: Option<&Value>,
        local: Option<&Value>,
        remote: Option<&Value>,
    ) -> Result<Option<Value>, MergeError> {
        match (base, local, remote) {
            (Some(b), Some(l), Some(r)) => {
                if l == r {
                    // 本地和远程相同
                    Ok(Some(l.clone()))
                } else if l == b {
                    // 本地未变，使用远程
                    Ok(Some(r.clone()))
                } else if r == b {
                    // 远程未变，使用本地
                    Ok(Some(l.clone()))
                } else {
                    // 都有变化，使用策略合并
                    let strategy = self.field_strategies.get(field_name)
                        .unwrap_or(&FieldMergeStrategy::LatestWins);
                    self.apply_merge_strategy(strategy, b, l, r).map(Some)
                }
            }
            (_, Some(l), Some(r)) => {
                // 基础版本不存在
                if l == r {
                    Ok(Some(l.clone()))
                } else {
                    let strategy = self.field_strategies.get(field_name)
                        .unwrap_or(&FieldMergeStrategy::LatestWins);
                    self.apply_merge_strategy(strategy, &Value::Null, l, r).map(Some)
                }
            }
            (_, Some(l), None) => Ok(Some(l.clone())),
            (_, None, Some(r)) => Ok(Some(r.clone())),
            _ => Ok(None),
        }
    }
}
```

#### 数组合并算法
```rust
impl ThreeWayMerger {
    /// 合并数组（智能去重）
    fn merge_arrays(&self, base: &Value, local: &Value, remote: &Value) -> Result<Value, MergeError> {
        let base_array = base.as_array().unwrap_or(&vec![]);
        let local_array = local.as_array().ok_or_else(|| 
            MergeError::InvalidFormat("本地值不是数组".to_string()))?;
        let remote_array = remote.as_array().ok_or_else(|| 
            MergeError::InvalidFormat("远程值不是数组".to_string()))?;

        // 计算变更
        let local_added: HashSet<_> = local_array.iter()
            .filter(|item| !base_array.contains(item))
            .collect();
        let remote_added: HashSet<_> = remote_array.iter()
            .filter(|item| !base_array.contains(item))
            .collect();
        
        let local_removed: HashSet<_> = base_array.iter()
            .filter(|item| !local_array.contains(item))
            .collect();
        let remote_removed: HashSet<_> = base_array.iter()
            .filter(|item| !remote_array.contains(item))
            .collect();

        // 构建合并结果
        let mut merged = Vec::new();
        
        // 保留基础中未被删除的项
        for item in base_array {
            if !local_removed.contains(&item) && !remote_removed.contains(&item) {
                merged.push(item.clone());
            }
        }
        
        // 添加新增的项
        for item in local_added.union(&remote_added) {
            merged.push((*item).clone());
        }

        Ok(Value::Array(merged))
    }
}
```

### 密码管理器专用配置

```rust
impl ThreeWayMerger {
    /// 配置密码管理器专用的合并策略
    fn configure_credential_strategies(&mut self) {
        // 密码字段：最新优先
        self.field_strategies.insert("password".to_string(), 
            FieldMergeStrategy::LatestWins);
        
        // 标签数组：智能合并
        self.field_strategies.insert("tags".to_string(), 
            FieldMergeStrategy::MergeArrays);
        
        // 自定义字段：递归合并
        self.field_strategies.insert("custom_fields".to_string(), 
            FieldMergeStrategy::MergeObjects);
        
        // 备注：保留非空值
        self.field_strategies.insert("notes".to_string(), 
            FieldMergeStrategy::PreferNonEmpty);
        
        // 2FA备份码：智能合并
        self.field_strategies.insert("backup_codes".to_string(), 
            FieldMergeStrategy::MergeArrays);
        
        // Passkey数据：最新优先
        self.field_strategies.insert("credential_id".to_string(), 
            FieldMergeStrategy::LatestWins);
        self.field_strategies.insert("counter".to_string(), 
            FieldMergeStrategy::Custom(merge_passkey_counter));
    }
}

/// Passkey计数器的自定义合并函数
fn merge_passkey_counter(base: &Value, local: &Value, remote: &Value) -> Result<Value, MergeError> {
    let base_counter = base.as_u64().unwrap_or(0);
    let local_counter = local.as_u64().unwrap_or(0);
    let remote_counter = remote.as_u64().unwrap_or(0);
    
    // Passkey计数器必须单调递增，取最大值
    let max_counter = std::cmp::max(local_counter, remote_counter);
    
    // 如果两个都比基础版本大，说明有并发使用，需要特殊处理
    if local_counter > base_counter && remote_counter > base_counter {
        // 取最大值并加1以避免重复
        Ok(Value::Number(serde_json::Number::from(max_counter + 1)))
    } else {
        Ok(Value::Number(serde_json::Number::from(max_counter)))
    }
}
```

---

## 🔧 集成架构

### 模块间交互图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   VectorClock   │    │ DependencyGraph │    │ ThreeWayMerger  │
│                 │    │                 │    │                 │
│ - tick()        │    │ - add_operation │    │ - merge()       │
│ - update()      │    │ - topological   │    │ - merge_field() │
│ - compare()     │    │ - detect_cycles │    │ - merge_arrays()│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  SyncManager    │
                    │                 │
                    │ - handle_conflict│
                    │ - sync_to_server │
                    │ - sync_from_server│
                    └─────────────────┘
```

### 数据流图

```
1. 本地操作 → LogTracker → VectorClock.tick() → SyncStorage
2. 远程同步 → ConflictResolver → VectorClock.compare() → 冲突检测
3. 冲突解决 → DependencyGraph → 拓扑排序 → ThreeWayMerger → 合并结果
4. 合并结果 → SyncStorage → 应用到本地数据
```

---

## 📊 性能考虑

### 时间复杂度
- **向量时钟比较**: O(n) - n为设备数量
- **拓扑排序**: O(V + E) - V为操作数，E为依赖边数
- **三路合并**: O(f) - f为字段数量

### 空间复杂度
- **向量时钟**: O(n) - n为设备数量
- **依赖图**: O(V + E) - V为操作数，E为依赖边数
- **合并缓存**: O(d) - d为数据大小

### 优化策略
1. **向量时钟压缩**: 定期清理不活跃设备的时钟
2. **依赖图缓存**: 缓存拓扑排序结果
3. **增量合并**: 只合并变更的字段
4. **并行处理**: 无依赖操作可并行执行

---

## 🧪 测试策略

### 单元测试覆盖
- [ ] 向量时钟算法正确性
- [ ] 依赖图拓扑排序
- [ ] 三路合并各种场景
- [ ] 边界条件和错误处理

### 集成测试场景
- [ ] 多设备并发操作
- [ ] 复杂依赖关系处理
- [ ] 大规模数据合并
- [ ] 网络异常恢复

### 性能测试指标
- [ ] 1000个设备的向量时钟性能
- [ ] 10000个操作的依赖图处理
- [ ] 大型JSON对象的合并性能
- [ ] 内存使用和垃圾回收

---

*本文档版本: v1.0*  
*最后更新: 2024年12月* 