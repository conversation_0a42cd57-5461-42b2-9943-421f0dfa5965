//! 同步系统使用示例
//! 
//! 展示如何在实际项目中集成和使用日志同步系统

#[cfg(feature = "example")]
use crate::sync::{SyncManager, SyncConfig, SyncModule, SyncRecordType};

/// 示例：基本的同步系统集成
#[cfg(feature = "example")]
pub async fn basic_sync_example() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 创建同步配置
    let config = SyncConfig {
        enabled: true,
        ntp_servers: vec![
            "time.cloudflare.com".to_string(),
            "pool.ntp.org".to_string(),
        ],
        sync_interval: 300, // 5分钟同步一次
        sync_server_url: Some("https://your-api.example.com".to_string()),
        max_retries: 3,
        timeout_ms: 5000,
        ..Default::default()
    };

    // 2. 创建同步管理器
    let mut sync_manager = SyncManager::new(config, "example_sync.db").await?;

    // 3. 启动同步服务
    sync_manager.start().await?;
    println!("✅ 同步服务已启动");

    // 4. 模拟一些数据操作
    println!("📝 记录数据变更...");
    
    // 创建用户
    sync_manager.record_change(
        "users",
        "user_001",
        SyncRecordType::Create,
        Some(r#"{"name": "张三", "email": "<EMAIL>", "role": "admin"}"#),
    ).await?;

    // 更新用户
    sync_manager.record_change(
        "users",
        "user_001",
        SyncRecordType::Update,
        Some(r#"{"name": "张三", "email": "<EMAIL>", "role": "user", "last_login": "2023-12-01T10:30:00Z"}"#),
    ).await?;

    // 创建文档
    sync_manager.record_change(
        "documents",
        "doc_001",
        SyncRecordType::Create,
        Some(r#"{"title": "重要文档", "content": "这是一个重要的文档内容", "author": "user_001"}"#),
    ).await?;

    // 5. 等待一段时间让变更被处理
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // 6. 获取同步状态
    let status = sync_manager.get_status().await?;
    println!("📊 同步状态:");
    println!("  - 正在同步: {}", status.is_syncing);
    println!("  - 待同步记录数: {}", status.pending_records);
    println!("  - 最后同步时间: {:?}", status.last_sync_time);
    println!("  - NTP时间偏移: {:?} 毫秒", status.ntp_offset_ms);
    println!("  - 服务端连接状态: {}", status.server_connected);

    // 7. 手动触发同步（在实际环境中，这会与服务端通信）
    println!("🔄 触发手动同步...");
    match sync_manager.trigger_sync().await {
        Ok(result) => {
            println!("✅ 同步完成:");
            println!("  - 成功: {}", result.success);
            println!("  - 同步记录数: {}", result.synced_count);
            println!("  - 冲突记录数: {}", result.conflict_count);
            if let Some(error) = result.error {
                println!("  - 错误: {}", error);
            }
        }
        Err(e) => {
            println!("❌ 同步失败: {}", e);
        }
    }

    // 8. 添加变更监听器
    let tracker = sync_manager.get_log_tracker();
    tracker.add_listener("example_listener", |event| {
        println!("🔔 数据变更通知: 表={}, 记录ID={}, 操作={:?}", 
                 event.table_name, event.record_id, event.operation_type);
    });

    // 9. 再记录一些变更来测试监听器
    sync_manager.record_change(
        "settings",
        "app_config",
        SyncRecordType::Update,
        Some(r#"{"theme": "dark", "language": "zh-CN", "auto_sync": true}"#),
    ).await?;

    // 等待监听器被触发
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 10. 停止同步服务
    sync_manager.stop().await?;
    println!("🛑 同步服务已停止");

    Ok(())
}

/// 示例：在Tauri应用中集成同步系统
#[cfg(feature = "example")]
pub fn tauri_integration_example() {
    use crate::sync::sync_commands::add_sync_commands;
    
    // 这是一个示例，展示如何在Tauri应用中集成同步系统
    let _app_builder = tauri::Builder::default()
        .setup(|app| {
            // 应用初始化逻辑
            println!("🚀 应用启动中...");
            
            // 这里可以添加其他初始化代码
            // 比如初始化数据库、配置日志等
            
            Ok(())
        })
        // 添加同步命令（目前暂时注释，等修复AppHandle问题后启用）
        .pipe(add_sync_commands);
        
    // 在实际应用中，你会这样运行：
    // app_builder.run(tauri::generate_context!())
    //     .expect("error while running tauri application");
    
    println!("📱 Tauri应用已配置同步功能");
}

/// 示例：错误处理和重试机制
#[cfg(feature = "example")]
pub async fn error_handling_example() -> Result<(), Box<dyn std::error::Error>> {
    use crate::sync::SyncError;
    
    let config = SyncConfig {
        enabled: true,
        // 使用一个不存在的服务器来演示错误处理
        sync_server_url: Some("https://nonexistent-server.example.com".to_string()),
        max_retries: 2,
        timeout_ms: 1000, // 短超时时间
        ..Default::default()
    };

    let mut sync_manager = SyncManager::new(config, "error_example_sync.db").await?;
    sync_manager.start().await?;

    // 记录一些数据
    sync_manager.record_change(
        "test_table",
        "test_record",
        SyncRecordType::Create,
        Some(r#"{"test": "data"}"#),
    ).await?;

    // 尝试同步，这会失败因为服务器不存在
    match sync_manager.trigger_sync().await {
        Ok(result) => {
            println!("意外成功: {:?}", result);
        }
        Err(SyncError::NetworkError(msg)) => {
            println!("🌐 网络错误（预期的）: {}", msg);
            println!("💡 建议：检查网络连接或服务器配置");
        }
        Err(SyncError::ConfigError(msg)) => {
            println!("⚙️ 配置错误: {}", msg);
            println!("💡 建议：检查同步配置");
        }
        Err(e) => {
            println!("❌ 其他错误: {}", e);
        }
    }

    sync_manager.stop().await?;
    Ok(())
}

/// 示例：性能监控和统计
#[cfg(feature = "example")]
pub async fn monitoring_example() -> Result<(), Box<dyn std::error::Error>> {
    let config = SyncConfig {
        enabled: true,
        sync_interval: 60, // 1分钟同步间隔
        ..Default::default()
    };

    let mut sync_manager = SyncManager::new(config, "monitoring_sync.db").await?;
    sync_manager.start().await?;

    // 模拟大量数据操作
    println!("📊 生成测试数据...");
    for i in 0..100 {
        sync_manager.record_change(
            "performance_test",
            &format!("record_{:03}", i),
            if i % 3 == 0 { SyncRecordType::Create } else { SyncRecordType::Update },
            Some(&format!(r#"{{"id": {}, "data": "test_data_{}", "timestamp": "{}"}}"#, 
                         i, i, chrono::Utc::now().to_rfc3339())),
        ).await?;
    }

    // 等待处理
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

    // 获取统计信息
    let status = sync_manager.get_status().await?;
    println!("📈 性能统计:");
    println!("  - 待同步记录数: {}", status.pending_records);
    
    let storage = sync_manager.get_storage();
    let pending_count = storage.get_pending_count().await?;
    println!("  - 存储层待同步记录数: {}", pending_count);

    // 强制NTP同步
    let tracker = sync_manager.get_log_tracker();
    match tracker.force_ntp_sync().await {
        Ok(offset) => {
            println!("  - NTP时间偏移: {} 毫秒", offset);
        }
        Err(e) => {
            println!("  - NTP同步失败: {}", e);
        }
    }

    sync_manager.stop().await?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[cfg(feature = "example")]
    async fn test_basic_sync_example() {
        // 这个测试只检查示例代码是否能编译和运行
        // 在实际环境中可能会因为网络问题而失败
        let result = basic_sync_example().await;
        // 我们不检查结果，因为可能会有网络错误
        println!("基本同步示例结果: {:?}", result);
    }

    #[tokio::test]
    #[cfg(feature = "example")]
    async fn test_error_handling_example() {
        let result = error_handling_example().await;
        // 错误处理示例应该能正常运行
        assert!(result.is_ok());
    }

    #[tokio::test]
    #[cfg(feature = "example")]
    async fn test_monitoring_example() {
        let result = monitoring_example().await;
        // 监控示例应该能正常运行
        assert!(result.is_ok());
    }
} 