//! 纯向量时钟同步系统演示程序
//!
//! 展示不依赖物理时间戳的纯向量时钟同步系统的功能：
//! 1. 基于向量时钟的因果关系检测
//! 2. 并发冲突的智能解决
//! 3. NTP时间戳辅助决策
//! 4. 三路合并智能冲突解决
//! 5. 多种冲突解决策略

use chrono::{DateTime, Utc};
use crate::sync_backup::{
    ConflictResolutionStrategy, NtpTimestamp, PureVectorClockConflictResolver,
    PureVectorSyncRecord, SyncRecordType, VectorClock,
};
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();

    println!("🚀 纯向量时钟同步系统演示");
    println!("=====================================\n");

    // 演示1: 基础向量时钟因果关系检测
    demo_causal_relationship().await?;

    // 演示2: 并发冲突检测和解决
    demo_concurrent_conflict_resolution().await?;

    // 演示3: NTP辅助智能合并
    demo_ntp_assisted_resolution().await?;

    // 演示4: 完整策略演示（向量时钟 + NTP + 三路合并）
    demo_full_strategy().await?;

    // 演示5: 不同冲突解决策略对比
    demo_strategy_comparison().await?;

    println!("\n✅ 所有演示完成！");
    Ok(())
}

/// 演示基础向量时钟因果关系检测
async fn demo_causal_relationship() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 演示1: 基础向量时钟因果关系检测");
    println!("-----------------------------------");

    // 创建设备A的记录
    let mut clock_a = VectorClock::new();
    clock_a.tick("device_a");

    let record_a = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "gmail_001".to_string(),
        SyncRecordType::Create,
        Some(r#"{"username": "<EMAIL>", "password": "secret123"}"#.to_string()),
        clock_a.clone(),
        "device_a".to_string(),
    );

    // 创建设备B的记录（基于A的时钟）
    let mut clock_b = VectorClock::new();
    clock_b.update(&clock_a, "device_b");

    let record_b = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "gmail_001".to_string(),
        SyncRecordType::Update,
        Some(r#"{"username": "<EMAIL>", "password": "new_secret456"}"#.to_string()),
        clock_b.clone(),
        "device_b".to_string(),
    );

    println!("设备A记录: {:?}", record_a.vector_clock);
    println!("设备B记录: {:?}", record_b.vector_clock);

    // 检测因果关系
    if record_a.happens_before(&record_b) {
        println!("✅ 检测到因果关系: A发生在B之前");
    } else if record_b.happens_before(&record_a) {
        println!("✅ 检测到因果关系: B发生在A之前");
    } else if record_a.is_concurrent_with(&record_b) {
        println!("⚠️  检测到并发冲突: A和B同时发生");
    }

    // 使用纯向量时钟解决器
    let resolver =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockOnly);
    let resolution = resolver
        .resolve_conflict(&record_a, &record_b, None)
        .await?;

    println!("解决方案: {}", resolution.resolution_method);
    println!("置信度: {:.2}", resolution.confidence);
    if let Some(resolved) = &resolution.resolved_record {
        println!("选择的记录来自: {}", resolved.device_id);
    }

    println!();
    Ok(())
}

/// 演示并发冲突检测和解决
async fn demo_concurrent_conflict_resolution() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 演示2: 并发冲突检测和解决");
    println!("----------------------------");

    // 创建两个并发的记录
    let mut clock_device1 = VectorClock::new();
    let mut clock_device2 = VectorClock::new();

    clock_device1.tick("device_1");
    clock_device2.tick("device_2");

    let record_device1 = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "github_001".to_string(),
        SyncRecordType::Update,
        Some(
            r#"{"username": "developer", "password": "github_secret_v1", "tags": ["work", "dev"]}"#
                .to_string(),
        ),
        clock_device1,
        "device_1".to_string(),
    );

    let record_device2 = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "github_001".to_string(),
        SyncRecordType::Update,
        Some(r#"{"username": "developer", "password": "github_secret_v2", "tags": ["work", "coding"]}"#.to_string()),
        clock_device2,
        "device_2".to_string(),
    );

    println!("设备1记录: {:?}", record_device1.vector_clock);
    println!("设备2记录: {:?}", record_device2.vector_clock);
    println!(
        "并发检测: {}",
        record_device1.is_concurrent_with(&record_device2)
    );

    // 使用向量时钟tie-breaker策略
    let resolver =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockOnly);
    let resolution = resolver
        .resolve_conflict(&record_device1, &record_device2, None)
        .await?;

    println!("解决方案: {}", resolution.resolution_method);
    println!("置信度: {:.2}", resolution.confidence);
    println!("详细信息: {:?}", resolution.details);

    if let Some(resolved) = &resolution.resolved_record {
        println!("选择的记录来自: {}", resolved.device_id);
        if let Some(data) = &resolved.data {
            println!("最终数据: {}", data);
        }
    }

    println!();
    Ok(())
}

/// 演示NTP辅助智能合并
async fn demo_ntp_assisted_resolution() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 演示3: NTP辅助智能合并");
    println!("------------------------");

    // 创建两个并发记录，但带有不同质量的NTP时间戳
    let mut clock1 = VectorClock::new();
    let mut clock2 = VectorClock::new();

    clock1.tick("mobile_device");
    clock2.tick("desktop_device");

    let mut mobile_record = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "bank_001".to_string(),
        SyncRecordType::Update,
        Some(
            r#"{"username": "customer", "password": "mobile_update", "last_login": "mobile"}"#
                .to_string(),
        ),
        clock1,
        "mobile_device".to_string(),
    );

    let mut desktop_record = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "bank_001".to_string(),
        SyncRecordType::Update,
        Some(
            r#"{"username": "customer", "password": "desktop_update", "last_login": "desktop"}"#
                .to_string(),
        ),
        clock2,
        "desktop_device".to_string(),
    );

    // 为移动设备添加低质量NTP时间戳（移动网络不稳定）
    let mobile_ntp = NtpTimestamp::new(
        Utc::now() - chrono::Duration::minutes(1),
        0.6, // 较低置信度
        vec!["mobile.ntp.pool".to_string()],
        1500, // 较高延迟
    );
    mobile_record.set_ntp_timestamp(mobile_ntp);

    // 为桌面设备添加高质量NTP时间戳（有线网络稳定）
    let desktop_ntp = NtpTimestamp::new(
        Utc::now(),
        0.95, // 高置信度
        vec![
            "time.google.com".to_string(),
            "time.cloudflare.com".to_string(),
        ],
        30, // 低延迟
    );
    desktop_record.set_ntp_timestamp(desktop_ntp);

    println!(
        "移动设备NTP质量: 置信度={:.2}, 延迟={}ms",
        mobile_record.ntp_timestamp.as_ref().unwrap().confidence,
        mobile_record.ntp_timestamp.as_ref().unwrap().deviation_ms
    );
    println!(
        "桌面设备NTP质量: 置信度={:.2}, 延迟={}ms",
        desktop_record.ntp_timestamp.as_ref().unwrap().confidence,
        desktop_record.ntp_timestamp.as_ref().unwrap().deviation_ms
    );

    // 使用NTP辅助策略
    let resolver =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockWithNtpAssist);
    let resolution = resolver
        .resolve_conflict(&mobile_record, &desktop_record, None)
        .await?;

    println!("解决方案: {}", resolution.resolution_method);
    println!("置信度: {:.2}", resolution.confidence);

    if let Some(resolved) = &resolution.resolved_record {
        println!("选择的记录来自: {} (基于NTP质量)", resolved.device_id);
        if let Some(data) = &resolved.data {
            println!("最终数据: {}", data);
        }
    }

    println!();
    Ok(())
}

/// 演示完整策略（向量时钟 + NTP + 三路合并）
async fn demo_full_strategy() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 演示4: 完整策略演示");
    println!("--------------------");

    // 创建基础版本
    let mut base_clock = VectorClock::new();
    base_clock.tick("server");

    let base_record = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "company_001".to_string(),
        SyncRecordType::Create,
        Some(r#"{"username": "employee", "password": "company123", "department": "IT", "access_level": "basic"}"#.to_string()),
        base_clock.clone(),
        "server".to_string(),
    );

    // 创建本地修改（基于base）
    let mut local_clock = base_clock.clone();
    local_clock.tick("laptop");

    let local_record = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "company_001".to_string(),
        SyncRecordType::Update,
        Some(r#"{"username": "employee", "password": "new_company456", "department": "IT", "access_level": "basic"}"#.to_string()),
        local_clock,
        "laptop".to_string(),
    );

    // 创建远程修改（基于base）
    let mut remote_clock = base_clock.clone();
    remote_clock.tick("workstation");

    let remote_record = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "company_001".to_string(),
        SyncRecordType::Update,
        Some(r#"{"username": "employee", "password": "company123", "department": "Engineering", "access_level": "advanced"}"#.to_string()),
        remote_clock,
        "workstation".to_string(),
    );

    println!("基础版本: {:?}", base_record.vector_clock);
    println!("本地版本: {:?} (修改了密码)", local_record.vector_clock);
    println!(
        "远程版本: {:?} (修改了部门和权限)",
        remote_record.vector_clock
    );

    // 使用完整策略（包含三路合并）
    let resolver = PureVectorClockConflictResolver::new(ConflictResolutionStrategy::Full);
    let resolution = resolver
        .resolve_conflict(&local_record, &remote_record, Some(&base_record))
        .await?;

    println!("解决方案: {}", resolution.resolution_method);
    println!("置信度: {:.2}", resolution.confidence);

    if let Some(resolved) = &resolution.resolved_record {
        println!("合并后的向量时钟: {:?}", resolved.vector_clock);
        if let Some(data) = &resolved.data {
            println!("智能合并结果: {}", data);
            println!("✅ 成功合并了密码更新和部门/权限更新");
        }
    }

    println!();
    Ok(())
}

/// 演示不同冲突解决策略对比
async fn demo_strategy_comparison() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 演示5: 不同冲突解决策略对比");
    println!("------------------------------");

    // 创建相同的冲突场景
    let mut clock1 = VectorClock::new();
    let mut clock2 = VectorClock::new();

    clock1.tick("device_alpha");
    clock2.tick("device_beta");

    let record1 = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "test_conflict".to_string(),
        SyncRecordType::Update,
        Some(r#"{"password": "alpha_version", "notes": "updated from alpha"}"#.to_string()),
        clock1,
        "device_alpha".to_string(),
    );

    let record2 = PureVectorSyncRecord::new(
        "passwords".to_string(),
        "test_conflict".to_string(),
        SyncRecordType::Update,
        Some(r#"{"password": "beta_version", "notes": "updated from beta"}"#.to_string()),
        clock2,
        "device_beta".to_string(),
    );

    println!("冲突场景: 两个设备同时修改同一记录");
    println!("设备Alpha数据: {:?}", record1.data);
    println!("设备Beta数据: {:?}", record2.data);
    println!();

    // 策略1: 仅向量时钟
    println!("策略1: 仅向量时钟");
    let resolver1 =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockOnly);
    let result1 = resolver1.resolve_conflict(&record1, &record2, None).await?;
    println!("  方法: {}", result1.resolution_method);
    println!("  置信度: {:.2}", result1.confidence);
    if let Some(resolved) = &result1.resolved_record {
        println!("  选择: {}", resolved.device_id);
    }
    println!();

    // 策略2: 向量时钟 + NTP辅助
    println!("策略2: 向量时钟 + NTP辅助");
    let resolver2 =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockWithNtpAssist);
    let result2 = resolver2.resolve_conflict(&record1, &record2, None).await?;
    println!("  方法: {}", result2.resolution_method);
    println!("  置信度: {:.2}", result2.confidence);
    if let Some(resolved) = &result2.resolved_record {
        println!("  选择: {}", resolved.device_id);
    }
    println!();

    // 策略3: 向量时钟 + 三路合并
    println!("策略3: 向量时钟 + 三路合并");
    let resolver3 =
        PureVectorClockConflictResolver::new(ConflictResolutionStrategy::VectorClockWithSmartMerge);
    let result3 = resolver3.resolve_conflict(&record1, &record2, None).await?;
    println!("  方法: {}", result3.resolution_method);
    println!("  置信度: {:.2}", result3.confidence);
    if let Some(resolved) = &result3.resolved_record {
        println!("  选择: {}", resolved.device_id);
    }
    println!();

    // 策略4: 完整策略
    println!("策略4: 完整策略 (向量时钟 + NTP + 三路合并)");
    let resolver4 = PureVectorClockConflictResolver::new(ConflictResolutionStrategy::Full);
    let result4 = resolver4.resolve_conflict(&record1, &record2, None).await?;
    println!("  方法: {}", result4.resolution_method);
    println!("  置信度: {:.2}", result4.confidence);
    if let Some(resolved) = &result4.resolved_record {
        println!("  选择: {}", resolved.device_id);
    }

    println!("\n📊 策略对比总结:");
    println!("- 仅向量时钟: 快速但置信度较低");
    println!("- NTP辅助: 依赖网络质量，适合在线场景");
    println!("- 三路合并: 智能合并，适合复杂数据结构");
    println!("- 完整策略: 最高置信度，适合关键数据");

    Ok(())
}

/// 创建测试用的NTP时间戳
fn create_test_ntp_timestamp(
    confidence: f64,
    deviation_ms: i64,
    servers: Vec<String>,
) -> NtpTimestamp {
    NtpTimestamp::new(Utc::now(), confidence, servers, deviation_ms)
}
