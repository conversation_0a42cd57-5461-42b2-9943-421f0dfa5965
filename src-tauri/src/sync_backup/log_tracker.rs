//! 日志变更追踪模块
//! 
//! 负责监听和记录数据变更，为同步系统提供变更日志，支持向量时钟

use crate::sync::{
    calculate_data_hash, generate_version, ntp_client, SyncConfig, SyncError, SyncRecord,
    SyncRecordType, VectorClock, VectorSyncRecord,
    sync_storage::SyncStorage,
};
use chrono::{DateTime, Utc};
use dashmap::DashMap;
use parking_lot::RwLock;
use std::sync::Arc;
use tokio::sync::mpsc;
use uuid::Uuid;

/// 变更事件
#[derive(Debug, Clone)]
pub struct ChangeEvent {
    /// 表名
    pub table_name: String,
    /// 记录ID
    pub record_id: String,
    /// 操作类型
    pub operation_type: SyncRecordType,
    /// 数据内容（JSON格式）
    pub data: Option<String>,
    /// 事件时间戳
    pub timestamp: DateTime<Utc>,
    /// 是否使用向量时钟
    pub use_vector_clock: bool,
}

/// 变更监听器
pub type ChangeListener = Box<dyn Fn(ChangeEvent) + Send + Sync>;

/// 日志追踪器
pub struct LogTracker {
    /// 同步配置
    config: Arc<RwLock<SyncConfig>>,
    /// 同步存储
    storage: Arc<SyncStorage>,
    /// 变更事件发送器
    change_sender: mpsc::UnboundedSender<ChangeEvent>,
    /// 变更事件接收器
    change_receiver: Arc<tokio::sync::Mutex<mpsc::UnboundedReceiver<ChangeEvent>>>,
    /// 监听器映射
    listeners: Arc<DashMap<String, ChangeListener>>,
    /// 是否正在运行
    is_running: Arc<parking_lot::RwLock<bool>>,
    /// NTP时间偏移缓存
    ntp_offset_cache: Arc<parking_lot::RwLock<Option<(DateTime<Utc>, i64)>>>,
    /// 向量时钟管理器
    vector_clock_manager: Arc<VectorClockManager>,
}

/// 向量时钟管理器
pub struct VectorClockManager {
    /// 当前设备的向量时钟
    current_clock: Arc<parking_lot::RwLock<VectorClock>>,
    /// 设备ID
    device_id: String,
    /// 是否启用向量时钟
    enabled: Arc<parking_lot::RwLock<bool>>,
}

impl VectorClockManager {
    /// 创建新的向量时钟管理器
    pub fn new(device_id: String, enabled: bool) -> Self {
        Self {
            current_clock: Arc::new(parking_lot::RwLock::new(VectorClock::new())),
            device_id,
            enabled: Arc::new(parking_lot::RwLock::new(enabled)),
        }
    }

    /// 本地事件发生时递增时钟
    pub fn tick(&self) -> VectorClock {
        if !*self.enabled.read() {
            return VectorClock::new();
        }

        let mut clock = self.current_clock.write();
        clock.tick(&self.device_id);
        clock.clone()
    }

    /// 接收远程事件时更新时钟
    pub fn update(&self, other_clock: &VectorClock) -> VectorClock {
        if !*self.enabled.read() {
            return VectorClock::new();
        }

        let mut clock = self.current_clock.write();
        clock.update(other_clock, &self.device_id);
        clock.clone()
    }

    /// 获取当前时钟的副本
    pub fn get_current_clock(&self) -> VectorClock {
        self.current_clock.read().clone()
    }

    /// 设置启用状态
    pub fn set_enabled(&self, enabled: bool) {
        *self.enabled.write() = enabled;
    }

    /// 检查是否启用
    pub fn is_enabled(&self) -> bool {
        *self.enabled.read()
    }

    /// 压缩向量时钟
    pub fn compress(&self, active_devices: &std::collections::HashSet<String>) {
        if *self.enabled.read() {
            let mut clock = self.current_clock.write();
            clock.compress(active_devices);
        }
    }

    /// 重置向量时钟
    pub fn reset(&self) {
        let mut clock = self.current_clock.write();
        *clock = VectorClock::new();
    }
}

impl LogTracker {
    /// 创建新的日志追踪器
    pub fn new(config: SyncConfig, storage: Arc<SyncStorage>) -> Self {
        let (change_sender, change_receiver) = mpsc::unbounded_channel();
        let vector_clock_manager = Arc::new(VectorClockManager::new(
            config.device_id.clone(),
            config.enabled, // 默认跟随同步配置
        ));

        Self {
            config: Arc::new(RwLock::new(config)),
            storage,
            change_sender,
            change_receiver: Arc::new(tokio::sync::Mutex::new(change_receiver)),
            listeners: Arc::new(DashMap::new()),
            is_running: Arc::new(parking_lot::RwLock::new(false)),
            ntp_offset_cache: Arc::new(parking_lot::RwLock::new(None)),
            vector_clock_manager,
        }
    }

    /// 启动日志追踪器
    pub async fn start(&self) -> Result<(), SyncError> {
        {
            let mut running = self.is_running.write();
            if *running {
                return Ok(());
            }
            *running = true;
        }

        log::info!("启动日志追踪器");

        // 启动变更事件处理任务
        let receiver = Arc::clone(&self.change_receiver);
        let storage = Arc::clone(&self.storage);
        let config = Arc::clone(&self.config);
        let ntp_offset_cache = Arc::clone(&self.ntp_offset_cache);
        let vector_clock_manager = Arc::clone(&self.vector_clock_manager);
        let is_running = Arc::clone(&self.is_running);

        tokio::spawn(async move {
            let mut receiver = receiver.lock().await;
            
            while *is_running.read() {
                match receiver.recv().await {
                    Some(event) => {
                        if let Err(e) = Self::process_change_event(
                            &event,
                            &storage,
                            &config,
                            &ntp_offset_cache,
                            &vector_clock_manager,
                        ).await {
                            log::error!("处理变更事件失败: {:?}", e);
                        }
                    }
                    None => {
                        log::warn!("变更事件通道已关闭");
                        break;
                    }
                }
            }
            
            log::info!("日志追踪器事件处理任务已停止");
        });

        // 启动NTP时间同步任务
        self.start_ntp_sync_task().await;

        Ok(())
    }

    /// 停止日志追踪器
    pub async fn stop(&self) -> Result<(), SyncError> {
        {
            let mut running = self.is_running.write();
            *running = false;
        }

        log::info!("日志追踪器已停止");
        Ok(())
    }

    /// 记录数据变更（使用传统时间戳）
    pub async fn record_change(
        &self,
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
    ) -> Result<(), SyncError> {
        self.record_change_with_options(table_name, record_id, operation_type, data, false).await
    }

    /// 记录数据变更（支持向量时钟选项）
    pub async fn record_change_with_options(
        &self,
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
        use_vector_clock: bool,
    ) -> Result<(), SyncError> {
        let event = ChangeEvent {
            table_name: table_name.to_string(),
            record_id: record_id.to_string(),
            operation_type,
            data: data.map(|s| s.to_string()),
            timestamp: Utc::now(),
            use_vector_clock,
        };

        // 发送变更事件
        self.change_sender
            .send(event.clone())
            .map_err(|_| SyncError::Unknown("发送变更事件失败".to_string()))?;

        // 通知监听器
        self.notify_listeners(&event).await;

        Ok(())
    }

    /// 记录向量时钟变更
    pub async fn record_vector_change(
        &self,
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
    ) -> Result<VectorClock, SyncError> {
        // 递增向量时钟
        let vector_clock = self.vector_clock_manager.tick();
        
        // 记录变更
        self.record_change_with_options(table_name, record_id, operation_type, data, true).await?;
        
        Ok(vector_clock)
    }

    /// 更新向量时钟（接收远程事件时）
    pub async fn update_vector_clock(&self, other_clock: &VectorClock) -> VectorClock {
        self.vector_clock_manager.update(other_clock)
    }

    /// 获取当前向量时钟
    pub fn get_current_vector_clock(&self) -> VectorClock {
        self.vector_clock_manager.get_current_clock()
    }

    /// 设置向量时钟启用状态
    pub fn set_vector_clock_enabled(&self, enabled: bool) {
        self.vector_clock_manager.set_enabled(enabled);
    }

    /// 检查向量时钟是否启用
    pub fn is_vector_clock_enabled(&self) -> bool {
        self.vector_clock_manager.is_enabled()
    }

    /// 压缩向量时钟
    pub fn compress_vector_clock(&self, active_devices: &std::collections::HashSet<String>) {
        self.vector_clock_manager.compress(active_devices);
    }

    /// 重置向量时钟
    pub fn reset_vector_clock(&self) {
        self.vector_clock_manager.reset();
    }

    /// 添加变更监听器
    pub fn add_listener<F>(&self, name: &str, listener: F)
    where
        F: Fn(ChangeEvent) + Send + Sync + 'static,
    {
        self.listeners.insert(name.to_string(), Box::new(listener));
        log::debug!("添加变更监听器: {}", name);
    }

    /// 移除变更监听器
    pub fn remove_listener(&self, name: &str) {
        self.listeners.remove(name);
        log::debug!("移除变更监听器: {}", name);
    }

    /// 获取当前NTP时间偏移
    pub fn get_ntp_offset(&self) -> Option<i64> {
        let cache = self.ntp_offset_cache.read();
        cache.as_ref().map(|(_, offset)| *offset)
    }

    /// 获取校正后的时间戳
    pub fn get_corrected_timestamp(&self) -> DateTime<Utc> {
        let offset = self.get_ntp_offset().unwrap_or(0);
        Utc::now() + chrono::Duration::milliseconds(offset)
    }

    /// 处理变更事件
    async fn process_change_event(
        event: &ChangeEvent,
        storage: &SyncStorage,
        config: &Arc<RwLock<SyncConfig>>,
        ntp_offset_cache: &Arc<parking_lot::RwLock<Option<(DateTime<Utc>, i64)>>>,
        vector_clock_manager: &Arc<VectorClockManager>,
    ) -> Result<(), SyncError> {
        let (enabled, device_id) = {
            let config_guard = config.read();
            (config_guard.enabled, config_guard.device_id.clone())
        };
        
        if !enabled {
            return Ok(());
        }

        // 获取校正后的时间戳
        let corrected_timestamp = {
            let cache = ntp_offset_cache.read();
            if let Some((_, offset)) = cache.as_ref() {
                event.timestamp + chrono::Duration::milliseconds(*offset)
            } else {
                event.timestamp
            }
        };

        // 生成版本号
        let version = generate_version(corrected_timestamp);

        // 计算数据哈希
        let data_hash = event.data.as_ref().map(|data| calculate_data_hash(data));

        if event.use_vector_clock && vector_clock_manager.is_enabled() {
            // 创建向量同步记录
            let base_record = SyncRecord {
                id: Uuid::new_v4(),
                table_name: event.table_name.clone(),
                record_id: event.record_id.clone(),
                operation_type: event.operation_type.clone(),
                data: event.data.clone(),
                local_timestamp: event.timestamp,
                server_timestamp: Some(corrected_timestamp),
                version,
                device_id,
                synced: false,
                retry_count: 0,
                data_hash,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            let vector_record = VectorSyncRecord::new(
                base_record,
                vector_clock_manager.get_current_clock(),
            );

            // 保存向量同步记录
            storage.insert_vector_record(&vector_record).await?;

            log::debug!(
                "记录向量变更: 表={}, 记录ID={}, 操作={:?}, 版本={}, 向量时钟={:?}",
                event.table_name,
                event.record_id,
                event.operation_type,
                version,
                vector_record.vector_clock
            );
        } else {
            // 创建传统同步记录
            let sync_record = SyncRecord {
                id: Uuid::new_v4(),
                table_name: event.table_name.clone(),
                record_id: event.record_id.clone(),
                operation_type: event.operation_type.clone(),
                data: event.data.clone(),
                local_timestamp: event.timestamp,
                server_timestamp: Some(corrected_timestamp),
                version,
                device_id,
                synced: false,
                retry_count: 0,
                data_hash,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            // 保存传统同步记录
            storage.insert_record(&sync_record).await?;

            log::debug!(
                "记录变更: 表={}, 记录ID={}, 操作={:?}, 版本={}",
                event.table_name,
                event.record_id,
                event.operation_type,
                version
            );
        }

        Ok(())
    }

    /// 通知所有监听器
    async fn notify_listeners(&self, event: &ChangeEvent) {
        for entry in self.listeners.iter() {
            let listener = entry.value();
            listener(event.clone());
        }
    }

    /// 启动NTP时间同步任务
    async fn start_ntp_sync_task(&self) {
        let config = Arc::clone(&self.config);
        let ntp_offset_cache = Arc::clone(&self.ntp_offset_cache);
        let is_running = Arc::clone(&self.is_running);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 5分钟同步一次

            while *is_running.read() {
                interval.tick().await;

                let (enabled, ntp_servers) = {
                    let config_guard = config.read();
                    (config_guard.enabled, config_guard.ntp_servers.clone())
                };
                
                if !enabled {
                    continue;
                }

                // 获取NTP时间偏移
                match ntp_client::get_time_offset(&ntp_servers).await {
                    Ok(offset) => {
                        let mut cache = ntp_offset_cache.write();
                        *cache = Some((Utc::now(), offset));
                        log::debug!("NTP时间偏移更新: {} 毫秒", offset);
                    }
                    Err(e) => {
                        log::warn!("NTP时间同步失败: {:?}", e);
                    }
                }
            }

            log::info!("NTP时间同步任务已停止");
        });
    }

    /// 更新配置
    pub fn update_config(&self, new_config: SyncConfig) {
        let mut config = self.config.write();
        
        // 更新向量时钟管理器的启用状态
        self.vector_clock_manager.set_enabled(new_config.enabled);
        
        *config = new_config;
        log::info!("日志追踪器配置已更新");
    }

    /// 获取配置副本
    pub fn get_config(&self) -> SyncConfig {
        self.config.read().clone()
    }

    /// 检查是否正在运行
    pub fn is_running(&self) -> bool {
        *self.is_running.read()
    }

    /// 获取待处理事件数量
    pub fn get_pending_events_count(&self) -> usize {
        // 这里返回一个估计值，实际实现可能需要更复杂的统计
        0
    }

    /// 强制NTP时间同步
    pub async fn force_ntp_sync(&self) -> Result<i64, SyncError> {
        let ntp_servers = {
            let config_guard = self.config.read();
            config_guard.ntp_servers.clone()
        };

        let offset = ntp_client::get_time_offset(&ntp_servers).await?;
        
        {
            let mut cache = self.ntp_offset_cache.write();
            *cache = Some((Utc::now(), offset));
        }

        log::info!("强制NTP时间同步完成，偏移: {} 毫秒", offset);
        Ok(offset)
    }

    /// 获取向量时钟管理器的引用（用于测试）
    #[cfg(test)]
    pub fn get_vector_clock_manager(&self) -> &VectorClockManager {
        &self.vector_clock_manager
    }
}

/// 便捷函数：创建变更监听器
pub fn create_change_listener<F>(callback: F) -> ChangeListener
where
    F: Fn(ChangeEvent) + Send + Sync + 'static,
{
    Box::new(callback)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sync::sync_storage::SyncStorage;
    use tempfile::NamedTempFile;

    async fn create_test_tracker() -> LogTracker {
        let temp_file = NamedTempFile::new().unwrap();
        let temp_path = temp_file.path().to_path_buf();
        // 确保文件被删除，这样SQLite可以创建新的数据库
        drop(temp_file);
        let storage = Arc::new(SyncStorage::new(temp_path).await.unwrap());
        let config = SyncConfig {
            enabled: true,
            ..Default::default()
        };
        LogTracker::new(config, storage)
    }

    #[tokio::test]
    async fn test_tracker_creation() {
        let tracker = create_test_tracker().await;
        assert!(!tracker.is_running());
        // 向量时钟默认跟随同步配置的enabled状态，这里是true
        assert!(tracker.is_vector_clock_enabled());
    }

    #[tokio::test]
    async fn test_start_stop_tracker() {
        let tracker = create_test_tracker().await;
        
        tracker.start().await.unwrap();
        assert!(tracker.is_running());
        
        tracker.stop().await.unwrap();
        assert!(!tracker.is_running());
    }

    #[tokio::test]
    async fn test_record_change() {
        let tracker = create_test_tracker().await;
        tracker.start().await.unwrap();

        // 记录传统变更
        tracker
            .record_change(
                "test_table",
                "test_record_1",
                SyncRecordType::Create,
                Some(r#"{"name": "test"}"#),
            )
            .await
            .unwrap();

        // 等待事件处理
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        tracker.stop().await.unwrap();
    }

    #[tokio::test]
    async fn test_vector_clock_operations() {
        let tracker = create_test_tracker().await;
        
        // 启用向量时钟
        tracker.set_vector_clock_enabled(true);
        assert!(tracker.is_vector_clock_enabled());
        
        // 获取初始时钟
        let initial_clock = tracker.get_current_vector_clock();
        assert!(initial_clock.is_empty());
        
        // 记录向量变更
        let vector_clock = tracker
            .record_vector_change(
                "test_table",
                "test_record_1",
                SyncRecordType::Create,
                Some(r#"{"name": "test"}"#),
            )
            .await
            .unwrap();
        
        // 验证时钟递增
        assert_eq!(vector_clock.get_clock(&tracker.get_config().device_id), 1);
        
        // 更新向量时钟
        let mut other_clock = VectorClock::new();
        other_clock.tick("other_device");
        
        let updated_clock = tracker.update_vector_clock(&other_clock).await;
        assert_eq!(updated_clock.get_clock(&tracker.get_config().device_id), 2);
        assert_eq!(updated_clock.get_clock("other_device"), 1);
    }

    #[tokio::test]
    async fn test_vector_clock_manager() {
        let manager = VectorClockManager::new("test_device".to_string(), true);
        
        // 测试tick操作
        let clock1 = manager.tick();
        assert_eq!(clock1.get_clock("test_device"), 1);
        
        let clock2 = manager.tick();
        assert_eq!(clock2.get_clock("test_device"), 2);
        
        // 测试update操作
        let mut other_clock = VectorClock::new();
        other_clock.tick("other_device");
        other_clock.tick("other_device");
        
        let updated_clock = manager.update(&other_clock);
        assert_eq!(updated_clock.get_clock("test_device"), 3);
        assert_eq!(updated_clock.get_clock("other_device"), 2);
        
        // 测试禁用状态
        manager.set_enabled(false);
        let disabled_clock = manager.tick();
        assert!(disabled_clock.is_empty());
    }

    #[tokio::test]
    async fn test_vector_clock_compression() {
        let tracker = create_test_tracker().await;
        tracker.set_vector_clock_enabled(true);
        
        // 创建多设备的向量时钟
        let mut other_clock1 = VectorClock::new();
        other_clock1.tick("device1");
        tracker.update_vector_clock(&other_clock1).await;
        
        let mut other_clock2 = VectorClock::new();
        other_clock2.tick("device2");
        tracker.update_vector_clock(&other_clock2).await;
        
        let mut other_clock3 = VectorClock::new();
        other_clock3.tick("device3");
        tracker.update_vector_clock(&other_clock3).await;
        
        // 压缩向量时钟，只保留活跃设备
        let active_devices: std::collections::HashSet<String> = 
            [tracker.get_config().device_id, "device1".to_string()].iter().cloned().collect();
        
        tracker.compress_vector_clock(&active_devices);
        
        let compressed_clock = tracker.get_current_vector_clock();
        assert!(compressed_clock.get_clock("device1") > 0);
        assert_eq!(compressed_clock.get_clock("device2"), 0);
        assert_eq!(compressed_clock.get_clock("device3"), 0);
    }

    #[tokio::test]
    async fn test_add_remove_listener() {
        let tracker = create_test_tracker().await;
        
        let listener_called = Arc::new(parking_lot::RwLock::new(false));
        let listener_called_clone = Arc::clone(&listener_called);
        
        tracker.add_listener("test_listener", move |_event| {
            let mut called = listener_called_clone.write();
            *called = true;
        });

        // 记录变更以触发监听器
        tracker
            .record_change(
                "test_table",
                "test_record_1",
                SyncRecordType::Create,
                Some(r#"{"name": "test"}"#),
            )
            .await
            .unwrap();

        // 等待监听器被调用
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        
        assert!(*listener_called.read());

        // 移除监听器
        tracker.remove_listener("test_listener");
    }

    #[tokio::test]
    async fn test_config_update() {
        let tracker = create_test_tracker().await;
        
        let mut new_config = tracker.get_config();
        new_config.sync_interval = 600;
        new_config.enabled = false;
        
        tracker.update_config(new_config);
        
        let updated_config = tracker.get_config();
        assert_eq!(updated_config.sync_interval, 600);
        assert!(!updated_config.enabled);
        assert!(!tracker.is_vector_clock_enabled()); // 应该跟随配置
    }

    #[tokio::test]
    async fn test_corrected_timestamp() {
        let tracker = create_test_tracker().await;
        
        // 在没有NTP偏移的情况下，应该返回当前时间
        let timestamp1 = tracker.get_corrected_timestamp();
        let now = Utc::now();
        
        // 时间差应该很小（小于1秒）
        let diff = (timestamp1.timestamp_millis() - now.timestamp_millis()).abs();
        assert!(diff < 1000);
    }

    #[tokio::test]
    async fn test_mixed_record_types() {
        let tracker = create_test_tracker().await;
        tracker.start().await.unwrap();
        tracker.set_vector_clock_enabled(true);

        // 记录传统变更
        tracker
            .record_change(
                "test_table",
                "test_record_1",
                SyncRecordType::Create,
                Some(r#"{"name": "traditional"}"#),
            )
            .await
            .unwrap();

        // 记录向量变更
        tracker
            .record_vector_change(
                "test_table",
                "test_record_2",
                SyncRecordType::Create,
                Some(r#"{"name": "vector"}"#),
            )
            .await
            .unwrap();

        // 等待事件处理
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        tracker.stop().await.unwrap();
    }

    #[tokio::test]
    async fn test_vector_clock_reset() {
        let tracker = create_test_tracker().await;
        tracker.set_vector_clock_enabled(true);
        
        // 递增时钟
        tracker.record_vector_change(
            "test_table",
            "test_record_1",
            SyncRecordType::Create,
            Some(r#"{"name": "test"}"#),
        ).await.unwrap();
        
        let clock_before_reset = tracker.get_current_vector_clock();
        assert!(!clock_before_reset.is_empty());
        
        // 重置时钟
        tracker.reset_vector_clock();
        
        let clock_after_reset = tracker.get_current_vector_clock();
        assert!(clock_after_reset.is_empty());
    }
} 