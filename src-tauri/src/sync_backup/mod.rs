//! 独立模块化的日志同步系统
//! 
//! 本模块提供基于时间戳/版本的多端数据同步功能，支持：
//! - NTP时间同步
//! - 版本控制和冲突检测
//! - 增量同步
//! - 离线支持
//! - 可插拔设计，不干扰现有应用流程
//! - 纯向量时钟同步系统（不依赖物理时间戳）

pub mod ntp_client;
pub mod sync_manager;
pub mod log_tracker;
pub mod conflict_resolver;
pub mod sync_storage;
pub mod sync_commands;
pub mod vector_clock;
pub mod vector_clock_example;
pub mod three_way_merge;
pub mod three_way_merge_tests;
pub mod pure_vector_clock_sync;

#[cfg(feature = "example")]
pub mod example;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use async_trait;

// 重新导出向量时钟相关类型
pub use vector_clock::{VectorClock, CausalRelation, VectorSyncRecord};

// 重新导出纯向量时钟同步系统类型
pub use pure_vector_clock_sync::{
    PureVectorSyncRecord, 
    PureVectorClockConflictResolver, 
    NtpTimestamp, 
    ConflictResolutionStrategy,
    PureConflictResolution,
};

// 重新导出NTP客户端增强功能
pub use ntp_client::{NtpQueryResult, NtpConsensusResult, NtpStatistics};

// 重新导出三路合并功能
pub use three_way_merge::{ThreeWayMerger, MergeConfig, MergeResult, FieldMergeStrategy};

// 重新导出冲突解决器
pub use conflict_resolver::{ConflictResolver, ConflictInfo, ConflictType, ConflictResolverTrait};

/// 同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    /// 是否启用同步功能
    pub enabled: bool,
    /// NTP服务器列表
    pub ntp_servers: Vec<String>,
    /// 同步间隔（秒）
    pub sync_interval: u64,
    /// 服务端同步API地址
    pub sync_server_url: Option<String>,
    /// 设备唯一标识
    pub device_id: String,
    /// 最大重试次数
    pub max_retries: u32,
    /// 连接超时时间（毫秒）
    pub timeout_ms: u64,
    /// 是否启用向量时钟
    pub enable_vector_clock: bool,
    /// 是否启用纯向量时钟同步（不依赖物理时间戳）
    pub enable_pure_vector_clock: bool,
    /// 纯向量时钟冲突解决策略
    pub pure_vector_clock_strategy: Option<ConflictResolutionStrategy>,
    /// 是否启用三路合并
    pub enable_three_way_merge: bool,
    /// 是否启用NTP辅助智能合并
    pub enable_ntp_assist: bool,
}

impl Default for SyncConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            ntp_servers: vec![
                "time.cloudflare.com:123".to_string(),
                "pool.ntp.org:123".to_string(),
                "time.google.com:123".to_string(),
                "time.apple.com:123".to_string(),
                "time.windows.com:123".to_string(),
            ],
            sync_interval: 300, // 5分钟
            sync_server_url: None,
            device_id: machine_uid::get().unwrap_or_else(|_| Uuid::new_v4().to_string()),
            max_retries: 3,
            timeout_ms: 5000,
            enable_vector_clock: true,
            enable_pure_vector_clock: false,
            pure_vector_clock_strategy: Some(ConflictResolutionStrategy::Full),
            enable_three_way_merge: true,
            enable_ntp_assist: true,
        }
    }
}

/// 同步记录类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SyncRecordType {
    /// 创建操作
    Create,
    /// 更新操作
    Update,
    /// 删除操作
    Delete,
    /// 软删除操作
    SoftDelete,
}

/// 同步记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncRecord {
    /// 记录ID
    pub id: Uuid,
    /// 表名
    pub table_name: String,
    /// 记录的主键ID
    pub record_id: String,
    /// 操作类型
    pub operation_type: SyncRecordType,
    /// 数据内容（JSON格式）
    pub data: Option<String>,
    /// 本地时间戳
    pub local_timestamp: DateTime<Utc>,
    /// 服务端时间戳（NTP同步后的时间）
    pub server_timestamp: Option<DateTime<Utc>>,
    /// 版本号
    pub version: i64,
    /// 设备ID
    pub device_id: String,
    /// 是否已同步到服务端
    pub synced: bool,
    /// 同步重试次数
    pub retry_count: i32,
    /// 数据哈希值（用于冲突检测）
    pub data_hash: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 同步状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatus {
    /// 是否正在同步
    pub is_syncing: bool,
    /// 最后同步时间
    pub last_sync_time: Option<DateTime<Utc>>,
    /// 待同步记录数量
    pub pending_records: usize,
    /// 同步错误信息
    pub last_error: Option<String>,
    /// NTP时间偏移（毫秒）
    pub ntp_offset_ms: Option<i64>,
    /// 服务端连接状态
    pub server_connected: bool,
}

/// 冲突解决策略
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConflictResolution {
    /// 使用服务端版本
    UseServer,
    /// 使用本地版本
    UseLocal,
    /// 使用最新版本（基于时间戳）
    UseLatest,
    /// 需要手动解决
    Manual,
}

/// 同步结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncResult {
    /// 是否成功
    pub success: bool,
    /// 同步的记录数量
    pub synced_count: usize,
    /// 冲突的记录数量
    pub conflict_count: usize,
    /// 错误信息
    pub error: Option<String>,
    /// 详细信息
    pub details: HashMap<String, String>,
}

/// 同步错误类型
#[derive(Debug, thiserror::Error)]
pub enum SyncError {
    #[error("NTP同步失败: {0}")]
    NtpError(String),
    
    #[error("数据库错误: {0}")]
    DatabaseError(String),
    
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("序列化错误: {0}")]
    SerializationError(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("冲突错误: {0}")]
    ConflictError(String),
    
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 同步模块的公共接口
#[async_trait::async_trait]
pub trait SyncModule {
    /// 初始化同步模块
    async fn initialize(&mut self, config: SyncConfig) -> Result<(), SyncError>;
    
    /// 启动同步服务
    async fn start(&mut self) -> Result<(), SyncError>;
    
    /// 停止同步服务
    async fn stop(&mut self) -> Result<(), SyncError>;
    
    /// 获取同步状态
    async fn get_status(&self) -> Result<SyncStatus, SyncError>;
    
    /// 手动触发同步
    async fn trigger_sync(&mut self) -> Result<SyncResult, SyncError>;
    
    /// 记录数据变更
    async fn record_change(
        &mut self,
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
    ) -> Result<(), SyncError>;
}

/// 获取当前NTP同步时间
pub async fn get_ntp_time(servers: &[String]) -> Result<DateTime<Utc>, SyncError> {
    ntp_client::get_ntp_time(servers).await
}

/// 计算数据哈希值
pub fn calculate_data_hash(data: &str) -> String {
    use sha2::{Digest, Sha256};
    let mut hasher = Sha256::new();
    hasher.update(data.as_bytes());
    hex::encode(hasher.finalize())
}

/// 生成版本号（基于时间戳）
pub fn generate_version(timestamp: DateTime<Utc>) -> i64 {
    timestamp.timestamp_millis()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sync_config_default() {
        let config = SyncConfig::default();
        assert!(!config.enabled);
        assert!(!config.ntp_servers.is_empty());
        assert_eq!(config.sync_interval, 300);
    }

    #[test]
    fn test_calculate_data_hash() {
        let data = r#"{"id": "123", "name": "test"}"#;
        let hash1 = calculate_data_hash(data);
        let hash2 = calculate_data_hash(data);
        assert_eq!(hash1, hash2);
        assert_eq!(hash1.len(), 64); // SHA256 hex length
    }

    #[test]
    fn test_generate_version() {
        let now = Utc::now();
        let version = generate_version(now);
        assert!(version > 0);
        assert_eq!(version, now.timestamp_millis());
    }
} 