//! NTP时间同步客户端
//! 
//! 提供与NTP服务器同步时间的功能，确保多端设备使用统一的时间基准
//! 优化版本支持共识时间获取、质量评估和统计分析

use crate::sync::SyncError;
use chrono::{DateTime, Utc};
use std::net::SocketAddr;
use std::time::Duration;
use tokio::time::timeout;
use serde::{Deserialize, Serialize};

/// NTP数据包结构
#[derive(Debug, Clone)]
struct NtpPacket {
    data: [u8; 48],
}

/// NTP查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpQueryResult {
    pub server: String,
    pub timestamp: DateTime<Utc>,
    pub round_trip_delay_ms: i64,
    pub success: bool,
    pub error: Option<String>,
}

/// NTP共识结果
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NtpConsensusResult {
    pub consensus_time: DateTime<Utc>,
    pub confidence: f64,
    pub participating_servers: Vec<String>,
    pub server_results: Vec<NtpQueryResult>,
    pub statistics: NtpStatistics,
}

/// NTP统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpStatistics {
    pub total_servers: usize,
    pub successful_servers: usize,
    pub median_time: DateTime<Utc>,
    pub max_deviation_ms: i64,
    pub average_round_trip_ms: f64,
    pub quality_score: f64,
}

impl NtpPacket {
    /// 创建新的NTP请求包
    fn new() -> Self {
        let mut packet = Self { data: [0; 48] };
        // 设置LI, VN, Mode字段
        // LI = 0 (无闰秒警告), VN = 4 (版本4), Mode = 3 (客户端)
        packet.data[0] = 0x1B; // 00 100 011
        packet
    }

    /// 获取传输时间戳（秒部分）
    fn get_transmit_timestamp_seconds(&self) -> u32 {
        u32::from_be_bytes([
            self.data[40], self.data[41], self.data[42], self.data[43]
        ])
    }

    /// 获取传输时间戳（分数部分）
    fn get_transmit_timestamp_fraction(&self) -> u32 {
        u32::from_be_bytes([
            self.data[44], self.data[45], self.data[46], self.data[47]
        ])
    }

    /// 将NTP时间戳转换为DateTime<Utc>
    fn ntp_to_datetime(&self) -> DateTime<Utc> {
        let seconds = self.get_transmit_timestamp_seconds();
        let fraction = self.get_transmit_timestamp_fraction();
        
        // NTP纪元是1900年1月1日，Unix纪元是1970年1月1日
        // 两者相差70年 = 2208988800秒
        const NTP_UNIX_OFFSET: u64 = 2208988800;
        
        if seconds == 0 {
            // 如果服务器没有返回有效时间戳，使用当前时间
            return Utc::now();
        }
        
        let unix_seconds = seconds as u64 - NTP_UNIX_OFFSET;
        let nanoseconds = ((fraction as u64 * 1_000_000_000) >> 32) as u32;
        
        DateTime::from_timestamp(unix_seconds as i64, nanoseconds)
            .unwrap_or_else(|| Utc::now())
    }
}

/// NTP客户端
pub struct NtpClient {
    servers: Vec<String>,
    timeout_duration: Duration,
    max_retries: u32,
}

impl NtpClient {
    /// 创建新的NTP客户端
    pub fn new(servers: Vec<String>, timeout_ms: u64, max_retries: u32) -> Self {
        Self {
            servers,
            timeout_duration: Duration::from_millis(timeout_ms),
            max_retries,
        }
    }

    /// 从单个NTP服务器获取时间（带性能统计）
    pub async fn query_server(&self, server: &str) -> Result<NtpQueryResult, SyncError> {
        let start_time = std::time::Instant::now();
        
        match self.query_server_internal(server).await {
            Ok(timestamp) => {
                let round_trip_delay = start_time.elapsed().as_millis() as i64;
                Ok(NtpQueryResult {
                    server: server.to_string(),
                    timestamp,
                    round_trip_delay_ms: round_trip_delay,
                    success: true,
                    error: None,
                })
            }
            Err(e) => {
                Ok(NtpQueryResult {
                    server: server.to_string(),
                    timestamp: Utc::now(), // 使用本地时间作为fallback
                    round_trip_delay_ms: start_time.elapsed().as_millis() as i64,
                    success: false,
                    error: Some(e.to_string()),
                })
            }
        }
    }

    /// 内部查询实现
    async fn query_server_internal(&self, server: &str) -> Result<DateTime<Utc>, SyncError> {
        let socket_addr = self.resolve_server(server).await?;
        
        // 使用tokio的异步UDP套接字
        let socket = tokio::net::UdpSocket::bind("0.0.0.0:0")
            .await
            .map_err(|e| SyncError::NetworkError(format!("创建UDP套接字失败: {}", e)))?;

        // 创建NTP请求包
        let request = NtpPacket::new();
        
        // 发送请求
        socket.send_to(&request.data, socket_addr)
            .await
            .map_err(|e| SyncError::NetworkError(format!("发送NTP请求失败: {}", e)))?;

        // 接收响应
        let mut response_data = [0u8; 48];
        let (bytes_received, _) = socket.recv_from(&mut response_data)
            .await
            .map_err(|e| SyncError::NetworkError(format!("接收NTP响应失败: {}", e)))?;

        if bytes_received != 48 {
            return Err(SyncError::NtpError(format!(
                "NTP响应包大小错误: 期望48字节，实际{}字节", 
                bytes_received
            )));
        }

        // 解析响应
        let response = NtpPacket { data: response_data };
        Ok(response.ntp_to_datetime())
    }

    /// 解析服务器地址
    async fn resolve_server(&self, server: &str) -> Result<SocketAddr, SyncError> {
        // 如果已经是IP地址格式，直接解析
        if let Ok(addr) = server.parse::<SocketAddr>() {
            return Ok(addr);
        }

        // 尝试添加默认端口
        let server_with_port = if server.contains(':') {
            server.to_string()
        } else {
            format!("{}:123", server)
        };

        // 使用tokio的DNS解析
        let mut addrs = tokio::net::lookup_host(&server_with_port)
            .await
            .map_err(|e| SyncError::NetworkError(format!("DNS解析失败 {}: {}", server, e)))?;
            
        addrs
            .next()
            .ok_or_else(|| SyncError::NetworkError(format!("无法解析服务器地址: {}", server)))
    }

    /// 从多个服务器获取时间并计算中位数
    pub async fn get_time(&self) -> Result<DateTime<Utc>, SyncError> {
        let consensus_result = self.get_consensus_time().await?;
        Ok(consensus_result.0)
    }

    /// 🎯 **获取多服务器共识时间（核心功能）**
    pub async fn get_consensus_time(&self) -> Result<(DateTime<Utc>, f64), SyncError> {
        let consensus_result = self.get_detailed_consensus().await?;
        Ok((consensus_result.consensus_time, consensus_result.confidence))
    }

    /// 获取详细的共识结果
    pub async fn get_detailed_consensus(&self) -> Result<NtpConsensusResult, SyncError> {
        let mut all_results = Vec::new();
        
        // 并发查询所有服务器
        let mut tasks = Vec::new();
        for server in &self.servers {
            let server_clone = server.clone();
            let client_clone = self.clone();
            tasks.push(tokio::spawn(async move {
                client_clone.query_server_with_retries(&server_clone).await
            }));
        }

        // 等待所有查询完成
        for task in tasks {
            if let Ok(result) = task.await {
                all_results.push(result);
            }
        }

        if all_results.is_empty() {
            return Err(SyncError::NtpError("所有NTP服务器都无法访问".to_string()));
        }

        // 分析结果并计算共识
        self.analyze_consensus_results(all_results).await
    }

    /// 带重试的服务器查询
    async fn query_server_with_retries(&self, server: &str) -> NtpQueryResult {
        for retry in 0..=self.max_retries {
            match timeout(self.timeout_duration, self.query_server(server)).await {
                Ok(Ok(result)) if result.success => {
                    log::debug!("从NTP服务器 {} 获取时间成功: {}", server, result.timestamp);
                    return result;
                }
                Ok(Ok(mut result)) => {
                    // 查询失败，但有结果
                    if retry < self.max_retries {
                        log::warn!("从NTP服务器 {} 获取时间失败 (重试 {}/{}): {:?}", 
                                 server, retry + 1, self.max_retries, result.error);
                        tokio::time::sleep(Duration::from_millis(100 * (retry + 1) as u64)).await;
                        continue;
                    }
                    return result;
                }
                Ok(Err(e)) => {
                    if retry < self.max_retries {
                        log::warn!("从NTP服务器 {} 查询失败 (重试 {}/{}): {}", 
                                 server, retry + 1, self.max_retries, e);
                        tokio::time::sleep(Duration::from_millis(100 * (retry + 1) as u64)).await;
                        continue;
                    }
                    return NtpQueryResult {
                        server: server.to_string(),
                        timestamp: Utc::now(),
                        round_trip_delay_ms: 0,
                        success: false,
                        error: Some(e.to_string()),
                    };
                }
                Err(_) => {
                    if retry < self.max_retries {
                        log::warn!("NTP服务器 {} 请求超时 (重试 {}/{})", 
                                 server, retry + 1, self.max_retries);
                        tokio::time::sleep(Duration::from_millis(100 * (retry + 1) as u64)).await;
                        continue;
                    }
                    return NtpQueryResult {
                        server: server.to_string(),
                        timestamp: Utc::now(),
                        round_trip_delay_ms: self.timeout_duration.as_millis() as i64,
                        success: false,
                        error: Some("请求超时".to_string()),
                    };
                }
            }
        }

        // 不应该到达这里
        NtpQueryResult {
            server: server.to_string(),
            timestamp: Utc::now(),
            round_trip_delay_ms: 0,
            success: false,
            error: Some("未知错误".to_string()),
        }
    }

    /// 分析共识结果
    async fn analyze_consensus_results(&self, results: Vec<NtpQueryResult>) -> Result<NtpConsensusResult, SyncError> {
        let successful_results: Vec<_> = results.iter().filter(|r| r.success).collect();
        
        if successful_results.is_empty() {
            return Err(SyncError::NtpError("没有成功的NTP查询结果".to_string()));
        }

        // 提取成功的时间戳
        let mut timestamps: Vec<DateTime<Utc>> = successful_results.iter()
            .map(|r| r.timestamp)
            .collect();
        timestamps.sort();

        // 计算中位数时间
        let median_time = timestamps[timestamps.len() / 2];

        // 计算时间偏差统计
        let deviations: Vec<i64> = timestamps.iter()
            .map(|t| t.signed_duration_since(median_time).num_milliseconds())
            .collect();

        let max_deviation = deviations.iter().map(|d| d.abs()).max().unwrap_or(0);
        let avg_deviation = deviations.iter().map(|d| d.abs()).sum::<i64>() as f64 / deviations.len() as f64;

        // 计算平均往返延迟
        let avg_round_trip = successful_results.iter()
            .map(|r| r.round_trip_delay_ms as f64)
            .sum::<f64>() / successful_results.len() as f64;

        // 计算置信度
        let confidence = self.calculate_confidence(
            successful_results.len(),
            results.len(),
            max_deviation,
            avg_round_trip,
        );

        // 计算质量评分
        let quality_score = self.calculate_quality_score(
            successful_results.len(),
            max_deviation,
            avg_round_trip,
            avg_deviation,
        );

        let statistics = NtpStatistics {
            total_servers: results.len(),
            successful_servers: successful_results.len(),
            median_time,
            max_deviation_ms: max_deviation,
            average_round_trip_ms: avg_round_trip,
            quality_score,
        };

        let participating_servers = successful_results.iter()
            .map(|r| r.server.clone())
            .collect();

        log::info!(
            "NTP共识时间: {} (置信度: {:.2}, 质量评分: {:.2}, 最大偏差: {}ms, 成功服务器: {}/{})",
            median_time.to_rfc3339(),
            confidence,
            quality_score,
            max_deviation,
            successful_results.len(),
            results.len()
        );

        Ok(NtpConsensusResult {
            consensus_time: median_time,
            confidence,
            participating_servers,
            server_results: results,
            statistics,
        })
    }

    /// 计算置信度
    fn calculate_confidence(&self, successful: usize, total: usize, max_deviation_ms: i64, avg_round_trip_ms: f64) -> f64 {
        // 基础置信度：基于成功率
        let success_rate = successful as f64 / total as f64;
        let base_confidence = success_rate;

        // 时间一致性评分
        let consistency_score = if max_deviation_ms <= 100 {
            1.0
        } else if max_deviation_ms <= 500 {
            0.8
        } else if max_deviation_ms <= 1000 {
            0.6
        } else if max_deviation_ms <= 2000 {
            0.4
        } else {
            0.2
        };

        // 网络质量评分
        let network_score = if avg_round_trip_ms <= 50.0 {
            1.0
        } else if avg_round_trip_ms <= 100.0 {
            0.9
        } else if avg_round_trip_ms <= 200.0 {
            0.8
        } else if avg_round_trip_ms <= 500.0 {
            0.6
        } else {
            0.4
        };

        // 服务器数量评分
        let server_count_score = if successful >= 3 {
            1.0
        } else if successful >= 2 {
            0.8
        } else {
            0.5
        };

        // 综合置信度
        (base_confidence * 0.3 + consistency_score * 0.4 + network_score * 0.2 + server_count_score * 0.1).min(1.0)
    }

    /// 计算质量评分
    fn calculate_quality_score(&self, successful: usize, max_deviation_ms: i64, avg_round_trip_ms: f64, avg_deviation: f64) -> f64 {
        let server_score = (successful as f64 / 5.0).min(1.0); // 最多5个服务器
        let deviation_score = if max_deviation_ms < 100 { 1.0 } else { 100.0 / max_deviation_ms as f64 };
        let latency_score = if avg_round_trip_ms < 100.0 { 1.0 } else { 100.0 / avg_round_trip_ms };
        let consistency_score = if avg_deviation < 50.0 { 1.0 } else { 50.0 / avg_deviation };

        (server_score * 0.25 + deviation_score * 0.35 + latency_score * 0.2 + consistency_score * 0.2).min(1.0)
    }

    /// 计算本地时间与NTP时间的偏移量（毫秒）
    pub async fn get_time_offset(&self) -> Result<i64, SyncError> {
        let local_time = Utc::now();
        let ntp_time = self.get_time().await?;
        
        let offset_ms = ntp_time.timestamp_millis() - local_time.timestamp_millis();
        log::info!("时间偏移量: {} 毫秒", offset_ms);
        
        Ok(offset_ms)
    }

    /// 获取详细的时间偏移分析
    pub async fn get_detailed_time_offset(&self) -> Result<(i64, NtpConsensusResult), SyncError> {
        let local_time = Utc::now();
        let consensus_result = self.get_detailed_consensus().await?;
        
        let offset_ms = consensus_result.consensus_time.timestamp_millis() - local_time.timestamp_millis();
        
        log::info!(
            "详细时间偏移分析: 偏移量={}ms, 置信度={:.2}, 质量评分={:.2}",
            offset_ms,
            consensus_result.confidence,
            consensus_result.statistics.quality_score
        );
        
        Ok((offset_ms, consensus_result))
    }

    /// 检查NTP服务器健康状态
    pub async fn check_server_health(&self) -> Vec<NtpQueryResult> {
        let mut results = Vec::new();
        
        for server in &self.servers {
            let result = self.query_server(server).await.unwrap_or_else(|e| {
                NtpQueryResult {
                    server: server.clone(),
                    timestamp: Utc::now(),
                    round_trip_delay_ms: 0,
                    success: false,
                    error: Some(e.to_string()),
                }
            });
            results.push(result);
        }
        
        results
    }
}

impl Clone for NtpClient {
    fn clone(&self) -> Self {
        Self {
            servers: self.servers.clone(),
            timeout_duration: self.timeout_duration,
            max_retries: self.max_retries,
        }
    }
}

/// 从NTP服务器获取时间（便捷函数）
pub async fn get_ntp_time(servers: &[String]) -> Result<DateTime<Utc>, SyncError> {
    if servers.is_empty() {
        return Err(SyncError::ConfigError("NTP服务器列表为空".to_string()));
    }

    let client = NtpClient::new(servers.to_vec(), 5000, 3);
    client.get_time().await
}

/// 获取时间偏移量（便捷函数）
pub async fn get_time_offset(servers: &[String]) -> Result<i64, SyncError> {
    if servers.is_empty() {
        return Err(SyncError::ConfigError("NTP服务器列表为空".to_string()));
    }

    let client = NtpClient::new(servers.to_vec(), 5000, 3);
    client.get_time_offset().await
}

/// 获取校正后的当前时间
pub async fn get_corrected_time(servers: &[String]) -> Result<DateTime<Utc>, SyncError> {
    let offset = get_time_offset(servers).await?;
    let corrected_time = Utc::now() + chrono::Duration::milliseconds(offset);
    Ok(corrected_time)
}

/// 获取NTP共识时间（便捷函数）
pub async fn get_consensus_time(servers: &[String]) -> Result<(DateTime<Utc>, f64), SyncError> {
    if servers.is_empty() {
        return Err(SyncError::ConfigError("NTP服务器列表为空".to_string()));
    }

    let client = NtpClient::new(servers.to_vec(), 3000, 2);
    client.get_consensus_time().await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_ntp_packet_creation() {
        let packet = NtpPacket::new();
        assert_eq!(packet.data[0], 0x1B);
        assert_eq!(packet.data.len(), 48);
    }

    #[tokio::test]
    async fn test_ntp_client_creation() {
        let servers = vec!["pool.ntp.org".to_string()];
        let client = NtpClient::new(servers.clone(), 5000, 3);
        assert_eq!(client.servers, servers);
        assert_eq!(client.timeout_duration, Duration::from_millis(5000));
        assert_eq!(client.max_retries, 3);
    }

    #[tokio::test]
    async fn test_get_ntp_time_empty_servers() {
        let result = get_ntp_time(&[]).await;
        assert!(result.is_err());
        
        if let Err(SyncError::ConfigError(msg)) = result {
            assert_eq!(msg, "NTP服务器列表为空");
        } else {
            panic!("期望ConfigError");
        }
    }

    #[tokio::test]
    async fn test_confidence_calculation() {
        let client = NtpClient::new(vec!["test.ntp.org".to_string()], 3000, 2);
        
        // 测试高质量场景
        let high_confidence = client.calculate_confidence(3, 3, 50, 30.0);
        assert!(high_confidence > 0.8);
        
        // 测试低质量场景
        let low_confidence = client.calculate_confidence(1, 3, 2000, 500.0);
        assert!(low_confidence < 0.5);
    }

    #[tokio::test]
    async fn test_quality_score_calculation() {
        let client = NtpClient::new(vec!["test.ntp.org".to_string()], 3000, 2);
        
        // 测试高质量评分
        let high_quality = client.calculate_quality_score(3, 50, 30.0, 25.0);
        assert!(high_quality > 0.8);
        
        // 测试低质量评分
        let low_quality = client.calculate_quality_score(1, 2000, 500.0, 1000.0);
        assert!(low_quality < 0.3);
    }

    #[test]
    fn test_ntp_query_result_creation() {
        let result = NtpQueryResult {
            server: "test.ntp.org".to_string(),
            timestamp: Utc::now(),
            round_trip_delay_ms: 50,
            success: true,
            error: None,
        };
        
        assert_eq!(result.server, "test.ntp.org");
        assert!(result.success);
        assert!(result.error.is_none());
        assert_eq!(result.round_trip_delay_ms, 50);
    }

    #[test]
    fn test_ntp_statistics_creation() {
        let stats = NtpStatistics {
            total_servers: 5,
            successful_servers: 3,
            median_time: Utc::now(),
            max_deviation_ms: 100,
            average_round_trip_ms: 50.0,
            quality_score: 0.85,
        };
        
        assert_eq!(stats.total_servers, 5);
        assert_eq!(stats.successful_servers, 3);
        assert_eq!(stats.max_deviation_ms, 100);
        assert_eq!(stats.quality_score, 0.85);
    }

    #[tokio::test]
    #[ignore] // 需要网络连接，在CI中忽略
    async fn test_real_ntp_query() {
        let servers = vec![
            "time.cloudflare.com".to_string(),
            "pool.ntp.org".to_string(),
        ];
        
        let client = NtpClient::new(servers, 5000, 2);
        
        // 测试基本时间获取
        match client.get_time().await {
            Ok(time) => {
                println!("NTP时间: {}", time);
                let now = Utc::now();
                let diff = (time.timestamp_millis() - now.timestamp_millis()).abs();
                assert!(diff < 60000); // 差异应该小于1分钟
            }
            Err(e) => {
                println!("NTP查询失败（可能是网络问题）: {}", e);
            }
        }
        
        // 测试共识时间获取
        match client.get_detailed_consensus().await {
            Ok(consensus) => {
                println!("共识结果: {:?}", consensus);
                assert!(consensus.confidence >= 0.0 && consensus.confidence <= 1.0);
                assert!(consensus.statistics.quality_score >= 0.0 && consensus.statistics.quality_score <= 1.0);
            }
            Err(e) => {
                println!("共识查询失败（可能是网络问题）: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_server_health_check() {
        let servers = vec![
            "invalid.ntp.server".to_string(),
            "another.invalid.server".to_string(),
        ];
        
        let client = NtpClient::new(servers, 1000, 1); // 短超时用于测试
        let health_results = client.check_server_health().await;
        
        assert_eq!(health_results.len(), 2);
        // 由于服务器无效，所有结果都应该失败
        for result in health_results {
            assert!(!result.success);
            assert!(result.error.is_some());
        }
    }
} 