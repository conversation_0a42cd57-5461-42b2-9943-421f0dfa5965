//! 同步系统的Tauri命令接口
//! 
//! 为前端提供同步功能的API，包括配置管理、状态查询、手动同步等

use crate::sync::{
    sync_manager::SyncManager,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;

/// 全局同步管理器状态
pub type GlobalSyncManager = Arc<Mutex<Option<SyncManager>>>;

/// 同步配置请求
#[derive(Debug, Serialize, Deserialize)]
pub struct SyncConfigRequest {
    /// 是否启用同步功能
    pub enabled: bool,
    /// NTP服务器列表
    pub ntp_servers: Option<Vec<String>>,
    /// 同步间隔（秒）
    pub sync_interval: Option<u64>,
    /// 服务端同步API地址
    pub sync_server_url: Option<String>,
    /// 最大重试次数
    pub max_retries: Option<u32>,
    /// 连接超时时间（毫秒）
    pub timeout_ms: Option<u64>,
}

/// 记录变更请求
#[derive(Debug, Serialize, Deserialize)]
pub struct RecordChangeRequest {
    /// 表名
    pub table_name: String,
    /// 记录ID
    pub record_id: String,
    /// 操作类型
    pub operation_type: String, // "create", "update", "delete", "soft_delete"
    /// 数据内容（JSON格式）
    pub data: Option<String>,
}

/// 同步统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct SyncStats {
    /// 总记录数
    pub total_records: usize,
    /// 待同步记录数
    pub pending_records: usize,
    /// 已同步记录数
    pub synced_records: usize,
    /// 冲突记录数
    pub conflict_records: usize,
    /// 最后同步时间
    pub last_sync_time: Option<String>,
    /// NTP时间偏移（毫秒）
    pub ntp_offset_ms: Option<i64>,
}

// 暂时注释掉所有Tauri命令，等修复AppHandle问题后再启用
/*
/// 初始化同步系统
#[tauri::command]
pub async fn init_sync_system(
    app: tauri::AppHandle,
    config_request: SyncConfigRequest,
) -> Result<String, String> {
    // 实现代码...
}

/// 启动同步服务
#[tauri::command]
pub async fn start_sync_service(app: tauri::AppHandle) -> Result<String, String> {
    // 实现代码...
}

/// 停止同步服务
#[tauri::command]
pub async fn stop_sync_service(app: tauri::AppHandle) -> Result<String, String> {
    // 实现代码...
}

/// 获取同步状态
#[tauri::command]
pub async fn get_sync_status(app: tauri::AppHandle) -> Result<SyncStatus, String> {
    // 实现代码...
}

/// 手动触发同步
#[tauri::command]
pub async fn trigger_manual_sync(app: tauri::AppHandle) -> Result<SyncResult, String> {
    // 实现代码...
}

/// 记录数据变更
#[tauri::command]
pub async fn record_data_change(
    app: tauri::AppHandle,
    change_request: RecordChangeRequest,
) -> Result<String, String> {
    // 实现代码...
}

/// 更新同步配置
#[tauri::command]
pub async fn update_sync_config(
    app: tauri::AppHandle,
    config_request: SyncConfigRequest,
) -> Result<String, String> {
    // 实现代码...
}

/// 获取同步统计信息
#[tauri::command]
pub async fn get_sync_stats(app: tauri::AppHandle) -> Result<SyncStats, String> {
    // 实现代码...
}

/// 强制NTP时间同步
#[tauri::command]
pub async fn force_ntp_sync(app: tauri::AppHandle) -> Result<i64, String> {
    // 实现代码...
}

/// 清理旧的同步记录
#[tauri::command]
pub async fn cleanup_old_sync_records(
    app: tauri::AppHandle,
    days_before: u32,
) -> Result<usize, String> {
    // 实现代码...
}

/// 检查同步系统是否已初始化
#[tauri::command]
pub async fn is_sync_system_initialized(app: tauri::AppHandle) -> Result<bool, String> {
    // 实现代码...
}

/// 销毁同步系统
#[tauri::command]
pub async fn destroy_sync_system(app: tauri::AppHandle) -> Result<String, String> {
    // 实现代码...
}
*/

/// 便捷函数：为应用添加同步命令
pub fn add_sync_commands<R: tauri::Runtime>(builder: tauri::Builder<R>) -> tauri::Builder<R> {
    builder
        .manage(GlobalSyncManager::default())
        // 暂时注释掉所有命令，等修复AppHandle问题后再启用
        // .invoke_handler(tauri::generate_handler![
        //     init_sync_system,
        //     start_sync_service,
        //     stop_sync_service,
        //     get_sync_status,
        //     trigger_manual_sync,
        //     record_data_change,
        //     update_sync_config,
        //     get_sync_stats,
        //     force_ntp_sync,
        //     cleanup_old_sync_records,
        //     is_sync_system_initialized,
        //     destroy_sync_system,
        // ])
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sync_config_request_serialization() {
        let config_request = SyncConfigRequest {
            enabled: true,
            ntp_servers: Some(vec!["pool.ntp.org".to_string()]),
            sync_interval: Some(300),
            sync_server_url: Some("https://api.example.com".to_string()),
            max_retries: Some(3),
            timeout_ms: Some(5000),
        };

        let json = serde_json::to_string(&config_request).unwrap();
        let deserialized: SyncConfigRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(deserialized.enabled, config_request.enabled);
        assert_eq!(deserialized.sync_interval, config_request.sync_interval);
    }

    #[test]
    fn test_record_change_request_serialization() {
        let change_request = RecordChangeRequest {
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            operation_type: "create".to_string(),
            data: Some(r#"{"name": "test"}"#.to_string()),
        };

        let json = serde_json::to_string(&change_request).unwrap();
        let deserialized: RecordChangeRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(deserialized.table_name, change_request.table_name);
        assert_eq!(deserialized.operation_type, change_request.operation_type);
    }
} 