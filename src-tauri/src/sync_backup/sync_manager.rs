//! 同步管理器
//! 
//! 作为整个同步系统的核心协调器，负责管理所有同步相关的组件和流程

use crate::sync::{
    conflict_resolver::{ConflictResolver, SmartConflictResolver},
    log_tracker::LogTracker,
    ntp_client,
    sync_storage::SyncStorage,
    SyncConfig, SyncError, SyncModule, SyncRecord, SyncRecordType, SyncResult, SyncStatus,
};
use chrono::{DateTime, Utc};
use parking_lot::RwLock;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::{interval, Duration};

/// 同步管理器
pub struct SyncManager {
    /// 同步配置
    config: Arc<RwLock<SyncConfig>>,
    /// 同步存储
    storage: Arc<SyncStorage>,
    /// 日志追踪器
    log_tracker: Arc<LogTracker>,
    /// 冲突解决器
    conflict_resolver: Arc<Mutex<ConflictResolver>>,
    /// 是否正在运行
    is_running: Arc<RwLock<bool>>,
    /// 同步状态
    sync_status: Arc<RwLock<SyncStatus>>,
    /// HTTP客户端
    http_client: reqwest::Client,
    /// 同步任务句柄
    sync_task_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
}

impl SyncManager {
    /// 创建新的同步管理器
    pub async fn new<P: AsRef<Path>>(
        config: SyncConfig,
        db_path: P,
    ) -> Result<Self, SyncError> {
        // 创建存储
        let storage = Arc::new(SyncStorage::new(db_path).await?);
        
        // 创建日志追踪器
        let log_tracker = Arc::new(LogTracker::new(config.clone(), Arc::clone(&storage)));
        
        // 创建冲突解决器
        let mut conflict_resolver = ConflictResolver::new(crate::sync::ConflictResolution::UseLatest);
        conflict_resolver.add_custom_resolver("smart", SmartConflictResolver);
        
        // 创建HTTP客户端
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_millis(config.timeout_ms))
            .build()
            .map_err(|e| SyncError::NetworkError(format!("创建HTTP客户端失败: {}", e)))?;

        // 初始化同步状态
        let sync_status = SyncStatus {
            is_syncing: false,
            last_sync_time: None,
            pending_records: 0,
            last_error: None,
            ntp_offset_ms: None,
            server_connected: false,
        };

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            storage,
            log_tracker,
            conflict_resolver: Arc::new(Mutex::new(conflict_resolver)),
            is_running: Arc::new(RwLock::new(false)),
            sync_status: Arc::new(RwLock::new(sync_status)),
            http_client,
            sync_task_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 获取日志追踪器的引用
    pub fn get_log_tracker(&self) -> Arc<LogTracker> {
        Arc::clone(&self.log_tracker)
    }

    /// 获取存储的引用
    pub fn get_storage(&self) -> Arc<SyncStorage> {
        Arc::clone(&self.storage)
    }

    /// 检查服务端连接
    async fn check_server_connection(&self) -> bool {
        let server_url = {
            let config = self.config.read();
            config.sync_server_url.clone()
        };
        
        if let Some(server_url) = server_url {
            let health_url = format!("{}/health", server_url);
            match self.http_client.get(&health_url).send().await {
                Ok(response) => response.status().is_success(),
                Err(_) => false,
            }
        } else {
            false
        }
    }

    /// 同步到服务端
    async fn sync_to_server(&self) -> Result<SyncResult, SyncError> {
        let server_url = {
            let config = self.config.read();
            config.sync_server_url.as_ref()
                .ok_or_else(|| SyncError::ConfigError("未配置服务端URL".to_string()))?
                .clone()
        };

        // 获取未同步的记录
        let unsynced_records = self.storage.get_unsynced_records(Some(100)).await?;
        let total_records = unsynced_records.len();
        
        if unsynced_records.is_empty() {
            return Ok(SyncResult {
                success: true,
                synced_count: 0,
                conflict_count: 0,
                error: None,
                details: HashMap::new(),
            });
        }

        let mut synced_count = 0;
        let mut conflict_count = 0;
        let mut details = HashMap::new();

        for record in &unsynced_records {
            match self.sync_single_record(record, &server_url).await {
                Ok(true) => {
                    synced_count += 1;
                    // 标记为已同步
                    let server_time = self.get_server_time().await.unwrap_or_else(|_| Utc::now());
                    if let Err(e) = self.storage.mark_as_synced(&record.id, server_time).await {
                        log::warn!("标记记录为已同步失败: {:?}", e);
                    }
                }
                Ok(false) => {
                    conflict_count += 1;
                    // 增加重试次数
                    if let Err(e) = self.storage.increment_retry_count(&record.id).await {
                        log::warn!("增加重试次数失败: {:?}", e);
                    }
                }
                Err(e) => {
                    log::error!("同步记录失败: {:?}", e);
                    if let Err(e) = self.storage.increment_retry_count(&record.id).await {
                        log::warn!("增加重试次数失败: {:?}", e);
                    }
                }
            }
        }

        details.insert("total_records".to_string(), total_records.to_string());
        details.insert("synced_count".to_string(), synced_count.to_string());
        details.insert("conflict_count".to_string(), conflict_count.to_string());

        Ok(SyncResult {
            success: true,
            synced_count,
            conflict_count,
            error: None,
            details,
        })
    }

    /// 同步单个记录
    async fn sync_single_record(&self, record: &SyncRecord, server_url: &str) -> Result<bool, SyncError> {
        let sync_url = format!("{}/sync/records", server_url);
        
        let response = self.http_client
            .post(&sync_url)
            .json(record)
            .send()
            .await
            .map_err(|e| SyncError::NetworkError(format!("发送同步请求失败: {}", e)))?;

        if response.status().is_success() {
            Ok(true)
        } else if response.status() == reqwest::StatusCode::CONFLICT {
            // 处理冲突
            let server_record: SyncRecord = response
                .json()
                .await
                .map_err(|e| SyncError::SerializationError(format!("解析服务端记录失败: {}", e)))?;
            
            self.handle_conflict(record, &server_record).await?;
            Ok(false)
        } else {
            Err(SyncError::NetworkError(format!(
                "服务端返回错误状态: {}",
                response.status()
            )))
        }
    }

    /// 处理冲突
    async fn handle_conflict(&self, local_record: &SyncRecord, server_record: &SyncRecord) -> Result<(), SyncError> {
        let resolver = self.conflict_resolver.lock().await;
        
        if let Some(conflict) = resolver.detect_conflict(local_record, server_record) {
            log::info!("检测到冲突: {}", conflict.description);
            
            let resolution_result = resolver.resolve_conflict(&conflict)?;
            
            if resolution_result.success {
                if let Some(resolved_record) = resolution_result.resolved_record {
                    // 更新本地记录
                    self.storage.update_record(&resolved_record).await?;
                    log::info!("冲突已自动解决: {:?}", resolution_result.strategy);
                }
            } else {
                log::warn!("冲突需要手动解决: {}", conflict.description);
                // 这里可以发送事件通知前端用户手动解决冲突
            }
        }
        
        Ok(())
    }

    /// 从服务端同步
    async fn sync_from_server(&self) -> Result<SyncResult, SyncError> {
        let (server_url, device_id) = {
            let config = self.config.read();
            let server_url = config.sync_server_url.as_ref()
                .ok_or_else(|| SyncError::ConfigError("未配置服务端URL".to_string()))?
                .clone();
            let device_id = config.device_id.clone();
            (server_url, device_id)
        };

        // 获取最后同步时间
        let last_sync_time = self.sync_status.read().last_sync_time;
        
        let sync_url = if let Some(last_sync) = last_sync_time {
            format!("{}/sync/records?device_id={}&since={}", 
                   server_url, device_id, last_sync.to_rfc3339())
        } else {
            format!("{}/sync/records?device_id={}", server_url, device_id)
        };

        let response = self.http_client
            .get(&sync_url)
            .send()
            .await
            .map_err(|e| SyncError::NetworkError(format!("获取服务端记录失败: {}", e)))?;

        if !response.status().is_success() {
            return Err(SyncError::NetworkError(format!(
                "服务端返回错误状态: {}",
                response.status()
            )));
        }

        let server_records: Vec<SyncRecord> = response
            .json()
            .await
            .map_err(|e| SyncError::SerializationError(format!("解析服务端记录失败: {}", e)))?;

        let mut synced_count = 0;
        let mut conflict_count = 0;

        for server_record in server_records {
            // 检查本地是否有对应记录
            if let Ok(Some(local_record)) = self.storage
                .get_latest_record(&server_record.table_name, &server_record.record_id)
                .await
            {
                // 检查冲突
                let resolver = self.conflict_resolver.lock().await;
                if let Some(_conflict) = resolver.detect_conflict(&local_record, &server_record) {
                    conflict_count += 1;
                    self.handle_conflict(&local_record, &server_record).await?;
                } else {
                    // 无冲突，直接更新
                    self.storage.update_record(&server_record).await?;
                    synced_count += 1;
                }
            } else {
                // 本地没有记录，直接插入
                self.storage.insert_record(&server_record).await?;
                synced_count += 1;
            }
        }

        let mut details = HashMap::new();
        details.insert("synced_count".to_string(), synced_count.to_string());
        details.insert("conflict_count".to_string(), conflict_count.to_string());

        Ok(SyncResult {
            success: true,
            synced_count,
            conflict_count,
            error: None,
            details,
        })
    }

    /// 获取服务端时间
    async fn get_server_time(&self) -> Result<DateTime<Utc>, SyncError> {
        let ntp_servers = {
            let config = self.config.read();
            config.ntp_servers.clone()
        };

        ntp_client::get_ntp_time(&ntp_servers).await
    }

    /// 启动定期同步任务
    async fn start_sync_task(&self) {
        let config = Arc::clone(&self.config);
        let is_running = Arc::clone(&self.is_running);
        let sync_status = Arc::clone(&self.sync_status);
        let _storage = Arc::clone(&self.storage);
        let _http_client = self.http_client.clone();
        let _conflict_resolver = Arc::clone(&self.conflict_resolver);

        let task_handle = tokio::spawn(async move {
            let mut sync_interval_duration = Duration::from_secs(300); // 默认5分钟
            let mut interval = interval(sync_interval_duration);

            while *is_running.read() {
                interval.tick().await;

                let config_guard = config.read();
                if !config_guard.enabled {
                    continue;
                }

                let new_sync_interval = config_guard.sync_interval;
                drop(config_guard);

                // 如果同步间隔发生变化，创建新的interval
                if new_sync_interval != sync_interval_duration.as_secs() {
                    sync_interval_duration = Duration::from_secs(new_sync_interval);
                    interval = tokio::time::interval(sync_interval_duration);
                }

                // 执行同步
                {
                    let mut status = sync_status.write();
                    status.is_syncing = true;
                }

                // 这里需要创建一个临时的SyncManager实例来执行同步
                // 由于我们在异步任务中，无法直接使用self
                // 实际实现中可能需要重构这部分代码
                log::info!("执行定期同步任务");

                {
                    let mut status = sync_status.write();
                    status.is_syncing = false;
                    status.last_sync_time = Some(Utc::now());
                }
            }

            log::info!("同步任务已停止");
        });

        let mut handle = self.sync_task_handle.lock().await;
        *handle = Some(task_handle);
    }

    /// 更新同步状态
    async fn update_sync_status(&self) {
        let pending_count = self.storage.get_pending_count().await.unwrap_or(0);
        let ntp_offset = self.log_tracker.get_ntp_offset();
        let server_connected = self.check_server_connection().await;

        let mut status = self.sync_status.write();
        status.pending_records = pending_count;
        status.ntp_offset_ms = ntp_offset;
        status.server_connected = server_connected;
    }
}

#[async_trait::async_trait]
impl SyncModule for SyncManager {
    /// 初始化同步模块
    async fn initialize(&mut self, config: SyncConfig) -> Result<(), SyncError> {
        {
            let mut current_config = self.config.write();
            *current_config = config.clone();
        }

        // 更新日志追踪器配置
        self.log_tracker.update_config(config);

        log::info!("同步管理器初始化完成");
        Ok(())
    }

    /// 启动同步服务
    async fn start(&mut self) -> Result<(), SyncError> {
        {
            let mut running = self.is_running.write();
            if *running {
                return Ok(());
            }
            *running = true;
        }

        log::info!("启动同步服务");

        // 启动日志追踪器
        self.log_tracker.start().await?;

        // 启动定期同步任务
        self.start_sync_task().await;

        // 初始同步状态更新
        self.update_sync_status().await;

        Ok(())
    }

    /// 停止同步服务
    async fn stop(&mut self) -> Result<(), SyncError> {
        {
            let mut running = self.is_running.write();
            *running = false;
        }

        // 停止日志追踪器
        self.log_tracker.stop().await?;

        // 停止同步任务
        let mut handle = self.sync_task_handle.lock().await;
        if let Some(task_handle) = handle.take() {
            task_handle.abort();
        }

        log::info!("同步服务已停止");
        Ok(())
    }

    /// 获取同步状态
    async fn get_status(&self) -> Result<SyncStatus, SyncError> {
        self.update_sync_status().await;
        Ok(self.sync_status.read().clone())
    }

    /// 手动触发同步
    async fn trigger_sync(&mut self) -> Result<SyncResult, SyncError> {
        let enabled = {
            let config = self.config.read();
            config.enabled
        };
        
        if !enabled {
            return Err(SyncError::ConfigError("同步功能未启用".to_string()));
        }

        {
            let mut status = self.sync_status.write();
            if status.is_syncing {
                return Err(SyncError::Unknown("同步正在进行中".to_string()));
            }
            status.is_syncing = true;
        }

        log::info!("开始手动同步");

        let result: Result<SyncResult, SyncError> = async {
            // 先同步到服务端
            let upload_result = self.sync_to_server().await?;
            
            // 再从服务端同步
            let download_result = self.sync_from_server().await?;

            // 合并结果
            let mut details = upload_result.details;
            details.extend(download_result.details);

            Ok(SyncResult {
                success: upload_result.success && download_result.success,
                synced_count: upload_result.synced_count + download_result.synced_count,
                conflict_count: upload_result.conflict_count + download_result.conflict_count,
                error: upload_result.error.or(download_result.error),
                details,
            })
        }.await;

        // 更新同步状态
        {
            let mut status = self.sync_status.write();
            status.is_syncing = false;
            status.last_sync_time = Some(Utc::now());
            if let Err(ref e) = result {
                status.last_error = Some(e.to_string());
            } else {
                status.last_error = None;
            }
        }

        self.update_sync_status().await;

        log::info!("手动同步完成");
        result
    }

    /// 记录数据变更
    async fn record_change(
        &mut self,
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
    ) -> Result<(), SyncError> {
        self.log_tracker
            .record_change(table_name, record_id, operation_type, data)
            .await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    async fn create_test_manager() -> SyncManager {
        let temp_file = NamedTempFile::new().unwrap();
        let config = SyncConfig {
            enabled: true,
            ..Default::default()
        };
        SyncManager::new(config, temp_file.path()).await.unwrap()
    }

    #[tokio::test]
    async fn test_manager_creation() {
        let _manager = create_test_manager().await;
    }

    #[tokio::test]
    async fn test_start_stop_manager() {
        let mut manager = create_test_manager().await;
        
        manager.start().await.unwrap();
        assert!(*manager.is_running.read());
        
        manager.stop().await.unwrap();
        assert!(!*manager.is_running.read());
    }

    #[tokio::test]
    async fn test_get_status() {
        let manager = create_test_manager().await;
        let status = manager.get_status().await.unwrap();
        assert!(!status.is_syncing);
        assert_eq!(status.pending_records, 0);
    }

    #[tokio::test]
    async fn test_record_change() {
        let mut manager = create_test_manager().await;
        manager.start().await.unwrap();

        manager
            .record_change(
                "test_table",
                "test_record_1",
                SyncRecordType::Create,
                Some(r#"{"name": "test"}"#),
            )
            .await
            .unwrap();

        // 等待处理
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        manager.stop().await.unwrap();
    }
} 