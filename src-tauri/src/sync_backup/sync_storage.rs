//! 同步数据存储模块
//! 
//! 负责同步相关数据的持久化存储，包括同步记录、配置信息等

use crate::sync::{SyncError, SyncRecord, SyncRecordType, VectorClock, VectorSyncRecord};
use chrono::{DateTime, Utc};
use rusqlite::{params, Connection, Result as SqliteResult, Row};
use std::path::Path;
use std::sync::Arc;
use tokio::sync::Mutex;
use uuid::Uuid;

/// 同步数据存储
pub struct SyncStorage {
    connection: Arc<Mutex<Connection>>,
}

impl SyncStorage {
    /// 创建新的同步存储实例
    pub async fn new<P: AsRef<Path>>(db_path: P) -> Result<Self, SyncError> {
        let conn = Connection::open(db_path)
            .map_err(|e| SyncError::DatabaseError(format!("打开数据库失败: {}", e)))?;

        let storage = Self {
            connection: Arc::new(Mutex::new(conn)),
        };

        storage.initialize_tables().await?;
        Ok(storage)
    }

    /// 初始化数据库表
    async fn initialize_tables(&self) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        // 创建同步记录表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS sync_records (
                id TEXT PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                operation_type TEXT NOT NULL,
                data TEXT,
                local_timestamp TEXT NOT NULL,
                server_timestamp TEXT,
                version INTEGER NOT NULL,
                device_id TEXT NOT NULL,
                synced BOOLEAN NOT NULL DEFAULT 0,
                retry_count INTEGER NOT NULL DEFAULT 0,
                data_hash TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            "#,
            [],
        ).map_err(|e| SyncError::DatabaseError(format!("创建sync_records表失败: {}", e)))?;

        // 创建向量时钟记录表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS vector_sync_records (
                id TEXT PRIMARY KEY,
                sync_record_id TEXT NOT NULL,
                vector_clock TEXT NOT NULL,
                causal_dependencies TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (sync_record_id) REFERENCES sync_records (id) ON DELETE CASCADE
            )
            "#,
            [],
        ).map_err(|e| SyncError::DatabaseError(format!("创建vector_sync_records表失败: {}", e)))?;

        // 创建配置表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS sync_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            "#,
            [],
        ).map_err(|e| SyncError::DatabaseError(format!("创建sync_config表失败: {}", e)))?;

        // 创建索引以提高查询性能
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_sync_records_table_record ON sync_records (table_name, record_id)",
            "CREATE INDEX IF NOT EXISTS idx_sync_records_synced ON sync_records (synced)",
            "CREATE INDEX IF NOT EXISTS idx_sync_records_device ON sync_records (device_id)",
            "CREATE INDEX IF NOT EXISTS idx_sync_records_timestamp ON sync_records (server_timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_vector_sync_records_sync_record ON vector_sync_records (sync_record_id)",
        ];

        for index_sql in &indexes {
            conn.execute(index_sql, [])
                .map_err(|e| SyncError::DatabaseError(format!("创建索引失败: {}", e)))?;
        }

        Ok(())
    }

    /// 插入同步记录
    pub async fn insert_record(&self, record: &SyncRecord) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            r#"
            INSERT INTO sync_records (
                id, table_name, record_id, operation_type, data,
                local_timestamp, server_timestamp, version, device_id,
                synced, retry_count, data_hash, created_at, updated_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14)
            "#,
            params![
                record.id.to_string(),
                record.table_name,
                record.record_id,
                self.operation_type_to_string(&record.operation_type),
                record.data,
                record.local_timestamp.to_rfc3339(),
                record.server_timestamp.map(|t| t.to_rfc3339()),
                record.version,
                record.device_id,
                record.synced,
                record.retry_count,
                record.data_hash,
                record.created_at.to_rfc3339(),
                record.updated_at.to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("插入同步记录失败: {}", e)))?;

        Ok(())
    }

    /// 插入向量同步记录
    pub async fn insert_vector_record(&self, record: &VectorSyncRecord) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        // 首先插入基础同步记录
        conn.execute(
            r#"
            INSERT OR REPLACE INTO sync_records (
                id, table_name, record_id, operation_type, data,
                local_timestamp, server_timestamp, version, device_id,
                synced, retry_count, data_hash, created_at, updated_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14)
            "#,
            params![
                record.base_record.id.to_string(),
                record.base_record.table_name,
                record.base_record.record_id,
                self.operation_type_to_string(&record.base_record.operation_type),
                record.base_record.data,
                record.base_record.local_timestamp.to_rfc3339(),
                record.base_record.server_timestamp.map(|t| t.to_rfc3339()),
                record.base_record.version,
                record.base_record.device_id,
                record.base_record.synced,
                record.base_record.retry_count,
                record.base_record.data_hash,
                record.base_record.created_at.to_rfc3339(),
                record.base_record.updated_at.to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("插入基础同步记录失败: {}", e)))?;

        // 序列化向量时钟和依赖关系
        let vector_clock_json = serde_json::to_string(&record.vector_clock)
            .map_err(|e| SyncError::SerializationError(format!("序列化向量时钟失败: {}", e)))?;
        
        let dependencies_json = serde_json::to_string(&record.causal_dependencies)
            .map_err(|e| SyncError::SerializationError(format!("序列化因果依赖失败: {}", e)))?;

        let now = Utc::now();
        
        // 插入向量时钟记录
        conn.execute(
            r#"
            INSERT OR REPLACE INTO vector_sync_records (
                id, sync_record_id, vector_clock, causal_dependencies, created_at, updated_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6)
            "#,
            params![
                Uuid::new_v4().to_string(),
                record.base_record.id.to_string(),
                vector_clock_json,
                dependencies_json,
                now.to_rfc3339(),
                now.to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("插入向量时钟记录失败: {}", e)))?;

        Ok(())
    }

    /// 更新同步记录
    pub async fn update_record(&self, record: &SyncRecord) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        let rows_affected = conn.execute(
            r#"
            UPDATE sync_records SET
                table_name = ?2, record_id = ?3, operation_type = ?4, data = ?5,
                local_timestamp = ?6, server_timestamp = ?7, version = ?8, device_id = ?9,
                synced = ?10, retry_count = ?11, data_hash = ?12, updated_at = ?13
            WHERE id = ?1
            "#,
            params![
                record.id.to_string(),
                record.table_name,
                record.record_id,
                self.operation_type_to_string(&record.operation_type),
                record.data,
                record.local_timestamp.to_rfc3339(),
                record.server_timestamp.map(|t| t.to_rfc3339()),
                record.version,
                record.device_id,
                record.synced,
                record.retry_count,
                record.data_hash,
                record.updated_at.to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("更新同步记录失败: {}", e)))?;

        if rows_affected == 0 {
            return Err(SyncError::DatabaseError("记录不存在".to_string()));
        }

        Ok(())
    }

    /// 更新向量同步记录
    pub async fn update_vector_record(&self, record: &VectorSyncRecord) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        // 更新基础同步记录
        self.update_record(&record.base_record).await?;
        
        // 序列化向量时钟和依赖关系
        let vector_clock_json = serde_json::to_string(&record.vector_clock)
            .map_err(|e| SyncError::SerializationError(format!("序列化向量时钟失败: {}", e)))?;
        
        let dependencies_json = serde_json::to_string(&record.causal_dependencies)
            .map_err(|e| SyncError::SerializationError(format!("序列化因果依赖失败: {}", e)))?;

        // 更新向量时钟记录
        conn.execute(
            r#"
            UPDATE vector_sync_records SET
                vector_clock = ?2, causal_dependencies = ?3, updated_at = ?4
            WHERE sync_record_id = ?1
            "#,
            params![
                record.base_record.id.to_string(),
                vector_clock_json,
                dependencies_json,
                Utc::now().to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("更新向量时钟记录失败: {}", e)))?;

        Ok(())
    }

    /// 标记记录为已同步
    pub async fn mark_as_synced(&self, record_id: &Uuid, server_timestamp: DateTime<Utc>) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "UPDATE sync_records SET synced = 1, server_timestamp = ?2, updated_at = ?3 WHERE id = ?1",
            params![
                record_id.to_string(),
                server_timestamp.to_rfc3339(),
                Utc::now().to_rfc3339(),
            ],
        ).map_err(|e| SyncError::DatabaseError(format!("标记同步状态失败: {}", e)))?;

        Ok(())
    }

    /// 增加重试次数
    pub async fn increment_retry_count(&self, record_id: &Uuid) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "UPDATE sync_records SET retry_count = retry_count + 1, updated_at = ?2 WHERE id = ?1",
            params![record_id.to_string(), Utc::now().to_rfc3339()],
        ).map_err(|e| SyncError::DatabaseError(format!("更新重试次数失败: {}", e)))?;

        Ok(())
    }

    /// 获取未同步的记录
    pub async fn get_unsynced_records(&self, limit: Option<usize>) -> Result<Vec<SyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let sql = if let Some(limit) = limit {
            format!("SELECT * FROM sync_records WHERE synced = 0 ORDER BY created_at ASC LIMIT {}", limit)
        } else {
            "SELECT * FROM sync_records WHERE synced = 0 ORDER BY created_at ASC".to_string()
        };

        let mut stmt = conn.prepare(&sql)
            .map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let records = stmt.query_map([], |row| self.row_to_sync_record(row))
            .map_err(|e| SyncError::DatabaseError(format!("查询未同步记录失败: {}", e)))?
            .collect::<SqliteResult<Vec<_>>>()
            .map_err(|e| SyncError::DatabaseError(format!("处理查询结果失败: {}", e)))?;

        Ok(records)
    }

    /// 获取未同步的向量记录
    pub async fn get_unsynced_vector_records(&self, limit: Option<usize>) -> Result<Vec<VectorSyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let sql = if let Some(limit) = limit {
            format!(
                r#"
                SELECT sr.*, vsr.vector_clock, vsr.causal_dependencies
                FROM sync_records sr
                JOIN vector_sync_records vsr ON sr.id = vsr.sync_record_id
                WHERE sr.synced = 0
                ORDER BY sr.created_at ASC
                LIMIT {}
                "#,
                limit
            )
        } else {
            r#"
            SELECT sr.*, vsr.vector_clock, vsr.causal_dependencies
            FROM sync_records sr
            JOIN vector_sync_records vsr ON sr.id = vsr.sync_record_id
            WHERE sr.synced = 0
            ORDER BY sr.created_at ASC
            "#.to_string()
        };

        let mut stmt = conn.prepare(&sql)
            .map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let records = stmt.query_map([], |row| self.row_to_vector_sync_record(row))
            .map_err(|e| SyncError::DatabaseError(format!("查询未同步向量记录失败: {}", e)))?
            .collect::<SqliteResult<Vec<_>>>()
            .map_err(|e| SyncError::DatabaseError(format!("处理查询结果失败: {}", e)))?;

        Ok(records)
    }

    /// 获取最新的记录
    pub async fn get_latest_record(&self, table_name: &str, record_id: &str) -> Result<Option<SyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT * FROM sync_records WHERE table_name = ?1 AND record_id = ?2 ORDER BY version DESC LIMIT 1"
        ).map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let mut rows = stmt.query_map(params![table_name, record_id], |row| self.row_to_sync_record(row))
            .map_err(|e| SyncError::DatabaseError(format!("查询最新记录失败: {}", e)))?;

        match rows.next() {
            Some(Ok(record)) => Ok(Some(record)),
            Some(Err(e)) => Err(SyncError::DatabaseError(format!("处理查询结果失败: {}", e))),
            None => Ok(None),
        }
    }

    /// 获取最新的向量记录
    pub async fn get_latest_vector_record(&self, table_name: &str, record_id: &str) -> Result<Option<VectorSyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            r#"
            SELECT sr.*, vsr.vector_clock, vsr.causal_dependencies
            FROM sync_records sr
            JOIN vector_sync_records vsr ON sr.id = vsr.sync_record_id
            WHERE sr.table_name = ?1 AND sr.record_id = ?2
            ORDER BY sr.version DESC
            LIMIT 1
            "#
        ).map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let mut rows = stmt.query_map(params![table_name, record_id], |row| self.row_to_vector_sync_record(row))
            .map_err(|e| SyncError::DatabaseError(format!("查询最新向量记录失败: {}", e)))?;

        match rows.next() {
            Some(Ok(record)) => Ok(Some(record)),
            Some(Err(e)) => Err(SyncError::DatabaseError(format!("处理查询结果失败: {}", e))),
            None => Ok(None),
        }
    }

    /// 获取指定时间范围内的记录
    pub async fn get_records_in_range(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<SyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT * FROM sync_records WHERE server_timestamp BETWEEN ?1 AND ?2 ORDER BY server_timestamp ASC"
        ).map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let records = stmt.query_map(
            params![start_time.to_rfc3339(), end_time.to_rfc3339()],
            |row| self.row_to_sync_record(row)
        ).map_err(|e| SyncError::DatabaseError(format!("查询时间范围记录失败: {}", e)))?
        .collect::<SqliteResult<Vec<_>>>()
        .map_err(|e| SyncError::DatabaseError(format!("处理查询结果失败: {}", e)))?;

        Ok(records)
    }

    /// 获取指定时间范围内的向量记录
    pub async fn get_vector_records_in_range(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<VectorSyncRecord>, SyncError> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            r#"
            SELECT sr.*, vsr.vector_clock, vsr.causal_dependencies
            FROM sync_records sr
            JOIN vector_sync_records vsr ON sr.id = vsr.sync_record_id
            WHERE sr.server_timestamp BETWEEN ?1 AND ?2
            ORDER BY sr.server_timestamp ASC
            "#
        ).map_err(|e| SyncError::DatabaseError(format!("准备查询语句失败: {}", e)))?;

        let records = stmt.query_map(
            params![start_time.to_rfc3339(), end_time.to_rfc3339()],
            |row| self.row_to_vector_sync_record(row)
        ).map_err(|e| SyncError::DatabaseError(format!("查询时间范围向量记录失败: {}", e)))?
        .collect::<SqliteResult<Vec<_>>>()
        .map_err(|e| SyncError::DatabaseError(format!("处理查询结果失败: {}", e)))?;

        Ok(records)
    }

    /// 清理旧记录
    pub async fn cleanup_old_records(&self, before_time: DateTime<Utc>) -> Result<usize, SyncError> {
        let conn = self.connection.lock().await;
        
        let deleted_count = conn.execute(
            "DELETE FROM sync_records WHERE synced = 1 AND server_timestamp < ?1",
            params![before_time.to_rfc3339()],
        ).map_err(|e| SyncError::DatabaseError(format!("清理旧记录失败: {}", e)))?;

        Ok(deleted_count)
    }

    /// 获取待同步记录数量
    pub async fn get_pending_count(&self) -> Result<usize, SyncError> {
        let conn = self.connection.lock().await;
        
        let count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM sync_records WHERE synced = 0",
            [],
            |row| row.get(0),
        ).map_err(|e| SyncError::DatabaseError(format!("查询待同步记录数量失败: {}", e)))?;

        Ok(count as usize)
    }

    /// 设置配置项
    pub async fn set_config(&self, key: &str, value: &str) -> Result<(), SyncError> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "INSERT OR REPLACE INTO sync_config (key, value, updated_at) VALUES (?1, ?2, ?3)",
            params![key, value, Utc::now().to_rfc3339()],
        ).map_err(|e| SyncError::DatabaseError(format!("设置配置失败: {}", e)))?;

        Ok(())
    }

    /// 获取配置项
    pub async fn get_config(&self, key: &str) -> Result<Option<String>, SyncError> {
        let conn = self.connection.lock().await;
        
        let result = conn.query_row(
            "SELECT value FROM sync_config WHERE key = ?1",
            params![key],
            |row| row.get::<_, String>(0),
        );

        match result {
            Ok(value) => Ok(Some(value)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(SyncError::DatabaseError(format!("获取配置失败: {}", e))),
        }
    }

    /// 将数据库行转换为同步记录
    fn row_to_sync_record(&self, row: &Row) -> SqliteResult<SyncRecord> {
        let id_str: String = row.get("id")?;
        let id = Uuid::parse_str(&id_str).map_err(|_| {
            rusqlite::Error::InvalidColumnType(0, "id".to_string(), rusqlite::types::Type::Text)
        })?;

        let local_timestamp_str: String = row.get("local_timestamp")?;
        let local_timestamp = DateTime::parse_from_rfc3339(&local_timestamp_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(0, "local_timestamp".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        let server_timestamp = if let Ok(server_timestamp_str) = row.get::<_, Option<String>>("server_timestamp") {
            if let Some(ts_str) = server_timestamp_str {
                Some(DateTime::parse_from_rfc3339(&ts_str)
                    .map_err(|_| rusqlite::Error::InvalidColumnType(0, "server_timestamp".to_string(), rusqlite::types::Type::Text))?
                    .with_timezone(&Utc))
            } else {
                None
            }
        } else {
            None
        };

        let created_at_str: String = row.get("created_at")?;
        let created_at = DateTime::parse_from_rfc3339(&created_at_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(0, "created_at".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        let updated_at_str: String = row.get("updated_at")?;
        let updated_at = DateTime::parse_from_rfc3339(&updated_at_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(0, "updated_at".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        let operation_type_str: String = row.get("operation_type")?;
        let operation_type = self.string_to_operation_type(&operation_type_str)?;

        Ok(SyncRecord {
            id,
            table_name: row.get("table_name")?,
            record_id: row.get("record_id")?,
            operation_type,
            data: row.get("data")?,
            local_timestamp,
            server_timestamp,
            version: row.get("version")?,
            device_id: row.get("device_id")?,
            synced: row.get("synced")?,
            retry_count: row.get("retry_count")?,
            data_hash: row.get("data_hash")?,
            created_at,
            updated_at,
        })
    }

    /// 将数据库行转换为向量同步记录
    fn row_to_vector_sync_record(&self, row: &Row) -> SqliteResult<VectorSyncRecord> {
        let base_record = self.row_to_sync_record(row)?;
        
        let vector_clock_json: String = row.get("vector_clock")?;
        let vector_clock: VectorClock = serde_json::from_str(&vector_clock_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(0, "vector_clock".to_string(), rusqlite::types::Type::Text))?;
        
        let dependencies_json: String = row.get("causal_dependencies")?;
        let causal_dependencies: Vec<Uuid> = serde_json::from_str(&dependencies_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(0, "causal_dependencies".to_string(), rusqlite::types::Type::Text))?;

        Ok(VectorSyncRecord {
            base_record,
            vector_clock,
            causal_dependencies,
        })
    }

    /// 操作类型转字符串
    fn operation_type_to_string(&self, op_type: &SyncRecordType) -> &'static str {
        match op_type {
            SyncRecordType::Create => "create",
            SyncRecordType::Update => "update",
            SyncRecordType::Delete => "delete",
            SyncRecordType::SoftDelete => "soft_delete",
        }
    }

    /// 字符串转操作类型
    fn string_to_operation_type(&self, s: &str) -> SqliteResult<SyncRecordType> {
        match s {
            "create" => Ok(SyncRecordType::Create),
            "update" => Ok(SyncRecordType::Update),
            "delete" => Ok(SyncRecordType::Delete),
            "soft_delete" => Ok(SyncRecordType::SoftDelete),
            _ => Err(rusqlite::Error::InvalidColumnType(0, "operation_type".to_string(), rusqlite::types::Type::Text)),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sync::{calculate_data_hash, VectorClock};
    use tempfile::NamedTempFile;

    async fn create_test_storage() -> SyncStorage {
        let temp_file = NamedTempFile::new().unwrap();
        let temp_path = temp_file.path().to_path_buf();
        // 确保文件被删除，这样SQLite可以创建新的数据库
        drop(temp_file);
        SyncStorage::new(temp_path).await.unwrap()
    }

    fn create_test_sync_record() -> SyncRecord {
        let now = Utc::now();
        SyncRecord {
            id: Uuid::new_v4(),
            table_name: "test_table".to_string(),
            record_id: "test_record_1".to_string(),
            operation_type: SyncRecordType::Create,
            data: Some(r#"{"name": "test", "value": 123}"#.to_string()),
            local_timestamp: now,
            server_timestamp: Some(now),
            version: now.timestamp_millis(),
            device_id: "test_device".to_string(),
            synced: false,
            retry_count: 0,
            data_hash: Some(calculate_data_hash(r#"{"name": "test", "value": 123}"#)),
            created_at: now,
            updated_at: now,
        }
    }

    fn create_test_vector_sync_record() -> VectorSyncRecord {
        let base_record = create_test_sync_record();
        let mut vector_clock = VectorClock::new();
        vector_clock.tick("test_device");
        
        VectorSyncRecord::new(base_record, vector_clock)
    }

    #[tokio::test]
    async fn test_storage_creation() {
        let _storage = create_test_storage().await;
        // 测试创建成功
        assert!(true);
    }

    #[tokio::test]
    async fn test_insert_and_get_record() {
        let storage = create_test_storage().await;
        let record = create_test_sync_record();
        
        // 插入记录
        storage.insert_record(&record).await.unwrap();
        
        // 获取记录
        let retrieved = storage.get_latest_record(&record.table_name, &record.record_id).await.unwrap();
        assert!(retrieved.is_some());
        
        let retrieved_record = retrieved.unwrap();
        assert_eq!(retrieved_record.id, record.id);
        assert_eq!(retrieved_record.table_name, record.table_name);
        assert_eq!(retrieved_record.record_id, record.record_id);
    }

    #[tokio::test]
    async fn test_insert_and_get_vector_record() {
        let storage = create_test_storage().await;
        let vector_record = create_test_vector_sync_record();
        
        // 插入向量记录
        storage.insert_vector_record(&vector_record).await.unwrap();
        
        // 获取向量记录
        let retrieved = storage.get_latest_vector_record(
            &vector_record.base_record.table_name, 
            &vector_record.base_record.record_id
        ).await.unwrap();
        
        assert!(retrieved.is_some());
        let retrieved_record = retrieved.unwrap();
        assert_eq!(retrieved_record.base_record.id, vector_record.base_record.id);
        assert_eq!(retrieved_record.vector_clock.get_clock("test_device"), 1);
    }

    #[tokio::test]
    async fn test_mark_as_synced() {
        let storage = create_test_storage().await;
        let record = create_test_sync_record();
        
        // 插入记录
        storage.insert_record(&record).await.unwrap();
        
        // 标记为已同步
        let sync_time = Utc::now();
        storage.mark_as_synced(&record.id, sync_time).await.unwrap();
        
        // 验证状态
        let retrieved = storage.get_latest_record(&record.table_name, &record.record_id).await.unwrap();
        assert!(retrieved.is_some());
        
        let retrieved_record = retrieved.unwrap();
        assert!(retrieved_record.synced);
        assert!(retrieved_record.server_timestamp.is_some());
    }

    #[tokio::test]
    async fn test_get_unsynced_vector_records() {
        let storage = create_test_storage().await;
        
        // 插入多个向量记录
        for i in 0..3 {
            let mut vector_record = create_test_vector_sync_record();
            vector_record.base_record.record_id = format!("test_record_{}", i);
            storage.insert_vector_record(&vector_record).await.unwrap();
        }
        
        // 获取未同步的向量记录
        let unsynced = storage.get_unsynced_vector_records(Some(2)).await.unwrap();
        assert_eq!(unsynced.len(), 2);
        
        // 验证向量时钟信息
        for record in &unsynced {
            assert_eq!(record.vector_clock.get_clock("test_device"), 1);
        }
    }

    #[tokio::test]
    async fn test_config_operations() {
        let storage = create_test_storage().await;
        
        // 设置配置
        storage.set_config("test_key", "test_value").await.unwrap();
        
        // 获取配置
        let value = storage.get_config("test_key").await.unwrap();
        assert_eq!(value, Some("test_value".to_string()));
        
        // 获取不存在的配置
        let missing = storage.get_config("missing_key").await.unwrap();
        assert_eq!(missing, None);
    }

    #[tokio::test]
    async fn test_cleanup_old_records() {
        let storage = create_test_storage().await;
        let mut record = create_test_sync_record();
        
        // 插入已同步的旧记录
        record.synced = true;
        record.server_timestamp = Some(Utc::now() - chrono::Duration::days(1));
        storage.insert_record(&record).await.unwrap();
        
        // 清理旧记录
        let cleanup_time = Utc::now() - chrono::Duration::hours(12);
        let deleted_count = storage.cleanup_old_records(cleanup_time).await.unwrap();
        assert_eq!(deleted_count, 1);
    }

    #[tokio::test]
    async fn test_vector_clock_serialization_in_storage() {
        let storage = create_test_storage().await;
        let mut vector_record = create_test_vector_sync_record();
        
        // 创建复杂的向量时钟
        vector_record.vector_clock.tick("device1");
        vector_record.vector_clock.tick("device2");
        vector_record.vector_clock.tick("device1");
        
        // 添加因果依赖
        let dep_id = Uuid::new_v4();
        vector_record.add_dependency(dep_id);
        
        // 插入记录
        storage.insert_vector_record(&vector_record).await.unwrap();
        
        // 获取记录并验证
        let retrieved = storage.get_latest_vector_record(
            &vector_record.base_record.table_name,
            &vector_record.base_record.record_id
        ).await.unwrap();
        
        assert!(retrieved.is_some());
        let retrieved_record = retrieved.unwrap();
        
        // 验证向量时钟
        assert_eq!(retrieved_record.vector_clock.get_clock("device1"), 2);
        assert_eq!(retrieved_record.vector_clock.get_clock("device2"), 1);
        
        // 验证因果依赖
        assert!(retrieved_record.has_dependency(&dep_id));
    }
} 