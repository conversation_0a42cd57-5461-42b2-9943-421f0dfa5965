//! 三路合并模块
//! 
//! 实现智能的三路合并算法，用于解决数据同步冲突
//! 支持字段级合并策略，适配密码管理器的复杂场景

use crate::sync::{SyncRecord, SyncRecordType, SyncError, calculate_data_hash};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::{Value, Map};
use std::collections::{HashMap, HashSet};
use uuid::Uuid;

/// 三路合并器
#[derive(Debug, Clone)]
pub struct ThreeWayMerger {
    /// 字段级合并策略配置
    field_strategies: HashMap<String, FieldMergeStrategy>,
    /// 全局合并配置
    config: MergeConfig,
}

/// 字段合并策略
#[derive(Debug, Clone)]
pub enum FieldMergeStrategy {
    /// 最新优先（基于时间戳）
    LatestWins,
    /// 合并数组（去重合并）
    MergeArrays,
    /// 合并对象（递归合并）
    MergeObjects,
    /// 保留非空值
    PreferNonEmpty,
    /// 保留更长的值
    PreferLonger,
    /// 自定义合并函数
    Custom(fn(&Value, &Value, &Value, &MergeContext) -> Result<Value, MergeError>),
}

/// 合并配置
#[derive(Debug, Clone)]
pub struct MergeConfig {
    /// 是否启用严格模式（更严格的冲突检测）
    pub strict_mode: bool,
    /// 是否保留合并历史
    pub preserve_history: bool,
    /// 最大递归深度
    pub max_recursion_depth: usize,
    /// 是否启用智能类型转换
    pub enable_type_coercion: bool,
}

/// 合并上下文
#[derive(Debug, Clone)]
pub struct MergeContext {
    /// 当前字段路径
    pub field_path: Vec<String>,
    /// 递归深度
    pub depth: usize,
    /// 合并配置
    pub config: MergeConfig,
    /// 基础记录的时间戳
    pub base_timestamp: Option<DateTime<Utc>>,
    /// 本地记录的时间戳
    pub local_timestamp: DateTime<Utc>,
    /// 远程记录的时间戳
    pub remote_timestamp: DateTime<Utc>,
}

/// 合并结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeResult {
    /// 是否成功合并
    pub success: bool,
    /// 合并后的记录
    pub merged_record: Option<SyncRecord>,
    /// 合并统计信息
    pub statistics: MergeStatistics,
    /// 冲突信息
    pub conflicts: Vec<FieldConflict>,
    /// 合并详情
    pub details: HashMap<String, String>,
}

/// 合并统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeStatistics {
    /// 总字段数
    pub total_fields: usize,
    /// 成功合并的字段数
    pub merged_fields: usize,
    /// 冲突字段数
    pub conflicted_fields: usize,
    /// 使用本地值的字段数
    pub local_wins: usize,
    /// 使用远程值的字段数
    pub remote_wins: usize,
    /// 智能合并的字段数
    pub smart_merged: usize,
}

/// 字段冲突信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldConflict {
    /// 字段路径
    pub field_path: String,
    /// 冲突类型
    pub conflict_type: ConflictType,
    /// 基础值
    pub base_value: Option<Value>,
    /// 本地值
    pub local_value: Option<Value>,
    /// 远程值
    pub remote_value: Option<Value>,
    /// 解决方案
    pub resolution: Option<Value>,
    /// 解决策略
    pub strategy: String,
}

/// 冲突类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConflictType {
    /// 值冲突（两边都修改了）
    ValueConflict,
    /// 类型冲突（类型不匹配）
    TypeConflict,
    /// 结构冲突（对象结构不同）
    StructureConflict,
    /// 删除冲突（一边删除一边修改）
    DeletionConflict,
}

/// 合并错误类型
#[derive(Debug, thiserror::Error)]
pub enum MergeError {
    #[error("无法合并冲突的值: {0}")]
    ConflictingValues(String),
    
    #[error("数据格式错误: {0}")]
    InvalidFormat(String),
    
    #[error("合并策略不支持: {0}")]
    UnsupportedStrategy(String),
    
    #[error("递归深度超限: {0}")]
    RecursionLimitExceeded(usize),
    
    #[error("类型不匹配: 期望 {expected}, 实际 {actual}")]
    TypeMismatch { expected: String, actual: String },
    
    #[error("序列化错误: {0}")]
    SerializationError(String),
    
    #[error("字段路径无效: {0}")]
    InvalidFieldPath(String),
}

impl Default for MergeConfig {
    fn default() -> Self {
        Self {
            strict_mode: false,
            preserve_history: true,
            max_recursion_depth: 10,
            enable_type_coercion: true,
        }
    }
}

impl Default for MergeStatistics {
    fn default() -> Self {
        Self {
            total_fields: 0,
            merged_fields: 0,
            conflicted_fields: 0,
            local_wins: 0,
            remote_wins: 0,
            smart_merged: 0,
        }
    }
}

impl ThreeWayMerger {
    /// 创建新的三路合并器
    pub fn new() -> Self {
        let mut merger = Self {
            field_strategies: HashMap::new(),
            config: MergeConfig::default(),
        };
        merger.configure_credential_strategies();
        merger
    }

    /// 使用自定义配置创建合并器
    pub fn with_config(config: MergeConfig) -> Self {
        let mut merger = Self {
            field_strategies: HashMap::new(),
            config,
        };
        merger.configure_credential_strategies();
        merger
    }

    /// 设置字段合并策略
    pub fn set_field_strategy(&mut self, field_name: &str, strategy: FieldMergeStrategy) {
        self.field_strategies.insert(field_name.to_string(), strategy);
    }

    /// 执行三路合并
    pub fn merge(
        &self,
        base: &SyncRecord,      // 共同祖先
        local: &SyncRecord,     // 本地版本
        remote: &SyncRecord,    // 远程版本
    ) -> Result<MergeResult, MergeError> {
        // 验证输入
        self.validate_inputs(base, local, remote)?;

        // 创建合并上下文
        let context = MergeContext {
            field_path: vec![],
            depth: 0,
            config: self.config.clone(),
            base_timestamp: base.server_timestamp.or(Some(base.local_timestamp)),
            local_timestamp: local.server_timestamp.unwrap_or(local.local_timestamp),
            remote_timestamp: remote.server_timestamp.unwrap_or(remote.local_timestamp),
        };

        // 解析JSON数据
        let base_data = self.parse_data(&base.data, "base")?;
        let local_data = self.parse_data(&local.data, "local")?;
        let remote_data = self.parse_data(&remote.data, "remote")?;

        // 执行字段级合并
        let mut statistics = MergeStatistics::default();
        let mut conflicts = Vec::new();
        
        let merged_data = self.merge_objects(
            &base_data, 
            &local_data, 
            &remote_data, 
            &context,
            &mut statistics,
            &mut conflicts
        )?;

        // 创建合并后的记录
        let merged_record = self.create_merged_record(local, remote, merged_data)?;

        // 创建详细信息
        let mut details = HashMap::new();
        details.insert("merge_timestamp".to_string(), Utc::now().to_rfc3339());
        details.insert("base_version".to_string(), base.version.to_string());
        details.insert("local_version".to_string(), local.version.to_string());
        details.insert("remote_version".to_string(), remote.version.to_string());
        details.insert("merged_version".to_string(), merged_record.version.to_string());

        Ok(MergeResult {
            success: conflicts.is_empty() || !self.config.strict_mode,
            merged_record: Some(merged_record),
            statistics,
            conflicts,
            details,
        })
    }

    /// 验证输入参数
    fn validate_inputs(
        &self,
        base: &SyncRecord,
        local: &SyncRecord,
        remote: &SyncRecord,
    ) -> Result<(), MergeError> {
        // 检查记录是否为同一条记录
        if local.table_name != remote.table_name || local.record_id != remote.record_id {
            return Err(MergeError::InvalidFormat(
                "本地和远程记录不是同一条记录".to_string()
            ));
        }

        if base.table_name != local.table_name || base.record_id != local.record_id {
            return Err(MergeError::InvalidFormat(
                "基础记录与本地记录不匹配".to_string()
            ));
        }

        Ok(())
    }

    /// 解析JSON数据
    fn parse_data(&self, data: &Option<String>, source: &str) -> Result<Value, MergeError> {
        match data {
            Some(json_str) => {
                serde_json::from_str(json_str).map_err(|e| {
                    MergeError::InvalidFormat(format!("{}数据JSON解析失败: {}", source, e))
                })
            }
            None => Ok(Value::Null),
        }
    }

    /// 创建合并后的记录
    fn create_merged_record(
        &self,
        local: &SyncRecord,
        remote: &SyncRecord,
        merged_data: Value,
    ) -> Result<SyncRecord, MergeError> {
        let data_string = serde_json::to_string(&merged_data)
            .map_err(|e| MergeError::SerializationError(e.to_string()))?;

        let mut merged_record = local.clone();
        merged_record.id = Uuid::new_v4();
        merged_record.data = Some(data_string.clone());
        merged_record.version = std::cmp::max(local.version, remote.version) + 1;
        merged_record.updated_at = Utc::now();
        merged_record.data_hash = Some(calculate_data_hash(&data_string));
        merged_record.operation_type = SyncRecordType::Update; // 合并结果总是更新操作

        Ok(merged_record)
    }

    /// 配置密码管理器专用的合并策略
    fn configure_credential_strategies(&mut self) {
        // 密码字段：最新优先
        self.field_strategies.insert("password".to_string(), FieldMergeStrategy::LatestWins);
        
        // 用户名：保留非空值
        self.field_strategies.insert("username".to_string(), FieldMergeStrategy::PreferNonEmpty);
        
        // 标签数组：智能合并
        self.field_strategies.insert("tags".to_string(), FieldMergeStrategy::MergeArrays);
        
        // 自定义字段：递归合并
        self.field_strategies.insert("custom_fields".to_string(), FieldMergeStrategy::MergeObjects);
        
        // 备注：保留更长的值
        self.field_strategies.insert("notes".to_string(), FieldMergeStrategy::PreferLonger);
        
        // 2FA备份码：智能合并
        self.field_strategies.insert("backup_codes".to_string(), FieldMergeStrategy::MergeArrays);
        
        // URL：保留非空值
        self.field_strategies.insert("url".to_string(), FieldMergeStrategy::PreferNonEmpty);
        
        // 标题：保留非空值
        self.field_strategies.insert("title".to_string(), FieldMergeStrategy::PreferNonEmpty);
        
        // Passkey数据：最新优先
        self.field_strategies.insert("credential_id".to_string(), FieldMergeStrategy::LatestWins);
        self.field_strategies.insert("counter".to_string(), FieldMergeStrategy::Custom(merge_passkey_counter));
        
        // 附件：智能合并
        self.field_strategies.insert("attachments".to_string(), FieldMergeStrategy::MergeArrays);
    }

    /// 合并对象
    fn merge_objects(
        &self,
        base: &Value,
        local: &Value,
        remote: &Value,
        context: &MergeContext,
        statistics: &mut MergeStatistics,
        conflicts: &mut Vec<FieldConflict>,
    ) -> Result<Value, MergeError> {
        // 检查递归深度
        if context.depth > context.config.max_recursion_depth {
            return Err(MergeError::RecursionLimitExceeded(context.depth));
        }

        let empty_map = Map::new();
        let base_obj = base.as_object().unwrap_or(&empty_map);
        let local_obj = local.as_object().unwrap_or(&empty_map);
        let remote_obj = remote.as_object().unwrap_or(&empty_map);

        let mut merged = Map::new();

        // 收集所有字段名
        let all_fields: HashSet<_> = base_obj.keys()
            .chain(local_obj.keys())
            .chain(remote_obj.keys())
            .collect();

        for field_name in all_fields {
            statistics.total_fields += 1;
            
            let base_value = base_obj.get(field_name);
            let local_value = local_obj.get(field_name);
            let remote_value = remote_obj.get(field_name);

            // 创建字段上下文
            let mut field_context = context.clone();
            field_context.field_path.push(field_name.clone());
            field_context.depth += 1;

            match self.merge_field(
                field_name,
                base_value,
                local_value,
                remote_value,
                &field_context,
                statistics,
                conflicts,
            ) {
                Ok(Some(merged_value)) => {
                    merged.insert(field_name.clone(), merged_value);
                    statistics.merged_fields += 1;
                }
                Ok(None) => {
                    // 字段被删除，不添加到结果中
                }
                Err(e) => {
                    // 记录冲突但继续处理
                    conflicts.push(FieldConflict {
                        field_path: field_context.field_path.join("."),
                        conflict_type: ConflictType::ValueConflict,
                        base_value: base_value.cloned(),
                        local_value: local_value.cloned(),
                        remote_value: remote_value.cloned(),
                        resolution: None,
                        strategy: "error".to_string(),
                    });
                    statistics.conflicted_fields += 1;
                    
                    if context.config.strict_mode {
                        return Err(e);
                    }
                    
                    // 非严格模式下，使用本地值作为fallback
                    if let Some(local_val) = local_value {
                        merged.insert(field_name.clone(), local_val.clone());
                        statistics.local_wins += 1;
                    }
                }
            }
        }

        Ok(Value::Object(merged))
    }

    /// 合并单个字段
    fn merge_field(
        &self,
        field_name: &str,
        base: Option<&Value>,
        local: Option<&Value>,
        remote: Option<&Value>,
        context: &MergeContext,
        statistics: &mut MergeStatistics,
        conflicts: &mut Vec<FieldConflict>,
    ) -> Result<Option<Value>, MergeError> {
        match (base, local, remote) {
            (Some(b), Some(l), Some(r)) => {
                if l == r {
                    // 本地和远程相同
                    Ok(Some(l.clone()))
                } else if l == b {
                    // 本地未变，使用远程
                    statistics.remote_wins += 1;
                    Ok(Some(r.clone()))
                } else if r == b {
                    // 远程未变，使用本地
                    statistics.local_wins += 1;
                    Ok(Some(l.clone()))
                } else {
                    // 都有变化，使用策略合并
                    let strategy = self.field_strategies.get(field_name)
                        .unwrap_or(&FieldMergeStrategy::LatestWins);
                    
                    match self.apply_merge_strategy(strategy, b, l, r, context) {
                        Ok(merged_value) => {
                            statistics.smart_merged += 1;
                            Ok(Some(merged_value))
                        }
                        Err(e) => {
                            // 记录冲突
                            conflicts.push(FieldConflict {
                                field_path: context.field_path.join("."),
                                conflict_type: ConflictType::ValueConflict,
                                base_value: Some(b.clone()),
                                local_value: Some(l.clone()),
                                remote_value: Some(r.clone()),
                                resolution: None,
                                strategy: format!("{:?}", strategy),
                            });
                            Err(e)
                        }
                    }
                }
            }
            (_, Some(l), Some(r)) => {
                // 基础版本不存在
                if l == r {
                    Ok(Some(l.clone()))
                } else {
                    let strategy = self.field_strategies.get(field_name)
                        .unwrap_or(&FieldMergeStrategy::LatestWins);
                    
                    match self.apply_merge_strategy(strategy, &Value::Null, l, r, context) {
                        Ok(merged_value) => {
                            statistics.smart_merged += 1;
                            Ok(Some(merged_value))
                        }
                        Err(_) => {
                            // 无基础版本的冲突，使用时间戳决定
                            if context.local_timestamp >= context.remote_timestamp {
                                statistics.local_wins += 1;
                                Ok(Some(l.clone()))
                            } else {
                                statistics.remote_wins += 1;
                                Ok(Some(r.clone()))
                            }
                        }
                    }
                }
            }
            (_, Some(l), None) => {
                statistics.local_wins += 1;
                Ok(Some(l.clone()))
            }
            (_, None, Some(r)) => {
                statistics.remote_wins += 1;
                Ok(Some(r.clone()))
            }
            _ => Ok(None),
        }
    }

    /// 应用合并策略
    fn apply_merge_strategy(
        &self,
        strategy: &FieldMergeStrategy,
        base: &Value,
        local: &Value,
        remote: &Value,
        context: &MergeContext,
    ) -> Result<Value, MergeError> {
        match strategy {
            FieldMergeStrategy::LatestWins => {
                if context.local_timestamp >= context.remote_timestamp {
                    Ok(local.clone())
                } else {
                    Ok(remote.clone())
                }
            }
            FieldMergeStrategy::MergeArrays => {
                self.merge_arrays(base, local, remote, context)
            }
            FieldMergeStrategy::MergeObjects => {
                let mut sub_statistics = MergeStatistics::default();
                let mut sub_conflicts = Vec::new();
                self.merge_objects(base, local, remote, context, &mut sub_statistics, &mut sub_conflicts)
            }
            FieldMergeStrategy::PreferNonEmpty => {
                self.prefer_non_empty(local, remote)
            }
            FieldMergeStrategy::PreferLonger => {
                self.prefer_longer(local, remote)
            }
            FieldMergeStrategy::Custom(merge_fn) => {
                merge_fn(base, local, remote, context)
            }
        }
    }

    /// 合并数组类型的值
    fn merge_arrays(
        &self,
        base: &Value,
        local: &Value,
        remote: &Value,
        _context: &MergeContext,
    ) -> Result<Value, MergeError> {
        let empty_vec = vec![];
        let base_array = base.as_array().unwrap_or(&empty_vec);
        let local_array = local.as_array().unwrap_or(&empty_vec);
        let remote_array = remote.as_array().unwrap_or(&empty_vec);

        // 计算变更
        let local_added: HashSet<_> = local_array.iter()
            .filter(|item| !base_array.contains(item))
            .collect();
        let remote_added: HashSet<_> = remote_array.iter()
            .filter(|item| !base_array.contains(item))
            .collect();
        
        let local_removed: HashSet<_> = base_array.iter()
            .filter(|item| !local_array.contains(item))
            .collect();
        let remote_removed: HashSet<_> = base_array.iter()
            .filter(|item| !remote_array.contains(item))
            .collect();

        // 构建合并结果
        let mut merged = Vec::new();
        
        // 保留基础中未被删除的项
        for item in base_array {
            if !local_removed.contains(&item) && !remote_removed.contains(&item) {
                merged.push(item.clone());
            }
        }
        
        // 添加新增的项
        for item in local_added.union(&remote_added) {
            merged.push((*item).clone());
        }

        Ok(Value::Array(merged))
    }

    /// 保留非空值
    fn prefer_non_empty(&self, local: &Value, remote: &Value) -> Result<Value, MergeError> {
        let local_empty = self.is_empty_value(local);
        let remote_empty = self.is_empty_value(remote);

        match (local_empty, remote_empty) {
            (true, false) => Ok(remote.clone()),
            (false, true) => Ok(local.clone()),
            (false, false) => {
                // 都非空，选择更长的
                self.prefer_longer(local, remote)
            }
            (true, true) => Ok(local.clone()), // 都为空，返回任意一个
        }
    }

    /// 保留更长的值
    fn prefer_longer(&self, local: &Value, remote: &Value) -> Result<Value, MergeError> {
        let local_len = self.get_value_length(local);
        let remote_len = self.get_value_length(remote);

        if local_len >= remote_len {
            Ok(local.clone())
        } else {
            Ok(remote.clone())
        }
    }

    /// 检查值是否为空
    fn is_empty_value(&self, value: &Value) -> bool {
        match value {
            Value::Null => true,
            Value::String(s) => s.is_empty(),
            Value::Array(arr) => arr.is_empty(),
            Value::Object(obj) => obj.is_empty(),
            _ => false,
        }
    }

    /// 获取值的长度
    fn get_value_length(&self, value: &Value) -> usize {
        match value {
            Value::String(s) => s.len(),
            Value::Array(arr) => arr.len(),
            Value::Object(obj) => obj.len(),
            _ => 0,
        }
    }
}

/// Passkey计数器的自定义合并函数
fn merge_passkey_counter(
    base: &Value,
    local: &Value,
    remote: &Value,
    _context: &MergeContext,
) -> Result<Value, MergeError> {
    let base_counter = base.as_u64().unwrap_or(0);
    let local_counter = local.as_u64().unwrap_or(0);
    let remote_counter = remote.as_u64().unwrap_or(0);
    
    // Passkey计数器必须单调递增，取最大值
    let max_counter = std::cmp::max(local_counter, remote_counter);
    
    // 如果两个都比基础版本大，说明有并发使用，需要特殊处理
    if local_counter > base_counter && remote_counter > base_counter {
        // 取最大值并加1以避免重复
        Ok(Value::Number(serde_json::Number::from(max_counter + 1)))
    } else {
        Ok(Value::Number(serde_json::Number::from(max_counter)))
    }
}

impl Default for ThreeWayMerger {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    fn create_test_record(
        table_name: &str,
        record_id: &str,
        data: Option<&str>,
        version: i64,
        timestamp: DateTime<Utc>,
    ) -> SyncRecord {
        SyncRecord {
            id: Uuid::new_v4(),
            table_name: table_name.to_string(),
            record_id: record_id.to_string(),
            operation_type: SyncRecordType::Update,
            data: data.map(|s| s.to_string()),
            local_timestamp: timestamp,
            server_timestamp: Some(timestamp),
            version,
            device_id: "test_device".to_string(),
            synced: false,
            retry_count: 0,
            data_hash: data.map(|d| calculate_data_hash(d)),
            created_at: timestamp,
            updated_at: timestamp,
        }
    }

    #[test]
    fn test_three_way_merger_creation() {
        let merger = ThreeWayMerger::new();
        assert!(!merger.field_strategies.is_empty());
        assert!(!merger.config.strict_mode);
    }

    #[test]
    fn test_simple_merge_no_conflict() {
        let merger = ThreeWayMerger::new();
        let now = Utc::now();

        let base = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "user", "password": "pass"}"#),
            1,
            now,
        );

        let local = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "user", "password": "new_pass"}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let remote = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "user", "password": "pass"}"#),
            1,
            now,
        );

        let result = merger.merge(&base, &local, &remote).unwrap();
        assert!(result.success);
        assert!(result.merged_record.is_some());
        
        let merged = result.merged_record.unwrap();
        let merged_data: Value = serde_json::from_str(merged.data.as_ref().unwrap()).unwrap();
        assert_eq!(merged_data["password"], "new_pass"); // 本地修改应该被保留
    }

    #[test]
    fn test_array_merge() {
        let merger = ThreeWayMerger::new();
        let now = Utc::now();

        let base = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"tags": ["work", "important"]}"#),
            1,
            now,
        );

        let local = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"tags": ["work", "important", "secure"]}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let remote = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"tags": ["work", "urgent"]}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let result = merger.merge(&base, &local, &remote).unwrap();
        assert!(result.success);
        
        let merged = result.merged_record.unwrap();
        let merged_data: Value = serde_json::from_str(merged.data.as_ref().unwrap()).unwrap();
        let tags = merged_data["tags"].as_array().unwrap();
        
        // 应该包含所有唯一的标签
        assert!(tags.contains(&Value::String("work".to_string())));
        assert!(tags.contains(&Value::String("secure".to_string())));
        assert!(tags.contains(&Value::String("urgent".to_string())));
        assert!(!tags.contains(&Value::String("important".to_string()))); // 被远程删除
    }

    #[test]
    fn test_conflict_detection() {
        let merger = ThreeWayMerger::with_config(MergeConfig {
            strict_mode: true,
            ..Default::default()
        });
        let now = Utc::now();

        let base = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"password": "old_pass"}"#),
            1,
            now,
        );

        let local = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"password": "local_pass"}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let remote = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"password": "remote_pass"}"#),
            2,
            now + chrono::Duration::minutes(2),
        );

        let result = merger.merge(&base, &local, &remote).unwrap();
        // 在严格模式下，应该成功合并（使用LatestWins策略）
        assert!(result.success);
        
        let merged = result.merged_record.unwrap();
        let merged_data: Value = serde_json::from_str(merged.data.as_ref().unwrap()).unwrap();
        assert_eq!(merged_data["password"], "remote_pass"); // 远程时间更新
    }

    #[test]
    fn test_passkey_counter_merge() {
        let base = Value::Number(serde_json::Number::from(5));
        let local = Value::Number(serde_json::Number::from(7));
        let remote = Value::Number(serde_json::Number::from(6));
        
        let context = MergeContext {
            field_path: vec!["counter".to_string()],
            depth: 1,
            config: MergeConfig::default(),
            base_timestamp: None,
            local_timestamp: Utc::now(),
            remote_timestamp: Utc::now(),
        };

        let result = merge_passkey_counter(&base, &local, &remote, &context).unwrap();
        assert_eq!(result.as_u64().unwrap(), 8); // max(7,6) + 1 = 8
    }

    #[test]
    fn test_prefer_non_empty_strategy() {
        let merger = ThreeWayMerger::new();
        
        let empty_val = Value::String("".to_string());
        let non_empty_val = Value::String("content".to_string());
        
        let result = merger.prefer_non_empty(&empty_val, &non_empty_val).unwrap();
        assert_eq!(result, non_empty_val);
        
        let result = merger.prefer_non_empty(&non_empty_val, &empty_val).unwrap();
        assert_eq!(result, non_empty_val);
    }

    #[test]
    fn test_prefer_longer_strategy() {
        let merger = ThreeWayMerger::new();
        
        let short_val = Value::String("hi".to_string());
        let long_val = Value::String("hello world".to_string());
        
        let result = merger.prefer_longer(&short_val, &long_val).unwrap();
        assert_eq!(result, long_val);
        
        let result = merger.prefer_longer(&long_val, &short_val).unwrap();
        assert_eq!(result, long_val);
    }

    #[test]
    fn test_invalid_input_validation() {
        let merger = ThreeWayMerger::new();
        let now = Utc::now();

        let base = create_test_record("credentials", "test_1", None, 1, now);
        let local = create_test_record("credentials", "test_2", None, 2, now); // 不同的record_id
        let remote = create_test_record("credentials", "test_1", None, 2, now);

        let result = merger.merge(&base, &local, &remote);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), MergeError::InvalidFormat(_)));
    }

    #[test]
    fn test_merge_statistics() {
        let merger = ThreeWayMerger::new();
        let now = Utc::now();

        let base = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "user", "password": "pass", "tags": ["work"]}"#),
            1,
            now,
        );

        let local = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "user", "password": "new_pass", "tags": ["work", "secure"]}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let remote = create_test_record(
            "credentials",
            "test_1",
            Some(r#"{"username": "new_user", "password": "pass", "tags": ["work", "urgent"]}"#),
            2,
            now + chrono::Duration::minutes(1),
        );

        let result = merger.merge(&base, &local, &remote).unwrap();
        assert!(result.success);
        
        let stats = result.statistics;
        assert!(stats.total_fields > 0);
        assert!(stats.merged_fields > 0);
        assert_eq!(stats.total_fields, stats.merged_fields + stats.conflicted_fields);
    }
} 