//! 向量时钟模块
//! 
//! 实现向量时钟算法，用于检测分布式系统中事件的因果关系和并发性

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::sync::{SyncRecord, SyncError};

/// 向量时钟
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VectorClock {
    /// 设备ID -> 逻辑时钟值的映射
    clocks: HashMap<String, u64>,
}

/// 因果关系枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum CausalRelation {
    /// self 发生在 other 之前
    Before,
    /// self 发生在 other 之后  
    After,
    /// 两个事件相等
    Equal,
    /// 两个事件并发
    Concurrent,
}

/// 扩展的同步记录，包含向量时钟信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VectorSyncRecord {
    /// 基础同步记录
    pub base_record: SyncRecord,
    /// 向量时钟
    pub vector_clock: VectorClock,
    /// 因果依赖的记录ID列表
    pub causal_dependencies: Vec<Uuid>,
}

impl VectorClock {
    /// 创建新的向量时钟
    pub fn new() -> Self {
        Self {
            clocks: HashMap::new(),
        }
    }

    /// 从设备列表创建向量时钟
    pub fn from_devices(devices: &[String]) -> Self {
        let mut clocks = HashMap::new();
        for device in devices {
            clocks.insert(device.clone(), 0);
        }
        Self { clocks }
    }

    /// 本地事件发生时递增时钟
    /// 
    /// # Arguments
    /// * `device_id` - 当前设备ID
    pub fn tick(&mut self, device_id: &str) {
        let current = self.clocks.get(device_id).unwrap_or(&0);
        self.clocks.insert(device_id.to_string(), current + 1);
    }

    /// 接收到远程事件时更新时钟
    /// 
    /// # Arguments
    /// * `other` - 远程向量时钟
    /// * `device_id` - 当前设备ID
    pub fn update(&mut self, other: &VectorClock, device_id: &str) {
        // 1. 对于其他设备，取最大值
        for (other_device, &other_time) in &other.clocks {
            if other_device != device_id {
                let current = self.clocks.get(other_device).unwrap_or(&0);
                self.clocks.insert(other_device.clone(), (*current).max(other_time));
            }
        }
        
        // 2. 递增本设备时钟
        self.tick(device_id);
    }

    /// 比较两个向量时钟的因果关系
    /// 
    /// # Arguments
    /// * `other` - 要比较的向量时钟
    /// 
    /// # Returns
    /// 返回因果关系
    pub fn compare(&self, other: &VectorClock) -> CausalRelation {
        let mut self_less = false;
        let mut other_less = false;

        // 收集所有设备ID
        let all_devices: std::collections::HashSet<_> = self.clocks.keys()
            .chain(other.clocks.keys())
            .collect();

        for device in all_devices {
            let self_time = self.clocks.get(device).unwrap_or(&0);
            let other_time = other.clocks.get(device).unwrap_or(&0);

            if self_time < other_time {
                self_less = true;
            } else if self_time > other_time {
                other_less = true;
            }
        }

        match (self_less, other_less) {
            (true, false) => CausalRelation::Before,
            (false, true) => CausalRelation::After,
            (false, false) => CausalRelation::Equal,
            (true, true) => CausalRelation::Concurrent,
        }
    }

    /// 检查是否并发
    pub fn is_concurrent(&self, other: &VectorClock) -> bool {
        matches!(self.compare(other), CausalRelation::Concurrent)
    }

    /// 检查是否发生在另一个事件之前
    pub fn happens_before(&self, other: &VectorClock) -> bool {
        matches!(self.compare(other), CausalRelation::Before)
    }

    /// 获取设备的时钟值
    pub fn get_clock(&self, device_id: &str) -> u64 {
        self.clocks.get(device_id).copied().unwrap_or(0)
    }

    /// 获取所有设备ID
    pub fn get_devices(&self) -> Vec<String> {
        self.clocks.keys().cloned().collect()
    }

    /// 获取时钟的总和（用于排序）
    pub fn sum(&self) -> u64 {
        self.clocks.values().sum()
    }

    /// 压缩向量时钟，移除不活跃的设备
    pub fn compress(&mut self, active_devices: &std::collections::HashSet<String>) {
        self.clocks.retain(|device_id, _| active_devices.contains(device_id));
    }

    /// 合并两个向量时钟（取每个设备的最大值）
    pub fn merge(&mut self, other: &VectorClock) {
        for (device, &time) in &other.clocks {
            let current = self.clocks.get(device).unwrap_or(&0);
            self.clocks.insert(device.clone(), (*current).max(time));
        }
    }

    /// 检查向量时钟是否为空
    pub fn is_empty(&self) -> bool {
        self.clocks.is_empty() || self.clocks.values().all(|&v| v == 0)
    }

    /// 获取向量时钟的大小（设备数量）
    pub fn size(&self) -> usize {
        self.clocks.len()
    }
}

impl Default for VectorClock {
    fn default() -> Self {
        Self::new()
    }
}

impl VectorSyncRecord {
    /// 创建新的向量同步记录
    pub fn new(base_record: SyncRecord, vector_clock: VectorClock) -> Self {
        Self {
            base_record,
            vector_clock,
            causal_dependencies: Vec::new(),
        }
    }

    /// 添加因果依赖
    pub fn add_dependency(&mut self, dependency_id: Uuid) {
        if !self.causal_dependencies.contains(&dependency_id) {
            self.causal_dependencies.push(dependency_id);
        }
    }

    /// 移除因果依赖
    pub fn remove_dependency(&mut self, dependency_id: &Uuid) {
        self.causal_dependencies.retain(|id| id != dependency_id);
    }

    /// 检查是否有因果依赖
    pub fn has_dependency(&self, dependency_id: &Uuid) -> bool {
        self.causal_dependencies.contains(dependency_id)
    }

    /// 获取记录ID
    pub fn get_id(&self) -> Uuid {
        self.base_record.id
    }

    /// 获取表名
    pub fn get_table_name(&self) -> &str {
        &self.base_record.table_name
    }

    /// 获取记录ID
    pub fn get_record_id(&self) -> &str {
        &self.base_record.record_id
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sync::{SyncRecordType, calculate_data_hash};

    /// 创建测试用的同步记录
    fn create_test_sync_record(
        table_name: &str,
        record_id: &str,
        operation_type: SyncRecordType,
        data: Option<&str>,
        device_id: &str,
    ) -> SyncRecord {
        let now = Utc::now();
        SyncRecord {
            id: Uuid::new_v4(),
            table_name: table_name.to_string(),
            record_id: record_id.to_string(),
            operation_type,
            data: data.map(|s| s.to_string()),
            local_timestamp: now,
            server_timestamp: Some(now),
            version: now.timestamp_millis(),
            device_id: device_id.to_string(),
            synced: false,
            retry_count: 0,
            data_hash: data.map(|d| calculate_data_hash(d)),
            created_at: now,
            updated_at: now,
        }
    }

    #[test]
    fn test_vector_clock_creation() {
        let clock = VectorClock::new();
        assert!(clock.is_empty());
        assert_eq!(clock.size(), 0);
    }

    #[test]
    fn test_vector_clock_from_devices() {
        let devices = vec!["device1".to_string(), "device2".to_string()];
        let clock = VectorClock::from_devices(&devices);
        
        assert_eq!(clock.size(), 2);
        assert_eq!(clock.get_clock("device1"), 0);
        assert_eq!(clock.get_clock("device2"), 0);
        assert_eq!(clock.get_clock("device3"), 0); // 不存在的设备
    }

    #[test]
    fn test_vector_clock_tick() {
        let mut clock = VectorClock::new();
        
        // 第一次tick
        clock.tick("device1");
        assert_eq!(clock.get_clock("device1"), 1);
        assert_eq!(clock.size(), 1);
        
        // 第二次tick
        clock.tick("device1");
        assert_eq!(clock.get_clock("device1"), 2);
        
        // 不同设备tick
        clock.tick("device2");
        assert_eq!(clock.get_clock("device2"), 1);
        assert_eq!(clock.size(), 2);
    }

    #[test]
    fn test_vector_clock_update() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        
        // 设备1发生事件
        clock1.tick("device1");
        assert_eq!(clock1.get_clock("device1"), 1);
        
        // 设备2发生事件
        clock2.tick("device2");
        assert_eq!(clock2.get_clock("device2"), 1);
        
        // 设备1接收设备2的事件
        clock1.update(&clock2, "device1");
        assert_eq!(clock1.get_clock("device1"), 2); // 递增了
        assert_eq!(clock1.get_clock("device2"), 1); // 同步了设备2的时钟
    }

    #[test]
    fn test_causal_relation_equal() {
        let clock1 = VectorClock::new();
        let clock2 = VectorClock::new();
        
        assert_eq!(clock1.compare(&clock2), CausalRelation::Equal);
    }

    #[test]
    fn test_causal_relation_before() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        
        clock1.tick("device1");
        clock2.update(&clock1, "device2");
        
        assert_eq!(clock1.compare(&clock2), CausalRelation::Before);
        assert_eq!(clock2.compare(&clock1), CausalRelation::After);
    }

    #[test]
    fn test_causal_relation_concurrent() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        
        // 两个设备并发发生事件
        clock1.tick("device1");
        clock2.tick("device2");
        
        assert_eq!(clock1.compare(&clock2), CausalRelation::Concurrent);
        assert_eq!(clock2.compare(&clock1), CausalRelation::Concurrent);
        assert!(clock1.is_concurrent(&clock2));
    }

    #[test]
    fn test_complex_causal_scenario() {
        let mut clock_a = VectorClock::new();
        let mut clock_b = VectorClock::new();
        let mut clock_c = VectorClock::new();
        
        // 场景：A -> B -> C 的因果链
        
        // A发生事件
        clock_a.tick("device_a");
        // A: {a:1}
        
        // B接收A的事件并发生新事件
        clock_b.update(&clock_a, "device_b");
        // B: {a:1, b:1}
        
        // C接收B的事件并发生新事件
        clock_c.update(&clock_b, "device_c");
        // C: {a:1, b:1, c:1}
        
        // 验证因果关系
        assert!(clock_a.happens_before(&clock_b));
        assert!(clock_b.happens_before(&clock_c));
        assert!(clock_a.happens_before(&clock_c));
    }

    #[test]
    fn test_concurrent_with_partial_order() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        let mut clock3 = VectorClock::new();
        
        // 复杂的并发场景
        clock1.tick("device1"); // {1:1}
        clock2.tick("device2"); // {2:1}
        
        // clock3同时接收clock1和clock2的事件
        clock3.update(&clock1, "device3"); // {1:1, 3:1}
        clock3.update(&clock2, "device3"); // {1:1, 2:1, 3:2}
        
        // clock1和clock2仍然是并发的
        assert!(clock1.is_concurrent(&clock2));
        
        // 但clock3发生在clock1和clock2之后
        assert_eq!(clock1.compare(&clock3), CausalRelation::Before);
        assert_eq!(clock2.compare(&clock3), CausalRelation::Before);
    }

    #[test]
    fn test_vector_clock_merge() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        
        clock1.tick("device1");
        clock1.tick("device1");
        clock1.tick("device2");
        
        clock2.tick("device2");
        clock2.tick("device2");
        clock2.tick("device3");
        
        clock1.merge(&clock2);
        
        assert_eq!(clock1.get_clock("device1"), 2);
        assert_eq!(clock1.get_clock("device2"), 2); // max(1, 2)
        assert_eq!(clock1.get_clock("device3"), 1);
    }

    #[test]
    fn test_vector_clock_compress() {
        let mut clock = VectorClock::new();
        clock.tick("device1");
        clock.tick("device2");
        clock.tick("device3");
        
        let active_devices: std::collections::HashSet<String> = 
            ["device1".to_string(), "device3".to_string()].iter().cloned().collect();
        
        clock.compress(&active_devices);
        
        assert_eq!(clock.size(), 2);
        assert_eq!(clock.get_clock("device1"), 1);
        assert_eq!(clock.get_clock("device2"), 0); // 被移除
        assert_eq!(clock.get_clock("device3"), 1);
    }

    #[test]
    fn test_vector_sync_record_creation() {
        let base_record = create_test_sync_record(
            "users",
            "user1",
            SyncRecordType::Create,
            Some(r#"{"name": "test"}"#),
            "device1",
        );
        
        let mut vector_clock = VectorClock::new();
        vector_clock.tick("device1");
        
        let vector_record = VectorSyncRecord::new(base_record, vector_clock);
        
        assert_eq!(vector_record.get_table_name(), "users");
        assert_eq!(vector_record.get_record_id(), "user1");
        assert_eq!(vector_record.vector_clock.get_clock("device1"), 1);
        assert!(vector_record.causal_dependencies.is_empty());
    }

    #[test]
    fn test_vector_sync_record_dependencies() {
        let base_record = create_test_sync_record(
            "users",
            "user1",
            SyncRecordType::Create,
            Some(r#"{"name": "test"}"#),
            "device1",
        );
        
        let vector_clock = VectorClock::new();
        let mut vector_record = VectorSyncRecord::new(base_record, vector_clock);
        
        let dep_id = Uuid::new_v4();
        
        // 添加依赖
        vector_record.add_dependency(dep_id);
        assert!(vector_record.has_dependency(&dep_id));
        assert_eq!(vector_record.causal_dependencies.len(), 1);
        
        // 重复添加不会增加
        vector_record.add_dependency(dep_id);
        assert_eq!(vector_record.causal_dependencies.len(), 1);
        
        // 移除依赖
        vector_record.remove_dependency(&dep_id);
        assert!(!vector_record.has_dependency(&dep_id));
        assert!(vector_record.causal_dependencies.is_empty());
    }

    #[test]
    fn test_distributed_scenario_simulation() {
        // 模拟分布式系统中的复杂场景
        let mut device_a = VectorClock::new();
        let mut device_b = VectorClock::new();
        let mut device_c = VectorClock::new();
        
        // 时间线：
        // T1: A发生事件
        device_a.tick("A");
        let event_a1 = device_a.clone();
        
        // T2: B发生事件（与A并发）
        device_b.tick("B");
        let event_b1 = device_b.clone();
        
        // T3: A接收B的事件
        device_a.update(&event_b1, "A");
        let event_a2 = device_a.clone();
        
        // T4: C接收A的最新状态
        device_c.update(&event_a2, "C");
        let event_c1 = device_c.clone();
        
        // T5: B接收C的状态
        device_b.update(&event_c1, "B");
        let event_b2 = device_b.clone();
        
        // 验证因果关系
        assert!(event_a1.is_concurrent(&event_b1)); // A1 || B1
        assert!(event_a1.happens_before(&event_a2)); // A1 -> A2
        assert!(event_b1.happens_before(&event_a2)); // B1 -> A2 (通过update)
        assert!(event_a2.happens_before(&event_c1)); // A2 -> C1
        assert!(event_c1.happens_before(&event_b2)); // C1 -> B2
        
        // 传递性检查
        assert!(event_a1.happens_before(&event_c1)); // A1 -> C1 (传递)
        assert!(event_b1.happens_before(&event_c1)); // B1 -> C1 (传递)
    }

    #[test]
    fn test_network_partition_scenario() {
        // 模拟网络分区场景
        let mut partition_1_device_a = VectorClock::new();
        let mut partition_1_device_b = VectorClock::new();
        let mut partition_2_device_c = VectorClock::new();
        let mut partition_2_device_d = VectorClock::new();
        
        // 分区1中的活动
        partition_1_device_a.tick("A");
        partition_1_device_b.update(&partition_1_device_a, "B");
        partition_1_device_a.update(&partition_1_device_b, "A");
        
        // 分区2中的活动（并发）
        partition_2_device_c.tick("C");
        partition_2_device_d.update(&partition_2_device_c, "D");
        partition_2_device_c.update(&partition_2_device_d, "C");
        
        // 网络分区愈合后，检查状态
        assert!(partition_1_device_a.is_concurrent(&partition_2_device_c));
        assert!(partition_1_device_b.is_concurrent(&partition_2_device_d));
        
        // 合并状态
        let mut merged_state = partition_1_device_a.clone();
        merged_state.merge(&partition_2_device_c);
        
        // 验证合并后的状态包含所有设备的最新时钟
        assert!(merged_state.get_clock("A") >= partition_1_device_a.get_clock("A"));
        assert!(merged_state.get_clock("C") >= partition_2_device_c.get_clock("C"));
    }

    #[test]
    fn test_clock_sum_for_ordering() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        
        clock1.tick("device1");
        clock1.tick("device2");
        // clock1 sum = 2
        
        clock2.tick("device3");
        // clock2 sum = 1
        
        assert!(clock1.sum() > clock2.sum());
        
        // 可以用于在并发事件中建立全序关系
        if clock1.is_concurrent(&clock2) {
            // 使用sum作为tie-breaker
            let order = clock1.sum().cmp(&clock2.sum());
            assert_eq!(order, std::cmp::Ordering::Greater);
        }
    }

    #[test]
    fn test_serialization() {
        let mut clock = VectorClock::new();
        clock.tick("device1");
        clock.tick("device2");
        
        // 测试序列化
        let serialized = serde_json::to_string(&clock).unwrap();
        let deserialized: VectorClock = serde_json::from_str(&serialized).unwrap();
        
        assert_eq!(clock, deserialized);
    }

    #[test]
    fn test_edge_cases() {
        let mut clock = VectorClock::new();
        
        // 空时钟的操作
        assert_eq!(clock.sum(), 0);
        assert!(clock.is_empty());
        assert_eq!(clock.get_devices().len(), 0);
        
        // 与空时钟比较
        let empty_clock = VectorClock::new();
        assert_eq!(clock.compare(&empty_clock), CausalRelation::Equal);
        
        // tick后不再为空
        clock.tick("device1");
        assert!(!clock.is_empty());
        assert_eq!(clock.compare(&empty_clock), CausalRelation::After);
    }
} 