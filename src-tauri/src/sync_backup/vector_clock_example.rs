//! 向量时钟集成示例
//! 
//! 展示如何在密码管理器场景中使用向量时钟进行多端同步

use crate::sync::{
    log_tracker::LogTracker,
    sync_storage::SyncStorage,
    SyncConfig, SyncRecordType, VectorClock, VectorSyncRecord,
};
use chrono::Utc;
use std::sync::Arc;
use tempfile::NamedTempFile;
use uuid::Uuid;

/// 模拟设备
pub struct SimulatedDevice {
    /// 设备ID
    pub device_id: String,
    /// 日志追踪器
    pub tracker: LogTracker,
    /// 存储
    pub storage: Arc<SyncStorage>,
}

impl SimulatedDevice {
    /// 创建新的模拟设备
    pub async fn new(device_id: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let temp_file = NamedTempFile::new()?;
        let temp_path = temp_file.path().to_path_buf();
        drop(temp_file);
        
        let storage = Arc::new(SyncStorage::new(temp_path).await?);
        
        let config = SyncConfig {
            enabled: true,
            device_id: device_id.to_string(),
            ..Default::default()
        };
        
        let tracker = LogTracker::new(config, Arc::clone(&storage));
        
        Ok(Self {
            device_id: device_id.to_string(),
            tracker,
            storage,
        })
    }

    /// 启动设备
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.tracker.set_vector_clock_enabled(true);
        self.tracker.start().await?;
        Ok(())
    }

    /// 停止设备
    pub async fn stop(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.tracker.stop().await?;
        Ok(())
    }

    /// 创建密码条目
    pub async fn create_password_entry(
        &self,
        entry_id: &str,
        title: &str,
        username: &str,
        password: &str,
    ) -> Result<VectorClock, Box<dyn std::error::Error>> {
        let data = serde_json::json!({
            "title": title,
            "username": username,
            "password": password,
            "created_at": Utc::now().to_rfc3339(),
            "updated_at": Utc::now().to_rfc3339()
        });

        let vector_clock = self.tracker.record_vector_change(
            "password_entries",
            entry_id,
            SyncRecordType::Create,
            Some(&data.to_string()),
        ).await?;

        println!("🔐 设备 {} 创建密码条目: {} (向量时钟: {:?})", 
                 self.device_id, title, vector_clock);

        Ok(vector_clock)
    }

    /// 更新密码条目
    pub async fn update_password_entry(
        &self,
        entry_id: &str,
        title: &str,
        username: &str,
        password: &str,
    ) -> Result<VectorClock, Box<dyn std::error::Error>> {
        let data = serde_json::json!({
            "title": title,
            "username": username,
            "password": password,
            "updated_at": Utc::now().to_rfc3339()
        });

        let vector_clock = self.tracker.record_vector_change(
            "password_entries",
            entry_id,
            SyncRecordType::Update,
            Some(&data.to_string()),
        ).await?;

        println!("📝 设备 {} 更新密码条目: {} (向量时钟: {:?})", 
                 self.device_id, title, vector_clock);

        Ok(vector_clock)
    }

    /// 删除密码条目
    pub async fn delete_password_entry(
        &self,
        entry_id: &str,
    ) -> Result<VectorClock, Box<dyn std::error::Error>> {
        let vector_clock = self.tracker.record_vector_change(
            "password_entries",
            entry_id,
            SyncRecordType::Delete,
            None,
        ).await?;

        println!("🗑️ 设备 {} 删除密码条目: {} (向量时钟: {:?})", 
                 self.device_id, entry_id, vector_clock);

        Ok(vector_clock)
    }

    /// 同步其他设备的向量时钟
    pub async fn sync_with_device(&self, other_clock: &VectorClock) -> VectorClock {
        let updated_clock = self.tracker.update_vector_clock(other_clock).await;
        
        println!("🔄 设备 {} 同步向量时钟: {:?}", 
                 self.device_id, updated_clock);
        
        updated_clock
    }

    /// 获取当前向量时钟
    pub fn get_vector_clock(&self) -> VectorClock {
        self.tracker.get_current_vector_clock()
    }

    /// 获取未同步的记录
    pub async fn get_unsynced_records(&self) -> Result<Vec<VectorSyncRecord>, Box<dyn std::error::Error>> {
        let records = self.storage.get_unsynced_vector_records(None).await?;
        Ok(records)
    }
}

/// 多设备同步场景演示
pub async fn demonstrate_multi_device_sync() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始多设备向量时钟同步演示");
    println!("{}", "=".repeat(60));

    // 创建三个设备：手机、电脑、平板
    let phone = SimulatedDevice::new("phone").await?;
    let laptop = SimulatedDevice::new("laptop").await?;
    let tablet = SimulatedDevice::new("tablet").await?;

    // 启动所有设备
    phone.start().await?;
    laptop.start().await?;
    tablet.start().await?;

    println!("\n📱 场景1: 设备独立操作（并发）");
    println!("{}", "-".repeat(40));

    // 手机创建Gmail密码
    let phone_clock1 = phone.create_password_entry(
        "gmail_001",
        "Gmail",
        "<EMAIL>",
        "phone_password_123"
    ).await?;

    // 电脑创建GitHub密码（并发）
    let laptop_clock1 = laptop.create_password_entry(
        "github_001",
        "GitHub",
        "developer",
        "laptop_secure_456"
    ).await?;

    // 平板创建Twitter密码（并发）
    let tablet_clock1 = tablet.create_password_entry(
        "twitter_001",
        "Twitter",
        "@myhandle",
        "tablet_pass_789"
    ).await?;

    // 检查并发关系
    println!("\n🔍 检查因果关系:");
    println!("手机 vs 电脑: {:?}", phone_clock1.compare(&laptop_clock1));
    println!("手机 vs 平板: {:?}", phone_clock1.compare(&tablet_clock1));
    println!("电脑 vs 平板: {:?}", laptop_clock1.compare(&tablet_clock1));

    println!("\n📱 场景2: 设备间同步");
    println!("{}", "-".repeat(40));

    // 手机接收电脑的更新
    let phone_clock2 = phone.sync_with_device(&laptop_clock1).await;
    
    // 手机再次操作（现在有因果关系）
    let phone_clock3 = phone.update_password_entry(
        "gmail_001",
        "Gmail",
        "<EMAIL>",
        "updated_from_phone"
    ).await?;

    // 电脑接收手机的更新
    let laptop_clock2 = laptop.sync_with_device(&phone_clock3).await;

    println!("\n🔍 检查同步后的因果关系:");
    println!("手机更新 vs 电脑同步: {:?}", phone_clock3.compare(&laptop_clock2));

    println!("\n📱 场景3: 复杂的多设备同步");
    println!("{}", "-".repeat(40));

    // 平板同步所有设备的状态
    let tablet_clock2 = tablet.sync_with_device(&phone_clock3).await;
    let tablet_clock3 = tablet.sync_with_device(&laptop_clock2).await;

    // 平板创建新条目
    let tablet_clock4 = tablet.create_password_entry(
        "facebook_001",
        "Facebook",
        "socialuser",
        "tablet_social_pass"
    ).await?;

    // 所有设备同步到最新状态
    let phone_final = phone.sync_with_device(&tablet_clock4).await;
    let laptop_final = laptop.sync_with_device(&tablet_clock4).await;

    println!("\n📊 最终状态:");
    println!("手机向量时钟: {:?}", phone_final);
    println!("电脑向量时钟: {:?}", laptop_final);
    println!("平板向量时钟: {:?}", tablet_clock4);

    println!("\n📱 场景4: 冲突检测");
    println!("{}", "-".repeat(40));

    // 创建冲突场景：两个设备同时修改同一条目
    let phone_conflict = phone.update_password_entry(
        "gmail_001",
        "Gmail",
        "<EMAIL>",
        "phone_conflict_version"
    ).await?;

    let laptop_conflict = laptop.update_password_entry(
        "gmail_001",
        "Gmail",
        "<EMAIL>",
        "laptop_conflict_version"
    ).await?;

    println!("🔍 冲突检测:");
    println!("手机冲突版本 vs 电脑冲突版本: {:?}", 
             phone_conflict.compare(&laptop_conflict));

    if phone_conflict.is_concurrent(&laptop_conflict) {
        println!("⚠️ 检测到并发冲突！需要冲突解决策略");
        
        // 使用向量时钟的总和作为tie-breaker
        if phone_conflict.sum() > laptop_conflict.sum() {
            println!("📱 选择手机版本（向量时钟总和更大）");
        } else {
            println!("💻 选择电脑版本（向量时钟总和更大）");
        }
    }

    println!("\n📱 场景5: 网络分区恢复");
    println!("{}", "-".repeat(40));

    // 模拟网络分区：手机和电脑分别在不同的网络分区
    println!("🌐 模拟网络分区...");
    
    // 分区1：手机独立操作
    let phone_partition1 = phone.create_password_entry(
        "bank_001",
        "Bank Account",
        "account123",
        "phone_bank_pass"
    ).await?;

    let phone_partition2 = phone.update_password_entry(
        "bank_001",
        "Bank Account",
        "account123",
        "phone_updated_bank_pass"
    ).await?;

    // 分区2：电脑独立操作
    let laptop_partition1 = laptop.create_password_entry(
        "work_001",
        "Work Email",
        "<EMAIL>",
        "laptop_work_pass"
    ).await?;

    let laptop_partition2 = laptop.update_password_entry(
        "work_001",
        "Work Email",
        "<EMAIL>",
        "laptop_updated_work_pass"
    ).await?;

    println!("🔗 网络分区恢复，开始同步...");
    
    // 分区恢复后的同步
    let phone_recovered = phone.sync_with_device(&laptop_partition2).await;
    let laptop_recovered = laptop.sync_with_device(&phone_partition2).await;

    println!("📊 分区恢复后的状态:");
    println!("手机恢复状态: {:?}", phone_recovered);
    println!("电脑恢复状态: {:?}", laptop_recovered);

    // 显示未同步记录统计
    println!("\n📈 同步统计:");
    let phone_unsynced = phone.get_unsynced_records().await?;
    let laptop_unsynced = laptop.get_unsynced_records().await?;
    let tablet_unsynced = tablet.get_unsynced_records().await?;

    println!("手机未同步记录: {} 条", phone_unsynced.len());
    println!("电脑未同步记录: {} 条", laptop_unsynced.len());
    println!("平板未同步记录: {} 条", tablet_unsynced.len());

    // 停止所有设备
    phone.stop().await?;
    laptop.stop().await?;
    tablet.stop().await?;

    println!("\n✅ 多设备向量时钟同步演示完成！");
    println!("{}", "=".repeat(60));

    Ok(())
}

/// 密码管理器特定场景演示
pub async fn demonstrate_password_manager_scenarios() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔐 密码管理器向量时钟场景演示");
    println!("{}", "=".repeat(60));

    let device1 = SimulatedDevice::new("primary_device").await?;
    let device2 = SimulatedDevice::new("secondary_device").await?;

    device1.start().await?;
    device2.start().await?;

    println!("\n📱 场景1: 密码条目的生命周期");
    println!("{}", "-".repeat(40));

    // 创建 -> 更新 -> 删除的完整生命周期
    let create_clock = device1.create_password_entry(
        "lifecycle_test",
        "Test Service",
        "testuser",
        "initial_password"
    ).await?;

    let update_clock = device1.update_password_entry(
        "lifecycle_test",
        "Test Service",
        "testuser",
        "updated_password"
    ).await?;

    let delete_clock = device1.delete_password_entry("lifecycle_test").await?;

    println!("生命周期因果链:");
    println!("创建 -> 更新: {:?}", create_clock.compare(&update_clock));
    println!("更新 -> 删除: {:?}", update_clock.compare(&delete_clock));
    println!("创建 -> 删除: {:?}", create_clock.compare(&delete_clock));

    println!("\n📱 场景2: 多设备密码同步");
    println!("{}", "-".repeat(40));

    // 设备1创建密码
    let d1_create = device1.create_password_entry(
        "shared_account",
        "Shared Service",
        "shareduser",
        "device1_password"
    ).await?;

    // 设备2同步并更新
    device2.sync_with_device(&d1_create).await;
    let d2_update = device2.update_password_entry(
        "shared_account",
        "Shared Service",
        "shareduser",
        "device2_updated_password"
    ).await?;

    // 设备1同步设备2的更新
    let d1_sync = device1.sync_with_device(&d2_update).await;

    println!("多设备同步链:");
    println!("设备1创建 -> 设备2更新: {:?}", d1_create.compare(&d2_update));
    println!("设备2更新 -> 设备1同步: {:?}", d2_update.compare(&d1_sync));

    println!("\n📱 场景3: 密码冲突解决");
    println!("{}", "-".repeat(40));

    // 两个设备同时更新同一个密码（模拟离线后同时修改）
    let d1_conflict = device1.update_password_entry(
        "conflict_test",
        "Conflict Service",
        "conflictuser",
        "device1_version"
    ).await?;

    let d2_conflict = device2.update_password_entry(
        "conflict_test",
        "Conflict Service",
        "conflictuser",
        "device2_version"
    ).await?;

    if d1_conflict.is_concurrent(&d2_conflict) {
        println!("⚠️ 检测到密码冲突！");
        println!("设备1版本时钟: {:?}", d1_conflict);
        println!("设备2版本时钟: {:?}", d2_conflict);
        
        // 密码管理器可以使用多种策略：
        // 1. 最新时间戳
        // 2. 向量时钟总和
        // 3. 用户手动选择
        // 4. 保留两个版本供用户选择
        
        println!("💡 可用的冲突解决策略:");
        println!("   1. 使用向量时钟总和较大的版本");
        println!("   2. 提示用户手动选择");
        println!("   3. 创建冲突副本供用户审查");
    }

    device1.stop().await?;
    device2.stop().await?;

    println!("\n✅ 密码管理器场景演示完成！");
    println!("{}", "=".repeat(60));

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_simulated_device_creation() {
        let device = SimulatedDevice::new("test_device").await.unwrap();
        assert_eq!(device.device_id, "test_device");
    }

    #[tokio::test]
    async fn test_device_operations() {
        let device = SimulatedDevice::new("test_device").await.unwrap();
        device.start().await.unwrap();

        // 测试创建密码条目
        let clock1 = device.create_password_entry(
            "test_entry",
            "Test Service",
            "testuser",
            "testpass"
        ).await.unwrap();

        assert_eq!(clock1.get_clock("test_device"), 1);

        // 测试更新密码条目
        let clock2 = device.update_password_entry(
            "test_entry",
            "Test Service",
            "testuser",
            "newpass"
        ).await.unwrap();

        assert_eq!(clock2.get_clock("test_device"), 2);

        device.stop().await.unwrap();
    }

    #[tokio::test]
    async fn test_multi_device_sync() {
        let device1 = SimulatedDevice::new("device1").await.unwrap();
        let device2 = SimulatedDevice::new("device2").await.unwrap();

        device1.start().await.unwrap();
        device2.start().await.unwrap();

        // 设备1创建条目
        let clock1 = device1.create_password_entry(
            "sync_test",
            "Sync Test",
            "user",
            "pass"
        ).await.unwrap();

        // 设备2同步
        let clock2 = device2.sync_with_device(&clock1).await;

        // 验证因果关系
        assert!(clock1.happens_before(&clock2));

        device1.stop().await.unwrap();
        device2.stop().await.unwrap();
    }

    #[tokio::test]
    async fn test_conflict_detection() {
        let device1 = SimulatedDevice::new("device1").await.unwrap();
        let device2 = SimulatedDevice::new("device2").await.unwrap();

        device1.start().await.unwrap();
        device2.start().await.unwrap();

        // 两个设备并发操作
        let clock1 = device1.create_password_entry(
            "conflict_test",
            "Conflict Test",
            "user1",
            "pass1"
        ).await.unwrap();

        let clock2 = device2.create_password_entry(
            "conflict_test",
            "Conflict Test",
            "user2",
            "pass2"
        ).await.unwrap();

        // 验证并发关系
        assert!(clock1.is_concurrent(&clock2));

        device1.stop().await.unwrap();
        device2.stop().await.unwrap();
    }
} 