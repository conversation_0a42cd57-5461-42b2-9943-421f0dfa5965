/// 托盘配置模块
///
/// 提供托盘的配置选项和建造者模式的配置构建器，
/// 支持配置验证和序列化。
use crate::tray::{TrayError, TrayResult, DEFAULT_TOOLTIP};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 托盘配置
///
/// 包含托盘的所有配置选项，如标题、工具提示、图标路径等。
/// 使用建造者模式来构建配置。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayConfig {
    /// 托盘标题（在某些平台上显示）
    pub title: String,

    /// 托盘工具提示文本
    pub tooltip: String,

    /// 托盘图标路径（相对于应用资源目录）
    pub icon_path: Option<String>,

    /// 是否在左键点击时显示菜单
    pub show_menu_on_left_click: bool,

    /// 是否在应用启动时自动显示托盘
    pub auto_show_on_startup: bool,

    /// 图标目录路径（用于查找图标文件）
    pub icon_directory: Option<PathBuf>,

    /// 平台特定配置
    pub platform_config: PlatformConfig,
}

/// 平台特定配置
///
/// 针对不同平台的特殊配置选项。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformConfig {
    /// Windows 特定配置
    #[cfg(target_os = "windows")]
    pub windows: WindowsConfig,

    /// macOS 特定配置
    #[cfg(target_os = "macos")]
    pub macos: MacOSConfig,

    /// Linux 特定配置
    #[cfg(target_os = "linux")]
    pub linux: LinuxConfig,
}

/// Windows 平台配置
#[cfg(target_os = "windows")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowsConfig {
    /// 是否启用气球提示
    pub enable_balloon_tips: bool,

    /// 是否使用系统主题图标
    pub use_system_theme_icon: bool,
}

/// macOS 平台配置
#[cfg(target_os = "macos")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MacOSConfig {
    /// 是否使用模板图标（自动适应深色/浅色主题）
    pub use_template_icon: bool,

    /// 是否显示在 Dock 中
    pub show_in_dock: bool,
}

/// Linux 平台配置
#[cfg(target_os = "linux")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LinuxConfig {
    /// 是否使用 AppIndicator
    pub use_app_indicator: bool,

    /// 桌面环境类型提示
    pub desktop_environment: Option<String>,
}

impl Default for TrayConfig {
    fn default() -> Self {
        Self {
            title: "Secure Password".to_string(),
            tooltip: DEFAULT_TOOLTIP.to_string(),
            icon_path: Some("icons/tray-icon.png".to_string()),
            show_menu_on_left_click: false,
            auto_show_on_startup: true,
            icon_directory: None,
            platform_config: PlatformConfig::default(),
        }
    }
}

impl Default for PlatformConfig {
    fn default() -> Self {
        Self {
            #[cfg(target_os = "windows")]
            windows: WindowsConfig::default(),
            #[cfg(target_os = "macos")]
            macos: MacOSConfig::default(),
            #[cfg(target_os = "linux")]
            linux: LinuxConfig::default(),
        }
    }
}

#[cfg(target_os = "windows")]
impl Default for WindowsConfig {
    fn default() -> Self {
        Self {
            enable_balloon_tips: true,
            use_system_theme_icon: true,
        }
    }
}

#[cfg(target_os = "macos")]
impl Default for MacOSConfig {
    fn default() -> Self {
        Self {
            use_template_icon: true,
            show_in_dock: false,
        }
    }
}

#[cfg(target_os = "linux")]
impl Default for LinuxConfig {
    fn default() -> Self {
        Self {
            use_app_indicator: true,
            desktop_environment: None,
        }
    }
}

/// 托盘配置建造者
///
/// 使用建造者模式来构建托盘配置，提供流畅的API。
#[derive(Debug, Default)]
pub struct TrayConfigBuilder {
    config: TrayConfig,
}

impl TrayConfigBuilder {
    /// 创建新的配置建造者
    ///
    /// # 返回值
    ///
    /// 返回新的配置建造者实例
    pub fn new() -> Self {
        Self {
            config: TrayConfig::default(),
        }
    }

    /// 设置托盘标题
    ///
    /// # 参数
    ///
    /// * `title` - 托盘标题
    pub fn title(mut self, title: impl Into<String>) -> Self {
        self.config.title = title.into();
        self
    }

    /// 设置工具提示
    ///
    /// # 参数
    ///
    /// * `tooltip` - 工具提示文本
    pub fn tooltip(mut self, tooltip: impl Into<String>) -> Self {
        self.config.tooltip = tooltip.into();
        self
    }

    /// 设置图标路径
    ///
    /// # 参数
    ///
    /// * `path` - 图标文件路径
    pub fn icon_path(mut self, path: impl Into<String>) -> Self {
        self.config.icon_path = Some(path.into());
        self
    }

    /// 设置是否在左键点击时显示菜单
    ///
    /// # 参数
    ///
    /// * `show` - 是否显示菜单
    pub fn show_menu_on_left_click(mut self, show: bool) -> Self {
        self.config.show_menu_on_left_click = show;
        self
    }

    /// 设置是否在右键点击时显示菜单
    ///
    /// # 参数
    ///
    /// * `_show` - 是否显示菜单（保留参数以保持API一致性）
    ///
    /// # 注意
    ///
    /// 右键点击显示菜单是默认行为，这个方法主要是为了API一致性。
    /// 在大多数平台上，右键点击托盘图标都会显示菜单。
    #[allow(unused_variables)]
    pub fn show_menu_on_right_click(self, _show: bool) -> Self {
        // 注意：右键点击显示菜单是默认行为，这里只是为了API一致性
        // 实际上大多数平台都是右键显示菜单
        self
    }

    /// 设置是否在应用启动时自动显示托盘
    ///
    /// # 参数
    ///
    /// * `auto_show` - 是否自动显示
    pub fn auto_show_on_startup(mut self, auto_show: bool) -> Self {
        self.config.auto_show_on_startup = auto_show;
        self
    }

    /// 设置图标目录
    ///
    /// # 参数
    ///
    /// * `path` - 图标目录路径
    pub fn icon_directory<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.config.icon_directory = Some(path.into());
        self
    }

    /// 设置平台特定配置
    ///
    /// # 参数
    ///
    /// * `platform_config` - 平台配置
    pub fn platform_config(mut self, platform_config: PlatformConfig) -> Self {
        self.config.platform_config = platform_config;
        self
    }

    /// Windows 平台特定配置
    #[cfg(target_os = "windows")]
    pub fn windows_config(mut self, windows_config: WindowsConfig) -> Self {
        self.config.platform_config.windows = windows_config;
        self
    }

    /// macOS 平台特定配置
    #[cfg(target_os = "macos")]
    pub fn macos_config(mut self, macos_config: MacOSConfig) -> Self {
        self.config.platform_config.macos = macos_config;
        self
    }

    /// Linux 平台特定配置
    #[cfg(target_os = "linux")]
    pub fn linux_config(mut self, linux_config: LinuxConfig) -> Self {
        self.config.platform_config.linux = linux_config;
        self
    }

    /// 构建配置
    ///
    /// # 返回值
    ///
    /// 返回验证后的托盘配置
    pub fn build(self) -> TrayResult<TrayConfig> {
        self.validate_config()?;
        Ok(self.config)
    }

    /// 验证配置的有效性
    ///
    /// # 返回值
    ///
    /// 如果配置有效则返回 Ok(())，否则返回错误
    fn validate_config(&self) -> TrayResult<()> {
        // 验证标题不为空
        if self.config.title.trim().is_empty() {
            return Err(TrayError::configuration_error("title", "标题不能为空"));
        }

        // 验证工具提示不为空
        if self.config.tooltip.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "tooltip",
                "工具提示不能为空",
            ));
        }

        Ok(())
    }
}

impl TrayConfig {
    /// 创建配置建造者
    ///
    /// # 返回值
    ///
    /// 返回新的配置建造者实例
    pub fn builder() -> TrayConfigBuilder {
        TrayConfigBuilder::new()
    }

    /// 从 JSON 字符串加载配置
    ///
    /// # 参数
    ///
    /// * `json` - JSON 字符串
    ///
    /// # 返回值
    ///
    /// 返回解析后的配置
    pub fn from_json(json: &str) -> TrayResult<Self> {
        serde_json::from_str(json).map_err(TrayError::from)
    }

    /// 将配置序列化为 JSON 字符串
    ///
    /// # 返回值
    ///
    /// 返回 JSON 字符串
    pub fn to_json(&self) -> TrayResult<String> {
        serde_json::to_string_pretty(self).map_err(TrayError::from)
    }

    /// 验证配置的有效性
    ///
    /// # 返回值
    ///
    /// 如果配置有效则返回 Ok(())，否则返回错误
    pub fn validate(&self) -> TrayResult<()> {
        if self.title.trim().is_empty() {
            return Err(TrayError::configuration_error("title", "标题不能为空"));
        }

        if self.tooltip.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "tooltip",
                "工具提示不能为空",
            ));
        }

        Ok(())
    }

    /// 获取当前平台的配置
    ///
    /// # 返回值
    ///
    /// 返回平台特定配置的引用
    pub fn get_current_platform_config(&self) -> &PlatformConfig {
        &self.platform_config
    }

    /// 检查是否应该在指定的鼠标按钮点击时显示菜单
    ///
    /// # 参数
    ///
    /// * `is_left_click` - 是否为左键点击
    ///
    /// # 返回值
    ///
    /// 如果应该显示菜单则返回 true
    pub fn should_show_menu_on_click(&self, is_left_click: bool) -> bool {
        if is_left_click {
            self.show_menu_on_left_click
        } else {
            true // 右键默认显示菜单
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = TrayConfig::default();
        assert_eq!(config.title, "Secure Password");
        assert_eq!(config.tooltip, DEFAULT_TOOLTIP);
        assert!(config.icon_path.is_some());
        assert!(!config.show_menu_on_left_click);
        assert!(config.auto_show_on_startup);
    }

    #[test]
    fn test_config_builder() {
        let config = TrayConfig::builder()
            .title("测试应用")
            .tooltip("这是一个测试应用")
            .icon_path("test-icon.png")
            .show_menu_on_left_click(true)
            .build()
            .unwrap();

        assert_eq!(config.title, "测试应用");
        assert_eq!(config.tooltip, "这是一个测试应用");
        assert_eq!(config.icon_path, Some("test-icon.png".to_string()));
        assert!(config.show_menu_on_left_click);
    }

    #[test]
    fn test_config_validation() {
        // 测试空标题
        let result = TrayConfig::builder().title("").build();
        assert!(result.is_err());

        // 测试空工具提示
        let result = TrayConfig::builder().title("测试").tooltip("").build();
        assert!(result.is_err());

        // 测试有效配置
        let result = TrayConfig::builder()
            .title("测试")
            .tooltip("测试工具提示")
            .build();
        assert!(result.is_ok());
    }

    #[test]
    fn test_config_serialization() {
        let config = TrayConfig::builder()
            .title("测试应用")
            .tooltip("测试工具提示")
            .build()
            .unwrap();

        let json = config.to_json().unwrap();
        let deserialized_config = TrayConfig::from_json(&json).unwrap();

        assert_eq!(config.title, deserialized_config.title);
        assert_eq!(config.tooltip, deserialized_config.tooltip);
    }

    #[test]
    fn test_should_show_menu_on_click() {
        let config = TrayConfig::builder()
            .title("测试")
            .show_menu_on_left_click(true)
            .build()
            .unwrap();

        assert!(config.should_show_menu_on_click(true)); // 左键
        assert!(config.should_show_menu_on_click(false)); // 右键

        let config2 = TrayConfig::builder()
            .title("测试")
            .show_menu_on_left_click(false)
            .build()
            .unwrap();

        assert!(!config2.should_show_menu_on_click(true)); // 左键
        assert!(config2.should_show_menu_on_click(false)); // 右键
    }
}
