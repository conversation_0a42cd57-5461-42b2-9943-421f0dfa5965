# 新托盘模块 (基于 Tauri 官方 API)

这是一个基于 Tauri 2.x 官方 API 重新实现的系统托盘模块，提供了简洁、可靠的托盘功能。

## 主要特性

- ✅ **基于 Tauri 官方 API**: 使用 Tauri 2.x 官方系统托盘 API，确保兼容性和稳定性
- ✅ **模块化设计**: 清晰的模块结构，便于维护和扩展
- ✅ **类型安全**: 完善的 TypeScript 类型定义和 Rust 类型系统
- ✅ **错误处理**: 使用 `thiserror` 提供精确的错误类型和信息
- ✅ **跨平台支持**: Windows、macOS、Linux 平台兼容
- ✅ **建造者模式**: 流畅的配置构建 API
- ✅ **完整测试**: 单元测试和集成测试覆盖

## 快速开始

### 1. 基本用法

```rust
use crate::tray::{TrayManager, TrayConfig, TrayMenu, TrayMenuItem, PredefinedMenuItemType};

// 创建配置
let config = TrayConfig::builder()
    .title("Secure Password")
    .tooltip("密码管理器")
    .icon_path("icons/tray-icon.png")
    .build()?;

// 创建菜单
let menu = TrayMenu::builder()
    .add_normal_item("show", "显示窗口")?
    .add_separator()?
    .add_checkbox("auto_start", "开机自启", false)?
    .add_separator()?
    .add_predefined(PredefinedMenuItemType::Quit)?
    .build()?;

// 创建并初始化托盘管理器
let mut manager = TrayManager::new(config)?;
manager.set_menu(menu)?;
manager.initialize(&app_handle).await?;
manager.show()?;
```

### 2. 使用默认配置

```rust
use crate::tray::{TrayManager, TrayConfig};

// 使用默认配置
let config = TrayConfig::default();
let mut manager = TrayManager::new(config)?;
manager.initialize(&app_handle).await?;
```

### 3. 处理菜单事件

```rust
// 在 Tauri 的主函数中设置菜单事件处理
fn main() {
    tauri::Builder::default()
        .on_menu_event(|app, event| {
            match event.menu_item_id() {
                "show" => {
                    // 显示主窗口
                    if let Some(window) = app.get_window("main") {
                        window.show().unwrap();
                        window.set_focus().unwrap();
                    }
                }
                "quit" => {
                    // 退出应用
                    app.exit(0);
                }
                _ => {}
            }
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

## 模块结构

```
src/tray/
├── mod.rs              # 主模块文件，导出公共接口
├── config.rs           # 托盘配置和建造者
├── errors.rs           # 错误类型定义
├── events.rs           # 事件类型和分发器
├── handlers.rs         # 事件处理器实现
├── manager.rs          # 托盘管理器核心
├── menu.rs             # 菜单创建和管理
├── utils.rs            # 工具函数
└── docs/
    ├── README.md       # 本文档
    ├── api.md          # API 参考
    └── examples.md     # 使用示例
```

## API 概览

### 核心类型

- `TrayManager`: 托盘管理器，负责托盘的创建、配置和生命周期管理
- `TrayConfig`: 托盘配置，包含标题、工具提示、图标路径等
- `TrayMenu`: 托盘菜单，支持普通菜单项、分隔符、复选框和预定义菜单项
- `TrayMenuItem`: 菜单项，支持多种类型和属性

### 错误处理

- `TrayError`: 统一的错误类型，使用 `thiserror` 提供详细的错误信息
- `TrayResult<T>`: 标准的结果类型 `Result<T, TrayError>`

### 工具函数

- `is_system_tray_supported()`: 检查系统是否支持托盘
- `get_platform_name()`: 获取当前平台名称
- `validate_icon_path()`: 验证图标路径
- `get_recommended_icon_size()`: 获取推荐的图标尺寸

## 与原模块的差异

| 功能 | 原模块 | 新模块 |
|------|--------|--------|
| API 基础 | 自定义实现 | Tauri 官方 API |
| 事件系统 | 复杂的可插拔架构 | 简化的事件处理 |
| 图标管理 | 复杂的图标缓存系统 | 简化的图标设置 |
| 状态管理 | 完整的状态跟踪 | 基本状态管理 |
| 配置系统 | 丰富的配置选项 | 精简的核心配置 |
| 测试覆盖 | 部分测试 | 完整测试覆盖 |

## 平台兼容性

### Windows
- ✅ 系统托盘图标显示
- ✅ 右键菜单
- ✅ 左键点击事件
- ✅ 工具提示显示

### macOS
- ✅ 菜单栏图标显示
- ✅ 下拉菜单
- ✅ 模板图标支持
- ✅ 工具提示显示

### Linux
- ✅ 系统托盘图标显示（需要桌面环境支持）
- ✅ 右键菜单
- ✅ 应用指示器支持
- ⚠️ 部分桌面环境可能有限制

## 故障排除

### 常见问题

1. **托盘图标不显示**
   - 检查系统是否支持托盘：`is_system_tray_supported()`
   - 确认图标文件路径正确
   - 验证图标格式是否支持

2. **菜单事件不响应**
   - 确认已正确设置菜单事件处理器
   - 检查菜单项ID是否匹配
   - 验证事件处理逻辑

3. **Linux 平台问题**
   - 确认桌面环境支持系统托盘
   - 检查环境变量 `DISPLAY` 或 `WAYLAND_DISPLAY`
   - 考虑使用应用指示器

### 调试技巧

1. 启用详细日志输出
2. 使用 `validate_platform_config()` 检查平台支持
3. 检查错误类型和严重程度
4. 使用推荐的图标尺寸

## 迁移指南

如果您正在从原模块迁移到新模块，请参考以下步骤：

1. **更新导入**
   ```rust
   // 原模块
   use crate::custom_tray::*;
   
   // 新模块
   use crate::tray::*;
   ```

2. **简化配置**
   - 移除复杂的平台特定配置
   - 使用建造者模式创建配置

3. **更新事件处理**
   - 使用 Tauri 标准菜单事件处理
   - 简化事件处理器注册

4. **测试功能**
   - 验证所有托盘功能正常工作
   - 测试跨平台兼容性

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 编写测试
4. 提交 Pull Request

## 许可证

本项目采用与主项目相同的许可证。 