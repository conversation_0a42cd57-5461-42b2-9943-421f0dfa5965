# 托盘菜单点击事件修复文档

## 问题描述

在 `tray_integration.rs` 中，托盘菜单的"显示主窗口"和"退出"选项点击后没有触发对应的事件处理。

## 问题原因

在 `TrayManager::initialize` 方法中，事件处理器是空的：

```rust
// 设置事件处理器
tray_builder = tray_builder.on_tray_icon_event(move |_tray, _event| {
    // 基本的事件处理  <-- 这里是空的！
});

// 设置菜单事件处理器
if self.menu.is_some() {
    tray_builder = tray_builder.on_menu_event(move |_app, _event| {
        // 基本的菜单事件处理  <-- 这里也是空的！
    });
}
```

## 修复方案

### 1. 添加主窗口标签支持

在 `TrayManager` 结构体中添加 `main_window_label` 字段：

```rust
pub struct TrayManager {
    /// 托盘配置
    config: TrayConfig,
    /// 托盘图标实例
    tray_icon: Option<TrayIcon<tauri::Wry>>,
    /// 托盘菜单
    menu: Option<TrayMenu>,
    /// 是否已初始化
    initialized: bool,
    /// 主窗口标签
    main_window_label: String,
}
```

### 2. 实现事件处理逻辑

在菜单事件处理器中实现具体的事件处理逻辑：

```rust
// 设置菜单事件处理器
if self.menu.is_some() {
    let main_window_label = self.main_window_label.clone();
    tray_builder = tray_builder.on_menu_event(move |app, event| {
        // 处理菜单点击事件
        info!("菜单事件: {:?}", event);
        
        // 获取菜单项ID
        let menu_id = event.id().as_ref();
        info!("处理菜单项点击: {}", menu_id);
        
        match menu_id {
            "show_window" => {
                // 显示主窗口
                info!("显示主窗口: {}", main_window_label);
                if let Some(window) = app.get_webview_window(&main_window_label) {
                    if let Err(e) = window.unminimize() {
                        warn!("恢复窗口失败: {}", e);
                    }
                    if let Err(e) = window.show() {
                        error!("显示窗口失败: {}", e);
                    } else {
                        info!("窗口已显示");
                    }
                    if let Err(e) = window.set_focus() {
                        warn!("设置窗口焦点失败: {}", e);
                    }
                } else {
                    error!("找不到主窗口: {}", main_window_label);
                }
            }
            "quit" => {
                // 退出应用
                info!("用户请求退出应用");
                app.exit(0);
            }
            _ => {
                warn!("未知的菜单项ID: {}", menu_id);
            }
        }
    });
}
```

### 3. 更新构造函数

添加新的构造函数方法：

```rust
/// 创建新的托盘管理器
pub fn new(config: TrayConfig) -> TrayResult<Self> {
    Self::with_window_label(config, "main")
}

/// 创建新的托盘管理器，指定主窗口标签
pub fn with_window_label(config: TrayConfig, main_window_label: impl Into<String>) -> TrayResult<Self> {
    // 验证配置
    config.validate()?;

    Ok(Self {
        config,
        tray_icon: None,
        menu: None,
        initialized: false,
        main_window_label: main_window_label.into(),
    })
}
```

### 4. 更新托盘集成

在 `tray_integration.rs` 中使用新的构造函数：

```rust
// 创建托盘管理器（此时状态为 Initialized）
let tray_manager = TrayManager::with_window_label(config, &main_window_name).map_err(|e| {
    error!("创建托盘管理器失败: {}", e);
    e
})?;
```

## 修复效果

修复后，托盘菜单的功能如下：

1. **显示主窗口** (`show_window`):
   - 恢复最小化的窗口
   - 显示隐藏的窗口
   - 将窗口置于前台并获得焦点
   - 详细的错误处理和日志记录

2. **退出** (`quit`):
   - 完全退出应用程序
   - 记录退出操作

3. **日志记录**:
   - 所有操作都有详细的日志记录
   - 使用 `log` 宏而不是 `println!`
   - 不同级别的日志：`info`、`warn`、`error`

## 测试验证

修复后的代码通过了所有现有测试：

```bash
cargo test tray
# 结果：40 个测试全部通过
```

## 兼容性

- 保持向后兼容性
- 默认窗口标签为 "main"
- 支持自定义窗口标签
- 与现有的 `tray_integration.rs` 完全兼容

## 注意事项

1. 确保主窗口标签与实际窗口名称匹配
2. 日志级别需要正确配置才能看到调试信息
3. 在生产环境中可能需要调整日志级别 