/// 托盘模块错误处理
///
/// 定义了托盘操作中可能遇到的各种错误类型，
/// 使用 thiserror 提供清晰的错误信息和类型安全。
use thiserror::Error;

/// 托盘操作结果类型
pub type TrayResult<T> = Result<T, TrayError>;

/// 托盘错误类型
///
/// 涵盖了托盘操作中可能遇到的所有错误情况，
/// 每个错误都包含详细的上下文信息。
#[derive(Error, Debug)]
pub enum TrayError {
    /// 托盘初始化失败
    #[error("托盘初始化失败: {reason}")]
    InitializationFailed { reason: String },

    /// 托盘图标设置失败
    #[error("托盘图标设置失败: {reason}")]
    IconSetFailed { reason: String },

    /// 托盘菜单创建失败
    #[error("托盘菜单创建失败: {reason}")]
    MenuCreationFailed { reason: String },

    /// 托盘事件处理失败
    #[error("托盘事件处理失败: {event_type} - {reason}")]
    EventHandlingFailed { event_type: String, reason: String },

    /// 配置错误
    #[error("托盘配置错误: {field} - {reason}")]
    ConfigurationError { field: String, reason: String },

    /// 托盘不可用
    #[error("系统托盘在当前环境中不可用: {reason}")]
    TrayUnavailable { reason: String },

    /// 菜单项限制超出
    #[error("菜单项数量超出限制: {count} > {max_allowed}")]
    MenuItemLimitExceeded { count: usize, max_allowed: usize },

    /// 菜单项ID重复
    #[error("菜单项ID重复: {id}")]
    DuplicateMenuItemId { id: String },

    /// 菜单项未找到
    #[error("菜单项未找到: {id}")]
    MenuItemNotFound { id: String },

    /// 平台不支持的操作
    #[error("当前平台不支持操作: {operation}")]
    UnsupportedOperation { operation: String },

    /// Tauri 错误
    #[error("Tauri 错误: {0}")]
    TauriError(String),

    /// IO 错误
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),

    /// 序列化错误
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),

    /// 内部错误
    #[error("内部错误: {message}")]
    Internal { message: String },
}

impl From<tauri::Error> for TrayError {
    fn from(err: tauri::Error) -> Self {
        TrayError::TauriError(err.to_string())
    }
}

impl TrayError {
    /// 创建初始化失败错误
    ///
    /// # 参数
    ///
    /// * `reason` - 失败原因
    pub fn initialization_failed(reason: impl Into<String>) -> Self {
        Self::InitializationFailed {
            reason: reason.into(),
        }
    }

    /// 创建图标设置失败错误
    ///
    /// # 参数
    ///
    /// * `reason` - 失败原因
    pub fn icon_set_failed(reason: impl Into<String>) -> Self {
        Self::IconSetFailed {
            reason: reason.into(),
        }
    }

    /// 创建菜单创建失败错误
    ///
    /// # 参数
    ///
    /// * `reason` - 失败原因
    pub fn menu_creation_failed(reason: impl Into<String>) -> Self {
        Self::MenuCreationFailed {
            reason: reason.into(),
        }
    }

    /// 创建事件处理失败错误
    ///
    /// # 参数
    ///
    /// * `event_type` - 事件类型
    /// * `reason` - 失败原因
    pub fn event_handling_failed(event_type: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::EventHandlingFailed {
            event_type: event_type.into(),
            reason: reason.into(),
        }
    }

    /// 创建配置错误
    ///
    /// # 参数
    ///
    /// * `field` - 配置字段
    /// * `reason` - 错误原因
    pub fn configuration_error(field: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ConfigurationError {
            field: field.into(),
            reason: reason.into(),
        }
    }

    /// 创建内部错误
    ///
    /// # 参数
    ///
    /// * `message` - 错误消息
    pub fn internal(message: impl Into<String>) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }

    /// 创建平台不支持错误
    ///
    /// # 参数
    ///
    /// * `platform` - 平台名称
    /// * `feature` - 不支持的功能
    pub fn unsupported_platform(platform: impl Into<String>, feature: impl Into<String>) -> Self {
        Self::UnsupportedOperation {
            operation: format!("{} 在 {} 平台上", feature.into(), platform.into()),
        }
    }

    /// 检查是否为可恢复的错误
    ///
    /// # 返回值
    ///
    /// 如果错误可以恢复则返回 `true`
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::InitializationFailed { .. } => false,
            Self::TrayUnavailable { .. } => false,
            Self::UnsupportedOperation { .. } => false,
            Self::IconSetFailed { .. } => true,
            Self::MenuCreationFailed { .. } => true,
            Self::EventHandlingFailed { .. } => true,
            Self::ConfigurationError { .. } => true,
            Self::MenuItemLimitExceeded { .. } => true,
            Self::DuplicateMenuItemId { .. } => true,
            Self::MenuItemNotFound { .. } => true,
            Self::TauriError(_) => true,
            Self::IoError(_) => true,
            Self::SerializationError(_) => true,
            Self::Internal { .. } => false,
        }
    }

    /// 获取错误的严重程度
    ///
    /// # 返回值
    ///
    /// 返回错误的严重程度级别
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            Self::InitializationFailed { .. } => ErrorSeverity::Critical,
            Self::TrayUnavailable { .. } => ErrorSeverity::Critical,
            Self::UnsupportedOperation { .. } => ErrorSeverity::High,
            Self::IconSetFailed { .. } => ErrorSeverity::Medium,
            Self::MenuCreationFailed { .. } => ErrorSeverity::Medium,
            Self::EventHandlingFailed { .. } => ErrorSeverity::Medium,
            Self::ConfigurationError { .. } => ErrorSeverity::Medium,
            Self::MenuItemLimitExceeded { .. } => ErrorSeverity::Low,
            Self::DuplicateMenuItemId { .. } => ErrorSeverity::Low,
            Self::MenuItemNotFound { .. } => ErrorSeverity::Low,
            Self::TauriError(_) => ErrorSeverity::Medium,
            Self::IoError(_) => ErrorSeverity::Medium,
            Self::SerializationError(_) => ErrorSeverity::Low,
            Self::Internal { .. } => ErrorSeverity::Critical,
        }
    }
}

/// 错误严重程度
///
/// 用于分类错误的严重程度，帮助决定如何处理错误。
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    /// 低严重程度 - 不影响核心功能
    Low,
    /// 中等严重程度 - 影响部分功能
    Medium,
    /// 高严重程度 - 影响主要功能
    High,
    /// 严重 - 影响核心功能
    Critical,
}

impl std::fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Low => write!(f, "低"),
            Self::Medium => write!(f, "中"),
            Self::High => write!(f, "高"),
            Self::Critical => write!(f, "严重"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = TrayError::initialization_failed("测试原因");
        assert!(matches!(error, TrayError::InitializationFailed { .. }));
        assert!(!error.is_recoverable());
        assert_eq!(error.severity(), ErrorSeverity::Critical);
    }

    #[test]
    fn test_error_severity() {
        let critical_error = TrayError::initialization_failed("测试");
        let medium_error = TrayError::icon_set_failed("测试");
        let low_error = TrayError::DuplicateMenuItemId {
            id: "test".to_string(),
        };

        assert_eq!(critical_error.severity(), ErrorSeverity::Critical);
        assert_eq!(medium_error.severity(), ErrorSeverity::Medium);
        assert_eq!(low_error.severity(), ErrorSeverity::Low);
    }

    #[test]
    fn test_error_recoverability() {
        let non_recoverable = TrayError::initialization_failed("测试");
        let recoverable = TrayError::icon_set_failed("测试");

        assert!(!non_recoverable.is_recoverable());
        assert!(recoverable.is_recoverable());
    }

    #[test]
    fn test_error_display() {
        let error = TrayError::configuration_error("icon", "文件不存在");
        let error_string = error.to_string();
        assert!(error_string.contains("托盘配置错误"));
        assert!(error_string.contains("icon"));
        assert!(error_string.contains("文件不存在"));
    }
}
