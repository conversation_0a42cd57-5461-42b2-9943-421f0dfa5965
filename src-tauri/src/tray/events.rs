/// 托盘事件处理模块
///
/// 提供托盘事件的定义、处理和分发功能，
/// 支持可插拔的事件处理器架构。
use crate::tray::{TrayError, TrayResult};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::{Arc, Mutex},
};
use tauri::{tray::TrayIconEvent, AppHandle};

/// 托盘事件类型
///
/// 定义托盘可能产生的各种事件类型。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrayEventType {
    /// 托盘图标被点击
    Click {
        /// 点击类型（左键、右键、双击等）
        click_type: TrayClickType,
        /// 点击位置
        position: Option<TrayPosition>,
    },
    /// 菜单项被点击
    MenuItemClick {
        /// 菜单项ID
        item_id: String,
    },
    /// 托盘图标鼠标进入
    MouseEnter,
    /// 托盘图标鼠标离开
    MouseLeave,
    /// 托盘图标拖拽
    Drop {
        /// 拖拽的文件路径列表
        paths: Vec<String>,
    },
}

/// 托盘点击类型
///
/// 描述托盘图标的点击方式。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrayClickType {
    /// 左键单击
    LeftClick,
    /// 右键单击
    RightClick,
    /// 双击
    DoubleClick,
    /// 中键点击
    MiddleClick,
}

/// 托盘位置
///
/// 描述托盘相关操作的屏幕位置。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct TrayPosition {
    /// X 坐标
    pub x: i32,
    /// Y 坐标
    pub y: i32,
}

/// 托盘事件
///
/// 包含事件类型和相关上下文信息的完整事件对象。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayEvent {
    /// 事件类型
    pub event_type: TrayEventType,
    /// 事件发生时间戳（毫秒）
    pub timestamp: u64,
    /// 事件ID（用于跟踪和去重）
    pub event_id: String,
    /// 事件的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

impl TrayEvent {
    /// 创建新的托盘事件
    ///
    /// # 参数
    ///
    /// * `event_type` - 事件类型
    ///
    /// # 返回值
    ///
    /// 返回新的事件实例
    pub fn new(event_type: TrayEventType) -> Self {
        Self {
            event_type,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as u64,
            event_id: uuid::Uuid::new_v4().to_string(),
            data: HashMap::new(),
        }
    }

    /// 添加附加数据
    ///
    /// # 参数
    ///
    /// * `key` - 数据键
    /// * `value` - 数据值
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }

    /// 获取附加数据
    ///
    /// # 参数
    ///
    /// * `key` - 数据键
    ///
    /// # 返回值
    ///
    /// 返回数据值的引用
    pub fn get_data(&self, key: &str) -> Option<&serde_json::Value> {
        self.data.get(key)
    }

    /// 检查事件是否为点击事件
    ///
    /// # 返回值
    ///
    /// 如果是点击事件则返回 true
    pub fn is_click_event(&self) -> bool {
        matches!(self.event_type, TrayEventType::Click { .. })
    }

    /// 检查事件是否为菜单点击事件
    ///
    /// # 返回值
    ///
    /// 如果是菜单点击事件则返回 true
    pub fn is_menu_click_event(&self) -> bool {
        matches!(self.event_type, TrayEventType::MenuItemClick { .. })
    }

    /// 获取菜单项ID（如果是菜单点击事件）
    ///
    /// # 返回值
    ///
    /// 返回菜单项ID的引用
    pub fn get_menu_item_id(&self) -> Option<&str> {
        match &self.event_type {
            TrayEventType::MenuItemClick { item_id } => Some(item_id),
            _ => None,
        }
    }
}

/// 托盘事件处理器特质
///
/// 定义事件处理器的接口，支持可插拔的事件处理架构。
#[async_trait::async_trait]
pub trait TrayEventHandler: Send + Sync {
    /// 处理托盘事件
    ///
    /// # 参数
    ///
    /// * `event` - 要处理的事件
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果处理成功则返回 Ok(())，否则返回错误
    async fn handle_event(&self, event: &TrayEvent, app_handle: &AppHandle) -> TrayResult<()>;

    /// 获取处理器名称
    ///
    /// # 返回值
    ///
    /// 返回处理器的名称
    fn name(&self) -> &str;

    /// 检查是否应该处理指定类型的事件
    ///
    /// # 参数
    ///
    /// * `event_type` - 事件类型
    ///
    /// # 返回值
    ///
    /// 如果应该处理则返回 true
    fn should_handle(&self, event_type: &TrayEventType) -> bool;

    /// 获取处理器优先级（数字越小优先级越高）
    ///
    /// # 返回值
    ///
    /// 返回优先级数值
    fn priority(&self) -> u32 {
        100 // 默认优先级
    }
}

/// 托盘事件分发器
///
/// 负责接收托盘事件并分发给注册的处理器。
pub struct TrayEventDispatcher {
    /// 注册的事件处理器列表
    handlers: Arc<Mutex<Vec<Arc<dyn TrayEventHandler>>>>,
    /// 事件历史记录
    event_history: Arc<Mutex<Vec<TrayEvent>>>,
    /// 最大历史记录数量
    max_history_size: usize,
}

impl TrayEventDispatcher {
    /// 创建新的事件分发器
    ///
    /// # 返回值
    ///
    /// 返回新的分发器实例
    pub fn new() -> Self {
        Self {
            handlers: Arc::new(Mutex::new(Vec::new())),
            event_history: Arc::new(Mutex::new(Vec::new())),
            max_history_size: 100,
        }
    }

    /// 设置最大历史记录数量
    ///
    /// # 参数
    ///
    /// * `size` - 最大历史记录数量
    pub fn set_max_history_size(&mut self, size: usize) {
        self.max_history_size = size;
    }

    /// 注册事件处理器
    ///
    /// # 参数
    ///
    /// * `handler` - 要注册的处理器
    ///
    /// # 返回值
    ///
    /// 如果注册成功则返回 Ok(())，否则返回错误
    pub fn register_handler(&self, handler: Arc<dyn TrayEventHandler>) -> TrayResult<()> {
        let mut handlers = self
            .handlers
            .lock()
            .map_err(|_| TrayError::internal("无法获取处理器列表锁"))?;

        // 检查是否已经注册了同名的处理器
        for existing_handler in handlers.iter() {
            if existing_handler.name() == handler.name() {
                return Err(TrayError::configuration_error(
                    "handler_name",
                    format!("处理器 {} 已经注册", handler.name()),
                ));
            }
        }

        handlers.push(handler);

        // 按优先级排序
        handlers.sort_by(|a, b| a.priority().cmp(&b.priority()));

        Ok(())
    }

    /// 注销事件处理器
    ///
    /// # 参数
    ///
    /// * `name` - 处理器名称
    ///
    /// # 返回值
    ///
    /// 如果注销成功则返回 Ok(())，否则返回错误
    pub fn unregister_handler(&self, name: &str) -> TrayResult<()> {
        let mut handlers = self
            .handlers
            .lock()
            .map_err(|_| TrayError::internal("无法获取处理器列表锁"))?;

        let initial_len = handlers.len();
        handlers.retain(|handler| handler.name() != name);

        if handlers.len() == initial_len {
            return Err(TrayError::configuration_error(
                "handler_name",
                format!("未找到名为 {} 的处理器", name),
            ));
        }

        Ok(())
    }

    /// 分发事件给所有注册的处理器
    ///
    /// # 参数
    ///
    /// * `event` - 要分发的事件
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果分发成功则返回 Ok(())，否则返回错误
    pub async fn dispatch_event(&self, event: TrayEvent, app_handle: &AppHandle) -> TrayResult<()> {
        // 添加到历史记录
        self.add_to_history(event.clone())?;

        // 获取处理器列表
        let handlers = {
            let handlers_guard = self
                .handlers
                .lock()
                .map_err(|_| TrayError::internal("无法获取处理器列表锁"))?;
            handlers_guard.clone()
        };

        // 分发给所有应该处理此事件的处理器
        for handler in handlers.iter() {
            if handler.should_handle(&event.event_type) {
                if let Err(err) = handler.handle_event(&event, app_handle).await {
                    eprintln!("处理器 {} 处理事件失败: {}", handler.name(), err);
                    // 不中断其他处理器的处理
                }
            }
        }

        Ok(())
    }

    /// 从 Tauri 托盘事件创建内部事件
    ///
    /// # 参数
    ///
    /// * `tauri_event` - Tauri 托盘事件
    ///
    /// # 返回值
    ///
    /// 返回转换后的内部事件
    pub fn from_tauri_event(tauri_event: &TrayIconEvent) -> TrayEvent {
        match tauri_event {
            TrayIconEvent::Click {
                id: _,
                position,
                rect: _,
                button,
                button_state: _,
            } => {
                let click_type = match button {
                    tauri::tray::MouseButton::Left => TrayClickType::LeftClick,
                    tauri::tray::MouseButton::Right => TrayClickType::RightClick,
                    tauri::tray::MouseButton::Middle => TrayClickType::MiddleClick,
                };

                let position = Some(TrayPosition {
                    x: position.x as i32,
                    y: position.y as i32,
                });

                TrayEvent::new(TrayEventType::Click {
                    click_type,
                    position,
                })
            }
            TrayIconEvent::Enter {
                id: _,
                position,
                rect: _,
            } => TrayEvent::new(TrayEventType::MouseEnter).with_data(
                "position".to_string(),
                serde_json::json!({
                    "x": position.x,
                    "y": position.y
                }),
            ),
            TrayIconEvent::Leave {
                id: _,
                position,
                rect: _,
            } => TrayEvent::new(TrayEventType::MouseLeave).with_data(
                "position".to_string(),
                serde_json::json!({
                    "x": position.x,
                    "y": position.y
                }),
            ),
            _ => {
                // 处理其他未知事件类型
                TrayEvent::new(TrayEventType::MouseEnter)
            }
        }
    }

    /// 创建菜单点击事件
    ///
    /// # 参数
    ///
    /// * `item_id` - 菜单项ID
    ///
    /// # 返回值
    ///
    /// 返回菜单点击事件
    pub fn create_menu_click_event(item_id: String) -> TrayEvent {
        TrayEvent::new(TrayEventType::MenuItemClick { item_id })
    }

    /// 获取事件历史记录
    ///
    /// # 返回值
    ///
    /// 返回事件历史记录的副本
    pub fn get_event_history(&self) -> TrayResult<Vec<TrayEvent>> {
        let history = self
            .event_history
            .lock()
            .map_err(|_| TrayError::internal("无法获取事件历史记录锁"))?;
        Ok(history.clone())
    }

    /// 清空事件历史记录
    ///
    /// # 返回值
    ///
    /// 如果清空成功则返回 Ok(())，否则返回错误
    pub fn clear_event_history(&self) -> TrayResult<()> {
        let mut history = self
            .event_history
            .lock()
            .map_err(|_| TrayError::internal("无法获取事件历史记录锁"))?;
        history.clear();
        Ok(())
    }

    /// 获取注册的处理器数量
    ///
    /// # 返回值
    ///
    /// 返回处理器数量
    pub fn handler_count(&self) -> usize {
        self.handlers
            .lock()
            .unwrap_or_else(|_| panic!("无法获取处理器列表锁"))
            .len()
    }

    /// 获取所有处理器的名称
    ///
    /// # 返回值
    ///
    /// 返回处理器名称列表
    pub fn get_handler_names(&self) -> Vec<String> {
        self.handlers
            .lock()
            .unwrap_or_else(|_| panic!("无法获取处理器列表锁"))
            .iter()
            .map(|h| h.name().to_string())
            .collect()
    }

    /// 添加事件到历史记录
    ///
    /// # 参数
    ///
    /// * `event` - 要添加的事件
    ///
    /// # 返回值
    ///
    /// 如果添加成功则返回 Ok(())，否则返回错误
    fn add_to_history(&self, event: TrayEvent) -> TrayResult<()> {
        let mut history = self
            .event_history
            .lock()
            .map_err(|_| TrayError::internal("无法获取事件历史记录锁"))?;

        history.push(event);

        // 保持历史记录在限制范围内
        while history.len() > self.max_history_size {
            history.remove(0);
        }

        Ok(())
    }
}

impl Default for TrayEventDispatcher {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    struct TestEventHandler {
        name: String,
        priority: u32,
        should_handle_all: bool,
    }

    impl TestEventHandler {
        fn new(name: &str, priority: u32, should_handle_all: bool) -> Self {
            Self {
                name: name.to_string(),
                priority,
                should_handle_all,
            }
        }
    }

    #[async_trait::async_trait]
    impl TrayEventHandler for TestEventHandler {
        async fn handle_event(
            &self,
            _event: &TrayEvent,
            _app_handle: &AppHandle,
        ) -> TrayResult<()> {
            Ok(())
        }

        fn name(&self) -> &str {
            &self.name
        }

        fn should_handle(&self, _event_type: &TrayEventType) -> bool {
            self.should_handle_all
        }

        fn priority(&self) -> u32 {
            self.priority
        }
    }

    #[test]
    fn test_tray_event_creation() {
        let event = TrayEvent::new(TrayEventType::Click {
            click_type: TrayClickType::LeftClick,
            position: Some(TrayPosition { x: 100, y: 200 }),
        });

        assert!(event.is_click_event());
        assert!(!event.is_menu_click_event());
        assert!(event.timestamp > 0);
        assert!(!event.event_id.is_empty());
    }

    #[test]
    fn test_menu_click_event() {
        let event = TrayEvent::new(TrayEventType::MenuItemClick {
            item_id: "test_menu".to_string(),
        });

        assert!(!event.is_click_event());
        assert!(event.is_menu_click_event());
        assert_eq!(event.get_menu_item_id(), Some("test_menu"));
    }

    #[test]
    fn test_event_dispatcher_registration() {
        let dispatcher = TrayEventDispatcher::new();
        let handler = Arc::new(TestEventHandler::new("test", 50, true));

        assert_eq!(dispatcher.handler_count(), 0);

        dispatcher.register_handler(handler).unwrap();
        assert_eq!(dispatcher.handler_count(), 1);

        let names = dispatcher.get_handler_names();
        assert_eq!(names, vec!["test"]);

        dispatcher.unregister_handler("test").unwrap();
        assert_eq!(dispatcher.handler_count(), 0);
    }

    #[test]
    fn test_duplicate_handler_registration() {
        let dispatcher = TrayEventDispatcher::new();
        let handler1 = Arc::new(TestEventHandler::new("test", 50, true));
        let handler2 = Arc::new(TestEventHandler::new("test", 60, true));

        dispatcher.register_handler(handler1).unwrap();
        let result = dispatcher.register_handler(handler2);
        assert!(result.is_err());
    }

    #[test]
    fn test_handler_priority_sorting() {
        let dispatcher = TrayEventDispatcher::new();
        let handler1 = Arc::new(TestEventHandler::new("high", 10, true));
        let handler2 = Arc::new(TestEventHandler::new("low", 100, true));
        let handler3 = Arc::new(TestEventHandler::new("medium", 50, true));

        dispatcher.register_handler(handler2).unwrap();
        dispatcher.register_handler(handler1).unwrap();
        dispatcher.register_handler(handler3).unwrap();

        let names = dispatcher.get_handler_names();
        assert_eq!(names, vec!["high", "medium", "low"]);
    }

    #[test]
    fn test_event_history() {
        let dispatcher = TrayEventDispatcher::new();
        let event = TrayEvent::new(TrayEventType::MouseEnter);

        dispatcher.add_to_history(event.clone()).unwrap();

        let history = dispatcher.get_event_history().unwrap();
        assert_eq!(history.len(), 1);
        assert_eq!(history[0].event_id, event.event_id);

        dispatcher.clear_event_history().unwrap();
        let history = dispatcher.get_event_history().unwrap();
        assert_eq!(history.len(), 0);
    }
}
