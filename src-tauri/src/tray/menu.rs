/// 托盘菜单模块
///
/// 提供托盘菜单的创建和管理功能，基于 Tauri 官方菜单 API。
/// 支持菜单构建者模式和验证。
use crate::tray::{TrayError, TrayResult, MAX_MENU_ITEMS};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{
    menu::{Menu, MenuBuilder, MenuItemBuilder, PredefinedMenuItem},
    AppHandle,
};

/// 托盘菜单项类型
///
/// 定义不同类型的菜单项。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrayMenuItemType {
    /// 普通菜单项
    Normal,
    /// 分隔符
    Separator,
    /// 复选框菜单项
    Checkbox,
    /// 预定义菜单项（如退出、关于等）
    Predefined(PredefinedMenuItemType),
}

/// 预定义菜单项类型
///
/// Tauri 提供的预定义菜单项类型。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PredefinedMenuItemType {
    /// 退出
    Quit,
    /// 关于
    About,
}

/// 托盘菜单项
///
/// 表示托盘菜单中的一个项目。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayMenuItem {
    /// 菜单项唯一标识符
    pub id: String,

    /// 菜单项显示文本
    pub text: String,

    /// 菜单项类型
    pub item_type: TrayMenuItemType,

    /// 是否启用
    pub enabled: bool,

    /// 是否可见
    pub visible: bool,

    /// 是否选中（用于复选框）
    pub checked: bool,

    /// 快捷键
    pub accelerator: Option<String>,

    /// 菜单项的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

impl TrayMenuItem {
    /// 创建新的普通菜单项
    ///
    /// # 参数
    ///
    /// * `id` - 菜单项ID
    /// * `text` - 显示文本
    ///
    /// # 返回值
    ///
    /// 返回新的菜单项实例
    pub fn new(id: impl Into<String>, text: impl Into<String>) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Normal,
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            data: HashMap::new(),
        }
    }

    /// 创建分隔符
    ///
    /// # 返回值
    ///
    /// 返回分隔符菜单项
    pub fn separator() -> Self {
        Self {
            id: format!("separator_{}", uuid::Uuid::new_v4()),
            text: String::new(),
            item_type: TrayMenuItemType::Separator,
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            data: HashMap::new(),
        }
    }

    /// 创建复选框菜单项
    ///
    /// # 参数
    ///
    /// * `id` - 菜单项ID
    /// * `text` - 显示文本
    /// * `checked` - 是否选中
    ///
    /// # 返回值
    ///
    /// 返回复选框菜单项
    pub fn checkbox(id: impl Into<String>, text: impl Into<String>, checked: bool) -> Self {
        Self {
            id: id.into(),
            text: text.into(),
            item_type: TrayMenuItemType::Checkbox,
            enabled: true,
            visible: true,
            checked,
            accelerator: None,
            data: HashMap::new(),
        }
    }

    /// 创建预定义菜单项
    ///
    /// # 参数
    ///
    /// * `predefined_type` - 预定义类型
    ///
    /// # 返回值
    ///
    /// 返回预定义菜单项
    pub fn predefined(predefined_type: PredefinedMenuItemType) -> Self {
        let (id, text) = match &predefined_type {
            PredefinedMenuItemType::Quit => ("quit".to_string(), "退出".to_string()),
            PredefinedMenuItemType::About => ("about".to_string(), "关于".to_string()),
        };

        Self {
            id,
            text,
            item_type: TrayMenuItemType::Predefined(predefined_type),
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            data: HashMap::new(),
        }
    }

    /// 设置是否启用
    ///
    /// # 参数
    ///
    /// * `enabled` - 是否启用
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置快捷键
    ///
    /// # 参数
    ///
    /// * `accelerator` - 快捷键字符串
    pub fn accelerator(mut self, accelerator: impl Into<String>) -> Self {
        self.accelerator = Some(accelerator.into());
        self
    }

    /// 检查是否为分隔符
    ///
    /// # 返回值
    ///
    /// 如果是分隔符则返回 true
    pub fn is_separator(&self) -> bool {
        matches!(self.item_type, TrayMenuItemType::Separator)
    }

    /// 验证菜单项
    ///
    /// # 返回值
    ///
    /// 如果菜单项有效则返回 Ok(())，否则返回错误
    pub fn validate(&self) -> TrayResult<()> {
        // 验证ID不为空（除了分隔符）
        if !self.is_separator() && self.id.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "menu_item_id",
                "菜单项ID不能为空",
            ));
        }

        // 验证文本不为空（除了分隔符）
        if !self.is_separator() && self.text.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "menu_item_text",
                "菜单项文本不能为空",
            ));
        }

        Ok(())
    }
}

/// 托盘菜单
///
/// 表示整个托盘菜单。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayMenu {
    /// 菜单项列表
    pub items: Vec<TrayMenuItem>,

    /// 菜单的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

impl TrayMenu {
    /// 创建新的空菜单
    ///
    /// # 返回值
    ///
    /// 返回新的菜单实例
    pub fn new() -> Self {
        Self {
            items: Vec::new(),
            data: HashMap::new(),
        }
    }

    /// 添加菜单项
    ///
    /// # 参数
    ///
    /// * `item` - 要添加的菜单项
    ///
    /// # 返回值
    ///
    /// 如果添加成功则返回 Ok(())，否则返回错误
    pub fn add_item(&mut self, item: TrayMenuItem) -> TrayResult<()> {
        // 检查菜单项数量限制
        if self.items.len() >= MAX_MENU_ITEMS {
            return Err(TrayError::MenuItemLimitExceeded {
                count: self.items.len() + 1,
                max_allowed: MAX_MENU_ITEMS,
            });
        }

        // 检查ID重复（除了分隔符）
        if !item.is_separator() {
            for existing_item in &self.items {
                if !existing_item.is_separator() && existing_item.id == item.id {
                    return Err(TrayError::DuplicateMenuItemId { id: item.id });
                }
            }
        }

        // 验证菜单项
        item.validate()?;

        self.items.push(item);
        Ok(())
    }

    /// 转换为 Tauri 菜单
    ///
    /// # 参数
    ///
    /// * `app_handle` - Tauri 应用句柄
    ///
    /// # 返回值
    ///
    /// 返回 Tauri 菜单实例
    pub fn to_tauri_menu(&self, app_handle: &AppHandle) -> TrayResult<Menu<tauri::Wry>> {
        let mut menu_builder = MenuBuilder::new(app_handle);

        for item in &self.items {
            menu_builder = self.add_item_to_builder(menu_builder, item, app_handle)?;
        }

        menu_builder.build().map_err(TrayError::from)
    }

    /// 添加菜单项到建造者
    ///
    /// # 参数
    ///
    /// * `builder` - 菜单建造者
    /// * `item` - 菜单项
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 返回更新后的菜单建造者
    fn add_item_to_builder<'a>(
        &self,
        builder: MenuBuilder<'a, tauri::Wry, tauri::AppHandle>,
        item: &TrayMenuItem,
        app_handle: &AppHandle,
    ) -> TrayResult<MenuBuilder<'a, tauri::Wry, tauri::AppHandle>> {
        match &item.item_type {
            TrayMenuItemType::Normal | TrayMenuItemType::Checkbox => {
                let menu_item = MenuItemBuilder::new(&item.text)
                    .id(&item.id)
                    .enabled(item.enabled)
                    .build(app_handle)
                    .map_err(TrayError::from)?;
                Ok(builder.item(&menu_item))
            }
            TrayMenuItemType::Separator => Ok(builder.separator()),
            TrayMenuItemType::Predefined(predefined_type) => match predefined_type {
                PredefinedMenuItemType::Quit => {
                    let quit_item =
                        PredefinedMenuItem::quit(app_handle, None).map_err(TrayError::from)?;
                    Ok(builder.item(&quit_item))
                }
                PredefinedMenuItemType::About => {
                    let about_item = PredefinedMenuItem::about(app_handle, None, None)
                        .map_err(TrayError::from)?;
                    Ok(builder.item(&about_item))
                }
            },
        }
    }

    /// 检查菜单是否为空
    ///
    /// # 返回值
    ///
    /// 如果菜单为空则返回 true
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// 验证菜单
    ///
    /// # 返回值
    ///
    /// 如果菜单有效则返回 Ok(())，否则返回错误
    pub fn validate(&self) -> TrayResult<()> {
        // 检查菜单项数量限制
        if self.items.len() > MAX_MENU_ITEMS {
            return Err(TrayError::MenuItemLimitExceeded {
                count: self.items.len(),
                max_allowed: MAX_MENU_ITEMS,
            });
        }

        // 验证每个菜单项
        for item in &self.items {
            item.validate()?;
        }

        Ok(())
    }
}

impl Default for TrayMenu {
    fn default() -> Self {
        Self::new()
    }
}

/// 托盘菜单建造者
///
/// 使用建造者模式来构建托盘菜单。
#[derive(Debug, Default)]
pub struct TrayMenuBuilder {
    menu: TrayMenu,
}

impl TrayMenuBuilder {
    /// 创建新的菜单建造者
    ///
    /// # 返回值
    ///
    /// 返回新的菜单建造者实例
    pub fn new() -> Self {
        Self {
            menu: TrayMenu::new(),
        }
    }

    /// 添加菜单项
    ///
    /// # 参数
    ///
    /// * `item` - 要添加的菜单项
    ///
    /// # 返回值
    ///
    /// 返回更新后的建造者实例
    pub fn add_item(mut self, item: TrayMenuItem) -> TrayResult<Self> {
        self.menu.add_item(item)?;
        Ok(self)
    }

    /// 添加普通菜单项
    ///
    /// # 参数
    ///
    /// * `id` - 菜单项ID
    /// * `text` - 显示文本
    ///
    /// # 返回值
    ///
    /// 返回更新后的建造者实例
    pub fn add_normal_item(
        self,
        id: impl Into<String>,
        text: impl Into<String>,
    ) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::new(id, text))
    }

    /// 添加分隔符
    ///
    /// # 返回值
    ///
    /// 返回更新后的建造者实例
    pub fn add_separator(self) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::separator())
    }

    /// 添加复选框菜单项
    ///
    /// # 参数
    ///
    /// * `id` - 菜单项ID
    /// * `text` - 显示文本
    /// * `checked` - 是否选中
    ///
    /// # 返回值
    ///
    /// 返回更新后的建造者实例
    pub fn add_checkbox(
        self,
        id: impl Into<String>,
        text: impl Into<String>,
        checked: bool,
    ) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::checkbox(id, text, checked))
    }

    /// 添加预定义菜单项
    ///
    /// # 参数
    ///
    /// * `predefined_type` - 预定义类型
    ///
    /// # 返回值
    ///
    /// 返回更新后的建造者实例
    pub fn add_predefined(self, predefined_type: PredefinedMenuItemType) -> TrayResult<Self> {
        self.add_item(TrayMenuItem::predefined(predefined_type))
    }

    /// 构建菜单
    ///
    /// # 返回值
    ///
    /// 返回验证后的托盘菜单
    pub fn build(self) -> TrayResult<TrayMenu> {
        self.menu.validate()?;
        Ok(self.menu)
    }
}

impl TrayMenu {
    /// 创建菜单建造者
    ///
    /// # 返回值
    ///
    /// 返回新的菜单建造者实例
    pub fn builder() -> TrayMenuBuilder {
        TrayMenuBuilder::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_menu_item_creation() {
        let item = TrayMenuItem::new("test", "测试菜单");
        assert_eq!(item.id, "test");
        assert_eq!(item.text, "测试菜单");
        assert_eq!(item.item_type, TrayMenuItemType::Normal);
        assert!(item.enabled);
        assert!(item.visible);
        assert!(!item.checked);
    }

    #[test]
    fn test_separator_creation() {
        let separator = TrayMenuItem::separator();
        assert!(separator.is_separator());
        assert!(separator.text.is_empty());
    }

    #[test]
    fn test_checkbox_creation() {
        let checkbox = TrayMenuItem::checkbox("check", "复选框", true);
        assert_eq!(checkbox.item_type, TrayMenuItemType::Checkbox);
        assert!(checkbox.checked);
    }

    #[test]
    fn test_menu_creation() {
        let mut menu = TrayMenu::new();
        assert!(menu.is_empty());

        let item = TrayMenuItem::new("test", "测试");
        menu.add_item(item).unwrap();
        assert_eq!(menu.items.len(), 1);
        assert!(!menu.is_empty());
    }

    #[test]
    fn test_menu_builder() {
        let menu = TrayMenu::builder()
            .add_normal_item("show", "显示窗口")
            .unwrap()
            .add_separator()
            .unwrap()
            .add_checkbox("auto_start", "开机自启", false)
            .unwrap()
            .add_separator()
            .unwrap()
            .add_predefined(PredefinedMenuItemType::Quit)
            .unwrap()
            .build()
            .unwrap();

        assert_eq!(menu.items.len(), 5);
        assert_eq!(menu.items[0].id, "show");
        assert!(menu.items[1].is_separator());
        assert_eq!(menu.items[2].item_type, TrayMenuItemType::Checkbox);
    }

    #[test]
    fn test_duplicate_id_error() {
        let mut menu = TrayMenu::new();
        let item1 = TrayMenuItem::new("test", "测试1");
        let item2 = TrayMenuItem::new("test", "测试2");

        menu.add_item(item1).unwrap();
        let result = menu.add_item(item2);
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            TrayError::DuplicateMenuItemId { .. }
        ));
    }

    #[test]
    fn test_menu_validation() {
        // 测试空ID错误
        let invalid_item = TrayMenuItem {
            id: String::new(),
            text: "测试".to_string(),
            item_type: TrayMenuItemType::Normal,
            enabled: true,
            visible: true,
            checked: false,
            accelerator: None,
            data: HashMap::new(),
        };

        let result = invalid_item.validate();
        assert!(result.is_err());

        // 测试正常验证
        let valid_item = TrayMenuItem::new("test", "测试");
        let result = valid_item.validate();
        assert!(result.is_ok());
    }
}
