/// 基于 Tauri 官方 API 的系统托盘模块
///
/// 此模块提供了一个基于 Tauri 2.x 官方系统托盘 API 的实现，
/// 具有模块化设计、可插拔架构和完善的错误处理机制。
///
/// # 主要特性
///
/// - 基于 Tauri 官方 API，确保兼容性和稳定性
/// - 模块化设计，便于维护和扩展
/// - 可插拔的事件处理器架构
/// - 完善的错误处理和类型安全
/// - 跨平台支持（Windows、macOS、Linux）
///
/// # 使用示例
///
/// ```rust
/// use crate::tray::{TrayManager, TrayConfig};
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     let config = TrayConfig::builder()
///         .title("我的应用")
///         .tooltip("点击显示主窗口")
///         .build();
///     
///     let manager = TrayManager::new(config).await?;
///     manager.setup().await?;
///     
///     Ok(())
/// }
/// ```
pub mod config;
pub mod errors;
pub mod events;
pub mod handlers;
pub mod manager;
pub mod menu;
pub mod utils;

// 重新导出核心类型
pub use config::{TrayConfig, TrayConfigBuilder};
pub use errors::{TrayError, TrayResult};
pub use events::{TrayEvent, TrayEventHandler, TrayEventType};
pub use handlers::{DefaultTrayHandler, TrayHandler};
pub use manager::TrayManager;
pub use menu::{TrayMenu, TrayMenuBuilder, TrayMenuItem, TrayMenuItemType};

/// 托盘模块版本
pub const TRAY_VERSION: &str = "2.0.0";

/// 默认托盘工具提示
pub const DEFAULT_TOOLTIP: &str = "Secure Password";

/// 最大菜单项数量
pub const MAX_MENU_ITEMS: usize = 20;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_module_exports() {
        // 测试模块导出是否正常
        let config = TrayConfig::default();
        assert!(!config.title.is_empty());
    }

    #[test]
    fn test_constants() {
        assert_eq!(TRAY_VERSION, "2.0.0");
        assert_eq!(DEFAULT_TOOLTIP, "Secure Password");
        assert_eq!(MAX_MENU_ITEMS, 20);
    }
}
