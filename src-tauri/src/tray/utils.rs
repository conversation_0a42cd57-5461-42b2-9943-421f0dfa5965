/// 托盘工具模块
///
/// 提供托盘相关的实用工具函数和帮助方法。
use crate::tray::{TrayError, TrayResult};

/// 检查系统是否支持系统托盘
///
/// # 返回值
///
/// 如果系统支持托盘则返回 true
pub fn is_system_tray_supported() -> bool {
    // 在 Tauri 2.x 中，这通常由 Tauri 本身处理
    // 这里提供一个基本的实现
    #[cfg(target_os = "windows")]
    {
        true
    }
    #[cfg(target_os = "macos")]
    {
        true
    }
    #[cfg(target_os = "linux")]
    {
        // 在 Linux 上，系统托盘支持取决于桌面环境
        std::env::var("DISPLAY").is_ok() || std::env::var("WAYLAND_DISPLAY").is_ok()
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        false
    }
}

/// 获取当前平台的名称
///
/// # 返回值
///
/// 返回平台名称字符串
pub fn get_platform_name() -> &'static str {
    #[cfg(target_os = "windows")]
    {
        "Windows"
    }
    #[cfg(target_os = "macos")]
    {
        "macOS"
    }
    #[cfg(target_os = "linux")]
    {
        "Linux"
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        "Unknown"
    }
}

/// 验证图标路径是否有效
///
/// # 参数
///
/// * `path` - 图标文件路径
///
/// # 返回值
///
/// 如果路径有效则返回 Ok(())，否则返回错误
pub fn validate_icon_path(path: &str) -> TrayResult<()> {
    if path.trim().is_empty() {
        return Err(TrayError::configuration_error(
            "icon_path",
            "图标路径不能为空",
        ));
    }

    // 检查文件扩展名
    let valid_extensions = ["png", "ico", "jpg", "jpeg", "svg"];
    let path_lower = path.to_lowercase();

    let has_valid_extension = valid_extensions
        .iter()
        .any(|ext| path_lower.ends_with(&format!(".{}", ext)));

    if !has_valid_extension {
        return Err(TrayError::configuration_error(
            "icon_path",
            format!(
                "不支持的图标格式。支持的格式: {}",
                valid_extensions.join(", ")
            ),
        ));
    }

    Ok(())
}

/// 生成唯一的标识符
///
/// # 返回值
///
/// 返回唯一标识符字符串
pub fn generate_unique_id() -> String {
    uuid::Uuid::new_v4().to_string()
}

/// 生成带前缀的唯一标识符
///
/// # 参数
///
/// * `prefix` - 标识符前缀
///
/// # 返回值
///
/// 返回带前缀的唯一标识符
pub fn generate_prefixed_id(prefix: &str) -> String {
    format!("{}_{}", prefix, uuid::Uuid::new_v4())
}

/// 格式化错误消息
///
/// # 参数
///
/// * `context` - 错误上下文
/// * `error` - 错误信息
///
/// # 返回值
///
/// 返回格式化的错误消息
pub fn format_error_message(context: &str, error: &str) -> String {
    format!("[{}] {}", context, error)
}

/// 截断字符串到指定长度
///
/// # 参数
///
/// * `text` - 要截断的文本
/// * `max_length` - 最大长度
///
/// # 返回值
///
/// 返回截断后的字符串
pub fn truncate_string(text: &str, max_length: usize) -> String {
    if text.len() <= max_length {
        text.to_string()
    } else {
        format!("{}...", &text[..max_length.saturating_sub(3)])
    }
}

/// 清理和验证字符串
///
/// # 参数
///
/// * `text` - 要清理的文本
///
/// # 返回值
///
/// 返回清理后的字符串
pub fn sanitize_string(text: &str) -> String {
    text.trim()
        .replace('\n', " ")
        .replace('\r', " ")
        .replace('\t', " ")
}

/// 检查字符串是否为有效的标识符
///
/// # 参数
///
/// * `id` - 要检查的标识符
///
/// # 返回值
///
/// 如果是有效标识符则返回 true
pub fn is_valid_identifier(id: &str) -> bool {
    if id.is_empty() {
        return false;
    }

    // 标识符应该以字母或下划线开头，后面可以跟字母、数字或下划线
    let first_char = id.chars().next().unwrap();
    if !first_char.is_ascii_alphabetic() && first_char != '_' {
        return false;
    }

    id.chars()
        .all(|c| c.is_ascii_alphanumeric() || c == '_' || c == '-')
}

/// 平台特定的配置验证
///
/// # 返回值
///
/// 如果当前平台配置有效则返回 Ok(())，否则返回错误
pub fn validate_platform_config() -> TrayResult<()> {
    if !is_system_tray_supported() {
        return Err(TrayError::TrayUnavailable {
            reason: format!("当前平台 {} 不支持系统托盘", get_platform_name()),
        });
    }

    Ok(())
}

/// 获取推荐的图标尺寸
///
/// # 返回值
///
/// 返回 (宽度, 高度) 元组
pub fn get_recommended_icon_size() -> (u32, u32) {
    #[cfg(target_os = "windows")]
    {
        (16, 16) // Windows 系统托盘图标通常是 16x16
    }
    #[cfg(target_os = "macos")]
    {
        (22, 22) // macOS 菜单栏图标通常是 22x22
    }
    #[cfg(target_os = "linux")]
    {
        (24, 24) // Linux 桌面环境通常使用 24x24
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        (16, 16) // 默认尺寸
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_system_tray_supported() {
        // 这个测试会根据运行的平台返回不同结果
        let supported = is_system_tray_supported();
        // 至少在主要平台上应该是支持的
        #[cfg(any(target_os = "windows", target_os = "macos"))]
        assert!(supported);
    }

    #[test]
    fn test_get_platform_name() {
        let platform = get_platform_name();
        assert!(!platform.is_empty());
        println!("当前平台: {}", platform);
    }

    #[test]
    fn test_validate_icon_path() {
        // 有效路径
        assert!(validate_icon_path("icon.png").is_ok());
        assert!(validate_icon_path("icons/app.ico").is_ok());
        assert!(validate_icon_path("image.jpg").is_ok());

        // 无效路径
        assert!(validate_icon_path("").is_err());
        assert!(validate_icon_path("   ").is_err());
        assert!(validate_icon_path("file.txt").is_err());
    }

    #[test]
    fn test_generate_unique_id() {
        let id1 = generate_unique_id();
        let id2 = generate_unique_id();

        assert_ne!(id1, id2);
        assert!(!id1.is_empty());
        assert!(!id2.is_empty());
    }

    #[test]
    fn test_generate_prefixed_id() {
        let id = generate_prefixed_id("tray");
        assert!(id.starts_with("tray_"));
        assert!(id.len() > 5);
    }

    #[test]
    fn test_truncate_string() {
        assert_eq!(truncate_string("hello", 10), "hello");
        assert_eq!(truncate_string("hello world", 8), "hello...");
        assert_eq!(truncate_string("hi", 5), "hi");
    }

    #[test]
    fn test_sanitize_string() {
        assert_eq!(sanitize_string("  hello world  "), "hello world");
        assert_eq!(sanitize_string("hello\nworld"), "hello world");
        assert_eq!(sanitize_string("hello\tworld"), "hello world");
        assert_eq!(sanitize_string("hello\r\nworld"), "hello  world");
    }

    #[test]
    fn test_is_valid_identifier() {
        // 有效标识符
        assert!(is_valid_identifier("valid_id"));
        assert!(is_valid_identifier("_private"));
        assert!(is_valid_identifier("test123"));
        assert!(is_valid_identifier("my-component"));

        // 无效标识符
        assert!(!is_valid_identifier(""));
        assert!(!is_valid_identifier("123invalid"));
        assert!(!is_valid_identifier("invalid id"));
        assert!(!is_valid_identifier("invalid@id"));
    }

    #[test]
    fn test_format_error_message() {
        let msg = format_error_message("托盘", "初始化失败");
        assert_eq!(msg, "[托盘] 初始化失败");
    }

    #[test]
    fn test_get_recommended_icon_size() {
        let (width, height) = get_recommended_icon_size();
        assert!(width > 0);
        assert!(height > 0);
        println!("推荐图标尺寸: {}x{}", width, height);
    }
}
