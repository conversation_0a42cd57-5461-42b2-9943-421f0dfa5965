{"$schema": "https://schema.tauri.app/config/2", "productName": "secure-password", "version": "0.1.0", "identifier": "com.secure-password.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:51420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "resources": ["run_native_host.sh"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}