/**
 * 应用主入口组件
 * 提供认证状态管理和页面路由
 */

import React from 'react';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider, useAuthState } from './contexts';
import { UserProvider, useUser } from './contexts/UserContext';
import { HybridCredentialsProvider } from './contexts/HybridCredentialsContext';
import { AppLayout } from './components';
import { AuthPage, MainPage } from './pages';
import { OrmInitializationStatusComponent } from './components/OrmInitializationStatus';
import { useOrmInitialization } from './hooks/useOrmInitialization';
import './App.css';

/**
 * 应用内容组件
 * 根据认证状态显示不同的页面
 */
const AppContent: React.FC = () => {
  const { loading: authLoading } = useAuthState();
  const { isLoggedIn, isLoading: userLoading, user } = useUser();
  const { isInitializing: ormInitializing, isError: ormError } = useOrmInitialization();

  console.log('ormInitializing', ormInitializing);

  // 显示加载状态（任一状态正在加载时）
  const loading = authLoading || userLoading;
  
  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <div className="text-gray-600">正在检查认证状态...</div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // 使用用户登录状态作为主要的认证判断
  // 如果用户已登录，显示主页面；否则显示认证页面
  const shouldShowMainPage = isLoggedIn && user;

  return (
    <AppLayout>
      {/* ORM 初始化状态显示 - 在页面顶部显示 */}
      {(ormInitializing || ormError) && (
        <div style={{ margin: '16px', marginBottom: '8px' }}>
          <OrmInitializationStatusComponent />
        </div>
      )}
      
      {shouldShowMainPage ? (
        <HybridCredentialsProvider>
          <MainPage />
        </HybridCredentialsProvider>
      ) : (
        <AuthPage />
      )}
    </AppLayout>
  );
};

/**
 * 应用主组件
 */
const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <UserProvider>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </UserProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
