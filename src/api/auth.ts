/**
 * 认证相关的 API 调用
 */

import { invoke } from '@tauri-apps/api/core';
import { message } from 'antd';
import {
  RegisterFormData,
  LoginFormData,
  CompleteRegistrationResult,
  RemoteLoginResponse,
  RemoteConnectionTestResult,
  PasswordStrengthResult,
  RegistrationType,
  ApiResponse
} from '../types';

/**
 * 完整的远程注册流程
 * 包含密码加密、数据验证和远程注册
 */
export const registerCompleteFlow = async (
  formData: RegisterFormData
): Promise<ApiResponse<CompleteRegistrationResult>> => {
  try {
    console.log('开始注册流程:', formData.username);
    
    const result = await invoke<CompleteRegistrationResult>('register_complete_flow', {
      username: formData.username,
      contact: formData.contact,
      registrationType: formData.registrationType,
      password: formData.password,
      verificationCode: formData.verificationCode,
      passwordHint: formData.passwordHint || null,
    });

    if (result.remote_success) {
      message.success('注册成功！');
      console.log('注册成功:', result);
      return {
        success: true,
        data: result
      };
    } else {
      message.error(`注册失败: ${result.remote_message}`);
      return {
        success: false,
        error: result.remote_message
      };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '注册过程出错';
    console.error('注册流程错误:', error);
    message.error(errorMessage);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 用户登录（使用通用密码派生方案）
 * 
 * 此函数使用新的通用密码派生方案，完全基于密码派生，不依赖本地存储
 * 支持跨设备一致性，自动从密码重新生成所有必要的密钥
 */
export const loginUserRemote = async (
  formData: LoginFormData
): Promise<ApiResponse<RemoteLoginResponse>> => {
  try {
    console.log('开始通用登录流程:', formData.contact);
    
    // 使用通用登录命令，无需手动哈希密码
    const result = await invoke<RemoteLoginResponse>('login_user_universal', {
      contact: formData.contact,
      password: formData.password,
      verificationCode: formData.verificationCode || '',
    });

    if (result.code === 0 && result.data) {
      message.success('登录成功！');
      console.log('通用登录成功:', result);
      return {
        success: true,
        data: result
      };
    } else {
      message.error(`登录失败: ${result.message}`);
      return {
        success: false,
        error: result.message
      };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '登录过程出错';
    console.error('通用登录流程错误:', error);
    message.error(errorMessage);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 发送验证码
 */
export const sendVerificationCode = async (
  contact: string,
  contactType: RegistrationType
): Promise<ApiResponse<void>> => {
  try {
    console.log('发送验证码:', contact);
    
    await invoke('send_verification_code', {
      contact,
      contactType
    });

    message.success('验证码发送成功！');
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '发送验证码失败';
    console.error('发送验证码错误:', error);
    message.error(errorMessage);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 验证密码强度
 */
export const validatePasswordStrength = async (
  password: string
): Promise<ApiResponse<PasswordStrengthResult>> => {
  try {
    const result = await invoke<PasswordStrengthResult>('validate_password_strength', {
      password
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '密码强度验证失败';
    console.error('密码强度验证错误:', error);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 检查用户名可用性
 */
export const checkUsernameAvailability = async (
  username: string
): Promise<ApiResponse<{ is_available: boolean; is_valid_format: boolean; message: string }>> => {
  try {
    const result = await invoke<{ is_available: boolean; is_valid_format: boolean; message: string }>(
      'check_username_availability',
      { username }
    );

    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '用户名检查失败';
    console.error('用户名检查错误:', error);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 检查联系方式可用性
 */
export const checkContactAvailability = async (
  contact: string,
  contactType: RegistrationType
): Promise<ApiResponse<{ is_available: boolean; is_valid_format: boolean; message: string }>> => {
  try {
    const result = await invoke<{ is_available: boolean; is_valid_format: boolean; message: string }>(
      'check_contact_availability',
      { 
        contact,
        contactType 
      }
    );

    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '联系方式检查失败';
    console.error('联系方式检查错误:', error);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 测试远程服务端连接
 */
export const testRemoteConnection = async (): Promise<ApiResponse<RemoteConnectionTestResult>> => {
  try {
    console.log('测试远程连接...');
    
    const result = await invoke<RemoteConnectionTestResult>('test_remote_connection');

    if (result.connected) {
      message.success('服务器连接正常');
    } else {
      message.warning('服务器连接失败');
    }

    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '连接测试失败';
    console.error('连接测试错误:', error);
    message.error(errorMessage);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 获取当前令牌
 */
export const getCurrentToken = async (): Promise<ApiResponse<string | null>> => {
  try {
    const result = await invoke<string | null>('get_current_token');
    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '获取令牌失败';
    console.error('获取令牌错误:', error);
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 清除令牌（登出）
 */
export const clearToken = async (): Promise<ApiResponse<void>> => {
  try {
    await invoke('clear_token');
    message.success('已安全登出');
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '登出失败';
    console.error('登出错误:', error);
    message.error(errorMessage);
    return {
      success: false,
      error: errorMessage
    };
  }
};