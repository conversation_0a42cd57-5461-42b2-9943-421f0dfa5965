/**
 * Hybrid Storage API 函数
 * 使用新的 hybrid_storage 系统进行密码管理
 */

import { invoke } from '@tauri-apps/api/core';
import type { 
    VaultInfo, 
    LoginCredentialInput, 
    LoginCredentialOutput,
    CurrentUser
} from '../types';

// === 密码库管理 API ===

/**
 * 获取所有密码库
 */
export async function getAllVaults(): Promise<VaultInfo[]> {
    return await invoke('get_all_vaults_hybrid');
}

/**
 * 创建新密码库
 */
export async function createVault(
    name: string, 
    description?: string
): Promise<VaultInfo> {
    return await invoke('create_vault_hybrid', { name, description });
}

/**
 * 更新密码库
 */
export async function updateVault(
    id: number,
    name?: string,
    description?: string
): Promise<VaultInfo> {
    return await invoke('update_vault_hybrid', { id, name, description });
}

/**
 * 删除密码库
 */
export async function deleteVault(id: number): Promise<void> {
    return await invoke('delete_vault_hybrid', { id });
}

// === 登录凭据管理 API ===

/**
 * 获取指定密码库的所有登录凭据
 */
export async function getLoginCredentialsByVault(
    vaultId: number
): Promise<LoginCredentialOutput[]> {
    return await invoke('get_login_credentials_by_vault_hybrid', { vaultId });
}

/**
 * 获取所有登录凭据（使用默认密码库）
 */
export async function getAllLoginCredentials(): Promise<LoginCredentialOutput[]> {
    return await invoke('get_all_login_credentials_hybrid');
}

/**
 * 根据ID获取登录凭据
 */
export async function getLoginCredentialById(
    id: number
): Promise<LoginCredentialOutput | null> {
    return await invoke('get_login_credential_by_id_hybrid', { id });
}

/**
 * 保存新的登录凭据到指定密码库
 */
export async function saveLoginCredential(
    vaultId: number,
    credential: LoginCredentialInput
): Promise<LoginCredentialOutput> {
    return await invoke('save_login_credential_hybrid', { vaultId, credential });
}

/**
 * 添加新的登录凭据（使用默认密码库）
 */
export async function addLoginCredential(
    credential: LoginCredentialInput
): Promise<LoginCredentialOutput> {
    return await invoke('add_login_credential_hybrid', { credential });
}

/**
 * 更新登录凭据
 */
export async function updateLoginCredential(
    id: number,
    updates: {
        name?: string;
        username?: string;
        password?: string;
        website?: string;
        notes?: string;
        favorite?: boolean;
    }
): Promise<LoginCredentialOutput> {
    return await invoke('update_login_credential_hybrid', {
        id,
        name: updates.name,
        username: updates.username,
        password: updates.password,
        website: updates.website,
        notes: updates.notes,
        favorite: updates.favorite,
    });
}

/**
 * 删除登录凭据
 */
export async function deleteLoginCredential(id: number): Promise<void> {
    return await invoke('delete_login_credential_hybrid', { id });
}

/**
 * 软删除登录凭据（放入回收站）
 */
export async function softDeleteLoginCredential(id: number): Promise<LoginCredentialOutput> {
    return await invoke('soft_delete_login_credential_hybrid', { id });
}

/**
 * 从回收站恢复登录凭据
 */
export async function restoreLoginCredential(id: number): Promise<LoginCredentialOutput> {
    return await invoke('restore_login_credential_hybrid', { id });
}

/**
 * 获取指定密码库的回收站凭据
 */
export async function getTrashLoginCredentials(vaultId: number): Promise<LoginCredentialOutput[]> {
    return await invoke('get_trash_login_credentials_hybrid', { vaultId });
}

/**
 * 获取所有回收站凭据（使用默认密码库）
 */
export async function getAllTrashLoginCredentials(): Promise<LoginCredentialOutput[]> {
    return await invoke('get_all_trash_login_credentials_hybrid');
}

/**
 * 永久删除登录凭据（物理删除）
 */
export async function permanentlyDeleteLoginCredential(id: number): Promise<void> {
    return await invoke('permanently_delete_login_credential_hybrid', { id });
}

/**
 * 清理过期的已删除项目（超过30天自动永久删除）
 */
export async function cleanupExpiredDeletedItems(): Promise<number> {
    return await invoke('cleanup_expired_deleted_items_hybrid');
}

/**
 * 获取需要永久删除的项目数量
 */
export async function getItemsPendingPermanentDeletionCount(): Promise<number> {
    return await invoke('get_items_pending_permanent_deletion_count_hybrid');
}

/**
 * 根据域名搜索凭据
 */
export async function searchCredentialsByDomain(
    domain: string
): Promise<LoginCredentialOutput[]> {
    return await invoke('search_credentials_by_domain_hybrid', { domain });
}

/**
 * 获取指定密码库的收藏凭据
 */
export async function getFavoriteCredentials(
    vaultId: number
): Promise<LoginCredentialOutput[]> {
    return await invoke('get_favorite_credentials_hybrid', { vaultId });
}

/**
 * 获取所有收藏凭据（使用默认密码库）
 */
export async function getAllFavoriteCredentials(): Promise<LoginCredentialOutput[]> {
    return await invoke('get_all_favorite_credentials_hybrid');
}

// === 便捷函数 ===

/**
 * 切换凭据的收藏状态
 */
export async function toggleCredentialFavorite(
    id: number,
    currentFavorite: boolean
): Promise<LoginCredentialOutput> {
    return await updateLoginCredential(id, { favorite: !currentFavorite });
}

/**
 * 更新凭据密码
 */
export async function updateCredentialPassword(
    id: number,
    newPassword: string
): Promise<LoginCredentialOutput> {
    return await updateLoginCredential(id, { password: newPassword });
}

/**
 * 批量删除凭据
 */
export async function deleteMultipleCredentials(ids: number[]): Promise<void> {
    const deletePromises = ids.map(id => deleteLoginCredential(id));
    await Promise.all(deletePromises);
}

/**
 * 搜索凭据（支持多种搜索条件）
 */
export async function searchCredentials(
    query: string,
    searchType: 'name' | 'username' | 'website' | 'all' = 'all'
): Promise<LoginCredentialOutput[]> {
    // 获取所有凭据
    const allCredentials = await getAllLoginCredentials();
    
    // 根据搜索类型过滤
    const filteredCredentials = allCredentials.filter(credential => {
        const searchQuery = query.toLowerCase();
        
        switch (searchType) {
            case 'name':
                return credential.name.toLowerCase().includes(searchQuery);
            case 'username':
                return credential.username?.toLowerCase().includes(searchQuery) || false;
            case 'website':
                return credential.website?.toLowerCase().includes(searchQuery) || false;
            case 'all':
            default:
                return (
                    credential.name.toLowerCase().includes(searchQuery) ||
                    credential.username?.toLowerCase().includes(searchQuery) ||
                    credential.website?.toLowerCase().includes(searchQuery) ||
                    credential.notes?.toLowerCase().includes(searchQuery)
                );
        }
    });
    
    return filteredCredentials;
}

// ============================================================================
// 用户状态管理 API
// ============================================================================

/**
 * 获取当前登录用户信息
 */
export const getCurrentUserInfo = async (): Promise<CurrentUser | null> => {
  try {
    const result = await invoke<CurrentUser | null>('get_current_user_info');
    return result;
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    throw error;
  }
};

/**
 * 检查用户是否已登录
 */
export const isUserLoggedIn = async (): Promise<boolean> => {
  try {
    const result = await invoke<boolean>('is_user_logged_in');
    return result;
  } catch (error) {
    console.error('检查用户登录状态失败:', error);
    return false;
  }
};

/**
 * 登出当前用户
 */
export const logoutCurrentUser = async (): Promise<void> => {
  try {
    await invoke<void>('logout_current_user');
    console.log('用户登出成功');
  } catch (error) {
    console.error('用户登出失败:', error);
    throw error;
  }
};

/**
 * 重新加载用户信息（从持久化存储）
 */
export const reloadUserInfo = async (): Promise<CurrentUser | null> => {
  try {
    const result = await invoke<CurrentUser | null>('reload_user_info');
    return result;
  } catch (error) {
    console.error('重新加载用户信息失败:', error);
    throw error;
  }
};

// === 封存管理 API ===

/**
 * 封存登录凭据
 */
export async function archiveLoginCredential(id: number): Promise<LoginCredentialOutput> {
    return await invoke('archive_login_credential_hybrid', { id });
}

/**
 * 从封存中恢复登录凭据
 */
export async function unarchiveLoginCredential(id: number): Promise<LoginCredentialOutput> {
    return await invoke('unarchive_login_credential_hybrid', { id });
}

/**
 * 获取已封存的登录凭据
 */
export async function getArchivedLoginCredentials(vaultId: number): Promise<LoginCredentialOutput[]> {
    return await invoke('get_archived_login_credentials_hybrid', { vaultId });
}

/**
 * 获取所有已封存的登录凭据（使用默认密码库）
 */
export async function getAllArchivedLoginCredentials(): Promise<LoginCredentialOutput[]> {
    return await invoke('get_all_archived_login_credentials_hybrid');
}

/**
 * 批量封存登录凭据
 */
export async function batchArchiveLoginCredentials(ids: number[]): Promise<number> {
    return await invoke('batch_archive_login_credentials_hybrid', { ids });
}

/**
 * 批量从封存中恢复登录凭据
 */
export async function batchUnarchiveLoginCredentials(ids: number[]): Promise<number> {
    return await invoke('batch_unarchive_login_credentials_hybrid', { ids });
} 