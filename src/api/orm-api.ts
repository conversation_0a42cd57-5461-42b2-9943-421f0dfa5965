import { invoke } from '@tauri-apps/api/core';

/**
 * ORM 服务状态信息
 */
export interface OrmServiceStatus {
  /** 是否已初始化 */
  is_initialized: boolean;
  /** 是否可用 */
  is_available: boolean;
  /** 加密系统状态 */
  crypto_state: string;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 获取 ORM 服务状态
 */
export async function getOrmServiceStatus(): Promise<OrmServiceStatus> {
  try {
    return await invoke<OrmServiceStatus>('get_orm_service_status');
  } catch (error) {
    console.error('获取 ORM 服务状态失败:', error);
    throw new Error(`获取 ORM 服务状态失败: ${error}`);
  }
}

/**
 * 重试 ORM 服务初始化
 */
export async function retryOrmServiceInitialization(): Promise<string> {
  try {
    return await invoke<string>('retry_orm_service_initialization');
  } catch (error) {
    console.error('重试 ORM 服务初始化失败:', error);
    throw new Error(`重试 ORM 服务初始化失败: ${error}`);
  }
}

/**
 * 重置 ORM 服务状态
 */
export async function resetOrmService(): Promise<string> {
  try {
    return await invoke<string>('reset_orm_service');
  } catch (error) {
    console.error('重置 ORM 服务失败:', error);
    throw new Error(`重置 ORM 服务失败: ${error}`);
  }
}

/**
 * 检查加密系统是否可以解锁
 */
export async function checkCryptoUnlockCapability(): Promise<boolean> {
  try {
    return await invoke<boolean>('check_crypto_unlock_capability');
  } catch (error) {
    console.error('检查加密系统解锁能力失败:', error);
    throw new Error(`检查加密系统解锁能力失败: ${error}`);
  }
} 