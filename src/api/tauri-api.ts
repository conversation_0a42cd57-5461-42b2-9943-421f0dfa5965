import { invoke } from '@tauri-apps/api/core'; // 使用 core 模块
import { message } from 'antd';
import type { CredentialInput, CredentialOutput, PasswordHistoryEntry } from '../types/index'; // 定义共享类型

export async function checkSetupStatus(): Promise<boolean> {
    try {
        return await invoke<boolean>('check_setup_status');
    } catch (error) {
        console.error("Error checking setup status:", error);
        throw new Error(String(error)); // 将 Rust 返回的 String 错误转换为 JS Error
    }
}

export async function setupVault(masterPassword: string): Promise<void> {
    try {
        await invoke('setup_vault', { masterPassword }); // 参数需要匹配 Rust 命令的签名
    } catch (error) {
        console.error("Error setting up vault:", error);
        throw new Error(String(error));
    }
}

export async function unlockVault(masterPassword: string): Promise<void> {
    try {
        await invoke('unlock_vault', { masterPassword });
    } catch (error) {
        console.error("Error unlocking vault:", error);
        // 特别处理密码验证失败的情况，给用户更友好的提示
        if (String(error).includes("Password verification failed")) {
            throw new Error("Incorrect Master Password.");
        }
        throw new Error(String(error));
    }
}

export async function lockVault(): Promise<void> {
    try {
        await invoke('lock_vault');
    } catch (error) {
        console.error("Error locking vault:", error);
        throw new Error(String(error));
    }
}

export async function addCredential(credential: CredentialInput): Promise<void> {
    try {
        await invoke('add_credential', { credential }); // 参数名为 credential，类型为 CredentialInput
    } catch (error) {
        console.error("Error adding credential:", error);
        throw new Error(String(error));
    }
}

export async function updateCredential(id: number, credential: CredentialInput): Promise<void> {
    try {
        await invoke('update_credential', { id, credential }); // 参数名为 credential，类型为 CredentialInput
    } catch (error) {
        console.error("Error adding credential:", error);
        throw new Error(String(error));
    }
}

export async function getAllCredentials(): Promise<CredentialOutput[]> {
    try {
        return await invoke<CredentialOutput[]>('get_all_credentials');
    } catch (error) {
        console.error("Error getting credentials:", error);
        if (String(error).includes("VaultLocked")) {
            // 可以根据错误类型引导用户解锁
            console.warn("Vault is locked. Please unlock first.");
            // 返回空数组或抛出特定错误，让 UI 处理
            return [];
        }
        throw new Error(String(error));
    }
}

export async function deleteCredential(id: number): Promise<void> {
    try {
        // Rust 端期望 i64，JS number 可以安全传递，但在 TS 类型中明确为 number
        await invoke('delete_credential', { id });
    } catch (error) {
        console.error(`Error deleting credential ${id}:`, error);
        throw new Error(String(error));
    }
}

// Function to get password history for a specific field
export async function getPasswordHistory(credentialId: number, fieldName: string): Promise<PasswordHistoryEntry[]> {
    try {
        return await invoke<PasswordHistoryEntry[]>('get_password_history', { credentialId, fieldName });
    } catch (error) {
        console.error('Error fetching password history:', error);
        // Assuming 'message' is imported via the other block
        // If not, replace message.error with console.error
        message.error(`Failed to fetch history for ${fieldName}: ${error}`);
        throw error; // Re-throw to allow caller handling
    }
}