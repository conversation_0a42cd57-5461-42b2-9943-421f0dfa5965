import { useState } from 'react';
import { Card, Form, Input, Button, message, Select, Flex } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import type { CredentialInput } from '../types'; // Assuming CustomField type is defined in types.ts
import PasswordGenerator from './PasswordGenerator';
import { useCredentials } from '../contexts/CredentialsContext';
import { useVaultAuth } from '../contexts/VaultAuthContext';

export function AddCredentialForm() {
    const { add } = useCredentials();
    const { isLocked } = useVaultAuth();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false); // Local loading for add button

    const handleAdd = async (formValues: any) => { // Form values now include custom_fields array
        if (isLocked) {
            message.warning("Vault is locked.");
            return;
        }
        setLoading(true);
        try {
            // Prepare data for backend
            const credentialData: CredentialInput = {
                service_name: formValues.service_name,
                username: formValues.username,
                password: formValues.password,
                notes: formValues.notes,
                // Serialize custom fields array into a JSON string
                custom_fields: formValues.custom_fields ? formValues.custom_fields.filter((f: any) => f && f.title) : undefined,
            };
            await add(credentialData); // Call context action with serialized data
            form.resetFields();
            // Success message handled in context
        } catch (err) {
            // Error message handled in context
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordGenerate = (password: string) => {
        form.setFieldsValue({ 'password': password });
    };

    return (
        <Card title="Add New Credential">
            <Form
                form={form}
                layout="vertical"
                onFinish={handleAdd}
                requiredMark={false}
                disabled={isLocked}
            >
                <Form.Item
                    name="service_name"
                    label="Service Name"
                    rules={[{ required: true, message: 'Service name is required' }]}
                >
                    <Input placeholder="e.g., Google, GitHub" />
                </Form.Item>
                <Form.Item
                    name="username"
                    label="Username / Email"
                    rules={[{ required: true, message: 'Username is required' }]}
                >
                    <Input placeholder="Your username or email for the service" />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                    rules={[{ required: true, message: 'Password is required' }]}
                >
                    <Input.Password placeholder="The password for the service" />
                </Form.Item>
                <div style={{ marginBottom: 20 }}>
                    {/* Ensure PasswordGenerator calls onPasswordGenerate prop */}
                    <PasswordGenerator onPasswordGenerate={handlePasswordGenerate} />
                </div>
                <Form.Item
                    name="notes"
                    label="Notes (Optional)"
                >
                    <Input.TextArea placeholder="Any additional notes" rows={2} />
                </Form.Item>

                {/* Dynamic Custom Fields Section */}
                <Form.List name="custom_fields">
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ key, name, ...restField }) => (
                                <Card key={key} title="Custom fields" style={{ margin: '0 auto 10px', borderStyle: 'dashed' }} hoverable>
                                    <Flex gap="small" style={{ marginBottom: 8 }}>
                                        <Flex vertical style={{flex: 1, width: '100%' }}>
                                            <Flex gap="middle">
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'title']}
                                                    rules={[{ required: true, message: 'Field title is required' }]}
                                                    style={{ flex: 1 }}
                                                >
                                                    <Input placeholder="Field Title (e.g., Security Question)" />
                                                </Form.Item>
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'type']}
                                                    initialValue="text" // Default type
                                                    style={{ width: '120px' }}
                                                >
                                                    <Select>
                                                        <Select.Option value="text">Text</Select.Option>
                                                        <Select.Option value="password">Password</Select.Option>
                                                        <Select.Option value="textarea">Textarea</Select.Option>
                                                        {/* Add more types if needed */}
                                                    </Select>
                                                </Form.Item>
                                            </Flex>
                                            <Flex vertical>
                                                {/* Use Form.Item's render prop with shouldUpdate for dynamic rendering */}
                                                <Form.Item
                                                    noStyle // Use noStyle on the wrapper to avoid extra margins
                                                    shouldUpdate={(prevValues, curValues) =>
                                                        // Only re-render if the 'type' of this specific field changes
                                                        prevValues.custom_fields?.[name]?.type !== curValues.custom_fields?.[name]?.type
                                                    }
                                                >
                                                    {({ getFieldValue, setFieldValue }) => {
                                                        const fieldType = getFieldValue(['custom_fields', name, 'type']) || 'text'; // Default to 'text' if undefined
                                                        return (
                                                            <>
                                                                <Form.Item
                                                                    {...restField} // Apply restField props here
                                                                    name={[name, 'value']} // Field name for the value
                                                                    rules={[{ required: true, message: 'Field value is required' }]} // Validation rules
                                                                    style={{ flex: 1 }} // Styling
                                                                >
                                                                    {/* Conditional Input Rendering based on fieldType */}
                                                                    {fieldType === 'password' ? (
                                                                        <Input.Password placeholder="Field Value" allowClear />
                                                                    ) : fieldType === 'textarea' ? (
                                                                        <Input.TextArea placeholder="Field Value" rows={2} />
                                                                    ) : (
                                                                        <Input placeholder="Field Value" />
                                                                    )}
                                                                </Form.Item>
                                                                {/* Ensure PasswordGenerator calls onPasswordGenerate prop */}
                                                                {fieldType === 'password' && (
                                                                    <PasswordGenerator
                                                                        onPasswordGenerate={
                                                                            password => setFieldValue(['custom_fields', name, 'value'], password)
                                                                        }
                                                                    />
                                                                )}
                                                            </>
                                                        );
                                                    }}
                                                </Form.Item>
                                            </Flex>
                                        </Flex>
                                        <Flex vertical justify='center' style={{ paddingBottom: 24, flexShrink: 0 }} >
                                            <MinusCircleOutlined onClick={() => remove(name)} />
                                        </Flex>
                                    </Flex>
                                </Card>
                            ))}
                            <Form.Item>
                                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />} disabled={isLocked}>
                                    Add Custom Field
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>

                <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<PlusOutlined />} disabled={isLocked}>
                        Add Credential
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
}