import React, { useMemo, useRef, useState } from 'react'; // Add useState
import { List, Button, Tooltip, Typography, Layout, Spin, message, Modal, theme } from 'antd';
import {
    CopyOutlined,
    EyeOutlined,
    EditOutlined,
    DeleteOutlined,
    InfoCircleOutlined, // Add InfoCircleOutlined for view details
} from '@ant-design/icons';
import type { CredentialOutput, CustomField } from '../types'; // Import CustomField
import pinyin from 'pinyin/esm/pinyin'; // Ensure pinyin is installed
import { useCredentials } from '../contexts/CredentialsContext';
import { writeText } from '@tauri-apps/plugin-clipboard-manager'; // Ensure @tauri-apps/plugin-clipboard-manager is installed and configured
import { CredentialDetailModal } from './CredentialDetailModal'; // Import the new modal

const { Text, Paragraph } = Typography;
const { confirm } = Modal;

interface CredentialListProps {
    searchTerm: string;
    // Callback to trigger opening the edit modal in the parent (VaultScreen)
    onEditRequest: (credential: CredentialOutput) => void;
}

// Helper function to determine grouping key (A-Z, #)
function getGroupKey(serviceName: string): string {
    if (!serviceName) return "#";
    const firstChar = serviceName.trim().charAt(0);
    if (/\d/.test(firstChar)) return "#"; // Digits go to '#'
    if (/^[A-Za-z]$/.test(firstChar)) return firstChar.toUpperCase(); // Latin letters

    // Handle Chinese characters using pinyin
    const py = pinyin(firstChar, { style: pinyin.STYLE_FIRST_LETTER, heteronym: false })[0]?.[0];
    if (py && /^[A-Za-z]$/.test(py)) return py.toUpperCase();

    return "#"; // Default group
}

export function CredentialList({ searchTerm, onEditRequest }: CredentialListProps) {
    const { credentials, isLoading, remove } = useCredentials();
    const [deletingId, setDeletingId] = React.useState<number | null>(null);
    const anchorRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [selectedCredential, setSelectedCredential] = useState<CredentialOutput | null>(null);

    // Filtering logic
    const filteredCredentials = useMemo(() => {
        if (!searchTerm) return credentials;
        const lowerSearchTerm = searchTerm.toLowerCase();
        const terms = lowerSearchTerm.split(/\s+/).filter(Boolean); // Split by space for multi-keyword search

        return credentials.filter(item => {
            const serviceLower = (item.service_name || "").toLowerCase();
            const usernameLower = (item.username || "").toLowerCase();
            const notesLower = (item.notes || "").toLowerCase();

            // Check if *all* search terms are included in any of the fields
            return terms.every(term =>
                serviceLower.includes(term) ||
                usernameLower.includes(term) ||
                notesLower.includes(term)
            );
        });
    }, [credentials, searchTerm]);

    // Grouping logic
    const groupedCredentials = useMemo(() => {
        const groups: Record<string, CredentialOutput[]> = {};
        filteredCredentials.forEach(item => {
            const key = getGroupKey(item.service_name);
            if (!groups[key]) groups[key] = [];
            groups[key].push(item);
        });
        // Sort keys alphabetically, with '#' at the end
        const sortedKeys = Object.keys(groups).sort((a, b) => {
            if (a === "#") return 1;
            if (b === "#") return -1;
            return a.localeCompare(b);
        });
        return { groups, sortedKeys };
    }, [filteredCredentials]);

    // Anchor scroll handler
    const handleAnchorClick = (key: string) => {
        const ref = anchorRefs.current[key];
        if (ref) {
            ref.scrollIntoView({ behavior: "smooth", block: "start" });
        }
    };

    const handleDelete = (id: number) => {
        confirm({
            title: 'Confirm Deletion',
            icon: <DeleteOutlined style={{ color: 'red' }} />,
            content: `Are you sure you want to delete this credential (ID: ${id})? This action cannot be undone.`,
            okText: 'Delete',
            okType: 'danger',
            cancelText: 'Cancel',
            onOk: async () => {
                setDeletingId(id);
                try {
                    await remove(id); // Call context action
                    // Success message handled in context
                } catch (err) {
                    // Error message handled in context
                } finally {
                    setDeletingId(null);
                }
            },
        });
    };

    const handleShowPassword = (password: string) => {
        Modal.info({
            title: 'Password',
            // Make modal width adjustable or wider for long passwords
            width: 600,
            content: (
                <Paragraph copyable={{ text: password, onCopy: () => message.success("Password copied!") }}>
                    {/* Allow text wrapping and use monospace */}
                    <Text style={{ fontFamily: 'monospace', fontSize: '1.1em', wordBreak: 'break-all' }}>{password}</Text>
                </Paragraph>
            ),
            okText: 'Close',
        });
    };

    const handleCopyPassword = async (text: string) => {
        try {
            await writeText(text);
            message.success('Password copied to clipboard!');
        } catch (err) {
            message.error('Failed to copy to clipboard.');
            console.error("Clipboard error:", err);
        }
    };

    const handleShowDetails = (credential: CredentialOutput) => {
        setSelectedCredential(credential);
        setDetailModalVisible(true);
    };

    const handleCloseDetailModal = () => {
        setDetailModalVisible(false);
        setSelectedCredential(null); // Clear selected credential when closing
    };

    const {
        token: { colorBgContainer },
    } = theme.useToken();

    return (
        <Spin spinning={isLoading}>
            <Layout style={{ position: 'relative', backgroundColor: colorBgContainer }}>
                {/* Add paddingRight to Layout.Content to prevent overlap with fixed anchors */}
                <Layout.Content style={{ paddingRight: groupedCredentials.sortedKeys.length > 1 ? 0 : 0 }}>
                    {credentials.length === 0 && !isLoading ? (
                        <Text type="secondary">No credentials stored yet.</Text>
                    ) : filteredCredentials.length === 0 && !isLoading ? (
                         <Text type="secondary">No credentials match your search term.</Text>
                    ) : (groupedCredentials.sortedKeys.map(key => (
                        <div key={key} ref={el => (anchorRefs.current[key] = el)} style={{ marginBottom: 15 }}>
                            <div style={{
                                fontWeight: 700,
                                fontSize: 20,
                                paddingTop: 12, // Add padding top for spacing after scroll
                                paddingBottom: 8,
                                color: '#1677ff',
                                position: 'sticky', // Make group header sticky
                                top: 0, // Stick to the top of the scrolling container
                                background: colorBgContainer, // Background to cover content underneath
                                zIndex: 10 // Ensure header is above list items
                            }}>{key}</div>
                            <List
                                itemLayout="horizontal"
                                dataSource={groupedCredentials.groups[key]}
                                renderItem={(item) => (
                                    <List.Item
                                        actions={[
                                            <Tooltip title="View Details" key="view">
                                                <Button type="text" icon={<InfoCircleOutlined />} onClick={() => handleShowDetails(item)} />
                                            </Tooltip>,
                                            <Tooltip title="Show Password" key="show">
                                                <Button type="text" icon={<EyeOutlined />} onClick={() => handleShowPassword(item.password)} />
                                            </Tooltip>,
                                            <Tooltip title="Copy Password" key="copy">
                                                <Button type="text" icon={<CopyOutlined />} onClick={() => handleCopyPassword(item.password)} />
                                            </Tooltip>,
                                            <Tooltip title="Edit Credential" key="edit">
                                                <Button type="text" icon={<EditOutlined />} onClick={() => onEditRequest(item)} />
                                            </Tooltip>,
                                            <Tooltip title="Delete Credential" key="delete">
                                                <Button type="text" danger icon={<DeleteOutlined />} onClick={() => handleDelete(item.id)} loading={deletingId === item.id} />
                                            </Tooltip>,
                                        ]}
                                    >
                                        <List.Item.Meta
                                            title={<Text strong>{item.service_name}</Text>}
                                            description={
                                                <>
                                                    <Text>Username: {item.username}</Text><br />
                                                    {item.notes && <Text type="secondary">Notes: {item.notes}<br /></Text>}
                                                    {/* Render Custom Fields */}
                                                    {item.custom_fields && (() => {
                                                        try {
                                                            const fields: CustomField[] = item.custom_fields;
                                                            if (Array.isArray(fields) && fields.length > 0) {
                                                                return fields.map((field, index) => (
                                                                    <div key={`cf-${item.id}-${index}`}>
                                                                        <Text strong>{field.title}: </Text>
                                                                        {field.type === 'password' ? (
                                                                            <Text code>********</Text> // Mask password type fields
                                                                        ) : (
                                                                            <Text>{field.value}</Text>
                                                                        )}
                                                                        {/* Add copy/show buttons for custom fields if needed */}
                                                                        <br />
                                                                    </div>
                                                                ));
                                                            }
                                                        } catch (e) {
                                                            console.error("Failed to parse custom fields for item", item.id, e);
                                                            return <Text type="danger">Error loading custom fields.<br /></Text>;
                                                        }
                                                        return null;
                                                    })()}
                                                    <Text type="secondary" style={{ fontSize: '0.8em', display: 'block', marginTop: '5px' }}>
                                                        Last Updated: {new Date(item.updated_at).toLocaleString()}
                                                    </Text>
                                                    <Text type="secondary" style={{ fontSize: '0.8em', display: 'block', marginTop: '5px' }}>
                                                        Created: {new Date(item.created_at).toLocaleString()}
                                                    </Text>
                                                </>
                                            }
                                        />
                                    </List.Item>
                                )}
                            />
                        </div>
                    ))
                    )}
                </Layout.Content>

                {/* Right-side Anchor Navigation - only show if needed */}
                {groupedCredentials.sortedKeys.length > 1 && (
                    <div style={{
                        position: 'fixed', // Fixed position relative to viewport
                        top: 120, // Adjust top position as needed
                        right: 24, // Adjust right position as needed
                        zIndex: 100,
                        background: colorBgContainer, // Slightly transparent background
                        borderRadius: 8,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        padding: '8px 4px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        maxHeight: 'calc(100vh - 160px)', // Limit height to prevent excessive length
                        overflowY: 'auto', // Add scroll if keys exceed height
                        scrollbarWidth: 'thin', // For Firefox
                    }}>
                        {groupedCredentials.sortedKeys.map(key => (
                            <div key={key}
                                 style={{ cursor: 'pointer', padding: '4px 8px', fontWeight: 600, color: '#1677ff', fontSize: 16, textAlign: 'center' }}
                                 onClick={() => handleAnchorClick(key)}
                                 title={`Go to group ${key}`} // Add tooltip
                                 >
                                {key}
                            </div>
                        ))}
                    </div>
                )}
            </Layout>
            {/* Render the Detail Modal */}
            <CredentialDetailModal
                visible={detailModalVisible}
                credential={selectedCredential}
                onCancel={handleCloseDetailModal}
            />
        </Spin>
    );
}