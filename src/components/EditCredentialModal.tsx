import { useEffect, useState } from 'react';
import { Modal, Form, Input, message, Select, Button, Flex, Card, List, Typography, Space } from 'antd';
import { PlusOutlined, MinusCircleOutlined, HistoryOutlined } from '@ant-design/icons';
import type { CredentialOutput, CustomField, CredentialInput, PasswordHistoryEntry } from '../types';
import PasswordGenerator from './PasswordGenerator'; // Assuming this exists
import { useCredentials } from '../contexts/CredentialsContext';
import { useVaultAuth } from '../contexts/VaultAuthContext';
import { getPasswordHistory } from '../api/tauri-api'; // Import the API function

interface EditCredentialModalProps {
    visible: boolean;
    credential: CredentialOutput | null;
    onCancel: () => void; // Still need cancel callback
}

export function EditCredentialModal({ visible, credential, onCancel }: EditCredentialModalProps) {
    const { update } = useCredentials();
    const { isLocked } = useVaultAuth();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false); // Local loading state for modal ok button
    const [historyVisible, setHistoryVisible] = useState(false);
    const [historyLoading, setHistoryLoading] = useState(false);
    const [historyData, setHistoryData] = useState<PasswordHistoryEntry[]>([]);
    const [historyFieldName, setHistoryFieldName] = useState<string>('');

    // Effect to pre-fill form when modal opens or credential changes
    useEffect(() => {
        if (visible && credential) {
            let customFieldsData: CustomField[] = [];
            try {
                if (credential.custom_fields) {
                    customFieldsData = credential.custom_fields;
                    if (!Array.isArray(customFieldsData)) {
                        console.warn('Custom fields data is not an array, resetting.');
                        customFieldsData = [];
                    }
                }
            } catch (e) {
                console.error('Failed to parse custom fields JSON:', e);
                message.error('Failed to load custom fields data.');
                customFieldsData = []; // Reset on parse error
            }

            form.setFieldsValue({
                service_name: credential.service_name,
                username: credential.username,
                password: credential.password, // Pre-fill with existing password
                notes: credential.notes || '',
                custom_fields: customFieldsData, // Set parsed custom fields
            });
        }
        // Form is reset automatically due to destroyOnHidden in Modal props
    }, [visible, credential, form]);


    const handleOk = async () => {
        if (!credential) return; // Should not happen if visible is true
        setLoading(true);
        try {
            const formValues = await form.validateFields(); // Validate fields before calling API

            // Prepare data for backend, including serializing custom fields
            const credentialData: CredentialInput = {
                service_name: formValues.service_name,
                username: formValues.username,
                password: formValues.password,
                notes: formValues.notes,
                custom_fields: formValues.custom_fields ? formValues.custom_fields.filter((f: any) => f && f.title) : undefined,
            };

            await update(credential.id, credentialData); // Call context update action with serialized data
            // Success message handled in context
            onCancel(); // Close modal on success
        } catch (errInfo) {
            // Error message handled in context, but log validation errors
            if (errInfo && typeof errInfo === 'object' && 'errorFields' in errInfo) {
                console.log('Validation Failed:', errInfo);
                message.error('Please check the form fields.');
            }
            // API errors are handled by context and will show message.error
            // Do not close modal on validation or API error
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordGenerate = (password: string) => {
        form.setFieldsValue({ 'password': password });
    };

    // Function to fetch and display password history
    const fetchAndShowHistory = async (fieldName: string) => {
        if (!credential) return;
        setHistoryFieldName(fieldName);
        setHistoryLoading(true);
        setHistoryVisible(true);
        try {
            const history = await getPasswordHistory(credential.id, fieldName);
            setHistoryData(history);
        } catch (error) {
            // Error message is handled in the API function
            setHistoryVisible(false); // Close modal on error
        } finally {
            setHistoryLoading(false);
        }
    };

    const handleCloseHistoryModal = () => {
        setHistoryVisible(false);
        setHistoryData([]); // Clear data when closing
        setHistoryFieldName('');
    };

    const InputPasswordWithHistory = (props: any) => {
        const { historyBtnOnClick, historyBtnDisabled, ...form_props } = props;
        return (
            <Space.Compact style={{ width: '100%' }}>
                <Input.Password {...form_props} placeholder="Field Value" allowClear style={{ flex: 1 }} />
                {credential && (
                    <Button
                        icon={<HistoryOutlined />}
                        onClick={historyBtnOnClick}
                        disabled={historyBtnDisabled}
                    />
                )}
            </Space.Compact>
        )
    }

    return (
        <Modal
            title="Edit Credential"
            open={visible}
            onOk={handleOk} // Use internal handler
            onCancel={onCancel} // Use prop for cancel
            confirmLoading={loading} // Use local loading state for button
            okText="Save"
            cancelText="Cancel"
            forceRender // Keep form instance available even if not visible
            destroyOnHidden // Destroy form state when modal is closed
        >
            <Form
                form={form}
                layout="vertical"
                requiredMark={false} // Adjust based on preference
            >
                <Form.Item
                    name="service_name"
                    label="Service Name"
                    rules={[{ required: true, message: 'Please enter service name' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="username"
                    label="Username / Email"
                    rules={[{ required: true, message: 'Please enter username' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                >
                    <InputPasswordWithHistory
                        historyBtnOnClick={() => fetchAndShowHistory('password')}
                        historyBtnDisabled={!form.getFieldValue('password')} // Disable if password is empty
                    />
                </Form.Item>
                 <div style={{ marginBottom: 20 }}>
                    <PasswordGenerator onPasswordGenerate={handlePasswordGenerate} />
                 </div>
                <Form.Item
                    name="notes"
                    label="Notes (Optional)"
                >
                    <Input.TextArea rows={2} />
                </Form.Item>

                {/* Dynamic Custom Fields Section */}
                <Form.List name="custom_fields">
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ key, name, ...restField }) => (
                                <Card key={key} title="Custom fields" style={{ margin: '0 auto 10px', borderStyle: 'dashed' }} hoverable>
                                    <Flex gap="small" style={{ marginBottom: 8 }}>
                                        <Flex vertical style={{flex: 1, width: '100%' }}>
                                            <Flex gap="middle">
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'title']}
                                                    rules={[{ required: true, message: 'Field title is required' }]}
                                                    style={{ flex: 1 }}
                                                >
                                                    <Input placeholder="Field Title (e.g., Security Question)" />
                                                </Form.Item>
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'type']}
                                                    initialValue="text" // Default type
                                                    style={{ width: '120px' }}
                                                >
                                                    <Select>
                                                        <Select.Option value="text">Text</Select.Option>
                                                        <Select.Option value="password">Password</Select.Option>
                                                        <Select.Option value="textarea">Textarea</Select.Option>
                                                        {/* Add more types if needed */}
                                                    </Select>
                                                </Form.Item>
                                            </Flex>
                                            <Flex vertical>
                                                {/* Use Form.Item's render prop with shouldUpdate for dynamic rendering */}
                                                <Form.Item
                                                    noStyle // Use noStyle on the wrapper to avoid extra margins
                                                    shouldUpdate={(prevValues, curValues) =>
                                                        // Only re-render if the 'type' of this specific field changes
                                                        prevValues.custom_fields?.[name]?.type !== curValues.custom_fields?.[name]?.type
                                                    }
                                                >
                                                    {({ getFieldValue, setFieldValue }) => {
                                                        const fieldType = getFieldValue(['custom_fields', name, 'type']) || 'text'; // Default to 'text' if undefined
                                                        return (
                                                            <>
                                                                <Form.Item
                                                                    {...restField} // Apply restField props here
                                                                    name={[name, 'value']} // Field name for the value
                                                                    rules={[{ required: true, message: 'Field value is required' }]} // Validation rules
                                                                    style={{ flex: 1 }} // Styling
                                                                >
                                                                    {/* Conditional Input Rendering based on fieldType */}
                                                                    {fieldType === 'password' ? (
                                                                        <InputPasswordWithHistory
                                                                            historyBtnOnClick={() => fetchAndShowHistory(form.getFieldValue(['custom_fields', name, 'title']))}
                                                                            historyBtnDisabled={!form.getFieldValue(['custom_fields', name, 'title'])} // Disable if password is empty
                                                                        />
                                                                    ) : fieldType === 'textarea' ? (
                                                                        <Input.TextArea placeholder="Field Value" rows={2} />
                                                                    ) : (
                                                                        <Input placeholder="Field Value" />
                                                                    )}
                                                                </Form.Item>
                                                                {/* Ensure PasswordGenerator calls onPasswordGenerate prop */}
                                                                {fieldType === 'password' && (
                                                                    <PasswordGenerator
                                                                        triggerOnGenerateCallbackOnInitialRender={false}
                                                                        onPasswordGenerate={
                                                                            password => setFieldValue(['custom_fields', name, 'value'], password)
                                                                        }
                                                                    />
                                                                )}
                                                            </>
                                                        );
                                                    }}
                                                </Form.Item>
                                            </Flex>
                                        </Flex>
                                        <Flex vertical justify='center' style={{ paddingBottom: 24, flexShrink: 0 }} >
                                            <MinusCircleOutlined onClick={() => remove(name)} />
                                        </Flex>
                                    </Flex>
                                </Card>
                            ))}
                            <Form.Item>
                                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />} disabled={isLocked}>
                                    Add Custom Field
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form>

            {/* Password History Modal */}
            <Modal
                title={`History for "${historyFieldName}"`}
                open={historyVisible}
                onCancel={handleCloseHistoryModal}
                footer={null} // No OK/Cancel buttons needed
                width={600}
            >
                <List
                    loading={historyLoading}
                    itemLayout="horizontal"
                    dataSource={historyData}
                    renderItem={(item) => (
                        <List.Item>
                            <List.Item.Meta
                                title={<Typography.Text copyable>{item.old_value}</Typography.Text>}
                                description={`Changed at: ${new Date(item.changed_at).toLocaleString()}`}
                            />
                        </List.Item>
                    )}
                    locale={{ emptyText: 'No history found for this field.' }}
                />
            </Modal>
        </Modal>
    );
}