import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, ChevronDown, Star, Share2, Edit, MoreHorizontal } from 'lucide-react';
import { useLocalStorage } from 'react-use';

/**
 * 密码项目接口定义
 */
interface PasswordItem {
  id: string;
  title: string;
  timestamp: string;
  icon: string;
  bgColor: string;
}

/**
 * 分类标签接口定义
 */
interface CategoryTab {
  name: string;
  active: boolean;
}

/**
 * 首页新版组件
 * 严格按照Figma设计稿还原UI
 */
const HomePageNew: React.FC = () => {
  // 分类标签状态
  const [categories] = useState<CategoryTab[]>([
    { name: '登录类型', active: false },
    { name: '全部', active: true },
    { name: '密码类', active: false },
    { name: '通行密钥', active: false },
    { name: 'OTP令牌', active: false },
    { name: '数字钱包', active: false },
  ]);

  // 密码项目数据
  const [passwordItems] = useState<PasswordItem[]>([
    { id: '1', title: '18611894998', timestamp: '05/25 20:20', icon: '$', bgColor: '#FF9500' },
    { id: '2', title: '18611894998', timestamp: '05/25 20:20', icon: '百', bgColor: '#007AFF' },
    { id: '3', title: '18611894998', timestamp: '05/25 20:20', icon: '豆', bgColor: '#34C759' },
    { id: '4', title: '18611894998', timestamp: '05/25 20:20', icon: '微', bgColor: '#00C851' },
    { id: '5', title: '18611894998', timestamp: '05/25 20:20', icon: 'Pa', bgColor: '#2E7D32' },
    { id: '6', title: '18611894998', timestamp: '05/25 20:20', icon: '🛡', bgColor: '#FF3B30' },
    { id: '7', title: '18611894998', timestamp: '05/25 20:20', icon: 'Ai', bgColor: '#FF6B35' },
    { id: '8', title: '18611894998', timestamp: '05/25 20:20', icon: 'in', bgColor: '#0077B5' },
    { id: '9', title: '18611894998', timestamp: '05/25 20:20', icon: 'On', bgColor: '#0078D4' },
    { id: '10', title: '18611894998', timestamp: '05/25 20:20', icon: '新', bgColor: '#E60012' },
  ]);

  // 选中的密码项目
  const [selectedItemId, setSelectedItemId] = useLocalStorage<string>('selected-password-item', '2');

  /**
   * 渲染分类标签
   */
  const renderCategoryTabs = () => (
    <div className="flex space-x-1 mb-6">
      {categories.map((category, index) => (
        <Button
          key={index}
          variant={category.active ? "default" : "ghost"}
          size="sm"
          className={`h-8 px-4 text-sm font-normal rounded-full transition-all duration-200 ${
            category.active
              ? "bg-[#007AFF] text-white hover:bg-[#0056CC] shadow-lg shadow-[#007AFF]/25"
              : "text-[#666] hover:bg-[#F0F0F0] hover:shadow-md"
          }`}
          style={{
            animationDelay: `${index * 0.05}s`,
          }}
        >
          {category.name}
        </Button>
      ))}
    </div>
  );

  /**
   * 渲染顶部工具栏
   */
  const renderTopToolbar = () => (
    <div className="flex items-center justify-between mb-6">
      {/* 左侧操作区 */}
      <div className="flex items-center space-x-4">
        <Button
          size="icon"
          className="w-12 h-12 rounded-full bg-[#5AC8FA] hover:bg-[#32ADE6] text-white transition-all duration-200 hover:scale-105 hover:shadow-lg shadow-[#5AC8FA]/25"
        >
          <Plus className="w-6 h-6" />
        </Button>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-[#666]">
            已全部加载，共{passwordItems.length}项
          </span>
          
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3 text-sm border-[#D1D1D1] transition-all duration-200 hover:shadow-md"
          >
            <span>按创建时间排序</span>
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>

      {/* 右侧操作区 */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] hover:text-[#333] transition-colors"
        >
          <Share2 className="w-4 h-4" />
          <span className="text-sm">分享</span>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] hover:text-[#333] transition-colors"
        >
          <Edit className="w-4 h-4" />
          <span className="text-sm">编辑</span>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] hover:text-[#333] transition-colors"
        >
          <Star className="w-4 h-4" />
          <span className="text-sm">添加到收藏夹</span>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 text-[#666] hover:text-[#333] transition-colors"
        >
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );

  /**
   * 渲染密码项目卡片
   */
  const renderPasswordItem = (item: PasswordItem, index: number) => (
    <div
      key={item.id}
      className={`w-[120px] h-[120px] p-3 cursor-pointer transition-all duration-200 rounded-lg ${
        selectedItemId === item.id
          ? "bg-[#E3F2FD] shadow-md"
          : "hover:bg-[#F5F5F5] hover:shadow-sm"
      }`}
      style={{
        animationDelay: `${index * 0.05}s`,
      }}
      onClick={() => setSelectedItemId(item.id)}
    >
      <div className="flex flex-col items-center h-full">
        {/* 图标 */}
        <div
          className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-medium text-lg mb-3 shadow-sm transition-transform duration-200 hover:scale-105"
          style={{ backgroundColor: item.bgColor }}
        >
          {item.icon}
        </div>
        
        {/* 文本信息 */}
        <div className="text-center flex-1 flex flex-col justify-center">
          <div className="text-sm font-medium text-[#333] mb-1 truncate w-full">
            {item.title}
          </div>
          <div className="text-xs text-[#999]">
            {item.timestamp}
          </div>
        </div>
      </div>
    </div>
  );

  /**
   * 渲染密码网格
   */
  const renderPasswordGrid = () => (
    <div className="grid grid-cols-6 gap-4 mb-8">
      {passwordItems.map((item, index) => renderPasswordItem(item, index))}
    </div>
  );

  /**
   * 渲染侧边栏标题
   */
  const renderSidebarTitles = () => (
    <div className="flex items-center space-x-8 mb-6">
      <div className="flex items-center space-x-3">
        <div className="w-6 h-6 text-[#007AFF]">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
        </div>
        <span className="text-lg font-medium text-[#333]">保险柜</span>
      </div>
      
      <div className="flex items-center space-x-3">
        <Star className="w-5 h-5 text-[#666]" />
        <span className="text-lg font-medium text-[#666]">收藏夹</span>
      </div>
    </div>
  );

  return (
    <div className="flex-1 bg-white p-6 min-h-screen">
      {/* 侧边栏标题 */}
      {renderSidebarTitles()}
      
      {/* 分类标签 */}
      {renderCategoryTabs()}
      
      {/* 顶部工具栏 */}
      {renderTopToolbar()}
      
      {/* 密码网格 */}
      {renderPasswordGrid()}
      
      {/* 底部提示 */}
      <div className="text-center text-[#999] text-sm py-8">
        没有更多了
      </div>
    </div>
  );
};

export default HomePageNew;