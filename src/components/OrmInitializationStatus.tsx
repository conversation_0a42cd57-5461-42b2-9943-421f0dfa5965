import React, { useState } from 'react';
import { Alert, Progress, Spin, Typography, Card, Space, Button, message } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  LoadingOutlined,
  LockOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useOrmInitialization } from '../hooks/useOrmInitialization';
import { OrmInitializationStatus } from '../types/orm-initialization';
import { retryOrmServiceInitialization } from '../api/orm-api';

const { Text, Title } = Typography;

/**
 * 获取状态对应的图标
 */
function getStatusIcon(status: OrmInitializationStatus['status']) {
  switch (status) {
    case 'checking_crypto_system':
      return <SafetyOutlined spin style={{ color: '#1890ff' }} />;
    case 'initializing_crypto':
      return <LockOutlined style={{ color: '#1890ff' }} />;
    case 'waiting_for_unlock':
      return <LoadingOutlined style={{ color: '#faad14' }} />;
    case 'initializing_database':
      return <DatabaseOutlined style={{ color: '#1890ff' }} />;
    case 'success':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'timeout':
    case 'crypto_error':
    case 'database_error':
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    default:
      return <LoadingOutlined />;
  }
}

/**
 * 获取状态对应的 Alert 类型
 */
function getAlertType(status: OrmInitializationStatus['status']): 'success' | 'info' | 'warning' | 'error' {
  switch (status) {
    case 'success':
      return 'success';
    case 'waiting_for_unlock':
      return 'warning';
    case 'timeout':
    case 'crypto_error':
    case 'database_error':
      return 'error';
    default:
      return 'info';
  }
}

interface OrmInitializationStatusProps {
  /** 是否显示为卡片形式 */
  showAsCard?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

/**
 * ORM 初始化状态显示组件
 * 
 * 显示 ORM 服务的初始化进度和状态信息
 */
export function OrmInitializationStatusComponent({ 
  showAsCard = false, 
  style, 
  className 
}: OrmInitializationStatusProps) {
  const { 
    status, 
    error, 
    isError, 
    isSuccess, 
    isProgress 
  } = useOrmInitialization();
  
  const [isRetrying, setIsRetrying] = useState(false);

  /**
   * 处理重试初始化
   */
  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      const result = await retryOrmServiceInitialization();
      message.success(result);
    } catch (error) {
      message.error(`重试失败: ${error}`);
    } finally {
      setIsRetrying(false);
    }
  };

  // 如果没有状态信息，不显示组件
  if (!status) {
    return null;
  }

  const content = (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      {/* 主要状态显示 */}
      <Alert
        message={
          <Space>
            {getStatusIcon(status.status)}
            <Text strong>{status.message}</Text>
          </Space>
        }
        type={getAlertType(status.status)}
        showIcon={false}
        style={{ marginBottom: 0 }}
      />

      {/* 进度条（仅在等待解锁时显示） */}
      {status.status === 'waiting_for_unlock' && status.progress !== undefined && (
        <div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            等待进度
          </Text>
          <Progress 
            percent={status.progress} 
            size="small"
            status={status.progress >= 100 ? 'exception' : 'active'}
            format={(percent) => `${percent}%`}
          />
        </div>
      )}

      {/* 加载指示器（进行中状态） */}
      {isProgress && (
        <div style={{ textAlign: 'center', padding: '8px 0' }}>
          <Spin 
            indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
            tip="正在处理中..."
          />
        </div>
      )}

      {/* 错误详情 */}
      {isError && error && (
        <Alert
          message="详细错误信息"
          description={error}
          type="error"
          showIcon
          style={{ fontSize: '12px' }}
        />
      )}

      {/* 成功提示 */}
      {isSuccess && (
        <Alert
          message="初始化完成"
          description="ORM 服务已成功初始化，您现在可以正常使用密码管理功能。"
          type="success"
          showIcon
        />
      )}

      {/* 操作建议 */}
      {status.status === 'waiting_for_unlock' && (
        <Alert
          message="需要用户操作"
          description="请在主界面解锁您的保险库以继续初始化过程。"
          type="warning"
          showIcon
        />
      )}

      {status.status === 'timeout' && (
        <Alert
          message="初始化超时"
          description={
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text>ORM 服务初始化超时，请检查保险库状态或手动重试。</Text>
              <Button
                type="primary"
                size="small"
                icon={<ReloadOutlined />}
                loading={isRetrying}
                onClick={handleRetry}
              >
                重试初始化
              </Button>
            </Space>
          }
          type="error"
          showIcon
        />
      )}

      {/* 通用重试按钮（用于其他错误状态） */}
      {isError && status.status !== 'timeout' && (
        <div style={{ textAlign: 'center', marginTop: '8px' }}>
          <Button
            type="primary"
            size="small"
            icon={<ReloadOutlined />}
            loading={isRetrying}
            onClick={handleRetry}
          >
            重试初始化
          </Button>
        </div>
      )}
    </Space>
  );

  if (showAsCard) {
    return (
      <Card
        title={
          <Space>
            <DatabaseOutlined />
            <Title level={5} style={{ margin: 0 }}>
              ORM 服务状态
            </Title>
          </Space>
        }
        size="small"
        style={style}
        className={className}
      >
        {content}
      </Card>
    );
  }

  return (
    <div style={style} className={className}>
      {content}
    </div>
  );
}

export default OrmInitializationStatusComponent; 