import React, { useState } from 'react';
import { Card, Button, Input, Radio, Slider, Switch, Typography, Space, message } from 'antd';
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons';

const { Text } = Typography;

type PasswordType = 'random' | 'memorable' | 'pin';

function getRandomPassword(length: number, useNumbers: boolean, useSymbols: boolean) {
    const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-={}[]|:;<>,.?/~';
    let chars = letters;
    if (useNumbers) chars += numbers;
    if (useSymbols) chars += symbols;
    let pwd = '';
    for (let i = 0; i < length; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return pwd;
}

function getMemorablePassword(wordsCount: number, capitalize: boolean, useFullWords: boolean) {
    // 简单实现：用内置词表
    const wordList = [
        'apple', 'banana', 'cat', 'dog', 'eagle', 'fish', 'grape', 'house', 'island', 'jungle', 'kite', 'lemon', 'mountain', 'night', 'orange', 'piano', 'queen', 'river', 'star', 'tree', 'umbrella', 'vivid', 'wolf', 'xenon', 'yellow', 'zebra', 'quirky', 'cyprus', 'gaines', 'fifteen'
    ];
    let words = [];
    for (let i = 0; i < wordsCount; i++) {
        let w = wordList[Math.floor(Math.random() * wordList.length)];
        if (capitalize && i === 0) w = w.charAt(0).toUpperCase() + w.slice(1);
        words.push(w);
    }
    if (useFullWords) {
        return words.join('-');
    } else {
        // 只取前几个字母
        return words.map(w => w.slice(0, 3)).join('-');
    }
}

function getPin(length: number) {
    let pin = '';
    for (let i = 0; i < length; i++) {
        pin += Math.floor(Math.random() * 10);
    }
    return pin;
}

const PasswordGenerator: React.FC<{
    onPasswordGenerate?: (pwd: string) => void,
    triggerOnGenerateCallbackOnInitialRender?: boolean,
}> = ({ onPasswordGenerate, triggerOnGenerateCallbackOnInitialRender = true }) => {
    const [type, setType] = useState<PasswordType>('random');
    const [length, setLength] = useState(20);
    const [useNumbers, setUseNumbers] = useState(true);
    const [useSymbols, setUseSymbols] = useState(false);
    const [memorableCap, setMemorableCap] = useState(false);
    const [memorableFull, setMemorableFull] = useState(true);
    const [memorableCount, setMemorableCount] = useState(4);
    const [pinLength, setPinLength] = useState(6);
    const [password, setPassword] = useState('');

    const generate = () => {
        let pwd = '';
        if (type === 'random') {
            pwd = getRandomPassword(length, useNumbers, useSymbols);
        } else if (type === 'memorable') {
            pwd = getMemorablePassword(memorableCount, memorableCap, memorableFull);
        } else if (type === 'pin') {
            pwd = getPin(pinLength);
        }
        setPassword(pwd);
        if (triggerOnGenerateCallbackOnInitialRender && onPasswordGenerate) onPasswordGenerate(pwd);
        return pwd;
    };

    const copy = () => {
        if (onPasswordGenerate) onPasswordGenerate(password);
        navigator.clipboard.writeText(password).then(() => {
            message.success('已复制到剪贴板');
        });
    };

    const refresh = () => {
        const pwd = generate();
        if (onPasswordGenerate) onPasswordGenerate(pwd);
    }

    React.useEffect(() => {
        generate();
        // eslint-disable-next-line
    }, [type, length, useNumbers, useSymbols, memorableCap, memorableFull, memorableCount, pinLength]);

    return (
        <Card title="密码生成器" style={{ maxWidth: 420, margin: '0 auto' }}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <Radio.Group
                    value={type}
                    onChange={e => setType(e.target.value)}
                    style={{ width: '100%' }}
                >
                    <Radio.Button value="random" style={{ width: '33%' }}>随机</Radio.Button>
                    <Radio.Button value="memorable" style={{ width: '33%' }}>易记</Radio.Button>
                    <Radio.Button value="pin" style={{ width: '33%' }}>PIN</Radio.Button>
                </Radio.Group>
                {type === 'random' && (
                    <>
                        <div>
                            <span>长度：</span>
                            <Slider min={8} max={32} value={length} onChange={setLength} style={{ width: 180, display: 'inline-block' }} />
                            <Input style={{ width: 50, marginLeft: 8 }} value={length} onChange={e => setLength(Number(e.target.value))} />
                        </div>
                        <div>
                            <span>数字</span>
                            <Switch checked={useNumbers} onChange={setUseNumbers} style={{ margin: '0 8px' }} />
                            <span>符号</span>
                            <Switch checked={useSymbols} onChange={setUseSymbols} style={{ margin: '0 8px' }} />
                        </div>
                    </>
                )}
                {type === 'memorable' && (
                    <>
                        <div>
                            <span>单词数：</span>
                            <Slider min={3} max={6} value={memorableCount} onChange={setMemorableCount} style={{ width: 180, display: 'inline-block' }} />
                            <Input style={{ width: 50, marginLeft: 8 }} value={memorableCount} onChange={e => setMemorableCount(Number(e.target.value))} />
                        </div>
                        <div>
                            <span>首字母大写</span>
                            <Switch checked={memorableCap} onChange={setMemorableCap} style={{ margin: '0 8px' }} />
                            <span>全单词</span>
                            <Switch checked={memorableFull} onChange={setMemorableFull} style={{ margin: '0 8px' }} />
                        </div>
                    </>
                )}
                {type === 'pin' && (
                    <div>
                        <span>长度：</span>
                        <Slider min={4} max={12} value={pinLength} onChange={setPinLength} style={{ width: 180, display: 'inline-block' }} />
                        <Input style={{ width: 50, marginLeft: 8 }} value={pinLength} onChange={e => setPinLength(Number(e.target.value))} />
                    </div>
                )}
                <div>
                    <Text type="secondary">生成的密码</Text>
                    <Input value={password} readOnly style={{ fontWeight: 600, fontSize: 18, margin: '8px 0' }} />
                </div>
                <Space>
                    <Button icon={<CopyOutlined />} onClick={copy} disabled={!password}>复制密码</Button>
                    <Button icon={<ReloadOutlined />} onClick={refresh}>刷新密码</Button>
                </Space>
            </Space>
        </Card>
    );
};

export default PasswordGenerator;