import React, { useState, useEffect } from 'react';
import { Button, Space, Card, Typography, message, Divider } from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  PoweroffOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';

const { Title, Text } = Typography;

/**
 * 托盘控制组件
 *
 * 提供与系统托盘交互的用户界面，包括：
 * - 显示/隐藏主窗口
 * - 退出应用程序
 * - 检查托盘状态
 */
const TrayControls: React.FC = () => {
  const [isTrayAvailable, setIsTrayAvailable] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [trayStatusInfo, setTrayStatusInfo] = useState<string>('');

  /**
   * 检查托盘是否可用
   */
  const checkTrayAvailability = async () => {
    try {
      const available = await invoke<boolean>('is_tray_available_command');
      setIsTrayAvailable(available);

      // 获取详细状态信息
      try {
        const statusInfo = await invoke<string>('get_tray_status_info_command');
        setTrayStatusInfo(statusInfo);
      } catch (statusError) {
        console.warn('获取托盘状态信息失败:', statusError);
        setTrayStatusInfo('状态信息不可用');
      }

      return available;
    } catch (error) {
      console.error('检查托盘可用性失败:', error);
      message.error('检查托盘可用性失败');
      setTrayStatusInfo('检查失败');
      return false;
    }
  };

  /**
   * 显示主窗口
   */
  const handleShowWindow = async () => {
    setLoading(true);
    try {
      await invoke('show_main_window_command');
      message.success('主窗口已显示');
    } catch (error) {
      console.error('显示主窗口失败:', error);
      message.error('显示主窗口失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 隐藏窗口到托盘
   */
  const handleHideToTray = async () => {
    setLoading(true);
    try {
      await invoke('hide_to_tray_command');
      message.success('窗口已隐藏到托盘');
    } catch (error) {
      console.error('隐藏到托盘失败:', error);
      message.error('隐藏到托盘失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 退出应用程序
   */
  const handleQuitApplication = async () => {
    setLoading(true);
    try {
      await invoke('quit_application_command');
      // 如果成功调用，应用程序将退出，不会执行到这里
    } catch (error) {
      console.error('退出应用程序失败:', error);
      message.error('退出应用程序失败');
      setLoading(false);
    }
  };

  /**
   * 组件挂载时检查托盘可用性
   */
  useEffect(() => {
    checkTrayAvailability();
  }, []);

  return (
    <Card
      title="系统托盘控制"
      style={{ maxWidth: 600, margin: '20px auto' }}
      extra={
        <Button
          size="small"
          onClick={checkTrayAvailability}
          icon={<CheckCircleOutlined />}
        >
          检查状态
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 托盘状态显示 */}
        <div>
          <Text strong>托盘状态: </Text>
          <Text
            type={isTrayAvailable ? 'success' : 'danger'}
            style={{ fontWeight: 'bold' }}
          >
            {isTrayAvailable ? '✅ 可用' : '❌ 不可用'}
          </Text>
          {trayStatusInfo && (
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                详细信息: {trayStatusInfo}
              </Text>
            </div>
          )}
        </div>

        <Divider />

        {/* 控制按钮 */}
        <Space wrap>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={handleShowWindow}
            loading={loading}
            disabled={!isTrayAvailable}
          >
            显示主窗口
          </Button>

          <Button
            icon={<EyeInvisibleOutlined />}
            onClick={handleHideToTray}
            loading={loading}
            disabled={!isTrayAvailable}
          >
            隐藏到托盘
          </Button>

          <Button
            danger
            icon={<PoweroffOutlined />}
            onClick={handleQuitApplication}
            loading={loading}
            disabled={!isTrayAvailable}
          >
            退出应用
          </Button>
        </Space>

        <Divider />

        {/* 使用说明 */}
        <div>
          <Title level={5}>使用说明:</Title>
          <ul style={{ paddingLeft: '20px', margin: 0 }}>
            <li>点击窗口关闭按钮时，应用会最小化到系统托盘而不是退出</li>
            <li>右键点击托盘图标可以看到菜单选项</li>
            <li>双击托盘图标可以快速显示主窗口</li>
            <li>只有通过托盘菜单的"退出"选项才能真正关闭应用</li>
          </ul>
        </div>

        {!isTrayAvailable && (
          <div style={{
            padding: '12px',
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '6px'
          }}>
            <Text type="warning">
              ⚠️ 系统托盘不可用。请确保您的系统支持系统托盘功能。
            </Text>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default TrayControls;
