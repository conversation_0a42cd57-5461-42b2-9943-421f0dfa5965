import { Layout, Typography, Space, Spin, Alert } from 'antd';
import { useVaultAuth } from '../contexts/VaultAuthContext';

// Import Child Components
import { VaultSetup } from './VaultSetup';
import { VaultUnlock } from './VaultUnlock';
import { VaultScreen } from './VaultScreen'; // Use the renamed component
import LetterGlitch from './LetterGlitch'; // Use the renamed component

const { Title } = Typography;

function Vault() {
    // Get all needed state from the authentication context
    const { isSetup, isLocked, isLoading: isLoadingAuth, error: authError } = useVaultAuth();

    // Determine which main component to render
    const renderContent = () => {
        // Show loading spinner only during initial status check
        if (isLoadingAuth && isSetup === null) {
            return <Spin size="large" tip="Loading Vault Status..."><div style={{ height: 100 }}></div></Spin>;
        }

        // Show Setup screen if not setup
        if (isSetup === false) {
            return <VaultSetup />;
        }

        // Show Unlock screen if setup but locked
        if (isSetup === true && isLocked) {
            return <VaultUnlock />;
        }

        // Show main Vault screen if setup and unlocked
        if (isSetup === true && !isLocked) {
            return <VaultScreen />;
        }

        // Fallback or handle null isSetup case after loading if needed
        return null; // Or some other placeholder/error state
    };

    return (
        <Layout style={{ padding: '20px', background: '#000' }}>
            <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, opacity: 0.5, zIndex: 0 }}>
                <LetterGlitch
                    glitchSpeed={250}
                    centerVignette={true}
                    outerVignette={false}
                    smooth={true}
                    glitchColors={['#2b4539', '#61dca3', '#61b3dc']}
                />
            </div>
            
            <div style={{ maxWidth: '800px', minWidth: '480px', margin: '0 auto' }}>
                <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <Title level={1} style={{
                        textAlign: 'center',
                        marginBottom: '30px',
                        zIndex: 1,
                        position: 'relative',
                        color: '#fff'
                    }}>Secure Password Vault</Title>

                    {/* Display Authentication/Setup errors prominently */}
                    {/* Avoid showing error during initial load */}
                    {authError && !(isLoadingAuth && isSetup === null) && (
                        <Alert
                            message="Vault Access Error"
                            description={authError}
                            type="error"
                            showIcon
                            // Consider if this should be closable or managed by context state
                        />
                    )}

                    {/* Render the appropriate content based on vault state */}
                    {renderContent()}
                </Space>
            </div>
        </Layout>
    );
}

export default Vault;