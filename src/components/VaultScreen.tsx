import { useState } from 'react';
import { Card, Button, Typography, Space, Input, message, Alert } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { AddCredentialForm } from './AddCredentialForm';
import { CredentialList } from './CredentialList';
import { EditCredentialModal } from './EditCredentialModal';
import type { CredentialOutput } from '../types';
import { useVaultAuth } from '../contexts/VaultAuthContext';
import { useCredentials } from '../contexts/CredentialsContext';

const { Title } = Typography;

// This component renders the main UI when the vault is unlocked
export function VaultScreen() {
    const { lock, isLocked } = useVaultAuth(); // Get lock action and status
    const { isLoading: isLoadingCredentials, error: credentialsError } = useCredentials(); // Get loading/error state
    const [searchTerm, setSearchTerm] = useState<string>("");

    // State for controlling the Edit Modal
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [editingCredential, setEditingCredential] = useState<CredentialOutput | null>(null);

    // Handler called by CredentialList when an edit button is clicked
    const handleEditRequest = (credential: CredentialOutput) => {
        if (isLocked) {
             message.warning("Vault is locked.");
             return;
        }
        setEditingCredential(credential);
        setEditModalVisible(true);
    };

    // Handler to close the edit modal
    const handleCloseEditModal = () => {
        setEditModalVisible(false);
        // Delay clearing to avoid flicker while modal closes
        setTimeout(() => setEditingCredential(null), 300);
    };

    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* Display errors specifically from CredentialsContext if any */}
            {credentialsError && (
                <Alert
                    message="Credentials Error"
                    description={credentialsError}
                    type="error"
                    showIcon
                    // Make it closable or rely on context to clear it
                />
            )}

            {/* Header Card */}
            <Card>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Title level={3} style={{ margin: 0 }}>Vault Unlocked</Title>
                    <Button type="default" danger onClick={lock} icon={<LockOutlined />}>
                        Lock Vault
                    </Button>
                </div>
            </Card>

            {/* Add Credential Form */}
            {/* No props needed as it uses context */}
            <AddCredentialForm />

            {/* Stored Credentials Card */}
            <Card
                title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '10px' }}>
                        <span>Stored Credentials</span>
                        <Input.Search
                            placeholder="Search..."
                            allowClear
                            value={searchTerm}
                            onChange={e => setSearchTerm(e.target.value)}
                            style={{ width: 260 }}
                            disabled={isLoadingCredentials} // Disable while loading
                        />
                    </div>
                }
            >
                {/* Credential List uses context for data, only needs search term and edit request handler */}
                <CredentialList
                    searchTerm={searchTerm}
                    onEditRequest={handleEditRequest}
                />
            </Card>

            {/* Edit Modal - Controlled by this component's state */}
            <EditCredentialModal
                visible={editModalVisible}
                credential={editingCredential}
                onCancel={handleCloseEditModal}
                // onOk is now handled internally by EditCredentialModal using context
            />
        </Space>
    );
}