import { useState } from 'react';
import { Card, Form, Input, Button, Typography, Alert } from 'antd';
import { LockOutlined, KeyOutlined } from '@ant-design/icons';
import { useVaultAuth } from '../contexts/VaultAuthContext';

const { Paragraph, Text } = Typography;

export function VaultSetup() {
    const { setup, isLoading: isAuthLoading, error: authError } = useVaultAuth();
    const [form] = Form.useForm();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleFinish = async (values: { masterPasswordSetup: string }) => {
        setIsSubmitting(true);
        try {
            // Context handles success message
            await setup(values.masterPasswordSetup);
            form.resetFields();
        } catch (err) {
            // Context handles error message
            // Form validation errors are handled by Form automatically
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Card title="Setup Secure Vault" styles={{ header: { textAlign: 'center' } }} style={{ maxWidth: 400, margin: 'auto' }}>
            {/* Display context error if relevant during setup action */}
            {authError && isSubmitting && <Alert message="Setup Error" description={authError} type="error" showIcon style={{ marginBottom: 16 }} />}
            <Paragraph>
                Choose a strong, unique Master Password. This password is the only way to access your stored credentials and is never stored directly. <Text strong>You must remember it!</Text>
            </Paragraph>
            <Form
                form={form}
                layout="vertical"
                onFinish={handleFinish}
                requiredMark={false}
            >
                <Form.Item
                    name="masterPasswordSetup"
                    label="Master Password"
                    rules={[{ required: true, message: 'Master Password is required!' }, { min: 12, message: 'Password must be at least 12 characters long.' }]}
                    hasFeedback
                >
                    <Input.Password
                        prefix={<KeyOutlined />}
                        placeholder="Choose a Strong Master Password"
                    />
                </Form.Item>
                <Form.Item
                    name="confirmPassword"
                    label="Confirm Master Password"
                    dependencies={['masterPasswordSetup']}
                    hasFeedback
                    rules={[
                        { required: true, message: 'Please confirm your password!' },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('masterPasswordSetup') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('The two passwords do not match!'));
                            },
                        }),
                    ]}
                >
                    <Input.Password prefix={<KeyOutlined />} placeholder="Confirm Password" />
                </Form.Item>
                <Form.Item>
                    {/* Button loading uses local submit state OR global auth loading state */}
                    <Button type="primary" htmlType="submit" loading={isSubmitting || isAuthLoading} block icon={<LockOutlined />}>
                        Setup Vault
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
}