import  { useState } from 'react';
import { Card, Form, Input, Button, Alert } from 'antd';
import { UnlockOutlined, KeyOutlined } from '@ant-design/icons';
import { useVaultAuth } from '../contexts/VaultAuthContext';

export function VaultUnlock() {
    const { unlock, isLoading: isAuthLoading, error: authError } = useVaultAuth();
    const [form] = Form.useForm();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleFinish = async (values: { masterPasswordUnlock: string }) => {
        setIsSubmitting(true);
        try {
            // Context handles success message
            await unlock(values.masterPasswordUnlock);
            form.resetFields();
        } catch (err) {
             // Context handles error message
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Card title="Unlock Vault" styles={{ header: { textAlign: 'center' } }} style={{ maxWidth: 400, margin: 'auto' }}>
            {/* Display context error if relevant during unlock action */}
            {authError && isSubmitting && <Alert message="Unlock Error" description={authError} type="error" showIcon style={{ marginBottom: 16 }} />}
            <Form
                form={form}
                layout="vertical"
                onFinish={handleFinish}
                requiredMark={false}
            >
                <Form.Item
                    name="masterPasswordUnlock"
                    label="Master Password"
                    rules={[{ required: true, message: 'Please enter your Master Password!' }]}
                >
                    <Input.Password
                        prefix={<KeyOutlined />}
                        placeholder="Enter Master Password"
                    />
                </Form.Item>
                <Form.Item>
                    <Button type="primary" htmlType="submit" loading={isSubmitting || isAuthLoading} block icon={<UnlockOutlined />}>
                        Unlock
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
}