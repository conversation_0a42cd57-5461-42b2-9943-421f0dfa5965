import { useState, useEffect } from 'react';
import { Button, Space } from 'antd';
import { PushpinOutlined, PushpinFilled, MinusOutlined, BorderOutlined, BlockOutlined, CloseOutlined } from '@ant-design/icons';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';

const appWindow = getCurrentWebviewWindow();

export const WindowControls = () => {
  const [isMaximized, setIsMaximized] = useState(false);
  const [isPined, setIsPined] = useState(false); // <PERSON><PERSON> doesn't have a direct isAlwaysOnTop getter, manage state locally

  useEffect(() => {
    let unlistenResized: (() => void) | null = null;

    const listenResize = async () => {
      unlistenResized = await appWindow.onResized(async () => {
        const maximized = await appWindow.isMaximized();
        setIsMaximized(maximized);
      });
      // Initial check
      const maximized = await appWindow.isMaximized();
      setIsMaximized(maximized);
    };

    listenResize();

    return () => {
      if (unlistenResized) {
        unlistenResized();
      }
    };
  }, []);

  const handlePin = () => {
    const newPinState = !isPined;
    appWindow.setAlwaysOnTop(newPinState);
    setIsPined(newPinState);
  };

  const handleMinimize = () => {
    appWindow.minimize();
  };

  const handleMaximize = () => {
    appWindow.toggleMaximize();
    // State update will happen via the onResized listener
  };

  const handleClose = () => {
    appWindow.close();
  };

  return (
    <Space size={0}>
      <Button
        type="text"
        icon={isPined ? <PushpinFilled /> : <PushpinOutlined />}
        onClick={handlePin}
        aria-label={isPined ? 'Unpin window' : 'Pin window'}
        size="small"
        style={{ borderRadius: 0 }}
      />
      <Button
        type="text"
        icon={<MinusOutlined />}
        onClick={handleMinimize}
        aria-label="Minimize window"
        size="small"
        style={{ borderRadius: 0 }}
      />
      <Button
        type="text"
        icon={isMaximized ? <BlockOutlined /> : <BorderOutlined />}
        onClick={handleMaximize}
        aria-label={isMaximized ? 'Restore window' : 'Maximize window'}
        size="small"
        style={{ borderRadius: 0 }}
      />
      <Button
        type="text"
        danger
        icon={<CloseOutlined />}
        onClick={handleClose}
        aria-label="Close window"
        size="small"
        style={{ borderRadius: 0 }}
      />
    </Space>
  );
};