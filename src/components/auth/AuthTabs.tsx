/**
 * 认证页面标签组件
 * 包含注册和登录两个标签页
 */

import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import type { TabsProps } from 'antd';
import RegisterForm from './RegisterForm';
import LoginForm from './LoginForm';

/**
 * 认证标签组件
 */
const AuthTabs: React.FC = () => {
  const [activeKey, setActiveKey] = useState<string>('register');

  const tabItems: TabsProps['items'] = [
    {
      key: 'register',
      label: '注册账户',
      children: <RegisterForm />
    },
    {
      key: 'login',
      label: '登录账户',
      children: <LoginForm />
    }
  ];

  return (
    <div className="w-full max-w-md mx-auto flex flex-col items-center justify-center">
      <Card 
        className="shadow-lg border-0 static!"
        bodyStyle={{ padding: '2rem' }}
      >
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Secure Vault
          </h1>
          <p className="text-gray-600">
            安全的密码管理器
          </p>
        </div>

        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          centered
          size="large"
          items={tabItems}
          className="auth-tabs"
        />
      </Card>
    </div>
  );
};

export default AuthTabs; 