/**
 * 用户注册表单组件
 */

import React, { useState, useCallback } from 'react';
import {
  Form,
  Input,
  Button,
  Steps,
  Progress,
  Tooltip,
  Alert,
  Typography,
  message
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  LockOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { RegisterFormData, RegistrationType, PasswordStrengthResult } from '../../types';
import { 
  registerCompleteFlow, 
  sendVerificationCode, 
  validatePasswordStrength,
  checkContactAvailability
} from '../../api';

const { Text } = Typography;
const { Step } = Steps;

interface RegisterFormProps {
  onSuccess?: () => void;
}

/**
 * 智能检测联系方式类型
 */
const detectContactType = (contact: string): RegistrationType => {
  return contact.includes('@') ? 'Email' : 'Phone';
};

/**
 * 根据联系方式生成默认用户名
 */
const generateUsername = (contact: string): string => {
  if (contact.includes('@')) {
    // 邮箱：取@前面的部分
    return contact.split('@')[0];
  } else {
    // 手机号：取后6位
    return `user_${contact.slice(-6)}`;
  }
};

/**
 * 注册表单组件
 */
const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrengthResult | null>(null);
  const [verificationSent, setVerificationSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 倒计时逻辑
  React.useEffect(() => {
    let timer: number;
    if (countdown > 0) {
      timer = window.setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  /**
   * 检验密码强度
   */
  const checkPasswordStrength = useCallback(async (password: string) => {
    if (!password) {
      setPasswordStrength(null);
      return;
    }

    try {
      const response = await validatePasswordStrength(password);
      if (response.success && response.data) {
        setPasswordStrength(response.data);
      }
    } catch (error) {
      console.error('密码强度检验失败:', error);
    }
  }, []);

  /**
   * 发送验证码
   */
  const handleSendVerificationCode = async () => {
    try {
      const contact = form.getFieldValue('contact');

      if (!contact) {
        message.warning('请先填写邮箱或手机号');
        return;
      }

      const contactType = detectContactType(contact);

      // 检查联系方式格式和可用性
      const availabilityResponse = await checkContactAvailability(contact, contactType);
      if (!availabilityResponse.success || !availabilityResponse.data?.is_available) {
        message.error(availabilityResponse.data?.message || '该邮箱或手机号已被使用');
        return;
      }

      setLoading(true);
      const response = await sendVerificationCode(contact, contactType);
      
      if (response.success) {
        setVerificationSent(true);
        setCountdown(60);
        const typeText = contactType === 'Phone' ? '短信' : '邮箱';
        message.success(`验证码已发送到您的${typeText}，请查收`);
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 提交注册表单
   */
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      const contact = values.contact;
      const contactType = detectContactType(contact);
      const username = generateUsername(contact);

      // 验证密码确认
      if (values.password !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      // 构造注册数据
      const registerData: RegisterFormData = {
        username,
        contact,
        registrationType: contactType,
        password: values.password,
        confirmPassword: values.confirmPassword,
        verificationCode: values.verificationCode,
        passwordHint: values.passwordHint
      };

      // 执行注册流程
      const response = await registerCompleteFlow(registerData);

      if (response.success && response.data?.remote_success) {
        message.success('注册成功！');
        setCurrentStep(1);
        
        // 显示注册结果信息
        console.log('注册成功:', {
          用户名: username,
          联系方式: contact,
          注册类型: contactType,
          用户信息: response.data.remote_data,
          保险库状态: response.data.vault_created ? '已创建' : '创建失败',
          安全信息: response.data.security_info
        });

        // 调用成功回调
        onSuccess?.();
      } else {
        message.error(response.error || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      message.error('注册过程出现错误');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取密码强度颜色
   */
  const getPasswordStrengthColor = (strength: number): string => {
    if (strength >= 80) return 'success';
    if (strength >= 60) return 'normal';
    if (strength >= 40) return 'active';
    return 'exception';
  };

  /**
   * 获取密码强度文本
   */
  const getPasswordStrengthText = (strength: number): string => {
    if (strength >= 80) return '强';
    if (strength >= 60) return '中等';
    if (strength >= 40) return '一般';
    return '弱';
  };

  /**
   * 获取联系方式的提示图标
   */
  const getContactIcon = () => {
    const contact = form.getFieldValue('contact');
    if (!contact) return <UserOutlined />;
    return detectContactType(contact) === 'Email' ? <MailOutlined /> : <PhoneOutlined />;
  };

  /**
   * 获取联系方式的占位符文本
   */
  const getContactPlaceholder = () => {
    const contact = form.getFieldValue('contact');
    if (!contact) return '请输入邮箱地址或手机号码';
    const type = detectContactType(contact);
    return type === 'Email' ? '邮箱地址' : '手机号码';
  };

  return (
    <div className="w-full">
      {/* 步骤指示器 */}
      <Steps current={currentStep} className="mb-8">
        <Step title="填写信息" icon={<UserOutlined />} />
        <Step title="注册成功" icon={<CheckCircleOutlined />} />
      </Steps>

      {currentStep < 1 && (
        <Form
          form={form}
          name="register"
          onFinish={handleSubmit}
          layout="vertical"
          autoComplete="off"
          size="large"
          className="space-y-4"
        >
          {/* 联系方式（智能识别邮箱/手机号） */}
          <Form.Item
            name="contact"
            label={
              <span>
                邮箱或手机号
                <Tooltip title="系统会自动识别邮箱或手机号，并作为您的登录账号">
                  <InfoCircleOutlined className="ml-1 text-gray-400" />
                </Tooltip>
              </span>
            }
            rules={[
              { required: true, message: '请输入邮箱地址或手机号码' },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  
                  const contactType = detectContactType(value);
                  
                  if (contactType === 'Email') {
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(value)) {
                      return Promise.reject(new Error('请输入有效的邮箱地址'));
                    }
                  } else {
                    const phonePattern = /^1[3-9]\d{9}$/;
                    if (!phonePattern.test(value)) {
                      return Promise.reject(new Error('请输入有效的手机号码'));
                    }
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input 
              prefix={getContactIcon()}
              placeholder={getContactPlaceholder()}
              className="rounded-lg"
              onChange={() => {
                // 强制重新渲染以更新图标和占位符
                form.validateFields(['contact']);
              }}
            />
          </Form.Item>

          {/* 显示检测到的联系方式类型和生成的用户名 */}
          {form.getFieldValue('contact') && (
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="text-sm text-blue-800">
                <div className="flex items-center space-x-2 mb-1">
                  {detectContactType(form.getFieldValue('contact')) === 'Email' ? (
                    <MailOutlined className="text-blue-600" />
                  ) : (
                    <PhoneOutlined className="text-blue-600" />
                  )}
                  <span>
                    检测为: {detectContactType(form.getFieldValue('contact')) === 'Email' ? '邮箱注册' : '手机注册'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <UserOutlined className="text-blue-600" />
                  <span>
                    默认用户名: {generateUsername(form.getFieldValue('contact'))}
                  </span>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  用户名可在注册成功后通过个人设置修改
                </div>
              </div>
            </div>
          )}

          {/* 密码 */}
          <Form.Item
            name="password"
            label={
              <span>
                密码
                <Tooltip title="建议使用包含大小写字母、数字和特殊字符的强密码">
                  <InfoCircleOutlined className="ml-1 text-gray-400" />
                </Tooltip>
              </span>
            }
            rules={[
              { required: true, message: '请输入密码' },
              { min: 8, message: '密码至少8个字符' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              className="rounded-lg"
              onChange={(e) => checkPasswordStrength(e.target.value)}
            />
          </Form.Item>

          {/* 密码强度指示器 */}
          {passwordStrength && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <Text className="text-sm text-gray-600">密码强度</Text>
                <Text className="text-sm font-medium">
                  {getPasswordStrengthText(passwordStrength.strength)}
                </Text>
              </div>
              <Progress
                percent={passwordStrength.strength}
                status={getPasswordStrengthColor(passwordStrength.strength) as any}
                strokeWidth={6}
                showInfo={false}
                className="mb-2"
              />
              <Text className="text-xs text-gray-500">
                {passwordStrength.feedback}
              </Text>
            </div>
          )}

          {/* 确认密码 */}
          <Form.Item
            name="confirmPassword"
            label="确认密码"
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                }
              })
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请再次输入密码"
              className="rounded-lg"
            />
          </Form.Item>

          {/* 密码提示词 */}
          <Form.Item
            name="passwordHint"
            label={
              <span>
                密码提示词 (可选)
                <Tooltip title="帮助您记住密码的提示，不要包含密码本身">
                  <InfoCircleOutlined className="ml-1 text-gray-400" />
                </Tooltip>
              </span>
            }
          >
            <Input 
              placeholder="如：我的生日+宠物名字"
              className="rounded-lg"
            />
          </Form.Item>

          {/* 验证码 */}
          <Form.Item
            name="verificationCode"
            label="验证码"
            rules={[
              { required: true, message: '请输入验证码' },
              { len: 6, message: '验证码为6位数字' },
              { pattern: /^\d{6}$/, message: '验证码只能包含数字' }
            ]}
          >
            <div className="flex space-x-3">
              <Input 
                placeholder="请输入6位验证码"
                className="flex-1 rounded-lg"
                maxLength={6}
              />
              <Button 
                onClick={handleSendVerificationCode}
                disabled={countdown > 0 || loading}
                className="rounded-lg min-w-[120px]"
              >
                {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}
              </Button>
            </div>
          </Form.Item>

          {verificationSent && (
            <Alert
              message="验证码发送成功"
              description={
                detectContactType(form.getFieldValue('contact') || '') === 'Phone' 
                  ? "请查收您的短信中的验证码，如未收到请稍后重试"
                  : "请查收您的邮箱中的验证码，如未收到请检查垃圾箱"
              }
              type="info"
              showIcon
              className="mb-4 rounded-lg"
            />
          )}

          {/* 提交按钮 */}
          <Form.Item className="mb-0 pt-4">
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              size="large"
              className="rounded-lg h-12 font-medium"
            >
              创建账户
            </Button>
          </Form.Item>
        </Form>
      )}

      {/* 注册成功页面 */}
      {currentStep === 1 && (
        <div className="text-center py-8">
          <CheckCircleOutlined className="text-6xl text-green-500 mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">注册成功！</h2>
          <p className="text-gray-600 mb-2">
            您的账户已创建成功
          </p>
          <p className="text-sm text-gray-500 mb-6">
            用户名: {generateUsername(form.getFieldValue('contact') || '')}
          </p>
          <Button 
            type="primary" 
            size="large"
            className="rounded-lg"
            onClick={onSuccess}
          >
            开始使用
          </Button>
        </div>
      )}
    </div>
  );
};

export default RegisterForm; 