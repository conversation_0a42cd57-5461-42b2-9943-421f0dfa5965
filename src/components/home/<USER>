import { Button } from "@/components/ui/button"
import { Plus, Share2, Edit, Star, MoreHorizontal, List, Grid } from "lucide-react"
import { PasswordItem } from "./password-item"

const categories = [
  { name: "登录类型", active: false },
  { name: "全部", active: true },
  { name: "密码类", active: false },
  { name: "通行密钥", active: false },
  { name: "OTP令牌", active: false },
  { name: "数字钱包", active: false },
]

const passwordItems = [
  { icon: "$", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-orange-500" },
  { icon: "百", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-blue-500" },
  { icon: "豆", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-green-500" },
  { icon: "微", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-green-600" },
  { icon: "Pa", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-green-700" },
  { icon: "🛡", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-red-500" },
  { icon: "Ai", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-orange-600" },
  { icon: "in", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-blue-600" },
  { icon: "On", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-purple-500" },
  { icon: "新", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-red-600" },
]

export function MainContent() {
  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Categories */}
      <div className="flex space-x-2">
        {categories.map((category, index) => (
          <Button key={index} variant={category.active ? "default" : "outline"} size="sm" className="rounded-full">
            {category.name}
          </Button>
        ))}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button size="icon" className="rounded-full w-12 h-12 bg-blue-500 hover:bg-blue-600">
            <Plus className="w-5 h-5" />
          </Button>
          <span className="text-sm text-muted-foreground">已全部加载，共10项</span>
          <Button variant="outline" size="sm">
            按创建时间排序
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon">
            <Share2 className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Edit className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Star className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
          <div className="border-l border-border h-6 mx-2"></div>
          <Button variant="ghost" size="icon">
            <List className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Grid className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Password Grid */}
      <div className="grid grid-cols-5 gap-4">
        {passwordItems.map((item, index) => (
          <PasswordItem
            key={index}
            icon={item.icon}
            title={item.title}
            timestamp={item.timestamp}
            bgColor={item.bgColor}
          />
        ))}
      </div>

      <div className="text-center text-muted-foreground text-sm py-8">没有更多了</div>
    </div>
  )
}
