import { Shield, Star, Share2, Archive, Trash2, HelpCircle, Settings, ArchiveIcon } from "lucide-react"

const sidebarItems = [
  { icon: Shield, label: "保险箱", active: true },
  { icon: Star, label: "收藏夹", active: false },
  { icon: Share2, label: "分享", active: false },
  { icon: Archive, label: "封存", active: false },
  { icon: Trash2, label: "回收站", active: false },
]

const bottomItems = [
  { icon: HelpCircle, label: "帮助" },
  { icon: Settings, label: "设置" },
  { icon: ArchiveIcon, label: "归档" },
]

export function LeftSidebar() {
  return (
    <div className="w-[72px] bg-[#f8f9fa] dark:bg-[#1e1e1e] border-r border-[#e9ecef] dark:border-[#333] flex flex-col smooth-transition">
      <div className="flex flex-col items-center pt-4 space-y-2">
        {sidebarItems.map((item, index) => (
          <button
            key={index}
            className={`flex flex-col items-center justify-center w-12 h-12 rounded-lg cursor-pointer sidebar-icon ${
              item.active
                ? "bg-[#007aff] text-white shadow-lg shadow-[#007aff]/25"
                : "text-[#666] dark:text-[#999] hover:bg-[#e9ecef] dark:hover:bg-[#333] hover:text-[#333] dark:hover:text-[#fff] hover:shadow-md"
            }`}
            style={{
              animationDelay: `${index * 0.1}s`,
            }}
          >
            <item.icon className="w-5 h-5 mb-1 transition-transform duration-200" />
            <span className="text-[10px] leading-none">{item.label}</span>
          </button>
        ))}
      </div>

      <div className="flex flex-col items-center mt-auto pb-4 space-y-2">
        {bottomItems.map((item, index) => (
          <button
            key={index}
            className="flex flex-col items-center justify-center w-12 h-12 rounded-lg cursor-pointer sidebar-icon text-[#666] dark:text-[#999] hover:bg-[#e9ecef] dark:hover:bg-[#333] hover:text-[#333] dark:hover:text-[#fff] hover:shadow-md"
            style={{
              animationDelay: `${(index + 5) * 0.1}s`,
            }}
          >
            <item.icon className="w-5 h-5 mb-1 transition-transform duration-200" />
            <span className="text-[10px] leading-none">{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  )
}
