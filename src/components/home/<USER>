import { Card } from "@/components/ui/card"

interface PasswordItemProps {
  icon: string
  title: string
  timestamp: string
  bgColor?: string
}

export function PasswordItem({ icon, title, timestamp, bgColor = "bg-blue-500" }: PasswordItemProps) {
  return (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
      <div className="flex flex-col items-center space-y-3">
        <div
          className={`w-12 h-12 rounded-lg ${bgColor} flex items-center justify-center text-white font-bold text-lg`}
        >
          {icon}
        </div>
        <div className="text-center">
          <div className="font-medium text-sm">{title}</div>
          <div className="text-xs text-muted-foreground mt-1">{timestamp}</div>
        </div>
      </div>
    </Card>
  )
}
