import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { useState, useEffect } from "react"

export function ThemeToggle() {
  const { setTheme, theme } = useTheme()
  const [isAnimating, setIsAnimating] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleThemeToggle = () => {
    setIsAnimating(true)

    // 添加页面级别的过渡效果
    document.documentElement.classList.add("theme-transition")

    setTimeout(() => {
      setTheme(theme === "light" ? "dark" : "light")
    }, 50)

    setTimeout(() => {
      setIsAnimating(false)
      document.documentElement.classList.remove("theme-transition")
    }, 500)
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="sm" className="w-7 h-7 p-0">
        <div className="w-4 h-4" />
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      className={`w-7 h-7 p-0 theme-toggle-btn ${isAnimating ? "ripple-effect" : ""}`}
      onClick={handleThemeToggle}
      disabled={isAnimating}
    >
      <div className="relative w-4 h-4">
        <Sun
          className={`absolute inset-0 h-4 w-4 transition-all duration-500 ease-in-out ${
            theme === "dark" ? "rotate-90 scale-0 opacity-0" : "rotate-0 scale-100 opacity-100"
          }`}
        />
        <Moon
          className={`absolute inset-0 h-4 w-4 transition-all duration-500 ease-in-out ${
            theme === "dark" ? "rotate-0 scale-100 opacity-100" : "-rotate-90 scale-0 opacity-0"
          }`}
        />
      </div>
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
