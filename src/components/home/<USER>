import { Search, RotateCcw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "./theme-toggle"

export function BrowserHeader() {
  return (
    <div className="h-12 bg-[#f6f6f6] dark:bg-[#2d2d2d] border-b border-[#e5e5e5] dark:border-[#404040] flex items-center px-3 smooth-transition">
      {/* Search bar */}
      <div className="flex-1 max-w-lg relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#999] dark:text-[#666] smooth-transition" />
        <input
          placeholder="搜索保险库中的所有产品"
          className="w-full h-8 pl-12 pr-4 bg-[#e8e8e8] dark:bg-[#3a3a3a] text-sm rounded-full border-0 outline-none smooth-transition focus:bg-white dark:focus:bg-[#4a4a4a] focus:ring-2 focus:ring-[#007aff]/20 placeholder:text-[#999] dark:placeholder:text-[#666]"
        />
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-3 ml-4">
        <Button
          variant="ghost"
          size="sm"
          className="w-7 h-7 p-0 button-transition hover:bg-black/5 dark:hover:bg-white/5"
        >
          <RotateCcw className="w-4 h-4 text-[#34c759]" />
        </Button>

        <div className="bg-[#fff3cd] dark:bg-[#664d03] px-3 py-1 rounded text-xs text-[#856404] dark:text-[#ffda6a] smooth-transition border border-[#ffeaa7] dark:border-[#8b6914]">
          7天/200个
        </div>

        <div className="text-xs text-[#666] dark:text-[#999] smooth-transition">186****4998</div>

        <div className="w-6 h-6 rounded-full bg-[#007aff] flex items-center justify-center smooth-transition hover:bg-[#0056cc] hover:scale-105">
          <span className="text-white text-xs">头</span>
        </div>

        <ThemeToggle />
      </div>
    </div>
  )
}
