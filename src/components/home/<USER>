import { Button } from "@/components/ui/button"
import { Plus, List, Grid3X3, ChevronDown } from "lucide-react"
import { useState } from "react"

const categories = [
  { name: "登录类型", active: false },
  { name: "全部", active: true },
  { name: "密码类", active: false },
  { name: "通行密钥", active: false },
  { name: "OTP令牌", active: false },
  { name: "数字钱包", active: false },
]

const passwordItems = [
  { icon: "$", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#ff9500]" },
  { icon: "百", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#007aff]" },
  { icon: "豆", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#34c759]" },
  { icon: "微", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#00c851]" },
  { icon: "Pa", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#2e7d32]" },
  { icon: "🛡", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#ff3b30]" },
  { icon: "Ai", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#ff6b35]" },
  { icon: "in", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#0077b5]" },
  { icon: "On", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#0078d4]" },
  { icon: "新", title: "18611894998", timestamp: "05/25 20:20", bgColor: "bg-[#e60012]" },
]

export function MainArea() {
  const [selectedIndex, setSelectedIndex] = useState(1)

  return (
    <div className="flex-1 bg-white dark:bg-[#1a1a1a] p-6 smooth-transition">
      {/* Category tabs */}
      <div className="flex space-x-1 mb-6">
        {categories.map((category, index) => (
          <Button
            key={index}
            variant={category.active ? "default" : "ghost"}
            size="sm"
            className={`h-8 px-4 text-sm font-normal rounded-full button-transition ${
              category.active
                ? "bg-[#007aff] text-white hover:bg-[#0056cc] shadow-lg shadow-[#007aff]/25"
                : "text-[#666] dark:text-[#999] hover:bg-[#f0f0f0] dark:hover:bg-[#333] hover:shadow-md"
            }`}
            style={{
              animationDelay: `${index * 0.05}s`,
            }}
          >
            {category.name}
          </Button>
        ))}
      </div>

      {/* Action bar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            size="icon"
            className="w-12 h-12 rounded-full bg-[#5ac8fa] hover:bg-[#32ade6] text-white button-transition hover:scale-105 hover:shadow-lg shadow-[#5ac8fa]/25"
          >
            <Plus className="w-6 h-6" />
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <span className="text-sm text-[#666] dark:text-[#999] smooth-transition">已全部加载，共10项</span>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3 text-sm border-[#d1d1d1] dark:border-[#555] button-transition hover:shadow-md"
          >
            <span>按创建时间排序</span>
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>

          <div className="flex items-center space-x-1 bg-[#f0f0f0] dark:bg-[#333] rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0 text-[#666] dark:text-[#999] button-transition hover:bg-white dark:hover:bg-[#444]"
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0 bg-white dark:bg-[#444] text-[#007aff] button-transition shadow-sm"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Password grid */}
      <div className="grid grid-cols-6 gap-4 mb-8">
        {passwordItems.map((item, index) => (
          <div
            key={index}
            className={`w-[120px] h-[120px] p-3 cursor-pointer transition-all duration-200 rounded-lg fade-in ${
              selectedIndex === index
                ? "bg-[#e3f2fd] dark:bg-[#1e3a8a]/20"
                : "hover:bg-[#f5f5f5] dark:hover:bg-[#2a2a2a]"
            }`}
            style={{
              animationDelay: `${index * 0.05}s`,
            }}
            onClick={() => setSelectedIndex(index)}
          >
            <div className="flex flex-col items-center h-full">
              <div
                className={`w-12 h-12 rounded-lg ${item.bgColor} flex items-center justify-center text-white font-medium text-lg mb-3 shadow-sm password-icon`}
              >
                {item.icon}
              </div>
              <div className="text-center flex-1 flex flex-col justify-center">
                <div className="text-sm font-medium text-[#333] dark:text-[#fff] mb-1 smooth-transition">
                  {item.title}
                </div>
                <div className="text-xs text-[#999] dark:text-[#666] smooth-transition">{item.timestamp}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center text-[#999] dark:text-[#666] text-sm py-8 smooth-transition">没有更多了</div>
    </div>
  )
}
