import { But<PERSON> } from "@/components/ui/button"
import { Share2, <PERSON>, Star, MoreHorizontal, ChevronDown, User, Lock } from "lucide-react"
import { useState } from "react"

export function RightPanel() {
  const [showPassword] = useState(false)

  return (
    <div className="w-[320px] bg-white dark:bg-[#1a1a1a] border-l border-[#e9ecef] dark:border-[#333] smooth-transition">
      {/* Top action buttons */}
      <div className="flex items-center justify-between p-4 border-b border-[#e9ecef] dark:border-[#333]">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] dark:text-[#999] hover:text-[#333] dark:hover:text-[#fff] button-transition"
        >
          <Share2 className="w-4 h-4" />
          <span className="text-sm">分享</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] dark:text-[#999] hover:text-[#333] dark:hover:text-[#fff] button-transition"
        >
          <Edit className="w-4 h-4" />
          <span className="text-sm">编辑</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 text-[#666] dark:text-[#999] hover:text-[#333] dark:hover:text-[#fff] button-transition"
        >
          <Star className="w-4 h-4" />
          <span className="text-sm">添加到搜藏夹</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 text-[#666] dark:text-[#999] hover:text-[#333] dark:hover:text-[#fff] button-transition"
        >
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col items-center space-y-3 mb-8 fade-in">
          <div className="w-12 h-12 bg-[#007aff] rounded-lg flex items-center justify-center smooth-transition hover:bg-[#0056cc] hover:scale-105">
            <span className="text-white font-medium text-lg">百</span>
          </div>
          <span className="font-medium text-[#333] dark:text-[#fff] text-lg smooth-transition">18611894998</span>
        </div>

        {/* Form fields */}
        <div className="space-y-6">
          {/* Username field */}
          <div className="fade-in" style={{ animationDelay: "0.1s" }}>
            <div className="flex items-center justify-between p-3 bg-[#f8f9fa] dark:bg-[#2a2a2a] rounded-lg smooth-transition">
              <div className="flex items-center space-x-3">
                <User className="w-4 h-4 text-[#666] dark:text-[#999]" />
                <span className="text-sm text-[#333] dark:text-[#fff] smooth-transition">13674847395</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-[#007aff] hover:bg-[#007aff]/10 button-transition text-sm px-3 py-1"
              >
                复制
              </Button>
            </div>
          </div>

          {/* Password field */}
          <div className="fade-in" style={{ animationDelay: "0.2s" }}>
            <div className="flex items-center justify-between p-3 bg-[#f8f9fa] dark:bg-[#2a2a2a] rounded-lg smooth-transition">
              <div className="flex items-center space-x-3">
                <Lock className="w-4 h-4 text-[#666] dark:text-[#999]" />
                <span className="text-sm text-[#333] dark:text-[#fff] smooth-transition font-mono">
                  {showPassword ? "password123" : "••••••••"}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-[#666] dark:text-[#999]">极佳</span>
                <div className="w-2 h-2 rounded-full bg-[#34c759]"></div>
              </div>
            </div>
          </div>

          {/* Website field */}
          <div className="fade-in" style={{ animationDelay: "0.3s" }}>
            <label className="text-sm text-[#666] dark:text-[#999] block mb-2 smooth-transition">网站</label>
            <a
              href="https://www.baidu.com"
              className="text-[#007aff] hover:underline text-sm block button-transition hover:text-[#0056cc]"
              target="_blank"
              rel="noopener noreferrer"
            >
              https://www.baidu.com
            </a>
          </div>
        </div>

        {/* History section */}
        <div className="mt-8 fade-in" style={{ animationDelay: "0.4s" }}>
          <button className="flex items-center space-x-2 cursor-pointer hover:bg-[#f8f9fa] dark:hover:bg-[#2a2a2a] p-2 rounded-lg smooth-transition w-full text-left">
            <ChevronDown className="w-4 h-4 text-[#666] dark:text-[#999]" />
            <span className="text-sm text-[#333] dark:text-[#fff] smooth-transition">
              最后编辑2024年11月14日星期四22:35:42
            </span>
          </button>
          <div className="ml-6 mt-2">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-[#ccc] dark:bg-[#555]"></div>
              <span className="text-xs text-[#999] dark:text-[#666] smooth-transition">
                已添加2024年11月13日星期三17:02:15
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
