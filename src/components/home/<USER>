import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { <PERSON>, <PERSON>Off, Co<PERSON>, ChevronDown } from "lucide-react"
import { useState } from "react"

export function DetailsPanel() {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <div className="w-80 bg-background border-l border-border p-6 space-y-6">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
          <span className="text-white font-bold">百</span>
        </div>
        <span className="font-medium">18611894998</span>
      </div>

      <div className="space-y-4">
        <div>
          <label className="text-sm text-muted-foreground">用户名</label>
          <div className="flex items-center justify-between mt-1">
            <span>13674847395</span>
            <Button variant="ghost" size="icon" className="w-6 h-6">
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>

        <div>
          <label className="text-sm text-muted-foreground">密码</label>
          <div className="flex items-center justify-between mt-1">
            <span>{showPassword ? "password123" : "••••••••"}</span>
            <div className="flex space-x-1">
              <Button variant="ghost" size="icon" className="w-6 h-6" onClick={() => setShowPassword(!showPassword)}>
                {showPassword ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
              </Button>
              <Button variant="ghost" size="icon" className="w-6 h-6">
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        <div>
          <label className="text-sm text-muted-foreground">网站</label>
          <div className="mt-1">
            <a href="https://www.baidu.com" className="text-blue-500 hover:underline text-sm">
              https://www.baidu.com
            </a>
          </div>
        </div>
      </div>

      <Card className="p-3">
        <div className="flex items-center justify-between">
          <span className="text-sm">最后编辑2024年11月14日星期四22:35:42</span>
          <ChevronDown className="w-4 h-4" />
        </div>
        <div className="text-xs text-muted-foreground mt-2">已添加2024年11月13日星期三17:02:15</div>
      </Card>
    </div>
  )
}
