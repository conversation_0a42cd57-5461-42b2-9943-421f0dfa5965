import { Shield, Star, Share2, Archive, Trash2 } from "lucide-react"
import { cn } from "@/lib/utils"

const sidebarItems = [
  { icon: Shield, label: "保险箱", active: true },
  { icon: Star, label: "收藏夹" },
  { icon: Share2, label: "分享" },
  { icon: Archive, label: "封存" },
  { icon: Trash2, label: "回收站" },
]

export function Sidebar() {
  return (
    <div className="w-20 bg-background border-r border-border flex flex-col items-center py-4 space-y-4">
      {sidebarItems.map((item, index) => (
        <div
          key={index}
          className={cn(
            "flex flex-col items-center justify-center w-12 h-12 rounded-lg cursor-pointer transition-colors",
            item.active
              ? "bg-primary text-primary-foreground"
              : "text-muted-foreground hover:bg-muted hover:text-foreground",
          )}
        >
          <item.icon className="w-5 h-5" />
          <span className="text-xs mt-1">{item.label}</span>
        </div>
      ))}
    </div>
  )
}
