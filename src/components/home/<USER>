import { ChevronLeft, ChevronRight, Search, RotateCcw } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "./theme-toggle"

export function Header() {
  return (
    <div className="h-16 bg-background border-b border-border flex items-center px-4 space-x-4">
      <div className="flex items-center space-x-2">
        <div className="flex space-x-1">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <Button variant="ghost" size="icon" className="w-8 h-8">
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="icon" className="w-8 h-8">
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      <div className="flex-1 max-w-md relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input placeholder="搜索保险库中的所有产品" className="pl-10 bg-muted/50" />
      </div>

      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon">
          <RotateCcw className="w-4 h-4" />
        </Button>
        <div className="bg-yellow-100 dark:bg-yellow-900 px-3 py-1 rounded text-sm">7天/200个</div>
        <div className="text-sm text-muted-foreground">186****4998</div>
        <ThemeToggle />
      </div>
    </div>
  )
}
