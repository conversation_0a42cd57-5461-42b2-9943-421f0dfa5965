/**
 * 应用主布局组件
 * 集成主题模式管理、窗口控制和认证状态
 */

import React, { useState, useEffect } from 'react';
import { Layout, Space, Radio, Button, Popover, ConfigProvider, theme } from 'antd';
import {
  SunOutlined,
  MoonOutlined,
  LaptopOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { platform } from '@tauri-apps/plugin-os';
import { useAuthState, useAuthActions } from '../../contexts';
import { WindowControls } from '../WindowControls';
import { ThemeMode } from '../../types';

const { Header, Content } = Layout;

interface AppLayoutProps {
  children: React.ReactNode;
}

/**
 * 应用布局组件
 */
const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuthState();
  const { logout } = useAuthActions();

  // 主题相关状态
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    // 从 localStorage 初始化主题模式，默认为 'auto'
    return (localStorage.getItem('themeMode') as ThemeMode) || 'auto';
  });
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');
  const [systemPrefersDark, setSystemPrefersDark] = useState(false);
  const [osType, setOsType] = useState<string | null>(null);

  // 获取操作系统类型
  useEffect(() => {
    let isMounted = true;
    const getCurrentPlatform = async () => {
      try {
        const currentPlatform = await platform();
        if (isMounted) {
          setOsType(currentPlatform);
        }
      } catch (error) {
        console.error('获取系统平台失败:', error);
      }
    };
    
    getCurrentPlatform();
    return () => { isMounted = false; };
  }, []);

  // 检测系统主题偏好
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPrefersDark(mediaQuery.matches);

    const handler = (e: MediaQueryListEvent) => setSystemPrefersDark(e.matches);
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);

  // 根据模式和系统偏好更新实际主题
  useEffect(() => {
    let newTheme: 'light' | 'dark';
    if (themeMode === 'auto') {
      newTheme = systemPrefersDark ? 'dark' : 'light';
    } else {
      newTheme = themeMode;
    }
    setActualTheme(newTheme);
    // 保存主题模式到 localStorage
    localStorage.setItem('themeMode', themeMode);
  }, [themeMode, systemPrefersDark]);

  /**
   * 处理主题切换
   */
  const handleThemeChange = (e: any) => {
    setThemeMode(e.target.value);
  };

  /**
   * 处理用户登出
   */
  const handleLogout = async () => {
    await logout();
  };

  // 主题算法
  const themeAlgorithm = actualTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm;

  // 主题切换器内容
  const themeSwitcherContent = (
    <Radio.Group onChange={handleThemeChange} value={themeMode}>
      <Space direction="vertical">
        <Radio value="auto"><LaptopOutlined /> 自动</Radio>
        <Radio value="light"><SunOutlined /> 浅色</Radio>
        <Radio value="dark"><MoonOutlined /> 深色</Radio>
      </Space>
    </Radio.Group>
  );

  return (
    <ConfigProvider
      theme={{
        algorithm: themeAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
          fontSize: 14,
        },
        components: {
          Button: {
            borderRadius: 8,
          },
          Input: {
            borderRadius: 8,
          },
          Card: {
            borderRadius: 12,
          },
        },
      }}
    >
      <Layout className={`h-full flex flex-col ${actualTheme === 'dark' ? 'bg-gray-900' : 'bg-gradient-to-br from-blue-50 to-indigo-100'}`}>
        {/* 自定义标题栏 - 始终显示 */}
        <Header
          data-tauri-drag-region
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            display: 'flex',
            zIndex: 0,
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '0 0 0 75px',
            height: '32px',
            lineHeight: '32px',
            background: 'transparent',
            flexShrink: 0,
          }}
        >
          <div></div>
          <Space size={0}>
            {/* 主题切换器 */}
            <Popover 
              content={themeSwitcherContent} 
              title="主题设置" 
              trigger="click"
              placement="bottomRight"
            >
              <Button 
                type="text" 
                icon={<SettingOutlined />}
                size="small"
                style={{ borderRadius: 0 }}
              />
            </Popover>

            {/* 登出按钮 - 仅在登录状态显示 */}
            {isAuthenticated && (
              <Button 
                type="text" 
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                size="small"
                style={{ borderRadius: 0 }}
                title="登出"
              />
            )}

            {/* 窗口控制按钮 - 仅在非 macOS 显示 */}
            {osType && osType !== 'macos' && <WindowControls />}
          </Space>
        </Header>

        {/* 主要内容区域 */}
        <Content className="flex-1 flex items-center justify-center overflow-auto">
          <div className="w-full">
            {children}
          </div>
        </Content>

        {/* 页脚 */}
        {/* <Footer className="text-center bg-transparent">
          <div className={`text-sm ${actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
            Secure Vault ©2024 - 安全密码管理器
          </div>
        </Footer> */}
      </Layout>
    </ConfigProvider>
  );
};

export default AppLayout; 