# AppLayout 集成功能总结

## 🎯 目标
将原来 App.tsx 中的主题模式管理、窗口控制和拖拽区域功能集成到 AppLayout.tsx 中，实现统一的布局管理。

## 🚀 实现的功能

### 1. 主题模式管理
- ✅ **自动主题检测**: 根据系统偏好自动切换浅色/深色主题
- ✅ **手动主题切换**: 支持浅色、深色、自动三种模式
- ✅ **主题持久化**: 主题设置保存到 localStorage
- ✅ **实时预览**: 主题切换立即生效，无需刷新

### 2. 窗口控制集成
- ✅ **跨平台支持**: 自动检测操作系统类型
- ✅ **macOS 适配**: 在 macOS 上隐藏窗口控制按钮
- ✅ **窗口操作**: 支持最小化、最大化、关闭、置顶
- ✅ **拖拽区域**: 添加 `data-tauri-drag-region` 支持窗口拖拽

### 3. 自定义标题栏
- ✅ **统一样式**: 标题栏高度固定为 40px，适配桌面应用
- ✅ **主题适配**: 标题栏颜色随主题自动切换
- ✅ **功能集成**: 集成应用标题、用户信息、主题切换器、登出按钮

### 4. 认证状态集成
- ✅ **条件渲染**: 根据登录状态显示用户名和登出按钮
- ✅ **状态管理**: 集成认证 Context 的状态和操作
- ✅ **用户体验**: 登录前后界面布局保持一致

## 📁 文件修改详情

### 1. 类型定义 (`src/types/index.ts`)
```typescript
// 新增主题模式类型
export type ThemeMode = 'light' | 'dark' | 'auto';
```

### 2. 应用布局 (`src/components/layout/AppLayout.tsx`)
#### 新增功能：
- 主题模式状态管理 (`themeMode`, `actualTheme`, `systemPrefersDark`)
- 操作系统类型检测 (`osType`)
- 系统主题偏好监听
- 主题切换器 UI 组件
- 自定义标题栏 (40px 高度，拖拽区域)
- 窗口控制按钮集成
- ConfigProvider 主题配置

#### 核心状态：
```typescript
const [themeMode, setThemeMode] = useState<ThemeMode>('auto');
const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');
const [systemPrefersDark, setSystemPrefersDark] = useState(false);
const [osType, setOsType] = useState<string | null>(null);
```

### 3. 应用入口 (`src/App.tsx`)
#### 简化内容：
- 移除重复的 ConfigProvider 主题配置
- 保留语言设置和认证状态管理
- 简化组件结构

### 4. 窗口控制 (`src/components/WindowControls.tsx`)
#### 优化：
- 移除硬编码的背景色
- 统一按钮大小为 `small`
- 去除内联样式依赖

### 5. 样式文件 (`src/App.css`)
#### 新增：
- 深色主题支持类
- 深色模式下的滚动条样式
- 主题相关的 CSS 变量

## 🎨 UI/UX 改进

### 标题栏设计
- **高度**: 32px (适合桌面应用)
- **布局**: 居中标题+用户名，右侧控制按钮
- **拖拽**: 整个标题栏区域支持窗口拖拽
- **主题**: 自动适配浅色/深色主题

### 主题切换器
- **位置**: 标题栏右侧，设置按钮触发
- **选项**: 自动、浅色、深色三个选项
- **图标**: 配合直观的图标（电脑、太阳、月亮）
- **实时**: 选择后立即生效

### 响应式适配
- **深色主题**: 完整的深色模式支持
- **系统集成**: 遵循操作系统主题偏好
- **跨平台**: Windows/Linux 显示窗口控制，macOS 隐藏

## 🔧 技术实现

### 主题检测算法
```typescript
// 检测系统主题偏好
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
setSystemPrefersDark(mediaQuery.matches);

// 计算实际主题
let newTheme: 'light' | 'dark';
if (themeMode === 'auto') {
  newTheme = systemPrefersDark ? 'dark' : 'light';
} else {
  newTheme = themeMode;
}
```

### 平台检测
```typescript
// 异步获取系统平台
const currentPlatform = await platform();
setOsType(currentPlatform);

// 条件渲染窗口控制
{osType && osType !== 'macos' && <WindowControls />}
```

### 主题持久化
```typescript
// 初始化时从 localStorage 读取
const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
  return (localStorage.getItem('themeMode') as ThemeMode) || 'auto';
});

// 状态变化时保存到 localStorage
localStorage.setItem('themeMode', themeMode);
```

## 🚀 使用方法

### 基本使用
AppLayout 现在是一个完全自包含的组件，包含了所有的主题和窗口管理功能：

```tsx
import { AppLayout } from './components';

function App() {
  return (
    <AppLayout>
      <YourContent />
    </AppLayout>
  );
}
```

### 主题访问
组件内部可以通过 ConfigProvider 访问当前主题：

```tsx
import { theme } from 'antd';
const { token } = theme.useToken();
// token 会根据当前主题自动调整
```

## 📱 兼容性

- ✅ **Windows**: 完整的窗口控制功能
- ✅ **Linux**: 完整的窗口控制功能  
- ✅ **macOS**: 自动隐藏窗口控制，使用系统原生
- ✅ **主题**: 所有平台支持完整的主题切换

## 🎯 下一步

1. 可以添加更多主题预设（如高对比度主题）
2. 可以扩展窗口控制功能（如窗口大小记忆）
3. 可以添加主题切换动画效果
4. 可以支持自定义主题颜色

这个集成方案提供了完整的桌面应用体验，同时保持了代码的模块化和可维护性。 