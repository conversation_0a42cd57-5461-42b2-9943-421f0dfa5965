/**
 * 封存视图组件
 * 显示已封存的密码凭据，支持恢复和批量操作
 */

import React, { useState, useMemo } from 'react';
import { 
    Card, 
    Empty, 
    Input, 
    Button, 
    Space, 
    Typography, 
    Tooltip, 
    Modal, 
    Table, 
    message,
    Popconfirm,
    Tag
} from 'antd';
import { 
    SearchOutlined, 
    InfoCircleOutlined,
    ReloadOutlined,
    UndoOutlined
} from '@ant-design/icons';
import { useHybridCredentials } from '../../contexts/HybridCredentialsContext';
import type { LoginCredentialOutput } from '../../types';
import { css } from '@emotion/react';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

/**
 * 封存视图组件
 */
const ArchivedView: React.FC = () => {
    const {
        archivedCredentials,
        isLoading,
        loadArchivedCredentials,
        unarchiveCredential,
        batchUnarchiveCredentials
    } = useHybridCredentials();

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [showInfo, setShowInfo] = useState(false);

    /**
     * 过滤后的封存凭据
     */
    const filteredCredentials = useMemo(() => {
        if (!searchQuery.trim()) {
            return archivedCredentials;
        }

        const query = searchQuery.toLowerCase();
        return archivedCredentials.filter(credential =>
            credential.name.toLowerCase().includes(query) ||
            credential.username?.toLowerCase().includes(query) ||
            credential.website?.toLowerCase().includes(query)
        );
    }, [archivedCredentials, searchQuery]);

    /**
     * 处理搜索
     */
    const handleSearch = (value: string) => {
        setSearchQuery(value);
    };

    /**
     * 处理恢复单个凭据
     */
    const handleUnarchive = async (id: number, name: string) => {
        try {
            await unarchiveCredential(id);
            message.success(`"${name}" 已从封存中恢复`);
            setSelectedRowKeys(prev => prev.filter(key => key !== id));
        } catch (error) {
            console.error('恢复凭据失败:', error);
        }
    };

    /**
     * 处理批量恢复
     */
    const handleBatchUnarchive = async () => {
        if (selectedRowKeys.length === 0) {
            message.warning('请选择要恢复的凭据');
            return;
        }

        try {
            const ids = selectedRowKeys.map(key => Number(key));
            const affectedRows = await batchUnarchiveCredentials(ids);
            message.success(`已恢复 ${affectedRows} 个凭据`);
            setSelectedRowKeys([]);
        } catch (error) {
            console.error('批量恢复失败:', error);
        }
    };

    /**
     * 处理刷新
     */
    const handleRefresh = async () => {
        try {
            await loadArchivedCredentials();
            message.success('封存列表已刷新');
        } catch (error) {
            console.error('刷新失败:', error);
        }
    };

    /**
     * 表格列定义
     */
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: LoginCredentialOutput) => (
                <Space direction="vertical" size={0}>
                    <Text strong>{text}</Text>
                    {record.website && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                            {record.website}
                        </Text>
                    )}
                </Space>
            ),
        },
        {
            title: '用户名',
            dataIndex: 'username',
            key: 'username',
            render: (username: string) => username || <Text type="secondary">-</Text>,
        },
        {
            title: '密码',
            dataIndex: 'password',
            key: 'password',
            render: () => (
                <Tag color="orange">已封存</Tag>
            ),
        },
        {
            title: '收藏',
            dataIndex: 'favorite',
            key: 'favorite',
            width: 80,
            render: (favorite: boolean) => (
                favorite ? <Tag color="gold">收藏</Tag> : null
            ),
        },
        {
            title: '操作',
            key: 'actions',
            width: 120,
            render: (_: any, record: LoginCredentialOutput) => (
                <Space>
                    <Tooltip title="从封存中恢复">
                        <Popconfirm
                            title="恢复凭据"
                            description={`确定要将 "${record.name}" 从封存中恢复吗？`}
                            onConfirm={() => handleUnarchive(record.id, record.name)}
                            okText="恢复"
                            cancelText="取消"
                        >
                            <Button 
                                type="text" 
                                icon={<UndoOutlined />}
                                size="small"
                            />
                        </Popconfirm>
                    </Tooltip>
                </Space>
            ),
        },
    ];

    /**
     * 行选择配置
     */
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record: LoginCredentialOutput) => ({
            name: record.name,
        }),
    };

    return (
        <div css={containerStyle}>
            {/* 头部区域 */}
            <div css={headerStyle}>
                <div css={titleSectionStyle}>
                    <Title level={3} style={{ margin: 0 }}>
                        封存
                    </Title>
                    <Text type="secondary">
                        已封存 {archivedCredentials.length} 个凭据
                    </Text>
                </div>

                <Space>
                    <Button
                        icon={<InfoCircleOutlined />}
                        onClick={() => setShowInfo(true)}
                    >
                        关于封存
                    </Button>
                    <Button
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                        loading={isLoading}
                    >
                        刷新
                    </Button>
                </Space>
            </div>

            {/* 搜索和批量操作区域 */}
            <Card css={searchCardStyle}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <Search
                        placeholder="搜索封存的凭据..."
                        allowClear
                        enterButton={<SearchOutlined />}
                        size="large"
                        onSearch={handleSearch}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        css={searchInputStyle}
                    />

                    {selectedRowKeys.length > 0 && (
                        <div css={batchActionsStyle}>
                            <Space>
                                <Text>
                                    已选择 {selectedRowKeys.length} 个凭据
                                </Text>
                                <Button
                                    type="primary"
                                    icon={<UndoOutlined />}
                                    onClick={handleBatchUnarchive}
                                    loading={isLoading}
                                >
                                    批量恢复
                                </Button>
                                <Button
                                    onClick={() => setSelectedRowKeys([])}
                                >
                                    取消选择
                                </Button>
                            </Space>
                        </div>
                    )}
                </Space>
            </Card>

            {/* 凭据列表 */}
            <Card css={listCardStyle}>
                {filteredCredentials.length === 0 ? (
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            searchQuery ? (
                                <span>
                                    没有找到匹配 "<Text code>{searchQuery}</Text>" 的封存凭据
                                </span>
                            ) : (
                                <span>
                                    暂无封存的凭据
                                    <br />
                                    <Text type="secondary">
                                        封存的凭据将显示在这里
                                    </Text>
                                </span>
                            )
                        }
                    />
                ) : (
                    <Table
                        rowSelection={rowSelection}
                        columns={columns}
                        dataSource={filteredCredentials}
                        rowKey="id"
                        loading={isLoading}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) =>
                                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                        }}
                        size="middle"
                    />
                )}
            </Card>

            {/* 关于封存的信息弹窗 */}
            <Modal
                title="关于封存功能"
                open={showInfo}
                onCancel={() => setShowInfo(false)}
                footer={[
                    <Button key="close" type="primary" onClick={() => setShowInfo(false)}>
                        知道了
                    </Button>
                ]}
                width={600}
            >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <div>
                        <Title level={4}>什么是封存？</Title>
                        <Paragraph>
                            封存功能让您可以将不常用的密码安全地存储起来，既保持保险箱的整洁，又确保重要数据不会丢失。
                        </Paragraph>
                    </div>

                    <div>
                        <Title level={4}>封存的特点</Title>
                        <ul>
                            <li>封存的凭据不会在主界面显示，保持界面整洁</li>
                            <li>封存的凭据仍然安全加密存储</li>
                            <li>可以随时恢复封存的凭据到主界面</li>
                            <li>支持批量封存和恢复操作</li>
                            <li>封存的凭据不会被搜索到（除非在封存页面）</li>
                        </ul>
                    </div>

                    <div>
                        <Title level={4}>如何使用</Title>
                        <ul>
                            <li>在主界面选择凭据，点击"封存"按钮</li>
                            <li>在封存页面查看所有已封存的凭据</li>
                            <li>点击"恢复"按钮将凭据恢复到主界面</li>
                            <li>支持批量选择进行批量恢复操作</li>
                        </ul>
                    </div>
                </Space>
            </Modal>
        </div>
    );
};

// 样式定义
const containerStyle = css`
    padding: 24px;
    height: 100%;
    overflow-y: auto;
`;

const headerStyle = css`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
`;

const titleSectionStyle = css`
    display: flex;
    flex-direction: column;
    gap: 4px;
`;

const searchCardStyle = css`
    margin-bottom: 16px;
    .ant-card-body {
        padding: 16px;
    }
`;

const searchInputStyle = css`
    .ant-input-search {
        .ant-input {
            border-radius: 8px 0 0 8px;
        }
        .ant-btn {
            border-radius: 0 8px 8px 0;
        }
    }
`;

const batchActionsStyle = css`
    padding: 12px 16px;
    background: #f0f2f5;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
`;

const listCardStyle = css`
    .ant-card-body {
        padding: 0;
    }
    
    .ant-table {
        .ant-table-thead > tr > th {
            background: #fafafa;
            font-weight: 600;
        }
        
        .ant-table-tbody > tr:hover > td {
            background: #f5f5f5;
        }
    }
`;

export default ArchivedView; 