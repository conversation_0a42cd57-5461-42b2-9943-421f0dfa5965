/**
 * 密码详情面板组件
 * 显示选中密码的详细信息和操作
 * 支持深浅色主题切换
 */

import React, { useState } from 'react';
import { Button, Typography, message, theme } from 'antd';
import {
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  GlobalOutlined,
  ExportOutlined,
  ShareAltOutlined,
  EditOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import type { CredentialOutput } from '../../types';

const { Text, Title } = Typography;

interface CredentialDetailProps {
  credential: CredentialOutput;
  onClose?: () => void;
}

// 服务图标映射
const getServiceIcon = (serviceName: string) => {
  const service = serviceName.toLowerCase();
  
  const iconMap: Record<string, { icon: string; color: string }> = {
    'dog coin': { icon: '💰', color: '#f59e0b' },
    'baidu': { icon: '🅱️', color: '#3b82f6' },
    'douban': { icon: '🌱', color: '#10b981' },
    'wechat': { icon: '💬', color: '#22c55e' },
    'paypal': { icon: 'Pa', color: '#059669' },
    'bear': { icon: '🐻', color: '#ef4444' },
    'adobe illustrator': { icon: 'Ai', color: '#f97316' },
    'linkedin': { icon: 'in', color: '#3b82f6' },
    'onedrive': { icon: 'On', color: '#8b5cf6' },
    'sina': { icon: '新', color: '#ef4444' },
    'default': { icon: '🔐', color: '#6b7280' }
  };
  
  return iconMap[service] || iconMap.default;
};

/**
 * 密码详情面板组件
 */
const CredentialDetail: React.FC<CredentialDetailProps> = ({ credential, onClose }) => {
  const { token } = theme.useToken(); // 获取主题令牌
  const [showPassword, setShowPassword] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const serviceIcon = getServiceIcon(credential.service_name);

  /**
   * 复制到剪贴板
   */
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${type}已复制到剪贴板`);
    } catch (error) {
      message.error('复制失败');
    }
  };

  /**
   * 分享密码信息
   */
  const handleShare = () => {
    // 这里可以实现分享功能，比如生成分享链接或调用系统分享API
    message.info('分享功能开发中...');
  };

  /**
   * 编辑密码信息
   */
  const handleEdit = () => {
    // 这里可以打开编辑模态框或跳转到编辑页面
    message.info('编辑功能开发中...');
  };

  /**
   * 切换收藏状态
   */
  const handleToggleFavorite = () => {
    setIsFavorited(!isFavorited);
    message.success(isFavorited ? '已取消收藏' : '已添加到收藏夹');
  };

  /**
   * 关闭详情面板
   */
  const handleClose = () => {
    onClose?.();
  };

  return (
    <div 
      className="w-80 border-l h-full overflow-auto relative"
      style={{ 
        backgroundColor: token.colorBgContainer,
        borderColor: token.colorBorder
      }}
    >
      <div className="p-6">
        
        {/* 关闭按钮 */}
        <Button
          type="text"
          size="small"
          icon={<CloseOutlined />}
          onClick={handleClose}
          className="absolute top-0 left-0 z-10"
          style={{ 
            width: '32px', 
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: token.colorTextTertiary
          }}
        />

        {/* 操作栏 */}
        <div 
          className="flex items-center justify-center space-x-6 mb-6 pb-4 border-b"
          style={{ borderColor: token.colorBorderSecondary }}
        >
          <div className="flex flex-col items-center cursor-pointer group" onClick={handleShare}>
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center mb-1 transition-colors"
              style={{ backgroundColor: token.colorFillSecondary }}
            >
              <ShareAltOutlined 
                className="transition-colors"
                style={{ color: token.colorTextSecondary }}
              />
            </div>
            <span 
              className="text-xs transition-colors"
              style={{ color: token.colorTextSecondary }}
            >
              分享
            </span>
          </div>
          
          <div className="flex flex-col items-center cursor-pointer group" onClick={handleEdit}>
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center mb-1 transition-colors"
              style={{ backgroundColor: token.colorFillSecondary }}
            >
              <EditOutlined 
                className="transition-colors"
                style={{ color: token.colorTextSecondary }}
              />
            </div>
            <span 
              className="text-xs transition-colors"
              style={{ color: token.colorTextSecondary }}
            >
              编辑
            </span>
          </div>
          
          <div className="flex flex-col items-center cursor-pointer group" onClick={handleToggleFavorite}>
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center mb-1 transition-colors"
              style={{ backgroundColor: token.colorFillSecondary }}
            >
              {isFavorited ? (
                <StarFilled 
                  className="transition-colors"
                  style={{ color: '#faad14' }}
                />
              ) : (
                <StarOutlined 
                  className="transition-colors"
                  style={{ color: token.colorTextSecondary }}
                />
              )}
            </div>
            <span 
              className="text-xs transition-colors"
              style={{ color: token.colorTextSecondary }}
            >
              {isFavorited ? '已收藏' : '收藏'}
            </span>
          </div>
          
          <div className="flex flex-col items-center cursor-pointer group">
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center mb-1 transition-colors"
              style={{ backgroundColor: token.colorFillSecondary }}
            >
              <MoreOutlined 
                className="transition-colors"
                style={{ color: token.colorTextSecondary }}
              />
            </div>
            <span 
              className="text-xs transition-colors"
              style={{ color: token.colorTextSecondary }}
            >
              更多
            </span>
          </div>
        </div>

        {/* 头部信息 */}
        <div className="text-center mb-8">
          <div
            className="w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold shadow-lg"
            style={{ backgroundColor: serviceIcon.color }}
          >
            {serviceIcon.icon}
          </div>
          <Title 
            level={4} 
            className="mb-2"
            style={{ color: token.colorText }}
          >
            {credential.service_name}
          </Title>
          <Text 
            type="secondary" 
            className="text-sm"
            style={{ color: token.colorTextSecondary }}
          >
            {credential.username}
          </Text>
        </div>

        {/* 详细信息 */}
        <div className="space-y-6">
          {/* 用户名 */}
          <div>
            <Text 
              type="secondary" 
              className="block mb-2 text-xs font-medium uppercase tracking-wide"
              style={{ color: token.colorTextSecondary }}
            >
              用户名
            </Text>
            <div 
              className="flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer"
              style={{ 
                backgroundColor: token.colorFillAlter
              }}
            >
              <Text 
                className="flex-1 text-sm font-medium"
                style={{ color: token.colorText }}
              >
                {credential.username}
              </Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(credential.username, '用户名')}
                className="ml-2"
                style={{ color: token.colorTextTertiary }}
              />
            </div>
          </div>

          {/* 密码 */}
          <div>
            <Text 
              type="secondary" 
              className="block mb-2 text-xs font-medium uppercase tracking-wide"
              style={{ color: token.colorTextSecondary }}
            >
              密码
            </Text>
            <div 
              className="flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer"
              style={{ 
                backgroundColor: token.colorFillAlter
              }}
            >
              <Text 
                className="flex-1 text-sm font-medium font-mono"
                style={{ color: token.colorText }}
              >
                {showPassword ? credential.password : '••••••••••'}
              </Text>
              <div className="flex items-center space-x-1 ml-2">
                <Button
                  type="text"
                  size="small"
                  icon={showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowPassword(!showPassword)}
                  style={{ color: token.colorTextTertiary }}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => copyToClipboard(credential.password, '密码')}
                  style={{ color: token.colorTextTertiary }}
                />
              </div>
            </div>
          </div>

          {/* 网站 */}
          <div>
            <Text 
              type="secondary" 
              className="block mb-2 text-xs font-medium uppercase tracking-wide"
              style={{ color: token.colorTextSecondary }}
            >
              网站
            </Text>
            <div 
              className="flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer"
              style={{ 
                backgroundColor: token.colorFillAlter
              }}
            >
              <div className="flex items-center space-x-2 flex-1">
                <GlobalOutlined 
                  className="text-sm"
                  style={{ color: token.colorTextTertiary }}
                />
                <Text 
                  className="text-sm cursor-pointer"
                  style={{ color: token.colorPrimary }}
                >
                  https://www.baidu.com
                </Text>
              </div>
              <Button
                type="text"
                size="small"
                icon={<ExportOutlined />}
                className="ml-2"
                style={{ color: token.colorTextTertiary }}
              />
            </div>
          </div>

          {/* 备注 */}
          {credential.notes && (
            <div>
              <Text 
                type="secondary" 
                className="block mb-2 text-xs font-medium uppercase tracking-wide"
                style={{ color: token.colorTextSecondary }}
              >
                备注
              </Text>
              <div 
                className="p-3 rounded-lg"
                style={{ backgroundColor: token.colorFillAlter }}
              >
                <Text 
                  className="text-sm leading-relaxed"
                  style={{ color: token.colorText }}
                >
                  {credential.notes}
                </Text>
              </div>
            </div>
          )}

          {/* 详细信息 */}
          <div 
            className="border-t pt-6"
            style={{ borderColor: token.colorBorder }}
          >
            <Text 
              type="secondary" 
              className="block mb-4 text-xs font-medium uppercase tracking-wide"
              style={{ color: token.colorTextSecondary }}
            >
              详细信息
            </Text>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Text 
                  type="secondary" 
                  className="text-sm"
                  style={{ color: token.colorTextSecondary }}
                >
                  最后编辑时间:
                </Text>
                <Text 
                  className="text-sm"
                  style={{ color: token.colorText }}
                >
                  {new Date(credential.updated_at).toLocaleString('zh-CN')}
                </Text>
              </div>
              <div className="flex justify-between items-center">
                <Text 
                  type="secondary" 
                  className="text-sm"
                  style={{ color: token.colorTextSecondary }}
                >
                  已添加:
                </Text>
                <Text 
                  className="text-sm"
                  style={{ color: token.colorText }}
                >
                  {new Date(credential.created_at).toLocaleString('zh-CN')}
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CredentialDetail; 