/**
 * 凭据表单组件
 * 用于添加和编辑凭据，集成密码生成器功能
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Switch, Button, Space, Divider, theme } from 'antd';
import { ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import PasswordGenerator from '../PasswordGenerator';
import type { LoginCredentialInput, LoginCredentialOutput } from '../../types';

const { TextArea } = Input;

interface CredentialFormProps {
  form: any;
  initialValues?: Partial<LoginCredentialOutput>;
  onFinish: (values: LoginCredentialInput) => void;
  onCancel?: () => void;
  isEditing?: boolean;
}

/**
 * 凭据表单组件
 */
const CredentialForm: React.FC<CredentialFormProps> = ({
  form,
  initialValues,
  onFinish,
  onCancel,
  isEditing = false,
}) => {
  const { token } = theme.useToken();
  const [showPasswordGenerator, setShowPasswordGenerator] = useState(false);
  const [generatedPassword, setGeneratedPassword] = useState('');

  /**
   * 处理密码生成
   */
  const handlePasswordGenerate = (password: string) => {
    setGeneratedPassword(password);
    form.setFieldsValue({ password });
  };

  /**
   * 使用生成的密码
   */
  const handleUseGeneratedPassword = () => {
    if (generatedPassword) {
      form.setFieldsValue({ password: generatedPassword });
      setShowPasswordGenerator(false);
    }
  };

  /**
   * 生成随机密码（快速生成）
   */
  const generateQuickPassword = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setFieldsValue({ password });
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (values: any) => {
    const credentialData: LoginCredentialInput = {
      name: values.name,
      username: values.username,
      password: values.password,
      website: values.website,
      notes: values.notes,
      favorite: values.favorite || false,
    };
    onFinish(credentialData);
  };

  // 设置初始值
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          favorite: false,
          ...initialValues,
        }}
      >
        {/* 基本信息 */}
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          <Input 
            placeholder="请输入凭据名称（如：百度账号、Gmail等）" 
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="username"
          label="用户名"
        >
          <Input 
            placeholder="请输入用户名或邮箱" 
            size="large"
          />
        </Form.Item>

        {/* 密码字段 */}
        <Form.Item
          name="password"
          label={
            <div className="flex items-center justify-between w-full">
              <span>密码</span>
              <Space size="small">
                <Button
                  type="text"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={generateQuickPassword}
                  style={{ color: token.colorPrimary }}
                >
                  快速生成
                </Button>
                <Button
                  type="text"
                  size="small"
                  icon={<SettingOutlined />}
                  onClick={() => setShowPasswordGenerator(!showPasswordGenerator)}
                  style={{ color: token.colorPrimary }}
                >
                  高级生成
                </Button>
              </Space>
            </div>
          }
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password 
            placeholder="请输入密码或使用生成器生成" 
            size="large"
          />
        </Form.Item>

        {/* 密码生成器 */}
        {showPasswordGenerator && (
          <div 
            className="mb-6 p-4 rounded-lg border"
            style={{ 
              backgroundColor: token.colorFillAlter,
              borderColor: token.colorBorder
            }}
          >
            <div className="mb-3">
              <span 
                className="text-sm font-medium"
                style={{ color: token.colorText }}
              >
                密码生成器
              </span>
            </div>
            
            <PasswordGenerator
              onPasswordGenerate={handlePasswordGenerate}
              triggerOnGenerateCallbackOnInitialRender={false}
            />
            
            <div className="mt-3 flex justify-end">
              <Space>
                <Button 
                  size="small"
                  onClick={() => setShowPasswordGenerator(false)}
                >
                  取消
                </Button>
                <Button 
                  type="primary" 
                  size="small"
                  onClick={handleUseGeneratedPassword}
                  disabled={!generatedPassword}
                >
                  使用此密码
                </Button>
              </Space>
            </div>
          </div>
        )}

        <Form.Item
          name="website"
          label="网站"
        >
          <Input 
            placeholder="请输入网站地址（如：https://www.baidu.com）" 
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea 
            placeholder="请输入备注信息（可选）" 
            rows={3}
            size="large"
          />
        </Form.Item>

        <Divider />

        {/* 设置选项 */}
        <Form.Item
          name="favorite"
          valuePropName="checked"
          style={{ marginBottom: 24 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <div 
                className="font-medium"
                style={{ color: token.colorText }}
              >
                添加到收藏夹
              </div>
              <div 
                className="text-sm"
                style={{ color: token.colorTextSecondary }}
              >
                收藏的凭据会显示在收藏夹页面中
              </div>
            </div>
            <Switch 
              checkedChildren="收藏" 
              unCheckedChildren="普通"
            />
          </div>
        </Form.Item>

        {/* 提交按钮 */}
        <Form.Item style={{ marginBottom: 0 }}>
          <div className="flex justify-end space-x-3">
            <Button size="large" onClick={onCancel}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              size="large"
            >
              {isEditing ? '保存更改' : '添加凭据'}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default CredentialForm; 