/**
 * 收藏夹视图组件
 * 显示用户收藏的凭据
 */

import React from 'react';
import { Typography, theme, Empty } from 'antd';
import { StarFilled } from '@ant-design/icons';
import { useHybridCredentials } from '../../contexts/HybridCredentialsContext';
import CredentialGrid from './CredentialGrid';
import type { LoginCredentialOutput } from '../../types';

const { Title, Text } = Typography;

interface FavoritesViewProps {
  selectedCredential: LoginCredentialOutput | null;
  onCredentialClick: (credential: LoginCredentialOutput) => void;
  searchValue: string;
}

/**
 * 收藏夹视图组件
 */
const FavoritesView: React.FC<FavoritesViewProps> = ({
  selectedCredential,
  onCredentialClick,
  searchValue,
}) => {
  const { token } = theme.useToken();
  const { favoriteCredentials, isLoading } = useHybridCredentials();

  /**
   * 过滤收藏的凭据
   */
  const filteredFavorites = favoriteCredentials.filter(credential => {
    const matchesSearch = credential.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                         (credential.username && credential.username.toLowerCase().includes(searchValue.toLowerCase())) ||
                         (credential.website && credential.website.toLowerCase().includes(searchValue.toLowerCase()));
    return matchesSearch;
  });

  // 如果没有收藏的凭据，显示空状态
  if (!isLoading && favoriteCredentials.length === 0) {
    return (
      <div 
        className="h-full flex flex-col"
        style={{ backgroundColor: token.colorBgContainer }}
      >
        {/* 头部 */}
        <div 
          className="px-6 py-4 border-b"
          style={{ borderColor: token.colorBorderSecondary }}
        >
          <div className="flex items-center">
            <StarFilled 
              className="mr-3 text-xl"
              style={{ color: '#faad14' }}
            />
            <div>
              <Title level={4} className="mb-1">
                收藏夹
              </Title>
              <Text type="secondary">
                您收藏的密码和凭据
              </Text>
            </div>
          </div>
        </div>

        {/* 空状态 */}
        <div className="flex-1 flex items-center justify-center p-6">
          <Empty
            image={
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
                style={{ backgroundColor: 'rgba(250, 173, 20, 0.1)' }}
              >
                <StarFilled 
                  className="text-2xl"
                  style={{ color: '#faad14' }}
                />
              </div>
            }
            description={
              <div className="space-y-2">
                <Title level={4} style={{ color: token.colorText }}>
                  还没有收藏的项目
                </Title>
                <Text type="secondary">
                  点击密码卡片上的星星图标来收藏您常用的密码
                </Text>
              </div>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div 
      className="h-full flex flex-col"
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {/* 头部 */}
      <div 
        className="px-6 py-4 border-b"
        style={{ borderColor: token.colorBorderSecondary }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <StarFilled 
              className="mr-3 text-xl"
              style={{ color: '#faad14' }}
            />
            <div>
              <Title level={4} className="mb-1">
                收藏夹
              </Title>
              <Text type="secondary">
                您收藏的密码和凭据
              </Text>
            </div>
          </div>
          
          <div 
            className="text-sm"
            style={{ color: token.colorTextSecondary }}
          >
            共 {filteredFavorites.length} 个收藏项目
          </div>
        </div>
      </div>

      {/* 收藏凭据网格 */}
      <div className="flex-1">
        <CredentialGrid
          credentials={filteredFavorites}
          selectedCredential={selectedCredential}
          onCredentialClick={onCredentialClick}
          viewMode="grid"
        />
      </div>
    </div>
  );
};

export default FavoritesView; 