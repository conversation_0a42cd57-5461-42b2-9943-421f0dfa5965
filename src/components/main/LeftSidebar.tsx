/** @jsxImportSource @emotion/react */

/**
 * 左侧导航栏组件
 * 使用Tailwind CSS + emotion混合方案实现样式
 * - 基础样式使用Tailwind CSS
 * - 动态样式和复杂交互使用emotion
 */

import React, { useState } from 'react';
import { Layout } from 'antd';
import styled from '@emotion/styled';
import { css } from '@emotion/react';
import {
  SafetyOutlined,
  StarOutlined,
  ShareAltOutlined,
  LockOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  MobileOutlined,
} from '@ant-design/icons';

const { Sider } = Layout;

interface LeftSidebarProps {
  selectedKey: string;
  onMenuClick: (key: string) => void;
}

interface MenuItemConfig {
  key: string;
  icon: React.ReactNode;
  label: string;
}

interface NavItemProps {
  isSelected: boolean;
  isHovered: boolean;
  isBottom?: boolean;
}

// 主题变量
const theme = {
  colors: {
    primary: '#1d4ed8',
    primaryLight: '#dbeafe',
    primaryShadow: 'rgba(29, 78, 216, 0.15)',
  },
  transition: '0.2s ease-in-out',
};

// 使用emotion的styled组件处理复杂的动态样式
const StyledNavItem = styled.div<NavItemProps>`
  transition: all ${theme.transition};
  
  ${props => props.isSelected && css`
    background-color: ${theme.colors.primaryLight};
    color: ${theme.colors.primary};
    box-shadow: 0 2px 8px ${theme.colors.primaryShadow};
    
    .nav-icon,
    .nav-label {
      color: ${theme.colors.primary};
    }
    
    .nav-label {
      font-weight: 600;
    }
  `}
  
  ${props => props.isHovered && !props.isSelected && css`
    background-color: #f8fafc;
    transform: translateY(-1px);
  `}
  
  &:active {
    transform: translateY(0);
  }
`;

// 使用emotion的css函数创建动态样式
const getIconStyles = (isSelected: boolean, isBottom: boolean) => css`
  transition: all ${theme.transition};
  color: ${isSelected ? theme.colors.primary : '#6b7280'};
  font-size: ${isBottom ? '16px' : '18px'};
  
  ${isSelected && css`
    // transform: scale(1.05);
  `}
`;

/**
 * 左侧导航栏组件
 */
const LeftSidebar: React.FC<LeftSidebarProps> = ({ selectedKey, onMenuClick }) => {
  const [hoveredKey, setHoveredKey] = useState<string | null>(null);

  // 主导航项配置
  const mainMenuItems: MenuItemConfig[] = [
    {
      key: 'vault',
      icon: <SafetyOutlined />,
      label: '保险箱',
    },
    {
      key: 'favorites',
      icon: <StarOutlined />,
      label: '收藏夹',
    },
    {
      key: 'shared',
      icon: <ShareAltOutlined />,
      label: '分享',
    },
    {
      key: 'archived',
      icon: <LockOutlined />,
      label: '封存',
    },
    {
      key: 'trash',
      icon: <DeleteOutlined />,
      label: '回收站',
    },
  ];

  // 底部功能项配置
  const bottomMenuItems: MenuItemConfig[] = [
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '',
    },
    {
      key: 'security',
      icon: <SettingOutlined />,
      label: '',
    },
    {
      key: 'desktop',
      icon: <MobileOutlined />,
      label: '',
    },
  ];

  /**
   * 渲染导航项
   */
  const renderMenuItem = (item: MenuItemConfig, isBottom = false) => {
    const isSelected = selectedKey === item.key;
    const isHovered = hoveredKey === item.key;

    return (
      <StyledNavItem
        key={item.key}
        isSelected={isSelected}
        isHovered={isHovered}
        isBottom={isBottom}
        className={`
          flex flex-col items-center justify-center cursor-pointer rounded-lg mx-2
          ${isBottom ? 'py-2 px-1' : 'py-3 px-1 my-1'}
        `}
        onClick={() => onMenuClick(item.key)}
        onMouseEnter={() => setHoveredKey(item.key)}
        onMouseLeave={() => setHoveredKey(null)}
      >
        <div 
          className="nav-icon flex items-center justify-center mb-1"
          css={getIconStyles(isSelected, isBottom)}
        >
          {item.icon}
        </div>
        <div 
          className={`
            nav-label text-xs font-medium text-center leading-tight
            whitespace-nowrap text-ellipsis overflow-hidden max-w-full
            ${isSelected ? 'text-blue-700 font-semibold' : 'text-gray-500'}
          `}
        >
          {item.label}
        </div>
      </StyledNavItem>
    );
  };

  return (
    <Sider 
      width={80} 
      className="h-screen bg-white shadow-lg"
    >
      <div className="h-full flex flex-col">
        {/* 主导航区域 - 使用Tailwind CSS */}
        <div className="flex-1 pt-6 pb-3">
          {mainMenuItems.map(item => renderMenuItem(item))}
        </div>
        
        {/* 底部功能区域 - 使用Tailwind CSS */}
        <div className="pt-4 pb-4">
          {bottomMenuItems.map(item => renderMenuItem(item, true))}
        </div>
      </div>
    </Sider>
  );
};

export default LeftSidebar; 