/**
 * 分享视图组件（占位页面）
 * 显示分享功能的占位内容
 */

import React from 'react';
import { Empty, Typography, theme } from 'antd';
import { ShareAltOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * 分享视图组件
 */
const SharedView: React.FC = () => {
  const { token } = theme.useToken();

  return (
    <div 
      className="h-full flex flex-col"
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {/* 头部 */}
      <div 
        className="px-6 py-4 border-b"
        style={{ borderColor: token.colorBorderSecondary }}
      >
        <div className="flex items-center">
          <ShareAltOutlined 
            className="mr-3 text-xl"
            style={{ color: token.colorPrimary }}
          />
          <div>
            <Title level={4} className="mb-1">
              分享
            </Title>
            <Text type="secondary">
              与他人安全分享您的密码和凭据
            </Text>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <Empty
            image={
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
                style={{ backgroundColor: token.colorPrimaryBg }}
              >
                <ShareAltOutlined 
                  className="text-2xl"
                  style={{ color: token.colorPrimary }}
                />
              </div>
            }
            description={
              <div className="space-y-3">
                <Title level={4} style={{ color: token.colorText }}>
                  分享功能即将上线
                </Title>
                <Text type="secondary" className="block">
                  我们正在开发安全的密码分享功能，让您可以与团队成员或家人安全地共享凭据。
                </Text>
                <div 
                  className="mt-4 p-4 rounded-lg"
                  style={{ backgroundColor: token.colorInfoBg }}
                >
                  <Text 
                    className="text-sm"
                    style={{ color: token.colorInfo }}
                  >
                    💡 即将支持的功能：
                  </Text>
                  <ul 
                    className="mt-2 text-sm text-left space-y-1"
                    style={{ color: token.colorTextSecondary }}
                  >
                    <li>• 端到端加密分享</li>
                    <li>• 临时访问链接</li>
                    <li>• 权限控制管理</li>
                    <li>• 分享记录追踪</li>
                  </ul>
                </div>
              </div>
            }
          />
        </div>
      </div>
    </div>
  );
};

export default SharedView; 