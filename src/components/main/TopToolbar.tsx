/** @jsxImportSource @emotion/react */

/**
 * 顶部工具栏组件
 * 使用Tailwind CSS + emotion混合方案实现样式
 * 只包含搜索框、联网状态、用户名+用户头像
 */

import React, { useState } from 'react';
import { Input, Avatar, Badge } from 'antd';
import styled from '@emotion/styled';
import { css } from '@emotion/react';
import {
  WifiOutlined,
  UserOutlined,
  SearchOutlined,
} from '@ant-design/icons';


interface TopToolbarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  userName?: string;
  userAvatar?: string;
  isOnline?: boolean;
}

// 主题变量
const theme = {
  colors: {
    primary: '#1d4ed8',
    gray50: '#f9fafb',
    gray100: '#f3f4f6',
    gray400: '#9ca3af',
    gray600: '#4b5563',
    gray900: '#111827',
    green: '#10b981',
    white: '#ffffff',
  },
  transition: '0.2s ease-in-out',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
};

// 使用emotion的styled组件处理复杂样式
const StyledSearchWrapper = styled.div`
  position: relative;
  transition: all ${theme.transition};
  
  .ant-input-search {
    .ant-input {
      padding: 8px 12px;
      font-size: 14px;
      transition: all ${theme.transition};
    }
    
    &::placeholder {
        color: #9ca3af;
    }
    
    .ant-input-search-button {
      border-radius: 0 8px 8px 0;
      border-color: #e5e7eb;
      background: ${theme.colors.gray50};
      
      &:hover {
        background: ${theme.colors.gray100};
        border-color: ${theme.colors.primary};
      }
    }
  }
`;

const StyledUserInfo = styled.div`
  transition: all ${theme.transition};
`;

// 联网状态指示器样式
const getNetworkStatusStyles = (isOnline: boolean) => css`
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: ${isOnline ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'};
  color: ${isOnline ? theme.colors.green : '#ef4444'};
  font-size: 12px;
  font-weight: 500;
  transition: all ${theme.transition};
  margin-right: 10px;
  
  .network-icon {
    margin-right: 4px;
    font-size: 14px;
  }
  
  &:hover {
    background-color: ${isOnline ? 'rgba(16, 185, 129, 0.15)' : 'rgba(239, 68, 68, 0.15)'};
  }
`;

/**
 * 顶部工具栏组件
 */
const TopToolbar: React.FC<TopToolbarProps> = ({
  searchValue,
  onSearchChange,
  userName = '用户',
  userAvatar,
  isOnline = true,
}) => {
  const [searchFocused, setSearchFocused] = useState(false);

  /**
   * 处理搜索框焦点变化
   */
  const handleSearchFocus = (focused: boolean) => {
    setSearchFocused(focused);
  };

  /**
   * 获取用户头像显示
   */
  const getUserAvatar = () => {
    if (userAvatar) {
      return <Avatar size={32} src={userAvatar} className="user-avatar" />;
    }
    return (
      <Avatar 
        size={32} 
        className="user-avatar bg-blue-500" 
        icon={<UserOutlined />} 
      />
    );
  };

  return (
    <div className="px-6 py-3">
      <div className="flex items-center justify-between">
        {/* 左侧：搜索框 */}
        <div className="flex-1 max-w-md">
          <StyledSearchWrapper>
            <Input
              placeholder="搜索保险箱中所有产品"
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              onFocus={() => handleSearchFocus(true)}
              onBlur={() => handleSearchFocus(false)}
              size="middle"
              allowClear
              className={`
                transition-all duration-200
                ${searchFocused ? 'scale-105' : 'scale-100'}
              `}
            />
          </StyledSearchWrapper>
        </div>

        {/* 右侧：联网状态 + 用户信息 */}
        <div className="flex items-center space-x-4 relative mr-2">
          {/* 联网状态指示器 */}
          <div css={getNetworkStatusStyles(isOnline)} className="pointer-events-none">
            <WifiOutlined className="network-icon" />
            <span>{isOnline ? '已连接' : '离线'}</span>
          </div>

          {/* 用户信息 */}
          <StyledUserInfo className="flex items-center space-x-3 cursor-pointer">
            <div className="text-right mr-2">
              <div className="text-sm font-mediu">
                {userName}
              </div>
              <div className="text-xs text-gray-500">
                77个/200个
              </div>
            </div>
            <Badge 
              dot 
              status={isOnline ? 'success' : 'default'}
              offset={[-2, 2]}
            >
              {getUserAvatar()}
            </Badge>
          </StyledUserInfo>
        </div>
      </div>
    </div>
  );
};

export default TopToolbar; 