/**
 * 回收站视图组件
 * 显示已软删除的凭据，支持恢复和永久删除
 */

import React from 'react';
import { Button, List, Modal, Typography, Space, theme, Empty } from 'antd';
import {
  RestOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { useHybridCredentials } from '../../contexts/HybridCredentialsContext';
import type { LoginCredentialOutput } from '../../types';

const { Title, Text } = Typography;

/**
 * 回收站视图组件
 */
const TrashView: React.FC = () => {
  const { token } = theme.useToken();
  const {
    trashCredentials,
    pendingDeletionCount,
    restoreCredential,
    permanentlyDeleteCredential,
    cleanupExpiredItems,
    isLoading,
  } = useHybridCredentials();

  /**
   * 处理恢复凭据
   */
  const handleRestore = async (credential: LoginCredentialOutput) => {
    Modal.confirm({
      title: '恢复凭据',
      content: `确定要恢复凭据 "${credential.name}" 吗？`,
      icon: <RestOutlined />,
      okText: '恢复',
      cancelText: '取消',
      onOk: async () => {
        try {
          await restoreCredential(credential.id);
        } catch (error) {
          console.error('恢复凭据失败:', error);
        }
      },
    });
  };

  /**
   * 处理永久删除凭据
   */
  const handlePermanentDelete = async (credential: LoginCredentialOutput) => {
    Modal.confirm({
      title: '永久删除凭据',
      content: (
        <div>
          <p>确定要永久删除凭据 <strong>"{credential.name}"</strong> 吗？</p>
          <div 
            className="mt-3 p-3 rounded-md"
            style={{ 
              backgroundColor: token.colorErrorBg,
              border: `1px solid ${token.colorErrorBorder}`
            }}
          >
            <div className="flex items-start space-x-2">
              <ExclamationCircleOutlined 
                style={{ color: token.colorError, fontSize: '16px' }}
              />
              <div className="text-sm">
                <div 
                  className="font-medium mb-1"
                  style={{ color: token.colorError }}
                >
                  警告：此操作不可撤销！
                </div>
                <div style={{ color: token.colorTextSecondary }}>
                  永久删除后将无法恢复此凭据的任何信息。
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
      icon: <ExclamationCircleOutlined style={{ color: token.colorError }} />,
      okText: '永久删除',
      okType: 'danger',
      cancelText: '取消',
      width: 480,
      onOk: async () => {
        try {
          await permanentlyDeleteCredential(credential.id);
        } catch (error) {
          console.error('永久删除凭据失败:', error);
        }
      },
    });
  };

  /**
   * 处理清理过期项目
   */
  const handleCleanupExpired = async () => {
    if (pendingDeletionCount === 0) {
      Modal.info({
        title: '无需清理',
        content: '当前没有需要清理的过期项目。',
      });
      return;
    }

    Modal.confirm({
      title: '清理过期项目',
      content: (
        <div>
          <p>发现 <strong>{pendingDeletionCount}</strong> 个超过30天的已删除项目。</p>
          <div 
            className="mt-3 p-3 rounded-md"
            style={{ 
              backgroundColor: token.colorWarningBg,
              border: `1px solid ${token.colorWarningBorder}`
            }}
          >
            <div className="flex items-start space-x-2">
              <ClearOutlined 
                style={{ color: token.colorWarning, fontSize: '16px' }}
              />
              <div className="text-sm">
                <div 
                  className="font-medium mb-1"
                  style={{ color: token.colorWarning }}
                >
                  自动清理说明：
                </div>
                <div style={{ color: token.colorTextSecondary }}>
                  这些项目将被永久删除且无法恢复。建议定期清理以释放存储空间。
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
      icon: <ClearOutlined style={{ color: token.colorWarning }} />,
      okText: '清理过期项目',
      cancelText: '取消',
      width: 480,
      onOk: async () => {
        try {
          await cleanupExpiredItems();
        } catch (error) {
          console.error('清理过期项目失败:', error);
        }
      },
    });
  };

  /**
   * 格式化删除时间
   */
  const formatDeletedTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天删除';
    } else if (diffDays === 1) {
      return '昨天删除';
    } else if (diffDays < 30) {
      return `${diffDays}天前删除`;
    } else {
      return `${diffDays}天前删除（即将永久删除）`;
    }
  };

  return (
    <div 
      className="h-full flex flex-col"
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {/* 头部 */}
      <div 
        className="px-6 py-4 border-b"
        style={{ borderColor: token.colorBorderSecondary }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={4} className="mb-1">
              回收站
            </Title>
            <Text type="secondary">
              已删除的凭据将在30天后自动永久删除
            </Text>
          </div>
          
          {pendingDeletionCount > 0 && (
            <Button
              type="primary"
              icon={<ClearOutlined />}
              onClick={handleCleanupExpired}
              loading={isLoading}
            >
              清理过期项目 ({pendingDeletionCount})
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 p-6 overflow-auto">
        {trashCredentials.length === 0 ? (
          <Empty
            description="回收站为空"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <List
            dataSource={trashCredentials}
            renderItem={(credential) => (
              <List.Item
                key={credential.id}
                style={{
                  backgroundColor: token.colorFillAlter,
                  marginBottom: '12px',
                  borderRadius: '8px',
                  padding: '16px',
                  border: `1px solid ${token.colorBorder}`,
                }}
                actions={[
                  <Button
                    key="restore"
                    type="primary"
                    icon={<RestOutlined />}
                    onClick={() => handleRestore(credential)}
                    size="small"
                  >
                    恢复
                  </Button>,
                  <Button
                    key="delete"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handlePermanentDelete(credential)}
                    size="small"
                  >
                    永久删除
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  title={
                    <div className="flex items-center justify-between">
                      <span style={{ color: token.colorText }}>
                        {credential.name}
                      </span>
                      <Text 
                        type="secondary" 
                        className="text-xs"
                      >
                        {formatDeletedTime(credential.updated_at)}
                      </Text>
                    </div>
                  }
                  description={
                    <Space direction="vertical" size={4}>
                      {credential.username && (
                        <Text type="secondary" className="text-sm">
                          用户名: {credential.username}
                        </Text>
                      )}
                      {credential.website && (
                        <Text type="secondary" className="text-sm">
                          网站: {credential.website}
                        </Text>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default TrashView; 