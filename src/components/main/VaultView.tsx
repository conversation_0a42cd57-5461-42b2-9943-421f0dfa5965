/**
 * 保险箱视图组件
 * 显示所有的凭据
 */

import React from 'react';
import { Typography, theme } from 'antd';
import { SafetyOutlined } from '@ant-design/icons';
import CredentialGrid from './CredentialGrid';
import type { LoginCredentialOutput } from '../../types';

const { Title, Text } = Typography;

interface VaultViewProps {
  credentials: LoginCredentialOutput[];
  selectedCredential: LoginCredentialOutput | null;
  onCredentialClick: (credential: LoginCredentialOutput) => void;
  searchValue: string;
}

/**
 * 保险箱视图组件
 */
const VaultView: React.FC<VaultViewProps> = ({
  credentials,
  selectedCredential,
  onCredentialClick,
  searchValue,
}) => {
  const { token } = theme.useToken();

  /**
   * 过滤凭据
   */
  const filteredCredentials = credentials.filter(credential => {
    const matchesSearch = credential.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                         (credential.username && credential.username.toLowerCase().includes(searchValue.toLowerCase())) ||
                         (credential.website && credential.website.toLowerCase().includes(searchValue.toLowerCase()));
    return matchesSearch;
  });

  return (
    <div 
      className="h-full flex flex-col"
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {/* 头部 */}
      <div 
        className="px-6 py-4 border-b"
        style={{ borderColor: token.colorBorderSecondary }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <SafetyOutlined 
              className="mr-3 text-xl"
              style={{ color: token.colorPrimary }}
            />
            <div>
              <Title level={4} className="mb-1">
                保险箱
              </Title>
              <Text type="secondary">
                您的所有密码和凭据
              </Text>
            </div>
          </div>
          
          <div 
            className="text-sm"
            style={{ color: token.colorTextSecondary }}
          >
            共 {filteredCredentials.length} 个项目
          </div>
        </div>
      </div>

      {/* 凭据网格 */}
      <div className="flex-1">
        <CredentialGrid
          credentials={filteredCredentials}
          selectedCredential={selectedCredential}
          onCredentialClick={onCredentialClick}
          viewMode="grid"
        />
      </div>
    </div>
  );
};

export default VaultView; 