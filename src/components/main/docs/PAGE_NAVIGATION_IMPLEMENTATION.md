# 页面导航功能实现文档

## 📋 概述

基于 LeftSidebar 组件实现了完整的页面导航切换功能，包括保险箱、收藏夹、分享、封存、回收站五个主要页面的切换。

## 🎯 实现功能

### ✅ 已实现的功能页面

#### 1. 保险箱 (vault)
- **功能**: 显示所有的密码凭据
- **组件**: `VaultView`
- **特性**: 
  - 显示所有凭据的网格视图
  - 支持搜索过滤
  - 显示项目统计
  - 支持凭据详情查看

#### 2. 收藏夹 (favorites)
- **功能**: 显示用户收藏的凭据
- **组件**: `FavoritesView`
- **特性**:
  - 显示收藏的凭据
  - 空状态友好提示
  - 收藏项目计数
  - 支持搜索过滤

#### 3. 回收站 (trash)
- **功能**: 显示已软删除的凭据
- **组件**: `TrashView`
- **特性**:
  - 列表形式显示已删除凭据
  - 支持恢复和永久删除操作
  - 显示删除时间和剩余天数
  - 批量清理过期项目功能

### 🚧 占位页面

#### 4. 分享 (shared)
- **功能**: 分享功能占位页面
- **组件**: `SharedView`
- **特性**:
  - 美观的占位界面
  - 功能介绍和即将上线提示
  - 主题适配支持

#### 5. 封存 (archived)
- **功能**: 封存功能占位页面
- **组件**: `ArchivedView`
- **特性**:
  - 美观的占位界面
  - 功能介绍和即将上线提示
  - 主题适配支持

## 🏗️ 技术实现

### 1. 组件结构

```
src/components/main/
├── VaultView.tsx          # 保险箱视图
├── FavoritesView.tsx      # 收藏夹视图
├── TrashView.tsx          # 回收站视图
├── SharedView.tsx         # 分享占位页面
├── ArchivedView.tsx       # 封存占位页面
├── LeftSidebar.tsx        # 左侧导航栏
├── CredentialGrid.tsx     # 凭据网格组件
├── CredentialDetail.tsx   # 凭据详情组件
└── index.ts               # 统一导出
```

### 2. 主页面切换逻辑

#### MainPage.tsx 核心实现
```tsx
const [selectedKey, setSelectedKey] = useState('vault');

const renderCurrentView = () => {
  const commonProps = {
    selectedCredential,
    onCredentialClick: handleCredentialClick,
    searchValue,
  };

  switch (selectedKey) {
    case 'vault':
      return <VaultView credentials={credentials} {...commonProps} />;
    case 'favorites':
      return <FavoritesView {...commonProps} />;
    case 'shared':
      return <SharedView />;
    case 'archived':
      return <ArchivedView />;
    case 'trash':
      return <TrashView />;
    default:
      return <VaultView credentials={credentials} {...commonProps} />;
  }
};
```

#### 页面切换处理
```tsx
const handleMenuClick = (key: string) => {
  setSelectedKey(key);
  setSelectedCredential(null); // 切换页面时清除选中的凭据
};
```

### 3. 详情面板显示逻辑

```tsx
{/* 右侧详情面板 - 只在有选中凭据且不是占位页面时显示 */}
{selectedCredential && (selectedKey === 'vault' || selectedKey === 'favorites' || selectedKey === 'trash') && (
  <CredentialDetail 
    credential={adaptCredentialForDetail(selectedCredential)} 
    onClose={() => setSelectedCredential(null)} 
  />
)}
```

## 🎨 设计特性

### 1. 统一的视觉风格
- 所有页面都使用相同的头部布局
- 统一的图标和颜色主题
- 一致的间距和排版

### 2. 主题适配
- 完全支持深浅色主题切换
- 使用 Ant Design 的 `theme.useToken()` 获取主题令牌
- 动态颜色和样式适配

### 3. 响应式设计
- 适配不同屏幕尺寸
- 灵活的布局结构
- 合理的内容展示

## 🔄 交互流程

### 1. 页面切换流程
1. 用户点击左侧导航栏菜单项
2. `LeftSidebar` 触发 `onMenuClick` 回调
3. `MainPage` 更新 `selectedKey` 状态
4. `renderCurrentView()` 根据新的 `selectedKey` 渲染对应页面
5. 清除当前选中的凭据

### 2. 凭据操作流程
1. 在支持的页面（保险箱、收藏夹、回收站）中点击凭据
2. 触发 `onCredentialClick` 回调
3. 更新 `selectedCredential` 状态
4. 显示右侧详情面板

### 3. 搜索过滤流程
1. 在顶部工具栏输入搜索关键词
2. `TopToolbar` 触发 `onSearchChange` 回调
3. 更新 `searchValue` 状态
4. 各个页面组件根据 `searchValue` 过滤显示内容

## 📊 状态管理

### 主页面状态
```tsx
const [selectedKey, setSelectedKey] = useState('vault');           // 当前选中的页面
const [searchValue, setSearchValue] = useState('');               // 搜索关键词
const [selectedCredential, setSelectedCredential] = useState(null); // 选中的凭据
```

### 数据来源
- `credentials`: 来自 `useHybridCredentials` 上下文
- `favoriteCredentials`: 来自 `useHybridCredentials` 上下文
- `trashCredentials`: 来自 `useHybridCredentials` 上下文

## 🚀 性能优化

### 1. 组件懒加载
- 只渲染当前选中的页面组件
- 避免不必要的组件渲染

### 2. 状态管理优化
- 页面切换时清除选中状态
- 避免状态冲突和内存泄漏

### 3. 搜索优化
- 在各个页面组件内部进行搜索过滤
- 避免重复的数据处理

## 🔮 未来扩展

### 1. 分享功能
- 端到端加密分享
- 临时访问链接
- 权限控制管理
- 分享记录追踪

### 2. 封存功能
- 批量封存操作
- 自动封存规则
- 快速恢复功能
- 封存历史记录

### 3. 其他优化
- 页面路由支持（URL 同步）
- 键盘快捷键导航
- 页面切换动画效果
- 更多视图模式支持

## ✅ 测试建议

### 1. 功能测试
- 验证所有页面切换正常
- 测试搜索功能在各页面的表现
- 验证详情面板的显示逻辑
- 测试占位页面的展示效果

### 2. 交互测试
- 测试页面切换时状态清除
- 验证凭据选中和取消选中
- 测试搜索实时过滤效果

### 3. 主题测试
- 验证深浅色主题切换
- 测试所有页面的主题适配
- 检查颜色和样式一致性

## 📝 总结

页面导航功能的实现提供了：

### 🎯 完整的功能体验
- 5个主要页面的完整切换
- 3个功能页面 + 2个占位页面
- 统一的交互体验

### 🛠️ 优雅的技术实现
- 清晰的组件结构
- 合理的状态管理
- 高效的渲染逻辑

### 🎨 一致的视觉设计
- 统一的设计语言
- 完整的主题支持
- 响应式布局适配

这种实现方式为后续功能的扩展和完善提供了良好的基础架构。 