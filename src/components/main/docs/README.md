# 主页面组件

本目录包含密码管理器主页面的所有组件，严格按照设计稿拆分和实现。

## 组件结构

### 1. LeftSidebar 左侧导航栏
- **功能**: 提供主要导航菜单（保险箱、收藏夹、分享、封存、回收站）
- **特点**: 紧凑设计，清晰的图标和标签
- **位置**: 页面左侧固定宽度200px

### 2. TopToolbar 顶部工具栏  
- **功能**: 分类标签页、操作按钮、搜索框、用户信息
- **特点**: 双层布局，第一层是标签页和操作按钮，第二层是搜索和视图切换
- **位置**: 页面顶部，横跨整个内容区域

### 3. CredentialGrid 密码列表
- **功能**: 显示密码卡片的网格或列表视图
- **特点**: 支持网格和列表两种显示模式，响应式布局
- **位置**: 主内容区域，可滚动

### 4. CredentialDetail 详情面板
- **功能**: 显示选中密码的详细信息和操作
- **特点**: 现代化卡片设计，支持密码显示/隐藏、一键复制等
- **位置**: 页面右侧，宽度320px

## 设计原则

1. **组件化**: 每个功能区域独立成组件，便于维护和复用
2. **响应式**: 支持不同屏幕尺寸的适配
3. **类型安全**: 完整的TypeScript类型定义
4. **样式一致**: 统一的设计语言和交互效果
5. **用户体验**: 流畅的动画和反馈

## 样式定制

所有组件的样式都在 `src/App.css` 中进行了统一定制，包括：
- 标签页样式
- 菜单项样式  
- 卡片悬停效果
- 滚动条样式
- 按钮圆角等

## 数据流

```
MainPage (主容器)
├── 状态管理 (选中项、搜索、视图模式等)
├── LeftSidebar (接收选中状态，触发回调)
├── TopToolbar (接收多个状态，触发多个回调)
├── CredentialGrid (接收凭据列表和选中状态)
└── CredentialDetail (接收选中的凭据详情)
```

## 使用方式

```tsx
import { LeftSidebar, TopToolbar, CredentialGrid, CredentialDetail } from '@/components/main';

// 在主页面中组合使用
<Layout>
  <LeftSidebar selectedKey={key} onMenuClick={handler} />
  <Layout>
    <TopToolbar {...props} />
    <Content>
      <CredentialGrid {...props} />
      {selected && <CredentialDetail credential={selected} />}
    </Content>
  </Layout>
</Layout>
``` 