/**
 * 认证状态管理 Context
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode, useState } from 'react';
import { 
  UserState, 
  RemoteUserData, 
  RemoteTokenInfo, 
  LoginFormData, 
  ApiResponse 
} from '../types';
import { getCurrentToken, clearToken, loginUserRemote } from '../api';

// 认证状态
interface AuthState extends UserState {
  loading: boolean;
  error?: string;
}

// 认证操作
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'SET_USER'; payload: RemoteUserData }
  | { type: 'SET_TOKEN'; payload: RemoteTokenInfo }
  | { type: 'LOGOUT' }
  | { type: 'RESET_STATE' };

// 认证 Context 类型
interface AuthContextType {
  state: AuthState;
  login: (formData: LoginFormData) => Promise<ApiResponse<any>>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  loading: false,
  user: undefined,
  token: undefined,
  error: undefined
};

// Reducer 函数
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
      
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
      
    case 'SET_USER':
      return { 
        ...state, 
        user: action.payload, 
        isAuthenticated: true, 
        loading: false,
        error: undefined 
      };
      
    case 'SET_TOKEN':
      return { 
        ...state, 
        token: action.payload, 
        isAuthenticated: true, 
        loading: false,
        error: undefined 
      };
      
    case 'LOGOUT':
      return { 
        ...initialState 
      };
      
    case 'RESET_STATE':
      return { ...initialState };
      
    default:
      return state;
  }
};

// 创建 Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider 组件属性
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * 认证状态 Provider 组件
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const [storedUser, setStoredUser] = useState<RemoteUserData | undefined>(() => {
    try {
      const stored = localStorage.getItem('auth-user');
      return stored ? JSON.parse(stored) : undefined;
    } catch {
      return undefined;
    }
  });

  // 保存用户信息到 localStorage
  const saveUserToStorage = (user: RemoteUserData | undefined) => {
    try {
      if (user) {
        localStorage.setItem('auth-user', JSON.stringify(user));
      } else {
        localStorage.removeItem('auth-user');
      }
      setStoredUser(user);
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  };

  /**
   * 检查认证状态
   */
  const checkAuth = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // 检查是否有有效的 token
      const tokenResponse = await getCurrentToken();
      
      if (tokenResponse.success && tokenResponse.data) {
        // 如果有 token 且有存储的用户信息，恢复认证状态
        if (storedUser) {
          dispatch({ type: 'SET_USER', payload: storedUser });
        }
      } else {
        // 没有有效 token，清除认证状态
        dispatch({ type: 'LOGOUT' });
        saveUserToStorage(undefined);
      }
    } catch (error) {
      console.error('检查认证状态失败:', error);
      dispatch({ type: 'SET_ERROR', payload: '认证状态检查失败' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * 用户登录
   */
  const login = async (formData: LoginFormData): Promise<ApiResponse<any>> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      const response = await loginUserRemote(formData);

      if (response.success && response.data) {
        const { data } = response.data;
        
        if (data?.token) {
          dispatch({ type: 'SET_TOKEN', payload: data.token });
          
          // 创建用户信息（从 token 或响应中获取）
          const userInfo: RemoteUserData = {
            id: 'current-user', // 实际应用中应该从响应中获取
            username: formData.contact.split('@')[0] || 'user',
            email: formData.contact.includes('@') ? formData.contact : undefined,
            phone: !formData.contact.includes('@') ? formData.contact : undefined,
            status: 'active',
            createdAt: new Date().toISOString()
          };
          
          dispatch({ type: 'SET_USER', payload: userInfo });
          saveUserToStorage(userInfo);
        }

        return response;
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || '登录失败' });
        return response;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录过程出错';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // 清除后端 token
      await clearToken();
      
      // 清除前端状态
      dispatch({ type: 'LOGOUT' });
      saveUserToStorage(undefined);
    } catch (error) {
      console.error('登出失败:', error);
      // 即使后端清除失败，也要清除前端状态
      dispatch({ type: 'LOGOUT' });
      saveUserToStorage(undefined);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * 清除错误
   */
  const clearError = (): void => {
    dispatch({ type: 'SET_ERROR', payload: undefined });
  };

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    checkAuth,
    clearError
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * 使用认证 Context 的 Hook
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * 认证状态相关的 Hooks
 */
export const useAuthState = () => {
  const { state } = useAuth();
  return state;
};

export const useAuthActions = () => {
  const { login, logout, checkAuth, clearError } = useAuth();
  return { login, logout, checkAuth, clearError };
}; 