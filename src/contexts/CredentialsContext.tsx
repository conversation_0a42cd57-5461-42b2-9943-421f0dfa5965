import { createContext, useState, useContext, useCallback, ReactNode, useEffect } from 'react';
import { App } from 'antd';
import {
    getAllCredentials,
    addCredential,
    updateCredential,
    deleteCredential
} from '../api/tauri-api';
import type { CredentialInput, CredentialOutput } from '../types';
import { useVaultAuth } from './VaultAuthContext'; // Depend on auth context

interface CredentialsState {
    credentials: CredentialOutput[];
    isLoading: boolean;
    error: string | null;
}

interface CredentialsActions {
    fetchCredentials: () => Promise<void>;
    add: (data: CredentialInput) => Promise<void>;
    update: (id: number, data: CredentialInput) => Promise<void>;
    remove: (id: number) => Promise<void>;
}

const CredentialsContext = createContext<(CredentialsState & CredentialsActions) | undefined>(undefined);

export const CredentialsProvider = ({ children }: { children: ReactNode }) => {
    // 提供可消费 React context 的 message.xxx、Modal.xxx、notification.xxx 的静态方法，可以简化 useMessage 等方法需要手动植入 contextHolder 的问题。
    const { message } = App.useApp();
    const { isLocked, isSetup } = useVaultAuth(); // Get lock status
    const [state, setState] = useState<CredentialsState>({
        credentials: [],
        isLoading: false, // Start not loading, fetch triggered by effect
        error: null,
    });

    const fetchCredentials = useCallback(async () => {
        // Only fetch if unlocked and setup
        if (isLocked || !isSetup) {
            setState(s => ({ ...s, credentials: [], isLoading: false, error: null })); // Clear if locked/not setup
            return;
        }

        setState(s => ({ ...s, isLoading: true, error: null }));
        try {
            const credentials = await getAllCredentials();
            setState(s => ({ ...s, credentials, isLoading: false }));
        } catch (err: any) {
            if (String(err.message).includes("VaultLocked")) {
                // Vault got locked externally, VaultAuthContext will handle the state change via its own checks/effects
                // We just clear credentials here.
                setState(s => ({ ...s, credentials: [], isLoading: false, error: "Vault is locked." }));
            } else {
                 const errorMsg = `Failed to fetch credentials: ${err.message}`;
                setState(s => ({ ...s, isLoading: false, error: errorMsg }));
                message.error(errorMsg);
            }
        }
    }, [isLocked, isSetup]); // Dependency on lock and setup status

    // Fetch when lock status changes (and vault is setup)
    useEffect(() => {
        // isSetup might initially be null, only fetch when it's explicitly true
        if (isSetup === true) {
             fetchCredentials();
        } else if (isSetup === false || isLocked === true) {
            // Clear credentials if not setup or locked
            setState(s => ({ ...s, credentials: [], isLoading: false, error: null }));
        }
    }, [fetchCredentials, isSetup, isLocked]); // Re-run whenever lock/setup status changes

    const add = useCallback(async (data: CredentialInput) => {
        if (isLocked) throw new Error("Vault is locked.");
        setState(s => ({ ...s, error: null }));
        try {
            await addCredential(data);
            await fetchCredentials(); // Refresh after add
        } catch (err: any) {
             const errorMsg = `Failed to add credential: ${err.message}`;
            setState(s => ({ ...s, error: errorMsg }));
            message.error(errorMsg);
            throw err; // Re-throw for form handling
        }
    }, [isLocked, fetchCredentials]);

    const update = useCallback(async (id: number, data: CredentialInput) => {
        if (isLocked) throw new Error("Vault is locked.");
        setState(s => ({ ...s, error: null }));
        // Implement optimistic update here if desired for better UX
        try {
            await updateCredential(id, data);
            await fetchCredentials(); // Refresh after update
            message.success('Credential updated successfully!');
        } catch (err: any) {
            const errorMsg = `Update failed: ${err.message}`;
            setState(s => ({ ...s, error: errorMsg }));
            message.error(errorMsg);
            throw err; // Re-throw for modal handling
        }
    }, [isLocked, fetchCredentials]);

    const remove = useCallback(async (id: number) => {
        if (isLocked) throw new Error("Vault is locked.");
        setState(s => ({ ...s, error: null }));
        // Implement optimistic update (filter locally) here if desired
        try {
            await deleteCredential(id);
            await fetchCredentials(); // Refresh after delete
            message.success('Credential deleted successfully!');
        } catch (err: any) {
             const errorMsg = `Failed to delete credential: ${err.message}`;
            setState(s => ({ ...s, error: errorMsg }));
            message.error(errorMsg);
            throw err; // Re-throw if needed elsewhere
        }
    }, [isLocked, fetchCredentials]);

    const value = { ...state, fetchCredentials, add, update, remove };

    return <CredentialsContext.Provider value={value}>{children}</CredentialsContext.Provider>;
};

export const useCredentials = (): CredentialsState & CredentialsActions => {
    const context = useContext(CredentialsContext);
    if (context === undefined) {
        throw new Error('useCredentials must be used within a CredentialsProvider');
    }
    return context;
};