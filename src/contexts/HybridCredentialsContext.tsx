/**
 * Hybrid Storage 凭据管理上下文
 * 使用新的 hybrid_storage 系统管理密码数据
 */

import React, { createContext, useState, useContext, useCallback, ReactNode, useEffect } from 'react';
import { App } from 'antd';
import {
    getAllLoginCredentials,
    addLoginCredential,
    updateLoginCredential,
    softDeleteLoginCredential,
    getAllFavoriteCredentials,
    searchCredentials,
    toggleCredentialFavorite,
    getAllVaults,
    getAllTrashLoginCredentials,
    restoreLoginCredential,
    permanentlyDeleteLoginCredential,
    cleanupExpiredDeletedItems,
    getItemsPendingPermanentDeletionCount,
    archiveLoginCredential,
    unarchiveLoginCredential,
    getAllArchivedLoginCredentials,
    batchArchiveLoginCredentials,
    batchUnarchiveLoginCredentials,
} from '../api/hybrid-api';
import { getOrmServiceStatus } from '../api/orm-api';
import { useOrmInitialization } from '../hooks/useOrmInitialization';
import type { LoginCredentialInput, LoginCredentialOutput, VaultInfo } from '../types';

interface HybridCredentialsState {
    credentials: LoginCredentialOutput[];
    favoriteCredentials: LoginCredentialOutput[];
    trashCredentials: LoginCredentialOutput[];
    archivedCredentials: LoginCredentialOutput[];
    vaults: VaultInfo[];
    isLoading: boolean;
    error: string | null;
    pendingDeletionCount: number;
}

interface HybridCredentialsActions {
    // 凭据操作
    loadCredentials: () => Promise<void>;
    addCredential: (credential: LoginCredentialInput) => Promise<LoginCredentialOutput>;
    updateCredential: (id: number, updates: Partial<LoginCredentialInput>) => Promise<LoginCredentialOutput>;
    deleteCredential: (id: number) => Promise<void>; // 软删除
    toggleFavorite: (id: number, currentFavorite: boolean) => Promise<LoginCredentialOutput>;
    searchCredentials: (query: string, searchType?: 'name' | 'username' | 'website' | 'all') => Promise<LoginCredentialOutput[]>;
    
    // 收藏夹操作
    loadFavoriteCredentials: () => Promise<void>;
    
    // 回收站操作
    loadTrashCredentials: () => Promise<void>;
    restoreCredential: (id: number) => Promise<void>;
    permanentlyDeleteCredential: (id: number) => Promise<void>;
    cleanupExpiredItems: () => Promise<number>;
    loadPendingDeletionCount: () => Promise<void>;
    
    // 封存操作
    loadArchivedCredentials: () => Promise<void>;
    archiveCredential: (id: number) => Promise<void>;
    unarchiveCredential: (id: number) => Promise<void>;
    batchArchiveCredentials: (ids: number[]) => Promise<number>;
    batchUnarchiveCredentials: (ids: number[]) => Promise<number>;
    
    // 密码库操作
    loadVaults: () => Promise<void>;
    
    // 状态管理
    clearError: () => void;
    setLoading: (loading: boolean) => void;
    
    // 新增方法
    reloadData: () => Promise<void>;
}

type HybridCredentialsContextType = HybridCredentialsState & HybridCredentialsActions;

const HybridCredentialsContext = createContext<HybridCredentialsContextType | undefined>(undefined);

interface HybridCredentialsProviderProps {
    children: ReactNode;
}

/**
 * Hybrid Storage 凭据管理提供者组件
 */
export const HybridCredentialsProvider: React.FC<HybridCredentialsProviderProps> = ({ children }) => {
    const { message } = App.useApp();
    const { isInitializing: ormInitializing, isSuccess: ormSuccess, isError: ormError } = useOrmInitialization();
    
    // 状态管理
    const [state, setState] = useState<HybridCredentialsState>({
        credentials: [],
        favoriteCredentials: [],
        trashCredentials: [],
        archivedCredentials: [],
        vaults: [],
        isLoading: false,
        error: null,
        pendingDeletionCount: 0,
    });

    // 添加一个标记来跟踪是否已经初始化过数据
    const [isDataInitialized, setIsDataInitialized] = useState(false);

    /**
     * 更新状态的辅助函数
     */
    const updateState = useCallback((updates: Partial<HybridCredentialsState>) => {
        setState(prev => ({ ...prev, ...updates }));
    }, []);

    /**
     * 错误处理函数
     */
    const handleError = useCallback((error: any, operation: string) => {
        const errorMessage = error?.message || error?.toString() || `${operation}失败`;
        console.error(`${operation} error:`, error);
        updateState({ error: errorMessage, isLoading: false });
        message.error(errorMessage);
    }, [message, updateState]);

    /**
     * 加载所有凭据
     */
    const loadCredentials = useCallback(async () => {
        try {
            updateState({ isLoading: true, error: null });
            const credentials = await getAllLoginCredentials();
            updateState({ credentials, isLoading: false });
        } catch (error) {
            handleError(error, '加载凭据');
        }
    }, [updateState, handleError]);

    /**
     * 加载收藏凭据
     */
    const loadFavoriteCredentials = useCallback(async () => {
        try {
            const favoriteCredentials = await getAllFavoriteCredentials();
            updateState({ favoriteCredentials });
        } catch (error) {
            handleError(error, '加载收藏凭据');
        }
    }, [updateState, handleError]);

    /**
     * 加载密码库
     */
    const loadVaults = useCallback(async () => {
        try {
            const vaults = await getAllVaults();
            updateState({ vaults });
        } catch (error) {
            handleError(error, '加载密码库');
        }
    }, [updateState, handleError]);

    /**
     * 添加新凭据
     */
    const addCredential = useCallback(async (credential: LoginCredentialInput): Promise<LoginCredentialOutput> => {
        try {
            updateState({ isLoading: true, error: null });
            const newCredential = await addLoginCredential(credential);
            
            // 更新本地状态
            updateState({
                credentials: [...state.credentials, newCredential],
                isLoading: false
            });
            
            message.success('凭据添加成功');
            return newCredential;
        } catch (error) {
            handleError(error, '添加凭据');
            throw error;
        }
    }, [state.credentials, updateState, handleError, message]);

    /**
     * 更新凭据
     */
    const updateCredential = useCallback(async (
        id: number, 
        updates: Partial<LoginCredentialInput>
    ): Promise<LoginCredentialOutput> => {
        try {
            updateState({ isLoading: true, error: null });
            const updatedCredential = await updateLoginCredential(id, updates);
            
            // 更新本地状态
            updateState({
                credentials: state.credentials.map(cred => 
                    cred.id === id ? updatedCredential : cred
                ),
                favoriteCredentials: state.favoriteCredentials.map(cred => 
                    cred.id === id ? updatedCredential : cred
                ),
                isLoading: false
            });
            
            message.success('凭据更新成功');
            return updatedCredential;
        } catch (error) {
            handleError(error, '更新凭据');
            throw error;
        }
    }, [state.credentials, state.favoriteCredentials, updateState, handleError, message]);

    /**
     * 删除凭据（软删除）
     */
    const deleteCredential = useCallback(async (id: number): Promise<void> => {
        try {
            updateState({ isLoading: true, error: null });
            await softDeleteLoginCredential(id);
            
            // 更新本地状态
            updateState({
                credentials: state.credentials.filter(cred => cred.id !== id),
                favoriteCredentials: state.favoriteCredentials.filter(cred => cred.id !== id),
                isLoading: false
            });
            
            message.success('凭据已放入回收站');
            
            // 异步重新加载回收站和待删除计数
            Promise.all([
                getAllTrashLoginCredentials().then(trashCredentials => 
                    updateState({ trashCredentials })
                ),
                getItemsPendingPermanentDeletionCount().then(count => 
                    updateState({ pendingDeletionCount: count })
                )
            ]).catch(error => {
                console.error('Failed to reload trash data:', error);
            });
        } catch (error) {
            handleError(error, '删除凭据');
            throw error;
        }
    }, [state.credentials, state.favoriteCredentials, updateState, handleError, message]);

    /**
     * 切换收藏状态
     */
    const toggleFavorite = useCallback(async (
        id: number, 
        currentFavorite: boolean
    ): Promise<LoginCredentialOutput> => {
        try {
            const updatedCredential = await toggleCredentialFavorite(id, currentFavorite);
            
            // 更新本地状态
            updateState({
                credentials: state.credentials.map(cred => 
                    cred.id === id ? updatedCredential : cred
                ),
                favoriteCredentials: updatedCredential.favorite 
                    ? [...state.favoriteCredentials.filter(cred => cred.id !== id), updatedCredential]
                    : state.favoriteCredentials.filter(cred => cred.id !== id)
            });
            
            message.success(updatedCredential.favorite ? '已添加到收藏夹' : '已从收藏夹移除');
            return updatedCredential;
        } catch (error) {
            handleError(error, '切换收藏状态');
            throw error;
        }
    }, [state.credentials, state.favoriteCredentials, updateState, handleError, message]);

    /**
     * 搜索凭据
     */
    const searchCredentialsAction = useCallback(async (
        query: string,
        searchType: 'name' | 'username' | 'website' | 'all' = 'all'
    ): Promise<LoginCredentialOutput[]> => {
        try {
            return await searchCredentials(query, searchType);
        } catch (error) {
            handleError(error, '搜索凭据');
            return [];
        }
    }, [handleError]);

    /**
     * 清除错误
     */
    const clearError = useCallback(() => {
        updateState({ error: null });
    }, [updateState]);

    /**
     * 设置加载状态
     */
    const setLoading = useCallback((loading: boolean) => {
        updateState({ isLoading: loading });
    }, [updateState]);

    /**
     * 加载回收站凭据
     */
    const loadTrashCredentials = useCallback(async () => {
        try {
            const trashCredentials = await getAllTrashLoginCredentials();
            updateState({ trashCredentials });
        } catch (error) {
            handleError(error, '加载回收站凭据');
        }
    }, [updateState, handleError]);

    /**
     * 从回收站恢复凭据
     */
    const restoreCredential = useCallback(async (id: number): Promise<void> => {
        try {
            updateState({ isLoading: true, error: null });
            const restoredCredential = await restoreLoginCredential(id);
            
            // 更新本地状态
            updateState({
                credentials: [...state.credentials, restoredCredential],
                trashCredentials: state.trashCredentials.filter(cred => cred.id !== id),
                isLoading: false
            });
            
            message.success('凭据已从回收站恢复');
            
            // 异步重新加载待删除计数
            getItemsPendingPermanentDeletionCount().then(count => 
                updateState({ pendingDeletionCount: count })
            ).catch(error => {
                console.error('Failed to reload pending deletion count:', error);
            });
        } catch (error) {
            handleError(error, '恢复凭据');
            throw error;
        }
    }, [state.credentials, state.trashCredentials, updateState, handleError, message]);

    /**
     * 永久删除凭据
     */
    const permanentlyDeleteCredential = useCallback(async (id: number): Promise<void> => {
        try {
            updateState({ isLoading: true, error: null });
            await permanentlyDeleteLoginCredential(id);
            
            // 更新本地状态
            updateState({
                trashCredentials: state.trashCredentials.filter(cred => cred.id !== id),
                isLoading: false
            });
            
            message.success('凭据已永久删除');
            
            // 异步重新加载待删除计数
            getItemsPendingPermanentDeletionCount().then(count => 
                updateState({ pendingDeletionCount: count })
            ).catch(error => {
                console.error('Failed to reload pending deletion count:', error);
            });
        } catch (error) {
            handleError(error, '永久删除凭据');
            throw error;
        }
    }, [state.trashCredentials, updateState, handleError, message]);

    /**
     * 清理过期的已删除项目
     */
    const cleanupExpiredItems = useCallback(async (): Promise<number> => {
        try {
            updateState({ isLoading: true, error: null });
            const deletedCount = await cleanupExpiredDeletedItems();
            
            // 异步重新加载回收站和待删除计数
            Promise.all([
                getAllTrashLoginCredentials().then(trashCredentials => 
                    updateState({ trashCredentials })
                ),
                getItemsPendingPermanentDeletionCount().then(count => 
                    updateState({ pendingDeletionCount: count })
                )
            ]).catch(error => {
                console.error('Failed to reload trash data:', error);
            });
            
            updateState({ isLoading: false });
            
            if (deletedCount > 0) {
                message.success(`已清理 ${deletedCount} 个过期项目`);
            } else {
                message.info('没有需要清理的过期项目');
            }
            
            return deletedCount;
        } catch (error) {
            handleError(error, '清理过期项目');
            return 0;
        }
    }, [updateState, handleError, message]);

    /**
     * 加载待永久删除的项目数量
     */
    const loadPendingDeletionCount = useCallback(async () => {
        try {
            const count = await getItemsPendingPermanentDeletionCount();
            updateState({ pendingDeletionCount: count });
        } catch (error) {
            handleError(error, '加载待删除计数');
        }
    }, [updateState, handleError]);

    /**
     * 加载已封存的凭据
     */
    const loadArchivedCredentials = useCallback(async () => {
        try {
            const archivedCredentials = await getAllArchivedLoginCredentials();
            updateState({ archivedCredentials });
        } catch (error) {
            handleError(error, '加载封存凭据');
        }
    }, [updateState, handleError]);

    /**
     * 封存凭据
     */
    const archiveCredential = useCallback(async (id: number): Promise<void> => {
        try {
            updateState({ isLoading: true, error: null });
            const archivedCredential = await archiveLoginCredential(id);
            
            // 更新本地状态
            updateState({
                credentials: state.credentials.filter(cred => cred.id !== id),
                favoriteCredentials: state.favoriteCredentials.filter(cred => cred.id !== id),
                archivedCredentials: [...state.archivedCredentials, archivedCredential],
                isLoading: false
            });
            
            message.success('凭据已封存');
        } catch (error) {
            handleError(error, '封存凭据');
            throw error;
        }
    }, [state.credentials, state.favoriteCredentials, state.archivedCredentials, updateState, handleError, message]);

    /**
     * 从封存中恢复凭据
     */
    const unarchiveCredential = useCallback(async (id: number): Promise<void> => {
        try {
            updateState({ isLoading: true, error: null });
            const restoredCredential = await unarchiveLoginCredential(id);
            
            // 更新本地状态
            updateState({
                credentials: [...state.credentials, restoredCredential],
                archivedCredentials: state.archivedCredentials.filter(cred => cred.id !== id),
                isLoading: false
            });
            
            message.success('凭据已从封存中恢复');
        } catch (error) {
            handleError(error, '恢复封存凭据');
            throw error;
        }
    }, [state.credentials, state.archivedCredentials, updateState, handleError, message]);

    /**
     * 批量封存凭据
     */
    const batchArchiveCredentials = useCallback(async (ids: number[]): Promise<number> => {
        try {
            updateState({ isLoading: true, error: null });
            const affectedRows = await batchArchiveLoginCredentials(ids);
            
            // 重新加载数据以确保状态同步
            await Promise.all([
                loadCredentials(),
                loadFavoriteCredentials(),
                loadArchivedCredentials()
            ]);
            
            updateState({ isLoading: false });
            message.success(`已封存 ${affectedRows} 个凭据`);
            
            return affectedRows;
        } catch (error) {
            handleError(error, '批量封存凭据');
            return 0;
        }
    }, [updateState, handleError, message, loadCredentials, loadFavoriteCredentials, loadArchivedCredentials]);

    /**
     * 批量从封存中恢复凭据
     */
    const batchUnarchiveCredentials = useCallback(async (ids: number[]): Promise<number> => {
        try {
            updateState({ isLoading: true, error: null });
            const affectedRows = await batchUnarchiveLoginCredentials(ids);
            
            // 重新加载数据以确保状态同步
            await Promise.all([
                loadCredentials(),
                loadFavoriteCredentials(),
                loadArchivedCredentials()
            ]);
            
            updateState({ isLoading: false });
            message.success(`已恢复 ${affectedRows} 个凭据`);
            
            return affectedRows;
        } catch (error) {
            handleError(error, '批量恢复封存凭据');
            return 0;
        }
    }, [updateState, handleError, message, loadCredentials, loadFavoriteCredentials, loadArchivedCredentials]);

    /**
     * 安全的数据加载函数 - 检查 ORM 状态后再加载
     */
    const safeLoadData = useCallback(async () => {
        try {
            // 检查 ORM 服务状态
            const ormStatus = await getOrmServiceStatus();
            
            if (!ormStatus.is_initialized || !ormStatus.is_available) {
                console.log('ORM 服务尚未就绪，跳过数据加载');
                return false;
            }

            console.log('ORM 服务已就绪，开始加载数据');
            updateState({ isLoading: true, error: null });

            // 直接调用 API 函数，避免依赖循环
            const [
                credentials,
                favoriteCredentials,
                vaults,
                trashCredentials,
                archivedCredentials,
                pendingDeletionCount
            ] = await Promise.all([
                getAllLoginCredentials(),
                getAllFavoriteCredentials(),
                getAllVaults(),
                getAllTrashLoginCredentials(),
                getAllArchivedLoginCredentials(),
                getItemsPendingPermanentDeletionCount()
            ]);

            updateState({
                credentials,
                favoriteCredentials,
                vaults,
                trashCredentials,
                archivedCredentials,
                pendingDeletionCount,
                isLoading: false,
                error: null
            });

            setIsDataInitialized(true);
            console.log('HybridCredentialsProvider 数据加载完成');
            return true;
        } catch (error) {
            const errorMessage = typeof error === 'string' ? error : (error as any)?.message || '数据加载失败';
            console.error('数据加载失败:', error);
            updateState({ 
                error: errorMessage, 
                isLoading: false 
            });
            
            // 只有在不是 ORM 未初始化错误时才显示错误消息
            if (!errorMessage.includes('ORM service not initialized')) {
                message.error(errorMessage);
            }
            return false;
        }
    }, [updateState, message]);

    /**
     * 监听 ORM 初始化状态变化
     */
    useEffect(() => {
        // 如果数据已经初始化过，不需要重复初始化
        if (isDataInitialized) {
            return;
        }

        // 如果 ORM 正在初始化，等待完成
        if (ormInitializing) {
            console.log('ORM 正在初始化，等待完成...');
            return;
        }

        // 如果 ORM 初始化成功，加载数据
        if (ormSuccess) {
            console.log('ORM 初始化成功，开始加载数据');
            safeLoadData();
            return;
        }

        // 如果 ORM 初始化失败，尝试检查状态并加载数据
        if (ormError) {
            console.log('ORM 初始化失败，尝试检查状态并加载数据');
            safeLoadData();
            return;
        }

        // 如果没有 ORM 状态信息，直接尝试加载数据
        console.log('没有 ORM 状态信息，直接尝试加载数据');
        safeLoadData();
    }, [ormInitializing, ormSuccess, ormError, isDataInitialized, safeLoadData]);

    /**
     * 重新加载数据的公共方法
     */
    const reloadData = useCallback(async () => {
        setIsDataInitialized(false);
        await safeLoadData();
    }, [safeLoadData]);

    // 上下文值
    const contextValue: HybridCredentialsContextType = {
        // 状态
        ...state,
        
        // 操作
        loadCredentials,
        addCredential,
        updateCredential,
        deleteCredential,
        toggleFavorite,
        searchCredentials: searchCredentialsAction,
        loadFavoriteCredentials,
        loadVaults,
        clearError,
        setLoading,
        loadTrashCredentials,
        restoreCredential,
        permanentlyDeleteCredential,
        cleanupExpiredItems,
        loadPendingDeletionCount,
        loadArchivedCredentials,
        archiveCredential,
        unarchiveCredential,
        batchArchiveCredentials,
        batchUnarchiveCredentials,
        
        // 新增重新加载方法
        reloadData,
    };

    return (
        <HybridCredentialsContext.Provider value={contextValue}>
            {children}
        </HybridCredentialsContext.Provider>
    );
};

/**
 * 使用 Hybrid Storage 凭据上下文的 Hook
 */
export const useHybridCredentials = (): HybridCredentialsContextType => {
    const context = useContext(HybridCredentialsContext);
    if (context === undefined) {
        throw new Error('useHybridCredentials must be used within a HybridCredentialsProvider');
    }
    return context;
};

export default HybridCredentialsContext; 