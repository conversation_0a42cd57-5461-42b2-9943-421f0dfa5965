import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { listen } from '@tauri-apps/api/event';
import { message } from 'antd';
import type { CurrentUser } from '../types';
import { 
  getCurrentUserInfo, 
  isUserLoggedIn, 
  logoutCurrentUser, 
  reloadUserInfo 
} from '../api/hybrid-api';

interface UserContextType {
  // 状态
  user: CurrentUser | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  checkLoginStatus: () => Promise<void>;
  logout: () => Promise<void>;
  reloadUser: () => Promise<void>;
  setUser: (user: CurrentUser | null) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<CurrentUser | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 检查登录状态
  const checkLoginStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const loggedIn = await isUserLoggedIn();
      setIsLoggedIn(loggedIn);
      
      if (loggedIn) {
        const userInfo = await getCurrentUserInfo();
        setUser(userInfo);
        console.log('用户已登录:', userInfo);
      } else {
        setUser(null);
        console.log('用户未登录');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检查登录状态失败';
      setError(errorMessage);
      console.error('检查登录状态失败:', err);
      message.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 登出用户
  const logout = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await logoutCurrentUser();
      setUser(null);
      setIsLoggedIn(false);
      
      console.log('用户登出成功');
      message.success('登出成功');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登出失败';
      setError(errorMessage);
      console.error('登出失败:', err);
      message.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 重新加载用户信息
  const reloadUser = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const userInfo = await reloadUserInfo();
      setUser(userInfo);
      setIsLoggedIn(!!userInfo);
      
      if (userInfo) {
        console.log('用户信息重新加载成功:', userInfo);
        message.success('用户信息已更新');
      } else {
        console.log('没有找到已保存的用户信息');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '重新加载用户信息失败';
      setError(errorMessage);
      console.error('重新加载用户信息失败:', err);
      message.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 设置用户信息（用于登录后更新状态）
  const setUserInfo = (newUser: CurrentUser | null) => {
    setUser(newUser);
    setIsLoggedIn(!!newUser);
    if (newUser) {
      console.log('用户信息已更新:', newUser);
    }
  };

  // 监听应用事件
  useEffect(() => {
    let unlistenAppReady: (() => void) | undefined;
    let unlistenUserLoggedIn: (() => void) | undefined;

    const setupEventListeners = async () => {
      try {
        // 监听应用准备就绪事件
        unlistenAppReady = await listen('app_ready', () => {
          console.log('应用准备就绪，检查用户登录状态');
          checkLoginStatus();
        });

        // 监听用户登录事件
        unlistenUserLoggedIn = await listen<CurrentUser>('user_logged_in', (event) => {
          console.log('收到用户登录事件:', event.payload);
          setUserInfo(event.payload);
        });

        console.log('事件监听器已设置');
      } catch (err) {
        console.error('设置事件监听器失败:', err);
      }
    };

    setupEventListeners();

    // 初始检查登录状态
    checkLoginStatus();

    // 清理函数
    return () => {
      if (unlistenAppReady) {
        unlistenAppReady();
      }
      if (unlistenUserLoggedIn) {
        unlistenUserLoggedIn();
      }
    };
  }, []);

  const contextValue: UserContextType = {
    user,
    isLoggedIn,
    isLoading,
    error,
    checkLoginStatus,
    logout,
    reloadUser,
    setUser: setUserInfo,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// Hook for using the user context
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserContext; 