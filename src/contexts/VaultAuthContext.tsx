import { createContext, useState, useContext, useCallback, ReactNode, useEffect } from 'react';
import { App } from 'antd';
import { checkSetupStatus, setupVault, unlockVault, lockVault } from '../api/tauri-api';

interface VaultAuthState {
    isSetup: boolean | null;
    isLocked: boolean;
    isLoading: boolean;
    error: string | null;
}

interface VaultAuthActions {
    checkStatus: () => Promise<void>;
    setup: (password: string) => Promise<void>;
    unlock: (password: string) => Promise<void>;
    lock: () => Promise<void>;
}

const VaultAuthContext = createContext<(VaultAuthState & VaultAuthActions) | undefined>(undefined);

export const VaultAuthProvider = ({ children }: { children: ReactNode }) => {
    // 提供可消费 React context 的 message.xxx、Modal.xxx、notification.xxx 的静态方法，可以简化 useMessage 等方法需要手动植入 contextHolder 的问题。
    const { message } = App.useApp();

    const [state, setState] = useState<VaultAuthState>({
        isSetup: null,
        isLocked: true,
        isLoading: true, // Start loading initially to check status
        error: null,
    });

    const checkStatus = useCallback(async () => {
        // Don't set loading true if already loading to prevent loops? Or allow refetch anytime.
        setState(s => ({ ...s, isLoading: true, error: null }));
        try {
            const status = await checkSetupStatus();
            setState(s => ({ ...s, isSetup: status, isLocked: true, isLoading: false, error: null }));
        } catch (err: any) {
            const errorMsg = `Failed to check vault status: ${err.message}`;
            setState(s => ({ ...s, isSetup: false, isLocked: true, isLoading: false, error: errorMsg }));
            message.error(errorMsg);
        }
    }, []);

    // Initial check on mount
    useEffect(() => {
        setState(s => ({ ...s, isLoading: true, error: null })); // Set loading before check
        checkStatus();
    }, [checkStatus]);


    const setup = useCallback(async (password: string) => {
        setState(s => ({ ...s, isLoading: true, error: null }));
        try {
            await setupVault(password);
            setState(s => ({ ...s, isSetup: true, isLocked: true, isLoading: false }));
            message.success('Vault setup successfully! Please unlock it.');
        } catch (err: any) {
            const errorMsg = `Setup failed: ${err.message}`;
            setState(s => ({ ...s, isLoading: false, error: errorMsg }));
            message.error(errorMsg);
            throw err; // Re-throw for form handling if needed
        }
    }, []);

    const unlock = useCallback(async (password: string) => {
        setState(s => ({ ...s, isLoading: true, error: null }));
        try {
            await unlockVault(password);
            setState(s => ({ ...s, isLocked: false, isLoading: false, error: null })); // Clear error on success
            message.success('Vault Unlocked');
        } catch (err: any) {
            const errorMsg = `Unlock failed: ${err.message}`;
            setState(s => ({ ...s, isLoading: false, isLocked: true, error: errorMsg })); // Ensure locked on failure
            message.error(errorMsg);
            throw err; // Re-throw for form handling
        }
    }, []);

    const lock = useCallback(async () => {
        // Optimistic update for faster UI feedback
        setState(s => ({ ...s, isLocked: true, error: null }));
        try {
            await lockVault();
            message.info('Vault Locked');
        } catch (err: any) {
            const errorMsg = `Failed to lock vault: ${err.message}`;
            // Revert optimistic update? Might cause flicker. Just show error.
            setState(s => ({ ...s, error: errorMsg })); // Keep locked=true
            message.error(errorMsg);
        }
    }, []);


    const value = { ...state, checkStatus, setup, unlock, lock };

    return <VaultAuthContext.Provider value={value}>{children}</VaultAuthContext.Provider>;
};

export const useVaultAuth = (): VaultAuthState & VaultAuthActions => {
    const context = useContext(VaultAuthContext);
    if (context === undefined) {
        throw new Error('useVaultAuth must be used within a VaultAuthProvider');
    }
    return context;
};