import { useState, useEffect, useCallback } from 'react';
import { listen, UnlistenFn } from '@tauri-apps/api/event';
import { message } from 'antd';
import { 
  OrmInitializationStatus, 
  ORM_STATUS_MESSAGES, 
  isErrorStatus, 
  isProgressStatus, 
  isSuccessStatus 
} from '../types/orm-initialization';

/**
 * ORM 初始化状态管理 Hook
 * 
 * 监听来自 Tauri 后端的 ORM 初始化状态事件，
 * 并提供相应的状态管理和用户反馈功能
 */
export function useOrmInitialization() {
  const [status, setStatus] = useState<OrmInitializationStatus | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 重置状态
   */
  const resetStatus = useCallback(() => {
    setStatus(null);
    setIsInitializing(false);
    setError(null);
  }, []);

  /**
   * 处理状态更新
   */
  const handleStatusUpdate = useCallback((newStatus: OrmInitializationStatus) => {
    setStatus(newStatus);
    
    if (isProgressStatus(newStatus.status)) {
      setIsInitializing(true);
      setError(null);
      
      // 显示进度信息
      if (newStatus.status === 'waiting_for_unlock' && newStatus.progress !== undefined) {
        message.loading({
          content: `${newStatus.message} (${newStatus.progress}%)`,
          key: 'orm-init',
          duration: 0, // 不自动关闭
        });
      } else {
        message.loading({
          content: newStatus.message,
          key: 'orm-init',
          duration: 0,
        });
      }
    } else if (isSuccessStatus(newStatus.status)) {
      setIsInitializing(false);
      setError(null);
      // 显示成功消息
      message.destroy('orm-init');

      // 不显示成功消息
      // message.success(newStatus.message, 3);
    } else if (isErrorStatus(newStatus.status)) {
      setIsInitializing(false);
      setError(newStatus.message);
      
      // 显示错误消息
      message.destroy('orm-init');
      message.error(newStatus.message, 5);
    }
  }, []);

  useEffect(() => {
    let unlisten: UnlistenFn | null = null;

    // 监听 ORM 初始化状态事件
    const setupListener = async () => {
      try {
        unlisten = await listen<OrmInitializationStatus>('orm_initialization_status', (event) => {
          console.log('收到 ORM 初始化状态更新:', event.payload);
          handleStatusUpdate(event.payload);
        });
      } catch (error) {
        console.error('设置 ORM 初始化状态监听器失败:', error);
      }
    };

    setupListener();

    // 清理函数
    return () => {
      if (unlisten) {
        unlisten();
      }
      // 清理可能存在的消息
      message.destroy('orm-init');
    };
  }, [handleStatusUpdate]);

  return {
    /** 当前 ORM 初始化状态 */
    status,
    /** 是否正在初始化 */
    isInitializing,
    /** 错误信息 */
    error,
    /** 重置状态 */
    resetStatus,
    /** 获取状态描述 */
    getStatusMessage: (statusKey: OrmInitializationStatus['status']) => 
      ORM_STATUS_MESSAGES[statusKey] || statusKey,
    /** 是否为错误状态 */
    isError: status ? isErrorStatus(status.status) : false,
    /** 是否为成功状态 */
    isSuccess: status ? isSuccessStatus(status.status) : false,
    /** 是否为进行中状态 */
    isProgress: status ? isProgressStatus(status.status) : false,
  };
} 