/**
 * 仪表板页面 - 登录后的主页
 */

import React from 'react';
import { Card, Row, Col, Statistic, Button, Space, Typography } from 'antd';
import {
  SafetyOutlined,
  KeyOutlined,
  SecurityScanOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { useAuthState, useAuthActions } from '../contexts';

const { Title, Paragraph } = Typography;

/**
 * 仪表板页面组件
 */
const DashboardPage: React.FC = () => {
  const { user } = useAuthState();
  const { logout } = useAuthActions();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="space-y-6">
      {/* 欢迎卡片 */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
        <div className="flex justify-between items-center">
          <div>
            <Title level={2} className="text-white m-0 mb-2">
              欢迎回来，{user?.username}！
            </Title>
            <Paragraph className="text-blue-100 mb-0">
              您的密码保险库已准备就绪，开始安全地管理您的密码吧
            </Paragraph>
          </div>
          <div className="text-right">
            <Button 
              type="primary" 
              ghost 
              icon={<LogoutOutlined />}
              onClick={handleLogout}
            >
              安全登出
            </Button>
          </div>
        </div>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="保存的密码"
              value={0}
              prefix={<KeyOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="安全评分"
              value={95}
              suffix="%"
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="保险库状态"
              value="已锁定"
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" className="shadow-sm">
        <Space size="large" wrap>
          <Button type="primary" size="large" icon={<KeyOutlined />}>
            添加密码
          </Button>
          <Button size="large" icon={<SafetyOutlined />}>
            解锁保险库
          </Button>
          <Button size="large" icon={<SecurityScanOutlined />}>
            安全检查
          </Button>
        </Space>
      </Card>

      {/* 用户信息 */}
      <Card title="账户信息" className="shadow-sm">
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div className="space-y-2">
              <div><strong>用户名:</strong> {user?.username}</div>
              <div><strong>邮箱:</strong> {user?.email || '未设置'}</div>
              <div><strong>手机:</strong> {user?.phone || '未设置'}</div>
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div className="space-y-2">
              <div><strong>账户状态:</strong> {user?.status || '正常'}</div>
              <div><strong>注册时间:</strong> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : '未知'}</div>
              <div><strong>保险库:</strong> 已创建</div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default DashboardPage; 