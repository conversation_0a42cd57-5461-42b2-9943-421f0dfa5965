import React from 'react';
import { Layout, Typography, Space, Alert } from 'antd';
import TrayControls from '../components/TrayControls';

const { Content } = Layout;
const { Title, Paragraph } = Typography;

/**
 * 托盘功能测试页面
 * 
 * 用于测试和演示系统托盘集成功能
 */
const TrayTestPage: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Content style={{ padding: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 页面标题 */}
          <div style={{ textAlign: 'center' }}>
            <Title level={2}>系统托盘集成测试</Title>
            <Paragraph type="secondary">
              测试应用程序的系统托盘功能，包括窗口隐藏、显示和退出控制
            </Paragraph>
          </div>

          {/* 功能说明 */}
          <Alert
            message="托盘功能说明"
            description={
              <div>
                <p><strong>已实现的功能：</strong></p>
                <ul>
                  <li>✅ 点击窗口关闭按钮时，应用最小化到系统托盘</li>
                  <li>✅ 系统托盘右键菜单，包含"显示主窗口"和"退出"选项</li>
                  <li>✅ 双击托盘图标快速显示主窗口</li>
                  <li>✅ 只有通过托盘菜单的"退出"选项才能真正关闭应用</li>
                  <li>✅ 前端可以通过 Tauri 命令控制托盘行为</li>
                </ul>
              </div>
            }
            type="info"
            showIcon
            style={{ maxWidth: 800, margin: '0 auto' }}
          />

          {/* 托盘控制组件 */}
          <TrayControls />

          {/* 测试说明 */}
          <Alert
            message="测试步骤"
            description={
              <div>
                <p><strong>请按以下步骤测试托盘功能：</strong></p>
                <ol>
                  <li>确认上方显示"托盘状态: ✅ 可用"</li>
                  <li>点击"隐藏到托盘"按钮，观察窗口是否隐藏</li>
                  <li>在系统托盘中找到应用图标</li>
                  <li>双击托盘图标，观察窗口是否重新显示</li>
                  <li>右键点击托盘图标，查看菜单选项</li>
                  <li>尝试点击窗口的关闭按钮（X），观察是否隐藏到托盘而不是退出</li>
                  <li>最后通过托盘菜单的"退出"选项或上方的"退出应用"按钮来关闭应用</li>
                </ol>
              </div>
            }
            type="warning"
            showIcon
            style={{ maxWidth: 800, margin: '0 auto' }}
          />
        </Space>
      </Content>
    </Layout>
  );
};

export default TrayTestPage;
