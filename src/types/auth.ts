/**
 * 认证相关的类型定义
 */

// 注册类型枚举
export type RegistrationType = 'Email' | 'Phone';

// 注册表单数据
export interface RegisterFormData {
  username: string;
  contact: string;
  registrationType: RegistrationType;
  password: string;
  confirmPassword: string;
  verificationCode: string;
  passwordHint?: string;
}

// 登录表单数据
export interface LoginFormData {
  contact: string;
  password: string;
  verificationCode?: string;
  rememberMe?: boolean;
}

// 远程用户数据
export interface RemoteUserData {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  status: string;
  createdAt: string;
}

// 注册安全信息
export interface RegistrationSecurityInfo {
  kdf_iterations: number;
  kdf_memory: number;
  kdf_parallelism: number;
  keychain_enabled: boolean;
}

// 完整注册结果
export interface CompleteRegistrationResult {
  remote_success: boolean;
  remote_message: string;
  remote_data?: RemoteUserData;
  vault_created: boolean;
  security_info: RegistrationSecurityInfo;
}

// 远程令牌信息
export interface RemoteTokenInfo {
  token_type: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

// 远程登录数据
export interface RemoteLoginData {
  token: RemoteTokenInfo;
}

// 远程登录响应
export interface RemoteLoginResponse {
  code: number;
  message: string;
  data?: RemoteLoginData;
}

// 连接测试结果
export interface RemoteConnectionTestResult {
  connected: boolean;
  message: string;
  server_url: string;
  response_time_ms?: number;
}

// 密码强度结果
export interface PasswordStrengthResult {
  strength: number;
  is_valid: boolean;
  feedback: string;
}

// 用户状态
export interface UserState {
  isAuthenticated: boolean;
  user?: RemoteUserData;
  token?: RemoteTokenInfo;
}

// API 响应包装器
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (value: any) => Promise<void> | void;
}

// 步骤状态
export interface StepStatus {
  current: number;
  status: 'wait' | 'process' | 'finish' | 'error';
  percent?: number;
} 