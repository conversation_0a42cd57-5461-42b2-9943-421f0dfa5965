/**
 * 类型定义统一导出
 */

export * from './auth';

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'auto';

// 通用类型
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}

// 页面状态
export interface PageState {
  loading: boolean;
  error?: string;
}

// 组件 Props 基础接口
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

// === 原有的数据结构（保持向后兼容） ===
export interface CredentialInput {
    service_name: string;
    username: string;
    password: string;
    notes?: string; // 可选备注
    custom_fields?: CustomField[] | undefined // 自定义字段;
}

export interface CredentialOutput {
    id: number; // JS 中使用 number 对应 Rust 的 i64
    service_name: string;
    username: string;
    password: string; // 解密后的密码
    notes?: string;
    custom_fields?: CustomField[] | undefined // 自定义字段;
    created_at: string; // ISO 格式日期字符串
    updated_at: string; // ISO 格式日期字符串
}

// Defines the structure for a single custom field
export interface CustomField {
    title: string;
    type: 'text' | 'password' | 'textarea'; // Add more types as needed
    value: string;
}

// Defines the structure for a password history entry returned from the backend
export interface PasswordHistoryEntry {
    id: number;
    field_name: string;
    old_value: string; // Decrypted old password value
    changed_at: string; // ISO format date string
}

// === 新的 Hybrid Storage 数据结构 ===

// 密码库信息
export interface VaultInfo {
    id: number;
    name: string;
    description?: string;
    created_at: string; // ISO格式日期字符串
    updated_at: string; // ISO格式日期字符串
    last_synced_at?: string; // ISO格式日期字符串
}

// 登录凭据输入结构
export interface LoginCredentialInput {
    name: string;
    username?: string;
    password: string;
    website?: string;
    notes?: string;
    favorite: boolean;
}

// 登录凭据输出结构
export interface LoginCredentialOutput {
    id: number;
    name: string;
    username?: string;
    password: string; // 解密后的密码
    website?: string;
    notes?: string;
    favorite: boolean;
    created_at: string; // ISO格式日期字符串
    updated_at: string; // ISO格式日期字符串
}

// 为了保持兼容性，创建类型别名
export type HybridCredentialInput = LoginCredentialInput;
export type HybridCredentialOutput = LoginCredentialOutput;

// ============================================================================
// 用户状态管理类型
// ============================================================================

/**
 * 当前用户信息
 */
export interface CurrentUser {
  /** 用户联系方式（邮箱或手机号） */
  contact: string;
  /** 用户昵称 */
  nickname?: string;
}

/**
 * 用户登录状态
 */
export interface UserLoginStatus {
  /** 是否已登录 */
  isLoggedIn: boolean;
  /** 当前用户信息 */
  user?: CurrentUser;
}