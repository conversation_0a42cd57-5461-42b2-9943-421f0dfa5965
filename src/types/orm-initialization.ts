/**
 * ORM 服务初始化状态类型定义
 */

export interface OrmInitializationStatus {
  /** 当前状态 */
  status: 'checking_crypto_system' | 'initializing_crypto' | 'waiting_for_unlock' | 'initializing_database' | 'success' | 'timeout' | 'crypto_error' | 'database_error';
  /** 状态描述信息 */
  message: string;
  /** 是否为错误状态 */
  error?: boolean;
  /** 进度百分比（仅在等待解锁时使用） */
  progress?: number;
}

/**
 * ORM 初始化状态的中文描述映射
 */
export const ORM_STATUS_MESSAGES: Record<OrmInitializationStatus['status'], string> = {
  checking_crypto_system: '正在检查加密系统状态...',
  initializing_crypto: '正在初始化加密系统...',
  waiting_for_unlock: '等待加密系统解锁...',
  initializing_database: '正在初始化数据库...',
  success: 'ORM 服务初始化成功',
  timeout: '初始化超时',
  crypto_error: '加密系统错误',
  database_error: '数据库错误'
};

/**
 * 判断是否为错误状态
 */
export function isErrorStatus(status: OrmInitializationStatus['status']): boolean {
  return ['timeout', 'crypto_error', 'database_error'].includes(status);
}

/**
 * 判断是否为进行中状态
 */
export function isProgressStatus(status: OrmInitializationStatus['status']): boolean {
  return ['checking_crypto_system', 'initializing_crypto', 'waiting_for_unlock', 'initializing_database'].includes(status);
}

/**
 * 判断是否为成功状态
 */
export function isSuccessStatus(status: OrmInitializationStatus['status']): boolean {
  return status === 'success';
} 